#!/usr/bin/env python3
"""
Test the implemented optimizations and suggest additional improvements
"""

import os
import sys
import time
import psutil
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.repomap import RepoMap, find_src_files
from aider.models import Model
from aider.io import InputOutput


def test_before_after_comparison():
    """Compare performance before and after optimizations"""
    
    print("🧪 Testing Before/After Optimization Comparison")
    print("=" * 60)
    
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Test 1: Original settings (4096 tokens)
    print("\n📊 Test 1: Original Settings (4,096 tokens)")
    start_time = time.time()
    memory_before = psutil.Process().memory_info().rss / 1024 / 1024
    
    repo_map_original = RepoMap(
        map_tokens=4096,
        root="aider-main",
        main_model=model,
        io=io,
        verbose=False,
        refresh="always"
    )
    
    all_files = find_src_files("aider-main")
    repo_content_original = repo_map_original.get_repo_map([], all_files)
    
    original_time = time.time() - start_time
    memory_after = psutil.Process().memory_info().rss / 1024 / 1024
    original_memory = memory_after - memory_before
    
    if repo_content_original:
        original_tokens = int(repo_map_original.token_count(repo_content_original))
        print(f"   ⏱️  Generation Time: {original_time:.2f} seconds")
        print(f"   💾 Memory Usage: {original_memory:.1f} MB")
        print(f"   🎯 Token Usage: {original_tokens:,} tokens")
        print(f"   📁 Files Processed: {len(all_files):,}")
        
        if original_tokens > 4096:
            print(f"   ⚠️  Over limit by {original_tokens - 4096:,} tokens")
        else:
            print(f"   ✅ Within limit")
    
    # Test 2: Optimized settings (20,202 tokens)
    print("\n📊 Test 2: Optimized Settings (20,202 tokens)")
    start_time = time.time()
    memory_before = psutil.Process().memory_info().rss / 1024 / 1024
    
    repo_map_optimized = RepoMap(
        map_tokens=20202,
        root="aider-main",
        main_model=model,
        io=io,
        verbose=False,
        refresh="always"
    )
    
    repo_content_optimized = repo_map_optimized.get_repo_map([], all_files)
    
    optimized_time = time.time() - start_time
    memory_after = psutil.Process().memory_info().rss / 1024 / 1024
    optimized_memory = memory_after - memory_before
    
    if repo_content_optimized:
        optimized_tokens = int(repo_map_optimized.token_count(repo_content_optimized))
        print(f"   ⏱️  Generation Time: {optimized_time:.2f} seconds")
        print(f"   💾 Memory Usage: {optimized_memory:.1f} MB")
        print(f"   🎯 Token Usage: {optimized_tokens:,} tokens")
        print(f"   📁 Files Processed: {len(all_files):,}")
        print(f"   ✅ Within limit ({20202 - optimized_tokens:,} tokens remaining)")
    
    # Test 3: File filtering impact (if available)
    print("\n📊 Test 3: File Filtering Impact")
    try:
        # Try to use optimized file discovery
        exec("""
# Inline optimized file discovery for testing
def find_src_files_optimized_test(root_path):
    import os
    from pathlib import Path
    
    EXCLUDE_EXTENSIONS = {
        '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.bmp', '.tiff',
        '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.ogg',
        '.wav', '.flac', '.aac', '.m4a', '.ttf', '.woff', '.woff2', '.eot', 
        '.otf', '.pyc', '.pyo', '.class', '.o', '.so', '.dll', '.exe', 
        '.obj', '.lib', '.a', '.dylib', '.db', '.db-shm', '.db-wal', 
        '.sqlite', '.sqlite3', '.cache', '.tmp', '.temp', '.log', '.txt', 
        '.pdf', '.doc', '.docx', '.rtf', '.zip', '.tar', '.gz', '.rar', 
        '.7z', '.bz2', '.swp', '.swo', '.bak', '.orig', '.rej', '.bin', 
        '.dat', '.dump'
    }
    
    SKIP_DIRS = {
        '__pycache__', '.git', '.svn', '.hg', '.bzr',
        'node_modules', '.venv', 'venv', 'env',
        '.pytest_cache', '.mypy_cache', '.tox',
        'build', 'dist', '.egg-info',
        '.idea', '.vscode', '.vs',
        'coverage_html_report', 'htmlcov'
    }
    
    src_files = []
    
    for root, dirs, files in os.walk(root_path):
        dirs[:] = [d for d in dirs if d not in SKIP_DIRS]
        
        for file in files:
            file_path = os.path.join(root, file)
            ext = Path(file).suffix.lower()
            
            if ext in EXCLUDE_EXTENSIONS:
                continue
                
            if file.startswith('.') and ext not in {'.py', '.js', '.ts', '.go', '.rs'}:
                continue
                
            try:
                if os.path.getsize(file_path) > 10 * 1024 * 1024:
                    continue
            except OSError:
                continue
                
            src_files.append(file_path)
    
    return src_files
""")
        
        filtered_files = find_src_files_optimized_test("aider-main")
        
        start_time = time.time()
        repo_content_filtered = repo_map_optimized.get_repo_map([], filtered_files)
        filtered_time = time.time() - start_time
        
        if repo_content_filtered:
            filtered_tokens = int(repo_map_optimized.token_count(repo_content_filtered))
            print(f"   ⏱️  Generation Time: {filtered_time:.2f} seconds")
            print(f"   🎯 Token Usage: {filtered_tokens:,} tokens")
            print(f"   📁 Files Processed: {len(filtered_files):,}")
            print(f"   💾 Files Excluded: {len(all_files) - len(filtered_files):,}")
            print(f"   📉 Token Reduction: {optimized_tokens - filtered_tokens:,} tokens")
    
    except Exception as e:
        print(f"   ⚠️  File filtering test failed: {e}")
    
    # Summary
    print("\n📋 Performance Summary")
    print("=" * 40)
    if repo_content_original and repo_content_optimized:
        time_improvement = ((original_time - optimized_time) / original_time) * 100
        memory_improvement = ((original_memory - optimized_memory) / original_memory) * 100
        
        print(f"⏱️  Time Change: {time_improvement:+.1f}%")
        print(f"💾 Memory Change: {memory_improvement:+.1f}%")
        print(f"🎯 Token Budget: {((20202 - 4096) / 4096) * 100:.0f}% increase")
        print(f"📊 Coverage: {optimized_tokens:,} tokens used of {20202:,} available")


def suggest_additional_improvements():
    """Suggest additional optimization opportunities"""
    
    print("\n🚀 Additional Optimization Opportunities")
    print("=" * 60)
    
    improvements = [
        {
            "title": "1. Intelligent File Prioritization",
            "description": "Implement PageRank-based file ranking",
            "impact": "High",
            "effort": "Medium",
            "details": [
                "Rank files by dependency importance",
                "Prioritize frequently modified files",
                "Weight by symbol reference count",
                "Focus on core architecture files"
            ]
        },
        {
            "title": "2. Incremental Repository Updates",
            "description": "Only process changed files",
            "impact": "High", 
            "effort": "High",
            "details": [
                "Track file modification timestamps",
                "Maintain persistent symbol cache",
                "Delta updates for changed files only",
                "Cross-session cache persistence"
            ]
        },
        {
            "title": "3. Parallel Processing",
            "description": "Multi-threaded file analysis",
            "impact": "Medium",
            "effort": "Medium",
            "details": [
                "Concurrent file parsing",
                "Parallel symbol extraction",
                "Async I/O for file reading",
                "Thread pool for tree-sitter parsing"
            ]
        },
        {
            "title": "4. Smart Token Allocation",
            "description": "Dynamic token distribution",
            "impact": "Medium",
            "effort": "Low",
            "details": [
                "Allocate more tokens to core files",
                "Reduce tokens for test files",
                "Adaptive based on file importance",
                "Context-aware token budgeting"
            ]
        },
        {
            "title": "5. Advanced Caching Strategy",
            "description": "Multi-level caching system",
            "impact": "High",
            "effort": "High",
            "details": [
                "File-level cache granularity",
                "Symbol-level caching",
                "Dependency-aware invalidation",
                "Compressed cache storage"
            ]
        },
        {
            "title": "6. Repository Structure Analysis",
            "description": "Understand project architecture",
            "impact": "Medium",
            "effort": "Medium",
            "details": [
                "Identify main entry points",
                "Map module dependencies",
                "Detect architectural patterns",
                "Prioritize public APIs"
            ]
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{improvement['title']}")
        print(f"   📝 {improvement['description']}")
        print(f"   📊 Impact: {improvement['impact']} | Effort: {improvement['effort']}")
        print("   🔧 Implementation:")
        for detail in improvement['details']:
            print(f"      • {detail}")
    
    return improvements


def create_implementation_roadmap(improvements):
    """Create a roadmap for implementing additional optimizations"""
    
    roadmap = """
# Repository Map Optimization Roadmap

## Phase 1: Quick Wins (1-2 weeks)
### 🎯 Smart Token Allocation
- Implement file importance scoring
- Allocate tokens based on file type and usage
- Reduce token allocation for test files
- **Expected Impact**: 20-30% better token utilization

### 🔧 Implementation
```python
def calculate_file_importance(file_path):
    score = 1.0
    
    # Core files get higher priority
    if 'main' in file_path or 'core' in file_path:
        score *= 2.0
    
    # Test files get lower priority  
    if 'test' in file_path or 'spec' in file_path:
        score *= 0.5
    
    # API files get higher priority
    if 'api' in file_path or 'interface' in file_path:
        score *= 1.5
    
    return score
```

## Phase 2: Performance Optimization (2-4 weeks)
### ⚡ Parallel Processing
- Multi-threaded file parsing
- Concurrent symbol extraction
- Async I/O operations
- **Expected Impact**: 40-60% faster generation

### 🔧 Implementation
```python
import concurrent.futures
import asyncio

async def process_files_parallel(files, max_workers=4):
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        tasks = [executor.submit(process_file, file) for file in files]
        results = await asyncio.gather(*[asyncio.wrap_future(task) for task in tasks])
    return results
```

## Phase 3: Intelligence Layer (4-6 weeks)
### 🧠 File Prioritization
- PageRank algorithm for file importance
- Dependency graph analysis
- Usage pattern learning
- **Expected Impact**: 50-70% better coverage with same tokens

### 🔧 Implementation
```python
def build_dependency_graph(files):
    graph = {}
    for file in files:
        imports = extract_imports(file)
        graph[file] = imports
    return graph

def calculate_pagerank(graph, iterations=50):
    # Implement PageRank algorithm
    ranks = {}
    # ... PageRank implementation
    return ranks
```

## Phase 4: Advanced Caching (6-8 weeks)
### 💾 Incremental Updates
- File modification tracking
- Symbol-level caching
- Persistent cache storage
- **Expected Impact**: 80-90% faster subsequent runs

### 🔧 Implementation
```python
class IncrementalRepoMap:
    def __init__(self):
        self.cache = PersistentCache()
        self.file_timestamps = {}
    
    def update_if_changed(self, file_path):
        current_mtime = os.path.getmtime(file_path)
        if file_path not in self.file_timestamps or 
           self.file_timestamps[file_path] < current_mtime:
            return self.process_file(file_path)
        return self.cache.get(file_path)
```

## Implementation Priority Matrix

| Optimization | Impact | Effort | Priority | Timeline |
|-------------|--------|--------|----------|----------|
| Smart Token Allocation | Medium | Low | High | Week 1-2 |
| File Type Exclusion | High | Low | High | ✅ Done |
| Token Limit Increase | High | Low | High | ✅ Done |
| Parallel Processing | Medium | Medium | Medium | Week 3-4 |
| File Prioritization | High | Medium | High | Week 5-6 |
| Incremental Updates | High | High | Medium | Week 7-8 |
| Advanced Caching | High | High | Low | Week 9-10 |

## Success Metrics

### Performance Targets
- **Generation Time**: < 2 seconds (currently ~6 seconds)
- **Memory Usage**: < 50MB (currently ~225MB)
- **Token Efficiency**: > 90% source code content
- **Cache Hit Rate**: > 80%

### Quality Targets
- **Symbol Detection**: Maintain 100% accuracy
- **Coverage**: Include all critical source files
- **LLM Response Quality**: Maintain or improve
- **CONTEXT_REQUEST Speed**: < 1 second

## Monitoring Dashboard

Track these metrics continuously:
```python
metrics = {
    'generation_time': time_seconds,
    'memory_usage_mb': memory_mb,
    'token_utilization': tokens_used / tokens_available,
    'cache_hit_rate': cache_hits / total_requests,
    'file_coverage': source_files / total_files,
    'symbol_accuracy': detected_symbols / actual_symbols
}
```
"""
    
    with open('optimization_roadmap.md', 'w', encoding='utf-8') as f:
        f.write(roadmap)
    
    print(f"\n📄 Created optimization_roadmap.md")


def main():
    """Main testing and improvement suggestion function"""
    
    # Test current optimizations
    test_before_after_comparison()
    
    # Suggest additional improvements
    improvements = suggest_additional_improvements()
    
    # Create implementation roadmap
    create_implementation_roadmap(improvements)
    
    print("\n" + "=" * 60)
    print("✅ OPTIMIZATION ANALYSIS COMPLETE")
    print("=" * 60)
    print("\n📋 Summary:")
    print("✅ File exclusion implemented")
    print("✅ Token limit increased to 20,202")
    print("✅ Configuration files created")
    print("📄 optimization_roadmap.md created")
    print("\n🚀 Ready for additional optimizations!")


if __name__ == "__main__":
    main()
