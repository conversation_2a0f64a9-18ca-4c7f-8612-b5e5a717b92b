# USER QUERY
Show me the UnknownEditFormat class and its __init__ method inheritance patterns

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze the UnknownEditFormat class inheritance from ValueError and its __init__ method super() call

## CRITICAL ENTITIES (2 most important)

### 1. dmp_apply (function)
- File: search_replace.py


- Criticality: medium | Risk: medium
- **Calls**: ["diff_match_patch", "diff_main", "diff_cleanupSemantic", "diff_cleanupEfficiency", "patch_make", "..."] (total: 12)
- **Used by**: []
- **Side Effects**: writes_log, network_io, modifies_file

### 2. map_patches (function)
- File: search_replace.py


- Criticality: medium | Risk: low
- **Calls**: ["diff_match_patch", "diff_main", "diff_prettyHtml", "write_text", "Path", "..."] (total: 7)
- **Used by**: ["search_replace"] (total: 1)
- **Side Effects**: writes_log, network_io, modifies_file

## KEY IMPLEMENTATIONS (2 functions)

### 1. dmp_apply
```python
def dmp_apply(texts, remap=True):
    debug = False
    # debug = True

    search_text, replace_text, original_text = texts

    dmp = diff_match_patch()
    dmp.Diff_Timeout = 5
    # dmp.Diff_EditCost = 16

    if remap:
        dmp.Match_Threshold = 0.95
        dmp.Match_Distance = 500
        dmp.Match_MaxBits = 128
        dmp.Patch_Margin = 32
    else:
        dmp.Match_Threshold = 0.5
        dmp.Match_Distance = 100_000
        dmp.Match_MaxBits = 32
        dmp.Patch_Margin = 8

    diff = dmp.diff_main(search_text, replace_text, None)
    dmp.diff_cleanupSemantic(diff)
    dmp.diff_cleanupEfficiency(diff)

    patches = dmp.patch_make(search_text, diff)

    if debug:
        html = dmp.diff_prettyHtml(diff)
        Path("tmp.search_replace_diff.html").write_text(html)

        for d in diff:
    # ... (implementation continues)
```

### 2. map_patches
```python
def map_patches(texts, patches, debug):
    search_text, replace_text, original_text = texts

    dmp = diff_match_patch()
    dmp.Diff_Timeout = 5

    diff_s_o = dmp.diff_main(search_text, original_text)
    # diff_r_s = dmp.diff_main(replace_text, search_text)

    # dmp.diff_cleanupSemantic(diff_s_o)
    # dmp.diff_cleanupEfficiency(diff_s_o)

    if debug:
        html = dmp.diff_prettyHtml(diff_s_o)
        Path("tmp.html").write_text(html)

        dump(len(search_text))
        dump(len(original_text))

    for patch in patches:
        start1 = patch.start1
        start2 = patch.start2

        patch.start1 = dmp.diff_xIndex(diff_s_o, start1)
        patch.start2 = dmp.diff_xIndex(diff_s_o, start2)

        if debug:
            print()
            print(start1, repr(search_text[start1 : start1 + 50]))
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 2 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Show me the UnknownEditFormat class and its __init__ method inheritance patterns

Provide specific, actionable insights based on this focused context.
