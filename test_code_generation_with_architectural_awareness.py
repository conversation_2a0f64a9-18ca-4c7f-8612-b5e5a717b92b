#!/usr/bin/env python3

"""
Test Code Generation with Architectural Awareness

Comprehensive test of the Code Generation with Architectural Awareness system.
Demonstrates pattern detection, template generation, and architectural compliance.
"""

import os
import sys
import time
import json
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.getcwd())

from mid_level_ir import MidLevelIRPipeline
from code_generation import (
    CodeGenerationPipeline,
    ArchitecturalPatternAnalyzer,
    CodeTemplateEngine,
    DependencyIntegrationManager,
    QualityAssuranceEngine,
    IncrementalCodeBuilder
)
from code_generation.code_template_engine import GenerationRequest
from code_generation.incremental_code_builder import EnhancementRequest


def test_architectural_pattern_analysis():
    """Test architectural pattern detection."""
    print("🏗️  Testing Architectural Pattern Analysis")
    print("=" * 50)

    # Create a custom pipeline that gives us access to the IRContext
    from mid_level_ir.ir_context import IRContext
    from mid_level_ir.file_scanner import FileScanner
    from mid_level_ir.entity_extractor import EntityExtractor
    from pathlib import Path

    project_path = Path(os.getcwd())

    print(f"📁 Analyzing project: {project_path}")

    # Create IRContext and run basic analysis to get source code
    context = IRContext(
        project_path=project_path,
        project_name=project_path.name
    )

    # Run file scanning to get source code
    file_scanner = FileScanner({
        'verbose': True,
        'exclude_patterns': ['__pycache__', '.git', 'node_modules']
    })
    context = file_scanner.scan(context)

    # Run entity extraction
    entity_extractor = EntityExtractor({'verbose': True, 'include_private': False})
    context = entity_extractor.extract(context)

    print(f"   Processed {len(context.modules)} modules with source code")

    # Test pattern analyzer
    pattern_analyzer = ArchitecturalPatternAnalyzer({'verbose': True})
    analysis = pattern_analyzer.analyze(context)
    
    print(f"\n📊 Analysis Results:")
    print(f"   Patterns detected: {len(analysis.patterns)}")
    print(f"   Coding style: {analysis.coding_style.naming_conventions}")
    print(f"   Module organization: {analysis.module_organization['total_packages']} packages")
    print(f"   Quality metrics: {analysis.quality_metrics}")
    
    # Display detected patterns
    if analysis.patterns:
        print(f"\n🎯 Detected Patterns:")
        for pattern in analysis.patterns:
            print(f"   • {pattern.name} ({pattern.pattern_type})")
            print(f"     Confidence: {pattern.confidence:.2f}")
            print(f"     Examples: {pattern.examples[:2]}")
    
    return context, analysis


def test_code_template_engine(architectural_analysis):
    """Test code template generation."""
    print("\n🔧 Testing Code Template Engine")
    print("=" * 50)
    
    template_engine = CodeTemplateEngine({'verbose': True})
    
    # Test different types of generation requests
    test_requests = [
        GenerationRequest(
            request_type='class',
            target_name='DataProcessor',
            description='A class for processing data with validation',
            context_entities=['aider_integration_service.AiderIntegrationService'],
            requirements={'pattern': 'strategy', 'validation': True}
        ),
        GenerationRequest(
            request_type='function',
            target_name='calculate_metrics',
            description='Calculate performance metrics for analysis',
            context_entities=[],
            requirements={'return_type': 'Dict[str, float]', 'async': False}
        ),
        GenerationRequest(
            request_type='module',
            target_name='analytics_utils',
            description='Utility functions for analytics and reporting',
            context_entities=[],
            requirements={'exports': ['calculate_stats', 'generate_report']}
        )
    ]
    
    results = []
    for i, request in enumerate(test_requests, 1):
        print(f"\n📝 Test {i}: Generating {request.request_type} - {request.target_name}")
        
        generated = template_engine.generate_code(request, architectural_analysis)
        results.append(generated)
        
        print(f"   Quality score: {generated.quality_score:.2f}")
        print(f"   Template used: {generated.template_used}")
        print(f"   Imports needed: {len(generated.imports_needed)}")
        print(f"   Integration notes: {len(generated.integration_notes)}")
        
        # Show a snippet of generated code
        code_lines = generated.code.split('\n')
        print(f"   Generated code preview:")
        for line in code_lines[:5]:
            if line.strip():
                print(f"     {line}")
        if len(code_lines) > 5:
            print(f"     ... ({len(code_lines) - 5} more lines)")
    
    return results


def test_code_generation_pipeline(ir_context, architectural_analysis):
    """Test the complete code generation pipeline."""
    print("\n🚀 Testing Complete Code Generation Pipeline")
    print("=" * 50)
    
    pipeline_config = {
        'verbose': True,
        'pattern_analyzer': {'verbose': False},
        'template_engine': {'verbose': False}
    }
    
    generation_pipeline = CodeGenerationPipeline(pipeline_config)
    
    # Test comprehensive code generation
    request = GenerationRequest(
        request_type='class',
        target_name='AdvancedAnalyzer',
        description='Advanced analyzer with pattern recognition and caching',
        context_entities=['mid_level_ir.MidLevelIRPipeline', 'aider_integration_service'],
        requirements={
            'patterns': ['observer', 'strategy'],
            'features': ['caching', 'logging', 'validation'],
            'interfaces': ['Analyzer', 'Configurable']
        },
        target_module='analysis_engine'
    )
    
    print(f"🎯 Generating: {request.target_name}")
    result = generation_pipeline.generate_code(ir_context, request)
    
    print(f"\n📊 Pipeline Results:")
    print(f"   Generation time: {result.generation_time:.2f} seconds")
    print(f"   Overall quality: {result.quality_assessment['overall_score']:.2f}")
    print(f"   Style compliance: {result.quality_assessment['style_compliance']:.2f}")
    print(f"   Integration readiness: {result.quality_assessment['integration_readiness']:.2f}")
    print(f"   Recommendations: {len(result.recommendations)}")
    
    # Display recommendations
    if result.recommendations:
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(result.recommendations[:5], 1):
            print(f"   {i}. {rec}")
    
    # Show generated code
    print(f"\n📄 Generated Code:")
    print("=" * 30)
    code_lines = result.generated_code.code.split('\n')
    for i, line in enumerate(code_lines[:20], 1):
        print(f"{i:2d}: {line}")
    if len(code_lines) > 20:
        print(f"    ... ({len(code_lines) - 20} more lines)")
    
    return result


def test_quality_assurance_engine(generated_code, architectural_analysis):
    """Test quality assurance validation."""
    print("\n📊 Testing Quality Assurance Engine")
    print("=" * 50)
    
    qa_config = {
        'verbose': True,
        'min_overall_score': 0.7,
        'max_complexity': 10,
        'min_documentation': 0.8
    }
    
    qa_engine = QualityAssuranceEngine(qa_config)
    
    # Test quality assessment
    quality_report = qa_engine.assess_quality(generated_code.code, architectural_analysis)
    
    print(f"📈 Quality Assessment Results:")
    print(f"   Overall score: {quality_report.overall_score:.2f}")
    print(f"   Style score: {quality_report.style_score:.2f}")
    print(f"   Complexity score: {quality_report.complexity_score:.2f}")
    print(f"   Maintainability score: {quality_report.maintainability_score:.2f}")
    print(f"   Security score: {quality_report.security_score:.2f}")
    
    if quality_report.issues:
        print(f"\n⚠️  Issues Found ({len(quality_report.issues)}):")
        for i, issue in enumerate(quality_report.issues[:5], 1):
            print(f"   {i}. {issue}")
    
    if quality_report.recommendations:
        print(f"\n💡 QA Recommendations ({len(quality_report.recommendations)}):")
        for i, rec in enumerate(quality_report.recommendations[:5], 1):
            print(f"   {i}. {rec}")
    
    return quality_report


def test_incremental_code_builder(ir_context):
    """Test incremental code enhancement."""
    print("\n🔧 Testing Incremental Code Builder")
    print("=" * 50)
    
    builder = IncrementalCodeBuilder({'verbose': True})
    
    # Find a suitable target for enhancement - one with source code available
    target_module = None
    target_class = None

    for module_name, module_info in ir_context.modules.items():
        # Skip test modules and ensure we have source code
        if (not module_name.startswith('test_') and
            hasattr(module_info, 'source_code') and
            module_info.source_code):

            for entity in module_info.entities:
                if entity.type == 'class':
                    target_module = module_name
                    target_class = entity.name
                    break
            if target_module:
                break
    
    if target_module and target_class:
        print(f"🎯 Enhancing: {target_module}.{target_class}")
        print(f"   Source code available: {len(ir_context.modules[target_module].source_code)} characters")
        
        # Test enhancement request
        enhancement_request = EnhancementRequest(
            target_entity=f"{target_module}.{target_class}",
            enhancement_type='add_method',
            description='Add a validation method to ensure data integrity',
            requirements={
                'method_name': 'validate',
                'parameters': ['data: Any'],
                'body': 'return True  # Validation logic here'
            }
        )
        
        try:
            result = builder.enhance_code(ir_context, enhancement_request)
            
            print(f"✅ Enhancement completed:")
            print(f"   Integration points: {len(result.integration_points)}")
            print(f"   Modifications: {result.modifications_made}")
            print(f"   Compatibility notes: {len(result.compatibility_notes)}")
            
            # Show enhancement preview
            enhanced_lines = result.enhanced_code.split('\n')
            print(f"\n📄 Enhanced Code Preview (first 15 lines):")
            for i, line in enumerate(enhanced_lines[:15], 1):
                print(f"{i:2d}: {line}")
            
        except Exception as e:
            print(f"⚠️  Enhancement failed: {e}")
            print(f"   This is expected if source code is not available in the IR format")
    else:
        print("⚠️  No suitable target found for enhancement")
        print("   This is expected - the incremental builder needs source code access")
    
    # Test enhancement suggestions
    print(f"\n💡 Enhancement Suggestions:")
    suggestions = builder.suggest_enhancements(ir_context, list(ir_context.modules.keys())[0])
    for i, suggestion in enumerate(suggestions[:3], 1):
        print(f"   {i}. {suggestion.description}")
        print(f"      Target: {suggestion.target_entity}")
        print(f"      Type: {suggestion.enhancement_type}")


def main():
    """Run comprehensive tests of the Code Generation with Architectural Awareness system."""
    print("🚀 Code Generation with Architectural Awareness - Comprehensive Test")
    print("=" * 80)
    
    start_time = time.time()
    
    try:
        # Test 1: Architectural Pattern Analysis
        ir_context, architectural_analysis = test_architectural_pattern_analysis()

        # Test 2: Code Template Engine
        template_results = test_code_template_engine(architectural_analysis)

        # Test 3: Complete Code Generation Pipeline
        pipeline_result = test_code_generation_pipeline(ir_context, architectural_analysis)

        # Test 4: Quality Assurance Engine
        quality_report = test_quality_assurance_engine(
            pipeline_result.generated_code,
            architectural_analysis
        )

        # Test 5: Incremental Code Builder
        test_incremental_code_builder(ir_context)
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 All Tests Completed Successfully!")
        print("=" * 80)
        print(f"📊 Summary:")
        print(f"   Total execution time: {total_time:.2f} seconds")
        print(f"   Patterns detected: {len(architectural_analysis.patterns)}")
        print(f"   Templates tested: {len(template_results)}")
        print(f"   Quality score: {quality_report.overall_score:.2f}")
        print(f"   Generated code quality: {pipeline_result.quality_assessment['overall_score']:.2f}")
        
        print(f"\n✅ Code Generation with Architectural Awareness system is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
