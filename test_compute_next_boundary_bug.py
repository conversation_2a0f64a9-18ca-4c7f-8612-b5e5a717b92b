#!/usr/bin/env python3
"""
Test the specific compute_next_boundary bug to prove the root cause.
"""

import os
import sys
import json

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_compute_next_boundary_search():
    """Test search for compute_next_boundary function specifically."""
    print("🔍 Testing compute_next_boundary Search Bug")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components pointing to the trading dashboard
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use absolute path to the trading dashboard
        dashboard_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        
        if not os.path.exists(dashboard_path):
            print(f"❌ Dashboard path does not exist: {dashboard_path}")
            return False
        
        print(f"✅ Dashboard path exists: {dashboard_path}")
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=dashboard_path,
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=dashboard_path,
            io=io
        )
        
        print("✅ Smart Map Request Handler created")
        
        # Test 1: Check if the file exists
        print("\n🧪 Test 1: Check if market_data_repository.py exists")
        print("-" * 60)
        
        target_file = os.path.join(dashboard_path, "market_data", "market_data_repository.py")
        file_exists = os.path.exists(target_file)
        print(f"File exists: {file_exists} ({target_file})")
        
        if file_exists:
            # Check if function exists in file
            with open(target_file, 'r', encoding='utf-8') as f:
                content = f.read()
                function_in_file = "compute_next_boundary" in content
                print(f"Function in file: {function_in_file}")
                
                if function_in_file:
                    # Show the function definition
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if "compute_next_boundary" in line:
                            print(f"   Line {i+1}: {line.strip()}")
                            # Show a few lines of context
                            for j in range(max(0, i-2), min(len(lines), i+5)):
                                if j != i:
                                    print(f"   Line {j+1}: {lines[j].strip()}")
                            break
        else:
            print("❌ Target file does not exist - this explains the issue!")
            return False
        
        # Test 2: Check repository files
        print("\n🧪 Test 2: Check repository files")
        print("-" * 60)
        
        all_files = handler._get_repository_files()
        print(f"Total repository files: {len(all_files)}")
        
        # Look for market_data files
        market_data_files = [f for f in all_files if "market_data" in f.lower()]
        print(f"Market data files: {len(market_data_files)}")
        for file_path in market_data_files:
            print(f"   - {file_path}")
        
        # Test 3: Check repository tags
        print("\n🧪 Test 3: Check repository tags")
        print("-" * 60)
        
        ranked_tags = handler._get_ranked_tags(all_files)
        print(f"Total repository tags: {len(ranked_tags)}")
        
        # Look for function tags
        function_tags = []
        compute_boundary_tags = []
        
        for tag in ranked_tags:
            if hasattr(tag, 'kind') and hasattr(tag, 'name'):
                if tag.kind in ['function', 'method']:
                    function_tags.append(tag)
                    if "compute_next_boundary" in tag.name.lower():
                        compute_boundary_tags.append(tag)
                        print(f"   🎯 FOUND: {tag.name} in {tag.rel_fname}")
        
        print(f"Total function/method tags: {len(function_tags)}")
        print(f"compute_next_boundary tags: {len(compute_boundary_tags)}")
        
        if function_tags:
            print("Example function tags:")
            for tag in function_tags[:10]:
                print(f"   - {tag.name} ({tag.kind}) in {tag.rel_fname}")
        
        # Test 4: Test the actual search
        print("\n🧪 Test 4: Test MAP_REQUEST search")
        print("-" * 60)
        
        map_request = {
            "keywords": ["compute_next_boundary", "boundary", "schedule", "interval"],
            "type": "implementation",
            "scope": "all",
            "max_results": 8
        }
        
        print(f"MAP_REQUEST: {json.dumps(map_request, indent=2)}")
        
        # Process the request
        result = handler.handle_map_request(map_request)
        
        print(f"\nResult length: {len(result)} characters")
        
        # Check if the function is in the result
        function_in_result = "compute_next_boundary" in result
        print(f"Function in result: {function_in_result}")
        
        if function_in_result:
            print("✅ SUCCESS: Function found in MAP_REQUEST result!")
            return True
        else:
            print("❌ FAILURE: Function NOT found in MAP_REQUEST result!")
            print(f"Result preview: {result[:500]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error in compute_next_boundary test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_function_search():
    """Test the function search method directly."""
    print("\n🔍 Testing Direct Function Search")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        dashboard_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=dashboard_path,
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=dashboard_path,
            io=io
        )
        
        # Get repository data
        all_files = handler._get_repository_files()
        ranked_tags = handler._get_ranked_tags(all_files)
        
        print(f"Files: {len(all_files)}, Tags: {len(ranked_tags)}")
        
        # Test function search directly
        keywords = ["compute_next_boundary"]
        function_matches = handler._search_function_names(keywords, ranked_tags)
        
        print(f"Direct function search results: {len(function_matches)}")
        
        for match in function_matches:
            print(f"   - {match['file']} (score: {match['relevance_score']:.1f}) - {match.get('symbol', 'N/A')}")
        
        if function_matches:
            print("✅ Direct function search found matches!")
            return True
        else:
            print("❌ Direct function search found no matches!")
            
            # Debug: Show what function tags we do have
            print("\nDebugging: Available function tags:")
            function_count = 0
            for tag in ranked_tags:
                if hasattr(tag, 'kind') and hasattr(tag, 'name') and tag.kind in ['function', 'method']:
                    function_count += 1
                    if function_count <= 10:  # Show first 10
                        print(f"   - {tag.name} ({tag.kind}) in {tag.rel_fname}")
            
            print(f"Total function tags available: {function_count}")
            return False
        
    except Exception as e:
        print(f"❌ Error in direct function search test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run compute_next_boundary bug tests."""
    print("🚀 Testing compute_next_boundary Bug")
    print("=" * 100)
    
    tests = [
        ("compute_next_boundary Search", test_compute_next_boundary_search),
        ("Direct Function Search", test_direct_function_search),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 compute_next_boundary BUG TESTING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == 0:
        print("🚨 BUG CONFIRMED!")
        print("   The Smart Map Request System is failing to find existing functions")
        print("   Root cause: Repository tag generation or file path issues")
    elif passed == total:
        print("🎉 BUG FIXED!")
        print("   The Smart Map Request System is working correctly")
    else:
        print("⚠️  PARTIAL SUCCESS!")
        print("   Some aspects working, others still broken")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
