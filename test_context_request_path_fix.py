#!/usr/bin/env python3

"""
Test to verify that the context request path fix works correctly.
This test verifies that the context request system uses the correct project path.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_context_request_path_resolution():
    """Test that context request system resolves to the correct project path."""
    
    print("🧪 Testing Context Request Path Resolution Fix")
    print("=" * 50)
    
    # Create a temporary trading project structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary project at: {temp_dir}")
        
        # Create the trading project structure
        trade_management_dir = os.path.join(temp_dir, "trade_management")
        services_dir = os.path.join(temp_dir, "services")
        os.makedirs(trade_management_dir)
        os.makedirs(services_dir)
        
        # Create position_exit_manager.py
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        # Check take profit conditions  
        if self._check_take_profit(app):
            await self._execute_close(app, "take_profit")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    def _check_take_profit(self, app):
        """Check if take profit conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_exit_manager_path = os.path.join(trade_management_dir, "position_exit_manager.py")
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        # Create position_observer.py
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    def add_observer(self, observer):
        """Add a position observer."""
        self.observers.append(observer)
    
    def notify_position_change(self, position):
        """Notify all observers of position changes."""
        for observer in self.observers:
            observer.on_position_change(position)
    
    async def close_position_based_on_conditions(self, app):
        """
        Alternative implementation of position closing.
        
        Args:
            app: The application context
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # This is a different implementation
        return self._evaluate_market_conditions(app)
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📄 {position_observer_path}")
        
        # Change to the temporary directory to simulate user running aider from project root
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            print(f"📁 Changed working directory to: {temp_dir}")
            
            # Test the context request system
            try:
                # Import the context request system
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
                from aider.context_request.aider_integration_service import AiderIntegrationService
                
                print("✅ Successfully imported context request modules")
                
                # Create a context request handler with the current directory
                aider_service = AiderIntegrationService()
                handler = ContextRequestHandler(temp_dir, aider_service)
                
                print(f"📁 Handler project path: {handler.project_path}")
                
                # Test finding the files
                symbol1 = SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"
                )
                
                symbol2 = SymbolRequest(
                    type="method_definition", 
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"
                )
                
                # Test file resolution
                file1 = handler._find_file_for_symbol(symbol1)
                file2 = handler._find_file_for_symbol(symbol2)
                
                print(f"🔍 File 1 resolution: {file1}")
                print(f"🔍 File 2 resolution: {file2}")
                
                # Verify the files were found
                if file1 and file2:
                    print("✅ SUCCESS: Both files were found correctly!")
                    
                    # Test symbol extraction
                    file_path1, symbol_name1, content1 = handler._extract_symbol_content(symbol1)
                    file_path2, symbol_name2, content2 = handler._extract_symbol_content(symbol2)
                    
                    if content1 and content2:
                        print("✅ SUCCESS: Symbol content extracted successfully!")
                        print(f"   📄 Extracted from {file_path1}: {len(content1)} characters")
                        print(f"   📄 Extracted from {file_path2}: {len(content2)} characters")
                        return True
                    else:
                        print("❌ FAILED: Could not extract symbol content")
                        return False
                else:
                    print("❌ FAILED: Files were not found")
                    print(f"   Expected: trade_management/position_exit_manager.py")
                    print(f"   Expected: services/position_observer.py")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import context request modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

def main():
    """Main test function."""
    success = test_context_request_path_resolution()
    
    if success:
        print("\n🎉 CONTEXT REQUEST PATH FIX VERIFICATION: PASSED")
        print("The fix correctly resolves project paths for context requests.")
    else:
        print("\n❌ CONTEXT REQUEST PATH FIX VERIFICATION: FAILED")
        print("The fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
