#!/usr/bin/env python

import os
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field

# Import the classes from simple_extraction_test.py
from simple_extraction_test import (
    SymbolInfo, 
    ExtractionRange, 
    read_file_content,
    get_file_line_count,
    get_symbols_in_file,
    extract_symbol_range,
    extract_symbol_content
)

@dataclass
class UsageContext:
    """Represents how a symbol is used in a file."""
    file_path: str
    line_number: int
    content: str
    usage_type: str

@dataclass
class EnhancedCodeContext:
    """Enhanced code context combining surgical context and file extraction."""
    # Primary symbol information
    symbol_name: str
    file_path: str
    symbol_type: str  # 'function', 'class', 'method'
    
    # Complete symbol implementation
    complete_implementation: Optional[str] = None
    extraction_range: Optional[ExtractionRange] = None
    
    # Essential imports and dependencies
    essential_imports: Optional[str] = None
    containing_class_signature: Optional[str] = None
    
    # Related code snippets
    usage_contexts: List[UsageContext] = field(default_factory=list)
    
    # Metadata
    token_count: int = 0
    relevance_score: float = 1.0

def extract_essential_imports(file_path: str) -> str:
    """Extract essential imports from a file."""
    import re
    
    content = read_file_content(file_path)
    if not content:
        return ""
    
    lines = content.splitlines()
    import_lines = []
    
    # Simple regex pattern to match import statements
    import_pattern = re.compile(r'^(import|from)\s+.+')
    
    for line in lines:
        if import_pattern.match(line):
            import_lines.append(line)
        elif import_lines and line.strip() == "":
            # Keep one blank line after imports
            import_lines.append("")
        elif import_lines and not line.startswith((" ", "\t")):
            # Stop when we hit non-import, non-blank lines that aren't continuations
            break
    
    return "\n".join(import_lines)

def extract_containing_class(file_path: str, symbol_info: SymbolInfo) -> Optional[str]:
    """Extract the class definition if the symbol is a method."""
    if symbol_info.symbol_type != "method":
        return None
    
    # Get all symbols in the file
    symbols = get_symbols_in_file(file_path)
    if not symbols:
        return None
    
    # Find potential containing classes
    classes = [s for s in symbols if s.symbol_type == "class" and s.start_line < symbol_info.start_line]
    if not classes:
        return None
    
    # Find the closest class (the one with the highest start line that's still before our method)
    containing_class = max(classes, key=lambda c: c.start_line)
    
    # Extract the class signature
    content = read_file_content(file_path)
    if not content:
        return None
    
    lines = content.splitlines()
    if containing_class.start_line > len(lines):
        return None
    
    # Get the class signature line
    class_line = lines[containing_class.start_line - 1]
    
    # Try to get a few lines of class docstring if available
    docstring_lines = []
    for i in range(containing_class.start_line, min(containing_class.start_line + 5, len(lines))):
        line = lines[i]
        if '"""' in line or "'''" in line:
            docstring_lines.append(line)
            # If the docstring is a single line, we're done
            if line.count('"""') == 2 or line.count("'''") == 2:
                break
            # Otherwise, collect lines until we find the closing quote
            for j in range(i + 1, min(i + 10, len(lines))):
                next_line = lines[j]
                docstring_lines.append(next_line)
                if '"""' in next_line or "'''" in next_line:
                    break
            break
    
    if docstring_lines:
        return class_line + "\n" + "\n".join(docstring_lines)
    else:
        return class_line

def find_symbol_usages(symbol_name: str, file_path: str) -> List[UsageContext]:
    """Find all usages of a symbol in a file."""
    import re
    
    content = read_file_content(file_path)
    if not content:
        return []
    
    lines = content.splitlines()
    usages = []
    
    # Simple regex pattern to match symbol usages
    # This is a simplified approach and might not catch all usages
    usage_pattern = re.compile(rf'\b{re.escape(symbol_name)}\b\s*\(')
    
    for i, line in enumerate(lines):
        if usage_pattern.search(line):
            # Determine usage type (simplified)
            usage_type = "function_call"
            
            # Get a few lines of context
            start_idx = max(0, i - 2)
            end_idx = min(len(lines) - 1, i + 2)
            context_lines = lines[start_idx:end_idx + 1]
            
            usages.append(UsageContext(
                file_path=file_path,
                line_number=i + 1,  # Convert to 1-based line numbers
                content="\n".join(context_lines),
                usage_type=usage_type
            ))
    
    return usages

def extract_enhanced_context(symbol_name: str, file_path: str) -> Optional[EnhancedCodeContext]:
    """
    Extract enhanced code context for a symbol, combining complete implementation and usage contexts.
    """
    # Get symbol information
    symbols = get_symbols_in_file(file_path)
    if not symbols:
        return None
    
    target_symbol = next((s for s in symbols if s.name == symbol_name), None)
    if not target_symbol:
        return None
    
    # Extract complete implementation
    extraction_range = extract_symbol_range(symbol_name, file_path)
    implementation = extract_symbol_content(symbol_name, file_path)
    
    # Extract essential imports
    essential_imports = extract_essential_imports(file_path)
    
    # Extract containing class signature if it's a method
    containing_class = extract_containing_class(file_path, target_symbol)
    
    # Extract usage contexts
    usage_contexts = find_symbol_usages(symbol_name, file_path)
    
    # Create the enhanced context
    context = EnhancedCodeContext(
        symbol_name=symbol_name,
        file_path=file_path,
        symbol_type=target_symbol.symbol_type,
        complete_implementation=implementation,
        extraction_range=extraction_range,
        essential_imports=essential_imports,
        containing_class_signature=containing_class,
        usage_contexts=usage_contexts
    )
    
    return context

def format_enhanced_context(context: EnhancedCodeContext) -> str:
    """Format the enhanced context for display or sending to an AI model."""
    if not context:
        return "No context available."
    
    output = "# 🎯 OPTIMIZED: Send only what's needed\n\n"
    output += f"### REQUESTED FILE CONTEXT (Surgical Extraction)\n\n"
    output += f"--- File: {context.file_path} (Targeted extraction for {context.symbol_name}) ---\n\n"
    
    # Add imports section if available
    if context.essential_imports:
        import_lines = context.essential_imports.splitlines()
        output += f"# File header & imports ({len(import_lines)} lines)\n"
        output += f"```python\n{context.essential_imports}\n```\n\n"
    
    # Add containing class if available
    if context.containing_class_signature:
        class_lines = context.containing_class_signature.splitlines()
        output += f"# Class/Module definition start ({len(class_lines)} lines)\n"
        output += f"```python\n{context.containing_class_signature}\n```\n\n"
    
    # Add the complete implementation
    if context.complete_implementation:
        impl_lines = context.complete_implementation.splitlines()
        output += f"# THE COMPLETE TARGET FUNCTION ({len(impl_lines)} lines)\n"
        output += f"```python\n{context.complete_implementation}\n```\n\n"
    
    # Add usage contexts if available
    if context.usage_contexts:
        output += f"### SURGICAL DEPENDENCY SNIPPETS\n"
        for i, usage in enumerate(context.usage_contexts[:3]):  # Limit to 3 usage examples
            output += f"#### Usage {i+1}: {usage.usage_type} at line {usage.line_number}\n"
            output += f"```python\n{usage.content}\n```\n\n"
    
    return output

def main():
    """Main function to demonstrate the simplified Enhanced Surgical Extractor."""
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python simple_enhanced_test.py <file_path> <symbol_name>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    symbol_name = sys.argv[2]
    
    print(f"\nExtracting enhanced context for symbol: {symbol_name} in {file_path}")
    
    # Extract the enhanced context
    context = extract_enhanced_context(symbol_name, file_path)
    
    if context:
        # Format and display the output
        output = format_enhanced_context(context)
        print("\n" + output)
        
        # Print some statistics
        print("\nExtraction Statistics:")
        print(f"  Symbol Type: {context.symbol_type}")
        if context.extraction_range:
            print(f"  Line Range: {context.extraction_range.start_line}-{context.extraction_range.end_line}")
            print(f"  Total Lines: {context.extraction_range.total_lines}")
        if context.usage_contexts:
            print(f"  Usage Contexts: {len(context.usage_contexts)}")
    else:
        print(f"  Failed to extract enhanced context for {symbol_name}")

if __name__ == "__main__":
    main()
