#!/usr/bin/env python3
"""
Test script to check if position management entities actually have inheritance.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_position_management_inheritance():
    """Test if position management entities have inheritance relationships."""
    
    print("🔍 TESTING POSITION MANAGEMENT INHERITANCE")
    print("=" * 60)
    
    try:
        # Generate IR data
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        ir_data = service.generate_mid_level_ir(external_project)
        
        if not ir_data or "modules" not in ir_data:
            print("❌ Failed to generate IR data")
            return False
        
        # Find all entities with inheritance
        all_inheritance_entities = []
        position_related_entities = []
        position_related_with_inheritance = []
        
        for module in ir_data['modules']:
            for entity in module.get('entities', []):
                entity_name = entity.get('name', '').lower()
                entity_type = entity.get('type', '')
                
                # Check for inheritance
                inherits_from = entity.get('inherits_from', [])
                method_overrides = entity.get('method_overrides', [])
                calls_super = entity.get('calls_super', False)
                overridden_by = entity.get('overridden_by', [])
                class_name = entity.get('class_name')
                
                has_inheritance = (inherits_from or method_overrides or calls_super or overridden_by or class_name)
                
                if has_inheritance:
                    all_inheritance_entities.append({
                        'module': module.get('name', 'unknown'),
                        'name': entity.get('name', 'unknown'),
                        'type': entity_type,
                        'inherits_from': inherits_from,
                        'method_overrides': method_overrides,
                        'calls_super': calls_super,
                        'overridden_by': overridden_by,
                        'class_name': class_name
                    })
                
                # Check if it's position-related
                position_keywords = ['position', 'trade', 'order', 'execution', 'management', 'manager']
                if any(keyword in entity_name for keyword in position_keywords):
                    position_related_entities.append({
                        'module': module.get('name', 'unknown'),
                        'name': entity.get('name', 'unknown'),
                        'type': entity_type,
                        'has_inheritance': has_inheritance,
                        'inherits_from': inherits_from,
                        'method_overrides': method_overrides,
                        'calls_super': calls_super,
                        'overridden_by': overridden_by,
                        'class_name': class_name
                    })
                    
                    if has_inheritance:
                        position_related_with_inheritance.append({
                            'module': module.get('name', 'unknown'),
                            'name': entity.get('name', 'unknown'),
                            'type': entity_type,
                            'inherits_from': inherits_from,
                            'method_overrides': method_overrides,
                            'calls_super': calls_super,
                            'overridden_by': overridden_by,
                            'class_name': class_name
                        })
        
        print(f"📊 INHERITANCE ANALYSIS:")
        print(f"   Total entities with inheritance: {len(all_inheritance_entities)}")
        print(f"   Position-related entities: {len(position_related_entities)}")
        print(f"   Position-related WITH inheritance: {len(position_related_with_inheritance)}")
        
        # Show position-related entities with inheritance
        if position_related_with_inheritance:
            print(f"\n🏗️ POSITION-RELATED ENTITIES WITH INHERITANCE:")
            for i, entity in enumerate(position_related_with_inheritance[:10], 1):
                print(f"   {i}. {entity['name']} ({entity['type']}) in {entity['module']}")
                if entity['inherits_from']:
                    print(f"      inherits_from: {entity['inherits_from']}")
                if entity['method_overrides']:
                    print(f"      method_overrides: {entity['method_overrides']}")
                if entity['calls_super']:
                    print(f"      calls_super: {entity['calls_super']}")
                if entity['overridden_by']:
                    print(f"      overridden_by: {entity['overridden_by']}")
                if entity['class_name']:
                    print(f"      class_name: {entity['class_name']}")
        else:
            print(f"\n❌ NO POSITION-RELATED ENTITIES WITH INHERITANCE FOUND!")
            print(f"This explains why the position management query didn't show inheritance data.")
        
        # Show some general inheritance entities for comparison
        print(f"\n🏗️ SAMPLE GENERAL INHERITANCE ENTITIES (first 10):")
        for i, entity in enumerate(all_inheritance_entities[:10], 1):
            print(f"   {i}. {entity['name']} ({entity['type']}) in {entity['module']}")
            if entity['inherits_from']:
                print(f"      inherits_from: {entity['inherits_from']}")
            if entity['class_name']:
                print(f"      class_name: {entity['class_name']}")
        
        # Now test what the context selector actually chooses
        print(f"\n🎯 TESTING CONTEXT SELECTOR FOR POSITION MANAGEMENT")
        print("=" * 60)
        
        from intelligent_context_selector import IntelligentContextSelector, TaskType
        
        selector = IntelligentContextSelector(ir_data, max_tokens=2000)
        
        # Select context for position management
        context_bundle = selector.select_optimal_context(
            task_description="How does position management work in the trading system?",
            task_type=TaskType.GENERAL_ANALYSIS,
            focus_entities=["position", "management", "trading", "system"]
        )
        
        print(f"✅ Context selector chose {len(context_bundle.entities)} entities")
        
        # Check if any of the selected entities have inheritance
        selected_with_inheritance = []
        for entity in context_bundle.entities:
            if (entity.inherits_from or entity.method_overrides or entity.calls_super or 
                entity.overridden_by or entity.class_name):
                selected_with_inheritance.append(entity)
        
        print(f"📊 Selected entities with inheritance: {len(selected_with_inheritance)}")
        
        if selected_with_inheritance:
            print(f"\n🏗️ SELECTED ENTITIES WITH INHERITANCE:")
            for i, entity in enumerate(selected_with_inheritance, 1):
                print(f"   {i}. {entity.entity_name} ({entity.entity_type}) in {entity.module_name}")
                if entity.inherits_from:
                    print(f"      inherits_from: {entity.inherits_from}")
                if entity.method_overrides:
                    print(f"      method_overrides: {entity.method_overrides}")
                if entity.calls_super:
                    print(f"      calls_super: {entity.calls_super}")
                if entity.overridden_by:
                    print(f"      overridden_by: {entity.overridden_by}")
                if entity.class_name:
                    print(f"      class_name: {entity.class_name}")
        else:
            print(f"\n❌ NO SELECTED ENTITIES HAVE INHERITANCE!")
            print(f"This confirms that the position management query doesn't select inheritance entities.")
        
        # Test with a different query that might select inheritance entities
        print(f"\n🎯 TESTING WITH INHERITANCE-FOCUSED QUERY")
        print("=" * 60)
        
        context_bundle2 = selector.select_optimal_context(
            task_description="How do the strategy classes inherit from base classes?",
            task_type=TaskType.GENERAL_ANALYSIS,
            focus_entities=["strategy", "base", "inherit", "class"]
        )
        
        print(f"✅ Inheritance-focused query chose {len(context_bundle2.entities)} entities")
        
        selected_with_inheritance2 = []
        for entity in context_bundle2.entities:
            if (entity.inherits_from or entity.method_overrides or entity.calls_super or 
                entity.overridden_by or entity.class_name):
                selected_with_inheritance2.append(entity)
        
        print(f"📊 Selected entities with inheritance: {len(selected_with_inheritance2)}")
        
        if selected_with_inheritance2:
            print(f"\n🏗️ INHERITANCE-FOCUSED SELECTED ENTITIES:")
            for i, entity in enumerate(selected_with_inheritance2[:5], 1):
                print(f"   {i}. {entity.entity_name} ({entity.entity_type}) in {entity.module_name}")
                if entity.inherits_from:
                    print(f"      inherits_from: {entity.inherits_from}")
                if entity.class_name:
                    print(f"      class_name: {entity.class_name}")
        
        # Summary
        print(f"\n📊 SUMMARY")
        print("=" * 60)
        print(f"Position management entities with inheritance: {len(position_related_with_inheritance)}")
        print(f"Position query selected inheritance entities: {len(selected_with_inheritance)}")
        print(f"Inheritance query selected inheritance entities: {len(selected_with_inheritance2)}")
        
        if len(position_related_with_inheritance) == 0:
            print("\n✅ CONCLUSION: Position management entities genuinely don't have inheritance!")
            print("The inheritance system is working correctly - it's just that position management")
            print("functions are standalone and don't use inheritance patterns.")
            return True
        elif len(selected_with_inheritance) == 0 and len(selected_with_inheritance2) > 0:
            print("\n✅ CONCLUSION: Inheritance system works, but position query doesn't select inheritance entities!")
            print("This is expected behavior - the context selector chooses the most relevant entities.")
            return True
        else:
            print("\n❓ CONCLUSION: Mixed results - need further investigation.")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_position_management_inheritance()
    
    if success:
        print("\n✅ Position management inheritance test completed!")
    else:
        print("\n❌ Position management inheritance test failed.")
