# LLM Workflow Example - Smart Map Request System

This document shows how the LLM should behave with the new Smart Map Request System.

## ✅ **CORRECT Workflow Example**

**User Query**: "How does authentication work in this system?"

**LLM Response**:
```
I need to explore the repository structure to understand how authentication works. Let me start by getting a focused repository map.

{MAP_REQUEST: {"keywords": ["authentication", "login", "auth", "user", "session"], "type": "implementation", "scope": "all", "max_results": 8}}
```

**System Response**: *[Provides focused repository map with authentication-related files]*

**LLM Evaluation**:
```
Based on the repository map, I can see several authentication-related files:
- auth/authentication.py (contains AuthManager class)
- auth/login_handler.py (handles login logic)
- middleware/auth_middleware.py (authentication middleware)
- models/user.py (user model)

This gives me a good overview, but I need to see the specific implementation details. Let me get the detailed code for the core authentication components.

{CONTEXT_REQUEST: {
  "original_user_query_context": "Understanding how authentication works in the system",
  "symbols_of_interest": [
    {"type": "class_definition", "name": "AuthManager", "file_hint": "auth/authentication.py"},
    {"type": "method_definition", "name": "LoginHandler.authenticate", "file_hint": "auth/login_handler.py"}
  ],
  "reason_for_request": "Need to see the core authentication logic and login process implementation"
}}
```

**System Response**: *[Provides detailed code context]*

**LLM Final Response**:
```
Based on the repository structure and code analysis, here's how authentication works in this system:

[Detailed explanation based on the actual code...]
```

---

## ❌ **INCORRECT Workflow Example (Old Behavior)**

**User Query**: "How does authentication work in this system?"

**LLM Response** *(This is what we DON'T want)*:
```
Let me look at the authentication implementation directly.

{CONTEXT_REQUEST: {"original_user_query_context": "authentication", "symbols_of_interest": [{"type": "class_definition", "name": "AuthManager"}], "reason_for_request": "need auth details"}}
```

**Problem**: The LLM tried to use CONTEXT_REQUEST without first exploring the repository structure with MAP_REQUEST. It doesn't know if AuthManager exists or where it might be located.

---

## 🔄 **Iterative Refinement Example**

**User Query**: "How does the payment processing work?"

**LLM Response**:
```
Let me explore the repository to understand the payment processing system.

{MAP_REQUEST: {"keywords": ["payment", "billing", "transaction", "charge"], "type": "implementation", "scope": "all", "max_results": 8}}
```

**System Response**: *[Returns map with only 2 files, not very comprehensive]*

**LLM Evaluation**:
```
The initial map only shows 2 payment-related files, which seems insufficient for understanding the complete payment system. Let me try broader keywords to get a more comprehensive view.

{MAP_REQUEST: {"keywords": ["pay", "money", "financial", "invoice", "order", "purchase"], "type": "implementation", "scope": "all", "max_results": 10}}
```

**System Response**: *[Returns more comprehensive map with 8 files]*

**LLM Continues**:
```
Now I have a better view of the payment system with 8 relevant files including order processing, invoice generation, and payment gateways. Let me examine the core payment logic...

{CONTEXT_REQUEST: {...}}
```

---

## 🎯 **Key Principles**

### **1. MANDATORY FIRST STEP**
- **Always start with MAP_REQUEST**
- Cannot use CONTEXT_REQUEST or REQUEST_FILE without repository context

### **2. EVALUATE MAP SUFFICIENCY**
- Check if the returned map contains relevant files for the user's query
- If insufficient, use MAP_REQUEST again with different/broader keywords

### **3. KEYWORD STRATEGY**
- Start with specific technical terms from user query
- If results are insufficient, try broader or related terms
- Consider synonyms and alternative terminology

### **4. PROGRESSIVE REFINEMENT**
- Use MAP_REQUEST to get overview
- Evaluate completeness
- Refine keywords if needed
- Then drill down with CONTEXT_REQUEST/REQUEST_FILE

---

## 📊 **Benefits of This Workflow**

1. **🎯 Perfect Relevance**: LLM gets exactly the repository context needed
2. **🚀 Faster Responses**: No more sifting through irrelevant code
3. **🧠 Better Understanding**: LLM builds comprehensive mental model before diving into details
4. **🔄 Adaptive**: Can refine search strategy based on initial results
5. **📈 Scalable**: Works with any repository size

---

## 🚨 **Critical Rules**

- **NEVER** use CONTEXT_REQUEST or REQUEST_FILE as the first action
- **ALWAYS** start with MAP_REQUEST to get repository context
- **EVALUATE** if the map is sufficient before proceeding
- **REFINE** keywords if the initial map is inadequate
- **ONLY THEN** proceed to detailed code analysis

This workflow ensures the LLM has proper repository understanding before attempting to analyze specific code components.
