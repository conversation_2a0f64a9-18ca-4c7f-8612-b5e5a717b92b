#!/usr/bin/env python3
"""
Debug script to trace inheritance data flow from IR generation to LLM package.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def debug_inheritance_data_flow():
    """Debug the inheritance data flow step by step."""
    
    print("🔍 DEBUGGING INHERITANCE DATA FLOW")
    print("=" * 60)
    
    try:
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Use external project path
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        print(f"📁 Using project path: {external_project}")
        
        # Create handler
        handler = ContextRequestHandler(external_project)
        
        # Create request
        request = IRContextRequest(
            user_query="How does position management work in the trading system?",
            task_description="Analyze and provide context for: How does position management work in the trading system?",
            task_type="general_analysis",
            focus_entities=["position", "management", "trading", "system"],
            max_tokens=2000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=8
        )
        
        print(f"📝 Processing request: {request.task_description}")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        # Check IR slices for inheritance data
        print(f"\n🔍 CHECKING IR SLICES FOR INHERITANCE DATA")
        print("=" * 60)
        
        ir_slices = result.get("ir_slices", [])
        print(f"Found {len(ir_slices)} IR slices")
        
        inheritance_found = False
        for i, ir_slice in enumerate(ir_slices):
            entity_name = ir_slice.get("entity_name", "unknown")
            entity_type = ir_slice.get("entity_type", "unknown")
            
            # Check for inheritance data
            class_name = ir_slice.get("class_name")
            inherits_from = ir_slice.get("inherits_from", [])
            method_overrides = ir_slice.get("method_overrides", [])
            calls_super = ir_slice.get("calls_super", False)
            overridden_by = ir_slice.get("overridden_by", [])
            
            print(f"\n{i+1}. {entity_name} ({entity_type})")
            print(f"   class_name: {class_name}")
            print(f"   inherits_from: {inherits_from}")
            print(f"   method_overrides: {method_overrides}")
            print(f"   calls_super: {calls_super}")
            print(f"   overridden_by: {overridden_by}")
            
            if inherits_from or method_overrides or calls_super or overridden_by:
                inheritance_found = True
                print(f"   ✅ INHERITANCE DATA FOUND!")
            else:
                print(f"   ❌ No inheritance data")
        
        # Check the LLM package for inheritance data
        print(f"\n🔍 CHECKING LLM PACKAGE FOR INHERITANCE DATA")
        print("=" * 60)
        
        llm_package = result.get("llm_friendly_package", "")
        if llm_package:
            print(f"LLM package size: {len(llm_package)} characters")
            
            # Check for inheritance indicators
            inheritance_indicators = [
                "Inherits From:",
                "inheritance chain:",
                "Methods that override parent:",
                "Methods overridden by children:",
                "🔁 Class Inheritance",
                "🧩 Method Details"
            ]
            
            found_indicators = []
            for indicator in inheritance_indicators:
                if indicator in llm_package:
                    found_indicators.append(indicator)
            
            print(f"Found inheritance indicators: {found_indicators}")
            
            # Show a sample of the package
            print(f"\nSample from LLM package (first 1000 chars):")
            print("-" * 50)
            print(llm_package[:1000])
            print("-" * 50)
            
            # Save the full package for inspection
            with open("debug_llm_package.txt", "w", encoding="utf-8") as f:
                f.write(llm_package)
            print(f"💾 Saved full LLM package to: debug_llm_package.txt")
        else:
            print("❌ No LLM package found")
        
        # Summary
        print(f"\n📊 INHERITANCE DATA FLOW SUMMARY")
        print("=" * 60)
        print(f"IR slices with inheritance data: {'✅ YES' if inheritance_found else '❌ NO'}")
        print(f"LLM package with inheritance indicators: {'✅ YES' if found_indicators else '❌ NO'}")
        
        if inheritance_found and not found_indicators:
            print("🚨 ISSUE IDENTIFIED: Inheritance data exists in IR slices but not in LLM package!")
            print("This suggests a problem in the _create_llm_friendly_package method.")
        elif not inheritance_found:
            print("🚨 ISSUE IDENTIFIED: No inheritance data found in IR slices!")
            print("This suggests a problem in the IR generation or entity extraction.")
        else:
            print("✅ Inheritance data flow appears to be working correctly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_trading_system_inheritance():
    """Check if the trading system actually has inheritance relationships."""
    
    print("\n🔍 CHECKING TRADING SYSTEM FOR INHERITANCE")
    print("=" * 60)
    
    try:
        # Import the standalone IR service
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        
        print(f"📁 Analyzing project: {external_project}")
        print("🔄 Generating IR data...")
        
        ir_data = service.generate_mid_level_ir(external_project)
        
        if not ir_data or "entities" not in ir_data:
            print("❌ Failed to generate IR data")
            return False
        
        entities = ir_data["entities"]
        print(f"📊 Found {len(entities)} entities")
        
        # Look for classes with inheritance
        classes_with_inheritance = []
        methods_with_inheritance = []
        
        for entity in entities:
            entity_type = entity.get("entity_type", "")
            entity_name = entity.get("entity_name", "")
            
            # Check for inheritance data
            inherits_from = entity.get("inherits_from", [])
            method_overrides = entity.get("method_overrides", [])
            calls_super = entity.get("calls_super", False)
            overridden_by = entity.get("overridden_by", [])
            class_name = entity.get("class_name")
            
            if entity_type == "class" and inherits_from:
                classes_with_inheritance.append({
                    "name": entity_name,
                    "inherits_from": inherits_from,
                    "file": entity.get("file_path", "")
                })
            
            if entity_type == "function" and (method_overrides or calls_super or overridden_by or class_name):
                methods_with_inheritance.append({
                    "name": entity_name,
                    "class_name": class_name,
                    "method_overrides": method_overrides,
                    "calls_super": calls_super,
                    "overridden_by": overridden_by,
                    "file": entity.get("file_path", "")
                })
        
        print(f"\n📊 INHERITANCE ANALYSIS RESULTS:")
        print(f"Classes with inheritance: {len(classes_with_inheritance)}")
        print(f"Methods with inheritance data: {len(methods_with_inheritance)}")
        
        if classes_with_inheritance:
            print(f"\n🏗️ CLASSES WITH INHERITANCE:")
            for cls in classes_with_inheritance[:10]:  # Show first 10
                print(f"   • {cls['name']} inherits from {cls['inherits_from']} ({cls['file']})")
        
        if methods_with_inheritance:
            print(f"\n🔧 METHODS WITH INHERITANCE DATA:")
            for method in methods_with_inheritance[:10]:  # Show first 10
                print(f"   • {method['name']} in class {method['class_name']} ({method['file']})")
                if method['method_overrides']:
                    print(f"     - Overrides: {method['method_overrides']}")
                if method['calls_super']:
                    print(f"     - Calls super(): {method['calls_super']}")
                if method['overridden_by']:
                    print(f"     - Overridden by: {method['overridden_by']}")
        
        return len(classes_with_inheritance) > 0 or len(methods_with_inheritance) > 0
        
    except Exception as e:
        print(f"❌ Error checking inheritance: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DEBUGGING INHERITANCE DATA IN IR CONTEXT SYSTEM")
    print("This will trace inheritance data from IR generation to LLM package\n")
    
    # First check if the trading system has inheritance
    has_inheritance = check_trading_system_inheritance()
    
    if not has_inheritance:
        print("\n⚠️  WARNING: No inheritance relationships found in trading system!")
        print("This might be expected if the codebase doesn't use inheritance.")
    
    # Then debug the data flow
    success = debug_inheritance_data_flow()
    
    if success:
        print("\n✅ Debugging completed successfully!")
        print("Check the output above to identify inheritance data flow issues.")
    else:
        print("\n❌ Debugging failed.")
    
    print(f"\n📁 Files created:")
    print(f"   • debug_llm_package.txt - Full LLM package for inspection")
