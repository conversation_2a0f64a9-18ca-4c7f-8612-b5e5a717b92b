│        mode_manager = ModeManager()
│        if mode_manager.is_backtest_mode():
│            db_path = mode_manager.get_backtest_db_path()
⋮
│    def calculate_trend_and_indicators(self, data: pd.DataFrame) -> dict:
⋮

models\__init__.py:
⋮
│Base = declarative_base()
│engine = create_engine('sqlite:///database/manage_positions.db')
│Session = sessionmaker(bind=engine)
│
⋮

models\backtest_indicator_data.py:
⋮
│class BacktestIndicatorData(Base):
│    """
│    Model for storing indicator data during backtest iterations.
│    Stores values per symbol/interval for each candle date.
----------------------------------------
│        """Create LiveIndicatorData instance from data dictionary"""
⋮
│        try:
│            print(f"DEBUG: from_dict called for {symbol} {interval} {candle_date}")
⋮
│            def get_value(key_prefix, default=0):
⋮
│def get_live_indicator_session():
⋮

models\position_repository.py:
⋮
│Session = sessionmaker(bind=engine)
│
│class Position(Base):
⋮
│def verify_position_records():
⋮

models\target_data.py:
⋮
----------------------------------------
⋮
│def get_live_indicator_session():
⋮

models\position_repository.py:
⋮
│Session = sessionmaker(bind=engine)
│
│class Position(Base):
⋮
│def verify_position_records():
⋮

models\target_data.py:
⋮
│@dataclass
│class TargetData:
│    """Encapsulates all target-related data with dynamic attributes"""
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str) -> 'TargetData':
----------------------------------------
⋮
│def log(message):
⋮
│class TelegramManager:
│    def __init__(self, bot_token, chat_id):
│        self.bot_token = bot_token
│        self.chat_id = chat_id
│        self.bot = telegram.Bot(token=bot_token) 
│        self.message_sent_w = False
│        self.message_sent = False
│        self.message_sent_positions = False
⋮
│    async def send_message(self, app, message, max_retries=3):
⋮
│    async def notify_success(self, app, message):
⋮
│    async def notify_failure(self, app, message):
⋮

utils\performance_analyzer.py:
⋮
----------------------------------------