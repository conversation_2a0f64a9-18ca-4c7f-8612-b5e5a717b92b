---
The REPOSITORY OVERVIEW (Aider-Generated Map Slice) follows:

backtest\README.md

backtest\backtest_position_manager.py:
⋮
│class BacktestPositionManager:
│    """
│    Backtest-specific version of the position entry manager
│    that doesn't rely on MetaTrader5 and uses the order simulator instead
│    """
│    def __init__(
│        self,
│        order_simulator,
│        candle_data_service,
│        target_service,
│        technical_analysis_service=None,
│        market_regime_service=None,
│        current_prices=None
⋮
│    async def process_all_positions(self, app, balance: float, current_prices=None) -> List[Dict]:
⋮
│    async def _analyze_trading_conditions(
│        self,
│        symbol: str,
│        setup: Dict,
│        price_data: Dict
⋮
│    def _is_valid_trade_condition(self, condition: Dict) -> bool:
⋮
│    def _calculate_position_quantity(
│        self,
│        amount_to_risk: float,
│        entry_price: float,
│        stop_loss: float,
│        contract_size: float,
│        max_allowed: float
⋮
│    def _execute_trade(self, symbol: str, trade: TradeSetup, price_data: Dict) -> Dict:
⋮
│    async def _fetch_targets(self, symbol: str, current_time: Optional[datetime] = None) -> Optiona
⋮

backtest\backtest_runner.py:
⋮
│class BacktestRunner:
│    """
│    Main class for running backtests.
│    Coordinates all components and manages the backtest execution.
⋮
│    @classmethod
│    def get_instance(cls):
⋮
│    def __init__(self, start_date: datetime, end_date: datetime,
│                symbols: List[str], intervals: List[Any],
│                initial_balance: float = 10000.0,
│                data_source: Optional[str] = None,
⋮
│    async def prepare(self) -> bool:
⋮
│    async def run(self) -> bool:
⋮
│    async def _update_current_prices(self):
⋮
│    async def _store_indicator_data(self, current_time: datetime, targets_results: Dict, tech_resul
⋮
│    def _record_closed_trades(self, current_time: datetime):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results') -> str:
⋮
│async def run_backtest_from_args(args):
⋮
│def parse_arguments():
⋮

backtest\backtest_trade_logger.py:
⋮
│class BacktestTradeLogger(TradeLogger):
│    """
│    Extension of TradeLogger for backtest mode.
│    Uses the same database schema but with backtest-specific functionality.
⋮
│    def __init__(self, clock=None):
⋮
│    def _setup_backtest_db(self):
⋮
│    def log_trade_entry(self,
│                        symbol: str,
│                        entry_price: float,
│                        direction: str,
│                        volume: float,
│                        stop_loss: Optional[float] = None,
│                        take_profit: Optional[float] = None,
│                        risk_amount: Optional[float] = None,
│                        risk_percentage: Optional[float] = None,
│                        strategy: Optional[str] = None,
⋮
│    def log_trade_exit(self,
│                       trade_id: str,
│                       exit_price: float,
│                       pnl: Optional[float] = None,
│                       pnl_percentage: Optional[float] = None,
│                       pnl_pips: Optional[float] = None,
⋮
│    def get_all_backtest_trades(self) -> List[TradeLog]:
⋮

backtest_logs\backtest_20250201_20250202.log

backtest_logs\backtest_20250301_20250302.log

backtest_logs\backtest_20250308_20250310.log

backtest_logs\backtest_20250312_20250313.log

backtest_logs\backtest_20250401_20250402.log

backtest_logs\backtest_20250401_20250403.log

backtest_logs\backtest_20250401_20250416.log

backtest_logs\backtest_20250408_20250409.log

backtest_logs\backtest_20250408_20250410.log

backtest_results\equity_curve.csv

backtest_results\equity_curve.png

backtest_results\equity_curve_interactive.html

backtest_results\interactive_equity_curve.html

backtest_results\metrics.json

backtest_results\report.html

backtest_results\trades.csv

config\__init__.py

config\backtest_config.py:
⋮
│class BacktestConfig:
│    # Transaction costs
│    DEFAULT_SLIPPAGE_PIPS = 2
⋮
│    @staticmethod
│    def get_config():
⋮

config\base_config.py:
⋮
│BASE_SYMBOLS_CONFIG = {
│    # 'EURUSD': {
│    #     'mt5_symbol': 'EURUSD',
│    #     'price_key': 'EURUSD',
│    #     'symbol_allowed': True,
│    #     'exchange': 'OANDA',
│    #     'prefix': 'eur',
│    #     'cache_intervals': [
│    #         Interval.in_1_hour
│    #     ],
⋮

config\environment.py:
│TELEGRAM_BOT_TOKEN = '**********************************************'
│TELEGRAM_CHAT_ID = '5185654136'
│
│TV_USERNAME = '<EMAIL>'
│TV_PASSWORD = '!#Uvv&Y9UM5t7F8'
│
│DB_URI = 'sqlite:///database/manage_positions.db'
│LIVE_DB_URI = 'sqlite:///database/live_data.db'
│BACKTEST_DB_URI = 'sqlite:///database/backtest_data.db'
│
⋮
│LIVE_TRADE_LOGS_DB_URI = 'sqlite:///database/live_trade_logs.db'
│BACKTEST_TRADE_LOGS_DB_URI = 'sqlite:///database/backtest_trade_logs.db'
│
⋮

config\logging_config.py:
⋮
│DEFAULT_LOG_LEVEL = LogLevel.INFO
│
⋮
│SHOW_DATABASE_LOGS = False  # Set to True to see database operations in the terminal
│SHOW_DETAILED_STRATEGY_LOGS = False
│SHOW_DETAILED_TARGET_LOGS = False
│SHOW_DETAILED_TECHNICAL_LOGS = False
│
⋮
│USE_COLORS = True
│SHOW_SEPARATORS = True
│
⋮
│SHOW_POSITION_STATUS = True
│SHOW_MARKET_REGIME = True
│SHOW_TRADE_DETAILS = True
│
⋮
│POSITION_STATUS_INTERVAL = 300  # Show position status every 5 minutes
│MARKET_DATA_INTERVAL = 600  # Show market data every 10 minutes
│
⋮
│TARGET_FIELDS_TO_SHOW = [
│    'buy_target_1h',
│    'sell_target_1h',
│    'first_price_bt_1h',
│    'first_price_st_1h',
│    'to_bt_1h',
│    'to_st_1h',
│    'market_regime_1h',
│    'trend_direction_1h'
⋮
│TECHNICAL_FIELDS_TO_SHOW = [
│    'cci_1h',
│    'adaptive_momentum_1h',
│    'trend_strength_1h'
⋮
│STRATEGY_FIELDS_TO_SHOW = [
│    'condition',
│    'sl',
│    'tp',
│    'entry_price',
│    'entry_time'
⋮

config\trading_pairs_config.py:
⋮
│SYMBOLS_CONFIG = copy.deepcopy(BASE_SYMBOLS_CONFIG)
│
⋮
│def configure_symbol_strategies():
⋮
│@dataclass
│class SymbolConfig:
⋮
│def init_config_store(config):
⋮

config\user_preferences.json

database\backtest_data.db

database\backtest_trade_logs.db

database\database_manager.py:
⋮
│class DatabaseManager:
│    """
│    Manages database connections for both live and backtest modes.
│    Ensures that backtest data doesn't contaminate live databases.
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def get_engine(self) -> Engine:
⋮
│    def _ensure_backtest_tables(self):
⋮
│    def _create_market_data_tables(self):
⋮
│    def _create_position_tables(self):
⋮
│    def copy_live_schema_to_backtest(self):
⋮

database\indicator_data.db

database\indicator_data______.db

database\live_data.db

database\live_trade_logs.db

database\read_db_head.py:
⋮
│def print_head_of_table(table_name: str, db_path: str = "database/live_data.db"):
⋮
│final_buy = 3290.74
│final_sell = 3212.61
│
⋮
│fib_div = final_buy - final_sell
│
⋮
│buy_stoploss = (fib_div * -0.0458) + final_sell
│sell_stoploss = (fib_div * 1.0458) + final_sell
⋮

docs\backtest_indicator_storage.md

docs\dashboard_modes.md

docs\live_indicator_storage.md

log\__init__.py

log\run.log

log\run.log.2025-04-25_01

log\run.log.2025-04-25_05

logs\data_flow.log

logs\debug_data.json

logs\tradingsystem_20250422.log

logs\tradingsystem_20250423.log

logs\tradingsystem_20250424.log

logs\tradingsystem_20250425.log

main.py:
⋮
│mode_manager = ModeManager()
│
⋮
│db_manager = DatabaseManager()
│session = Session(bind=db_manager.get_engine())
│
⋮
│components = {}
│
⋮
│async def periodic_flush_async(logger, interval=60):
⋮
│async def wait_for_all_symbols_data(cash_manager, BASE_SYMBOLS_CONFIG):
⋮
│async def initialize_components(is_backtest=False):
⋮
│async def main():
⋮
│def parse_arguments():
⋮

manual_operations\__init__.py

manual_operations\manual_position_handler.py:
⋮
│class PositionAdjustments:
│    def __init__(self, telegram_manager, session: Session, order_executor):
│        self.telegram_manager = telegram_manager
│        self.session = session
⋮
│    async def handle_manual_position_changes(self, app, symbol, existing_row):
⋮

market_data\__init__.py

market_data\data_provider.py:
⋮
│class DataProvider:
│    """
│    Abstraction layer for data access that works in both live and backtest modes.
│    Delegates to the appropriate data source based on the current mode.
│    """
│    def __init__(self):
⋮
│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op
⋮
│    def _get_live_candles(self, symbol: str, interval: Any, count: Optional[int] = None) -> pd.Data
⋮
│    def _get_symbol_config(self, symbol: str) -> Dict:
⋮
│    def prepare_backtest_data(self, symbols: List[str], intervals: List[Any],
│                             start_date: datetime, end_date: datetime,
⋮
│    def validate_backtest_data(self, symbols: List[str], intervals: List[Any],
⋮

market_data\historical_data_provider.py:
⋮
│class HistoricalDataProvider:
│    """
│    Provides historical data for backtesting.
│    Loads data from source files/databases and prepares it for backtesting.
│    """
│    def __init__(self):
⋮
│    def prepare_data(self, symbols: List[str], intervals: List[Any],
│                    start_date: datetime, end_date: datetime,
⋮
│    def _get_symbol_config(self, symbol: str) -> Dict:
⋮
│    def _validate_table_data(self, table_name: str, start_date: datetime, end_date: datetime) -> Tu
⋮
│    def _load_csv_data(self, file_path: str, price_key: str, interval: Any,
⋮
│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op
⋮
│    def validate_data(self, symbol: str, interval: Any,
⋮
│    def _get_interval_minutes(self, interval: Any) -> int:
⋮

market_data\market_data_repository.py:
⋮
│class DatabaseManager:
│    """Handles robust data operations from old version"""
│    def __init__(self, engine, symbols_config: Dict, tv_client: TvDatafeed):
⋮
│    def get_table_name(self, price_key: str, interval) -> str:
⋮
│    def create_tables(self) -> None:
⋮
│    def get_and_save_data(self, symbol: str, interval, timestamp: datetime, max_retries: int = 20,
⋮
│class ScheduleManager:
│    """Handles scheduling logic from new version"""
│    def __init__(self, engine, symbols_config: Dict):
⋮
│    def create_scheduling_table(self):
⋮
│    def get_latest_candle_time(self, symbol: str, interval) -> Optional[datetime]:
⋮
│    def get_interval_minutes(self, symbol: str, interval) -> int:
⋮
│    def compute_next_boundary(self, current_utc: datetime, interval_minutes: int,
⋮
│    def initialize_schedules(self):
⋮
│class MarketDataManager:
│    """Main class coordinating database and scheduling operations"""
│    def __init__(self, db_path: str, tv_username: str, tv_password: str, symbols_config: Dict):
⋮
│    def initialize(self):
⋮
│    def do_initial_fetch(self):
⋮
│    async def catch_up_and_update(self, row_id: int, symbol: str, interval_str: str,
⋮
│    async def check_and_update(self):
⋮
│async def run_scheduler(manager: MarketDataManager):
⋮

market_data\realtime_data_manager.py:
⋮
│class PriceManager:
│    def __init__(self, telegram_manager=None, max_concurrent_tasks=1):
│        self.tv = TvDatafeed(TV_USERNAME, TV_PASSWORD)
│        self.cache_lock = threading.Lock()
│        self.price_cache = self._initialize_price_cache()
│        self.internet_connected = True
│        self.telegram_manager = telegram_manager
│        self.update_queue = asyncio.Queue()
│        self.pending_internet_restore_notification = False
│        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)  # Add semaphore
│
⋮
│    def _get_timeframes_for_symbol(self, symbol: str) -> list:
⋮
│    def _initialize_price_cache(self) -> dict:
⋮
│    async def _fetch_and_update_price(self, symbol, exchange, interval, timeframe):
⋮
│    async def fetch_price_data(self):
⋮
│    def _handle_internet_disconnect(self):
⋮
│    async def _handle_internet_reconnect(self):
⋮
│    async def _process_updates(self):
⋮
│    async def start_price_updater(self):
⋮
│    def check_price_cache(self) -> bool:
⋮

market_data\tick.py:
⋮
│symbol = "EURUSD"
│symbol_info = mt5.symbol_info(symbol)
│digits = symbol_info.digits if symbol_info else None
│
⋮

market_data\timeframe_data.py:
⋮
│def analyze_volume(data: pd.DataFrame, lookback: int = 20) -> pd.DataFrame:
│    data = data.copy()
│
⋮
│    def calculate_poc(prices: np.ndarray) -> float:
│
⋮
│def get_volume_signals(data: pd.DataFrame, thresh_strong: float = 75, thresh_weak: float = 25) -> p
│
⋮
│class CandleData:
│    def __init__(self, symbol: str, intervals: list, price_key: str, engine=None):
│        self.symbol = symbol
│        self.price_key = price_key
│        self.intervals = intervals
│        self.data_by_interval = {}
│
│        # Use the appropriate database based on mode
│        mode_manager = ModeManager()
│        if mode_manager.is_backtest_mode():
│            db_path = mode_manager.get_backtest_db_path()
⋮
│    def ensure_tables_exist(self, intervals: list, price_key: str) -> None:
⋮
│    def initialize_data(self, intervals: list, price_key: str, engine) -> None:
⋮
│    def get_data(self, interval_suffix: str) -> Dict:
⋮
│    def get_all_data(self) -> Dict:
⋮
│    def calculate_trend_and_indicators(self, data: pd.DataFrame) -> dict:
⋮
│    def refresh_interval_data(self, interval: str) -> bool:
⋮

models\__init__.py:
⋮
│Base = declarative_base()
│engine = create_engine('sqlite:///database/manage_positions.db')
│Session = sessionmaker(bind=engine)
│
⋮
│__all__ = ['Base', 'engine', 'Session']

models\backtest_indicator_data.py:
⋮
│Base = declarative_base()
│
│class BacktestIndicatorData(Base):
│    """
│    Model for storing indicator data during backtest iterations.
│    Stores values per symbol/interval for each candle date.
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt) -> 'BacktestIndicat
⋮
│def get_backtest_indicator_session():
⋮

models\live_indicator_data.py:
⋮
│Base = declarative_base()
│
│class LiveIndicatorData(Base):
│    """
│    Model for storing indicator data during live trading.
│    Stores values per symbol/interval for each candle date.
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt, is_complete_candle:
│        """Create LiveIndicatorData instance from data dictionary"""
⋮
│        try:
│            print(f"DEBUG: from_dict called for {symbol} {interval} {candle_date}")
⋮
│            def get_value(key_prefix, default=0):
⋮
│def get_live_indicator_session():
⋮

models\position_repository.py:
⋮
│Base = declarative_base()
│engine = create_engine(LIVE_DB_URI)
│Session = sessionmaker(bind=engine)
│
│class Position(Base):
⋮
│def initialize_positions_if_needed(amount_to_risk=0.0):
⋮
│def verify_position_records():
⋮

models\target_data.py:
⋮
│@dataclass
│class TargetData:
│    """Encapsulates all target-related data with dynamic attributes"""
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str) -> 'TargetData':
⋮
│    def to_dict(self) -> Dict:
⋮
│    def get_timeframe_data(self, tf: str) -> Dict:
⋮
│    def set_attribute(self, key: str, value: Any) -> None:
⋮
│    def get_attribute(self, key: str) -> Optional[Any]:
⋮
│    def __getattr__(self, name: str) -> Any:
⋮
│class TargetDataService:
│    """Manages target data caching and access"""
│    def __init__(self):
⋮
│    def update_target(self, symbol: str, target_data: TargetData) -> None:
⋮
│    def get_target(self, symbol: str) -> Optional[TargetData]:
⋮
│    def get_timeframe_target(self, symbol: str, tf: str) -> Optional[Dict]:
⋮
│    def get_all_targets(self) -> Dict[str, TargetData]:
⋮

models\target_history.py:
⋮
│Base = declarative_base()
│
│class TargetHistory(Base):
│    __tablename__ = 'target_history'
│
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, timeframe: str, timestamp: dt) -> 'TargetHistory':
⋮
│class TargetHistoryRepository:
│    def __init__(self, session):
⋮
│    def save(self, target_history: TargetHistory) -> None:
⋮
│    def get_history(self, symbol: str, timeframe: str,
│                   from_date: dt, to_date: Optional[dt] = None,
⋮

models\trade_log.py:
⋮
│TradeLogBase = declarative_base()
│
⋮
│def get_trade_log_engine():
⋮
│trade_log_engine = get_trade_log_engine()
│TradeLogSession = sessionmaker(bind=trade_log_engine)
│
│class TradeLog(TradeLogBase):
│    """Model for logging trade information for each symbol"""
⋮
│    def to_dict(self) -> Dict:
⋮
│class TradeExecution(TradeLogBase):
│    """Model for tracking individual trade executions"""
⋮
│    def to_dict(self) -> Dict:
⋮
│def ensure_trade_log_tables_exist():
⋮
│def get_trade_log_session():
⋮

models\trigger_repository.py:
⋮
│class TriggerTimeframe(Base):
⋮
│class Trigger(Base):
⋮
│class TriggerRepository:
│    def __init__(self, session: SQLAlchemySession, trigger_strategy=None):  # Add trigger_strategy
│        self.session = session
│        self.logger = logging.getLogger(__name__)
│        self.trigger_strategy = trigger_strategy  # Store the strategy
⋮
│    def _ensure_tables_exist(self):
⋮
│    def initialize_triggers_if_needed(self, symbols_list: List[str], symbol_config: Dict) -> None:
⋮
│    def get_latest_triggers(self, symbol: str, interval_name: str = None, limit: int = 2) -> List[T
⋮
│    def update_trigger(self, trigger: Union[Trigger, Dict], update_data: Dict) -> Optional[Trigger]
⋮
│    def create_trigger(self, trigger_data: Dict) -> Optional[Trigger]:
⋮
│    def _convert_to_datetime(self, date_value):
⋮

order_management\__init__.py

order_management\order_executor.py:
⋮
│@dataclass
│class TradeResult:
⋮
│class TradeExecutor:
│    def __init__(self, telegram_manager, session):
│        self.telegram_manager = telegram_manager
│        self.session = session
│
│        # Initialize trade logger
│        try:
│            from services.trade_logger import TradeLogger
│            self.trade_logger = TradeLogger()
│        except Exception as e:
│            from utils.logger import log
⋮
│    async def open_position(self, app, symbol: str, direction: str, volume: float, stop_loss: float
⋮
│    async def close_added_manually(
│        self,
│        app,
│        symbol: str,
│        position_ticket: int
⋮
│    async def close_all_positions(
│        self,
│        app,
│        symbol: str,
│        volume: float
⋮

order_management\order_simulator.py:
⋮
│class OrderSimulator:
│    """
│    Simulates order execution for backtesting.
│    Handles slippage, transaction costs, and realistic execution behavior.
│    """
│    def __init__(self, clock=None):
⋮
│    def place_order(self, symbol: str, order_type: int, volume: float,
│                   price: Optional[float] = None, sl: Optional[float] = None,
⋮
│    def close_position(self, ticket: int, volume: Optional[float] = None, close_price: Optional[flo
⋮
│    def modify_position(self, ticket: int, sl: Optional[float] = None, tp: Optional[float] = None)
⋮
│    def get_positions(self, symbol: Optional[str] = None) -> List[Dict]:
⋮
│    def get_orders(self, symbol: Optional[str] = None) -> List[Dict]:
⋮
│    def update_positions(self, current_prices: Dict[str, Dict[str, float]]):
⋮
│    def _get_current_price(self, symbol: str, order_type: int) -> float:
⋮
│    def _apply_slippage(self, price: float, order_type: int) -> float:
⋮
│    def _calculate_commission(self, volume: float, price: float) -> float:
⋮
│    def _get_contract_size(self, symbol: str) -> float:
⋮

plots\trade_performance_20250415_003717.png

scripts\check_db.py:
⋮
│def main():
⋮

scripts\check_indicator_data.py:
⋮
│def check_indicator_data():
⋮

scripts\check_live_indicators.py:
⋮
│def main():
⋮

scripts\check_live_indicators_detailed.py:
⋮
│def check_live_indicators_detailed():
⋮

scripts\debug_data_values.py:
⋮
│async def main():
⋮
│def check_database_structure():
⋮
│def verify_with_sql(symbol, interval, candle_date):
⋮

scripts\debug_live_indicator_service.py:
⋮
│class DebugLiveIndicatorService(LiveIndicatorService):
│    """Enhanced version of LiveIndicatorService with more detailed logging"""
│
│    async def _handle_candle_data_update(self, symbol: str, interval: str):
⋮
│    async def _handle_technical_analysis_update(self, symbol: str, indicators: dict):
⋮
│    async def _handle_target_data_update(self, symbol: str, targets: dict):
⋮
│    async def _try_store_indicator_data(self, symbol: str, data: dict):
⋮
│    async def store_indicator_data(self, symbol: str, interval: str, data: dict, is_complete_candle
⋮
│async def test_debug_service():
⋮

scripts\debug_live_indicators.py:
⋮
│async def test_direct_db_write():
⋮
│async def main():
⋮

scripts\debug_try_store_indicator_data.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service"""
│    def get_candle_data(self, symbol, interval):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service"""
│    def get_technical_indicators(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service"""
│    def get_target_data(self, symbol):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service"""
│    def get_market_regime(self, symbol):
⋮
│class DebugLiveIndicatorService(LiveIndicatorService):
│    """Enhanced version of LiveIndicatorService with more detailed logging"""
│
│    async def _try_store_indicator_data(self, symbol: str, data: dict):
⋮
│async def debug_try_store_indicator_data():
⋮

scripts\fix_live_indicator_service.py:
⋮
│class FixedLiveIndicatorService(LiveIndicatorService):
│    """Enhanced version of LiveIndicatorService with better error handling"""
│
│    async def _try_store_indicator_data(self, symbol: str, data: dict):
⋮
│async def test_fixed_service():
⋮

scripts\initialize_components.py:
⋮
│MOCK_CANDLE_DATA = {
│    'candle_date_in_1_hour': datetime.now(timezone.utc),
│    'open_in_1_hour': 1.2345,
│    'high_in_1_hour': 1.2400,
│    'low_in_1_hour': 1.2300,
│    'close_in_1_hour': 1.2350,
│    'in_1_hour': {
│        'candle_date_in_1_hour': datetime.now(timezone.utc),
│        'open_in_1_hour': 1.2345,
│        'high_in_1_hour': 1.2400,
⋮
│MOCK_TECHNICAL_DATA = {
│    'cci_1h': 100.0,
│    'adaptive_momentum_1h': 0.5,
│    'trend_strength_1h': 0.8,
│    'price_complexity_1h': 0.7,
│    'volume_complexity_1h': 0.6,
│    'level_strength_1h': 0.9
⋮
│MOCK_TARGET_DATA = {
│    'buy_target_in_1_hour': 1.2500,
│    'sell_target_in_1_hour': 1.2200,
│    'price_bt_in_1_hour': 1.2450,
│    'price_st_in_1_hour': 1.2250,
│    'first_price_bt_in_1_hour': 1.2400,
│    'first_price_st_in_1_hour': 1.2300,
│    'to_bt_in_1_hour': 0.0100,
│    'to_st_in_1_hour': 0.0100
⋮
│MOCK_MARKET_REGIME_DATA = {
│    'market_regime': 'Trending',
│    'market_direction': 'Bullish'
⋮
│class MockCandleDataService:
│    """Mock candle data service that returns predefined data"""
│    def __init__(self):
⋮
│    async def get_candle_data(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service that returns predefined data"""
│    def __init__(self):
⋮
│    def get_technical_indicators(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service that returns predefined data"""
│    def __init__(self):
⋮
│    async def get_targets(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol, symbols_config=None):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service that returns predefined data"""
│    def __init__(self):
⋮
│    def get_market_regime(self, symbol):
⋮
│async def initialize_components():
⋮

scripts\insert_test_record.py:
⋮
│def insert_test_record():
⋮

scripts\monitor_database_file.py:
⋮
│def main():
⋮

scripts\monitor_events.py:
⋮
│received_events = []
│
│async def event_callback(*args, **kwargs):
⋮
│async def monitor_events():
⋮

scripts\monitor_live_indicator_events.py:
⋮
│received_events = []
│
│async def event_callback(event_name, *args, **kwargs):
⋮
│async def monitor_events():
⋮

scripts\print_indicator_tables.py:
⋮
│def get_table_info(db_path, table_name):
⋮
│def print_table_data(db_path, table_name, limit=10, where_clause=None):
⋮
│def main():
⋮

scripts\recreate_backtest_indicator_table.py:
⋮
│def recreate_backtest_indicator_table(confirm=False):
⋮
│def parse_arguments():
⋮

scripts\register_mock_components.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service"""
│    def get_candle_data(self, symbol, interval):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service"""
│    def get_technical_indicators(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service"""
│    def get_target_data(self, symbol):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service"""
│    def get_market_regime(self, symbol):
⋮
│async def register_mock_components():
⋮

scripts\simulate_data_flow.py:
⋮
│async def simulate_data_flow():
⋮

scripts\test_database.py:
⋮
│def test_database_connection():
⋮

scripts\test_db_read.py:
⋮
│async def main():
⋮

scripts\test_db_write.py:
⋮
│def main():
⋮

scripts\test_fixed_model.py:
⋮
│async def main():
⋮

scripts\test_fixes.py:
⋮
│async def test_event_system():
│    """Test the event system with coroutine callbacks"""
⋮
│    async def test_callback(symbol, data):
⋮

scripts\test_live_indicators_with_components.py:
⋮
│async def test_with_components():
⋮

scripts\test_live_indicators_with_mock_data.py:
⋮
│MOCK_CANDLE_DATA = {
│    'candle_date_in_1_hour': datetime.now(timezone.utc),
│    'open_in_1_hour': 1.2345,
│    'high_in_1_hour': 1.2400,
│    'low_in_1_hour': 1.2300,
│    'close_in_1_hour': 1.2350,
│    'in_1_hour': {
│        'candle_date_in_1_hour': datetime.now(timezone.utc),
│        'open_in_1_hour': 1.2345,
│        'high_in_1_hour': 1.2400,
⋮
│MOCK_TECHNICAL_DATA = {
│    'cci_1h': 100.0,
│    'adaptive_momentum_1h': 0.5,
│    'trend_strength_1h': 0.8,
│    'price_complexity_1h': 0.7,
│    'volume_complexity_1h': 0.6,
│    'level_strength_1h': 0.9
⋮
│MOCK_TARGET_DATA = {
│    'buy_target_in_1_hour': 1.2500,
│    'sell_target_in_1_hour': 1.2200,
│    'price_bt_in_1_hour': 1.2450,
│    'price_st_in_1_hour': 1.2250,
│    'first_price_bt_in_1_hour': 1.2400,
│    'first_price_st_in_1_hour': 1.2300,
│    'to_bt_in_1_hour': 0.0100,
│    'to_st_in_1_hour': 0.0100
⋮
│MOCK_MARKET_REGIME_DATA = {
│    'market_regime': 'Trending',
│    'market_direction': 'Bullish'
⋮
│class MockCandleDataService:
│    """Mock candle data service that returns predefined data"""
│    def __init__(self):
⋮
│    async def get_candle_data(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service that returns predefined data"""
│    def __init__(self):
⋮
│    def get_technical_indicators(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service that returns predefined data"""
│    def __init__(self):
⋮
│    async def get_targets(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol, symbols_config=None):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service that returns predefined data"""
│    def __init__(self):
⋮
│    def get_market_regime(self, symbol):
⋮
│async def test_with_mock_services():
│    """Test the live indicator storage with mock services"""
⋮
│    async def patched_method(self, symbol, data):
⋮

scripts\trade_log_analyzer.py:
⋮
│def main():
⋮
│def plot_performance(symbol, days, save_path):
⋮
│def show_detailed_stats(symbol, days):
⋮

scripts\view_backtest_indicators.py:
⋮
│def view_backtest_indicators(symbol, interval, start_date=None, end_date=None, limit=100):
⋮
│def parse_arguments():
⋮

scripts\view_live_indicators.py:
⋮
│def main():
⋮

services\__init__.py

services\backtest_indicator_service.py:
⋮
│class BacktestIndicatorService:
│    """
│    Service for storing and retrieving indicator data during backtests.
│    """
│    def __init__(self):
⋮
│    def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any]) -> bool:
⋮
│    def get_indicator_data(self, symbol: str, interval: str,
│                          from_date: datetime, to_date: Optional[datetime] = None,
⋮
│    def get_latest_indicator_data(self, symbol: str, interval: str) -> Optional[BacktestIndicatorDa
⋮

services\candle_data_service.py:
⋮
│class CandleDataService:
│    def __init__(self, price_manager, event_system=None):
│        self.price_manager = price_manager
│        self.historical_data = {}
⋮
│    async def initialize_symbol(self, symbol: str) -> bool:
⋮
│    async def _handle_data_update(self, symbol: str, interval: str, timestamp: datetime):
│
⋮
│    async def refresh_interval_data(self, interval: str):
⋮
│    async def get_candle_data(self, symbol: str, price_key: str = None) -> dict:
⋮

services\event_system.py:
⋮
│class DataEventSystem:
│    _instance = None
│    def __new__(cls):
⋮
│    def subscribe(self, event_or_symbol: str, callback: Callable) -> None:
⋮
│    def unsubscribe(self, event_or_symbol: str, callback: Callable) -> None:
⋮
│    def publish(self, event_name: str, *args, **kwargs) -> None:
⋮
│    async def notify_update(self, symbol: str, interval: str, timestamp: datetime) -> None:
⋮
│    def get_last_update(self, symbol: str, interval: str) -> datetime:
⋮
│    def is_subscribed(self, event_or_symbol: str, callback: Callable) -> bool:
⋮

services\indicator_service_factory.py:
⋮
│class IndicatorServiceFactory:
│    """
│    Factory class for creating the appropriate indicator service based on the current mode.
│    """
│    @staticmethod
│    def get_indicator_service() -> Union[BacktestIndicatorService, LiveIndicatorService]:
⋮

services\live_indicator_service.py:
⋮
│class LiveIndicatorService:
│    """
│    Service for storing and retrieving indicator data during live trading.
│    """
│    def __init__(self):
⋮
│    async def _handle_candle_data_update(self, symbol: str, interval: str):
⋮
│    async def _handle_technical_analysis_update(self, symbol: str, data: Dict[str, Any]):
⋮
│    async def _handle_target_data_update(self, symbol: str, targets: Dict[str, Any]):
⋮
│    async def _check_for_components(self):
⋮
│    async def _process_pending_updates(self):
⋮
│    async def _register_services(self, candle_data_service, technical_analysis_service, target_serv
⋮
│    async def _try_store_indicator_data(self, symbol: str, data: Dict[str, Any]):
⋮
│    async def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any], is_compl
│        """
│        Store indicator data for a symbol and interval
│
│        Args:
│            symbol: Trading symbol (e.g., 'GBPUSD')
│            interval: Timeframe interval (e.g., '1h')
│            data: Dictionary containing all indicator values
│            is_complete_candle: Whether this is a complete candle or a partial update
│
│        Returns:
⋮
│        try:
│            print(f"DEBUG: store_indicator_data called for {symbol} {interval}")
⋮
│            def get_value(key_prefix, default=0.0):
⋮
│    def get_indicator_data(self, symbol: str, interval: str,
│                          from_date: datetime, to_date: Optional[datetime] = None,
⋮
│    def get_latest_indicator_data(self, symbol: str, interval: str, complete_only: bool = False) ->
⋮
│    def get_storage_statistics(self) -> Dict[str, Any]:
⋮

services\market_regime_service.py:
⋮
│class MarketRegimeService:
│    def __init__(self, candle_data_service: CandleDataService, event_system: DataEventSystem):
│        self.candle_data_service = candle_data_service
│        self.event_system = event_system
│        self.market_regimes = {}
│        self.version = "1.0.0"
│
│        # Default parameters
│        self.volatility_window = 14
│        self.linear_reg_period = 20
│        self.ranging_sensitivity = 4
│
⋮
│    def on_candle_data_updated(self, symbol: str, interval: str):
⋮
│    def initialize_all_market_regimes(self):
⋮
│    def get_market_regime(self, symbol: str, interval: str) -> Dict[str, Any]:
⋮
│    def _get_dataframe_for_interval(self, symbol: str, interval_name: str, current_time: Optional[d
⋮
│    def analyze_market_regime(self, symbol: str, interval: str, config_symbol: str = None, current_
⋮
│    def _rolling_linear_regression(self, series: pd.Series, window: int) -> pd.Series:
⋮
│    def _rolling_mode(self, series: pd.Series, window: int = 3) -> pd.Series:
⋮
│    def analyze_all_market_regimes(self, current_time: Optional[datetime] = None) -> Dict[str, Dict
⋮

services\mock_technical_analysis_service.py:
⋮
│class MockTechnicalAnalysisService:
│    """
│    Mock version of TechnicalAnalysisService that returns predefined values
│    without doing any actual calculations.
│    """
│    def __init__(self):
⋮
│    async def initialize_symbols(self, symbols_config):
⋮
│    async def process_all_symbols(self, current_time=None):
⋮
│    async def process_symbol(self, symbol, interval=None, current_time=None):
⋮
│    def get_technical_indicators(self, symbol):
⋮
│    def set_custom_indicators(self, symbol, indicators):
⋮
│    def _get_default_indicators(self):
⋮

services\position_observer.py:
⋮
│class PositionObserver:
│    """
│    Observer class that monitors position changes and logs trades
│    without modifying the position management system.
⋮
│    def __init__(self):
⋮
│    def observe_position_open(self,
│                             symbol: str,
│                             direction: str,
│                             volume: float,
│                             ticket: int,
│                             entry_price: float,
⋮
│    def observe_position_close(self,
│                              symbol: str,
│                              ticket: int,
│                              exit_price: float,
⋮
│    def observe_split_position(self,
│                              symbol: str,
│                              direction: str,
│                              total_volume: float,
│                              tickets: List[int],
│                              entry_prices: List[float],
⋮
│    def observe_position_partial_close(self,
│                                      symbol: str,
│                                      ticket: int,
│                                      closed_volume: float,
│                                      remaining_volume: float,
⋮
│    def _get_contract_size(self, symbol: str) -> float:
⋮
│position_observer = PositionObserver()

services\target_service.py:
⋮
│class TargetService:
│    def __init__(self, session: Session):
│        self.trigger_repository = TriggerRepository(session)
│        self.trigger_strategy = TriggerStrategy(self.trigger_repository)
│        self.target_data_service = TargetDataService()
│        self.event_system = DataEventSystem()
│
│        # Cache for target calculations - no expiration, purely event-driven
│        self.target_cache = {}
│
│        class MinimalPriceManager:
│            def __init__(self):
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
⋮
│    async def initialize_symbols(self, symbols_config: Dict):
⋮
│    async def _handle_candle_data_updated(self, symbol: str, interval: str) -> None:
⋮
│    async def _handle_target_update(self, symbol: str, interval: str, timestamp: datetime) -> None:
⋮
│    async def _calculate_targets(self, symbol: str, symbol_config: Dict, current_time: Optional[dat
⋮
│    async def process_symbol(self, symbol: str, symbols_config: Dict, current_time: Optional[dateti
⋮
│    def get_cached_targets(self, symbol: str) -> Optional[Dict[str, Any]]:
⋮
│    def update_target_cache(self, symbol: str, targets: Dict[str, Any]) -> None:
⋮
│    def clear_target_cache(self, symbol: Optional[str] = None) -> None:
⋮
│    async def get_targets(self, symbol: str, force_recalculate: bool = False) -> Optional[Dict[str,
⋮
│    async def process_all_symbols(self, current_time: Optional[datetime] = None, force_recalculate:
⋮
│async def main():
⋮

services\technical_analysis_service.py:
⋮
│class TechnicalAnalysisService:
│    """
│    Service for calculating technical indicators that combine data from
│    both candle data and target data sources.
│    """
│    def __init__(self, candle_data_service: CandleDataService, target_service: TargetService):
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
⋮
│    async def initialize_symbols(self, symbols_config: Dict):
⋮
│    async def _handle_target_data_updated(self, symbol: str, targets_data: Dict[str, Any]):
⋮
│    async def _handle_candle_data_updated(self, symbol: str, interval: str) -> None:
⋮
│    async def _handle_data_update(self, symbol: str, interval: str, timestamp: datetime) -> None:
⋮
│    async def process_symbol(self, symbol: str, targets_data: Optional[Dict] = None, current_time:
⋮
│    def _get_historical_price_data(self, symbol: str, interval, current_time: Optional[datetime] =
⋮
│    def _calculate_cci_indicators(self, price_data: Dict[str, Any], target_data: Any, interval_suff
⋮
│    async def process_all_symbols(self, current_time: Optional[datetime] = None) -> Dict[str, Dict]
⋮
│    def get_technical_indicators(self, symbol: str) -> Optional[Dict[str, Any]]:
⋮
│class PriceStructureAnalyzer:
│    """Analyzes market structure and price levels for CCI adjustment"""
│
│    def calculate_support_resistance(self, df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
⋮
│    def calculate_price_structure(self, df: pd.DataFrame) -> pd.DataFrame:
⋮
│    def _calculate_level_strength(self, df: pd.DataFrame, levels: np.ndarray) -> pd.Series:
⋮
│    def _calculate_kde_level_strength(self, df: pd.DataFrame, kde_window: int) -> pd.Series:
⋮
│class WaveletAnalyzer:
│    """Advanced wavelet-based analysis for market decomposition"""
│
│    def __init__(self, wavelet: str = 'db4', max_level: int = 4):
⋮
│    def decompose_signal(self, data: np.ndarray) -> List[np.ndarray]:
⋮
│    def compute_complexity(self, coeffs: List[np.ndarray]) -> float:
⋮
│    def analyze_market_state(self, df: pd.DataFrame) -> pd.DataFrame:
⋮
│class VolumeAnalyzer:
│    """Enhanced volume analysis with targeted pressure detection"""
│    def __init__(self, lookback_window: int = 200, vol_impact: float = 0.5):
⋮
│    def calculate_volume_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
⋮
│    def calculate_targeted_pressure(self, df: pd.DataFrame) -> pd.Series:
⋮
│    def calculate_adaptive_volatility(self, df: pd.DataFrame) -> pd.Series:
⋮
│class MarketDynamicsAnalyzer:
│    """Analyzes complex market dynamics including volatility, momentum, and trends"""
│    def __init__(self, lookback_periods: Dict[str, int] = None):
⋮
│    def calculate_price_targets(self, df: pd.DataFrame, n_levels: int = 3) -> pd.DataFrame:
⋮
│    def calculate_trend_strength(self, df: pd.DataFrame) -> pd.Series:
⋮
│    def calculate_momentum_factor(self, df: pd.DataFrame) -> pd.Series:
⋮
│class FrequencyAnalyzer:
│    """Handles frequency domain analysis and filtering"""
│    def __init__(self, sampling_rate: int = 1):
⋮
│    def wavelet_denoise(self, data: np.ndarray, wavelet: str = 'db4',
⋮
│    def initialize_butterworth_filter(self, cutoff: float, order: int = 4):
⋮
│    def apply_realtime_butterworth(self, data_chunk: np.ndarray) -> np.ndarray:
⋮
│    def apply_combined_filtering(self, series: pd.Series) -> pd.Series:
⋮
│    def butterworth_filter(self, data: np.ndarray, cutoff: float,
⋮
│class AdaptiveMomentum:
│    def __init__(self, lookback_window: int = 20):
⋮
│    def calculate_momentum(self, df: pd.DataFrame) -> pd.Series:
⋮
│class EnhancedCCICalculator:
│    def __init__(
│        self,
│        period: int = 80,  # Reduced from 100 for faster response
│        use_ema: bool = True,
│        weights: Dict[str, float] = None,
│        ema_alphas: Dict[str, float] = None,
│        min_period: int = 40,  # Reduced from 50
│        max_period: int = 120,  # Reduced from 150
│        volatility_sensitivity: float = 0.4  # Reduced from 0.5
⋮
│    def calculate_single_cci_value(self, tp: pd.Series, weights: Dict[str, float]) -> float:
⋮
│    def calculate_adaptive_deltas(self, df: pd.DataFrame) -> pd.DataFrame:
⋮
│    def calculate_multi_period_volatility(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
⋮
│    def calculate_proximity_factor(self, df: pd.DataFrame) -> pd.Series:
⋮
│    def apply_trend_adjusted_volume(self, df: pd.DataFrame) -> pd.Series:
⋮
│    def calculate_area_size(self, df: pd.DataFrame) -> Tuple[pd.Series, pd.Series, pd.DataFrame]:
⋮
│    def calculate_adaptive_weights(self, df: pd.DataFrame, proximity_factor: pd.Series,
│                                   trend_adjusted_volume: pd.Series, volatility_metrics: Dict[str,
⋮
│    def calculate_typical_price(self, data: pd.DataFrame, weights: Dict[str, float]) -> pd.Series:
⋮
│    def calculate_dynamic_cci_period(self, volatility: pd.Series,
│                                   deltas: pd.DataFrame,
│                                   df: pd.DataFrame,
⋮
│    def calculate_cci(self, df: pd.DataFrame) -> pd.DataFrame:
⋮
│    def post_process_cci(self, cci: pd.Series, df: pd.DataFrame,
⋮
│def modify_cci_calculator(calculator):
⋮

services\trade_logger.py:
⋮
│class TradeLogger:
│    """Service for logging trade information"""
│
│    def __init__(self):
⋮
│    def log_trade_entry(self,
│                        symbol: str,
│                        entry_price: float,
│                        direction: str,
│                        volume: float,
│                        stop_loss: Optional[float] = None,
│                        take_profit: Optional[float] = None,
│                        risk_amount: Optional[float] = None,
│                        risk_percentage: Optional[float] = None,
│                        strategy: Optional[str] = None,
⋮
│    def add_execution(self,
│                     trade_id: str,
│                     volume: float,
│                     execution_price: float,
⋮
│    def log_trade_exit(self,
│                       trade_id: str,
│                       exit_price: float,
│                       pnl: Optional[float] = None,
│                       pnl_percentage: Optional[float] = None,
│                       pnl_pips: Optional[float] = None,
⋮
│    def update_trade(self,
│                     trade_id: str,
⋮
│    def log_execution_exit(self,
│                          trade_id: str,
│                          execution_number: int,
⋮
│    def get_trades_by_symbol(self, symbol: str, status: Optional[str] = None) -> List[TradeLog]:
⋮
│    def get_trade_by_id(self, trade_id: str) -> Optional[TradeLog]:
⋮
│    def get_executions(self, trade_id: str) -> List[TradeExecution]:
⋮
│    def calculate_pnl(self, trade_id: str, exit_price: float) -> Dict:
⋮
│    def get_performance_stats(self, symbol: Optional[str] = None,
│                             start_date: Optional[datetime] = None,
⋮
│    def _get_contract_size(self, symbol: str) -> float:
⋮

strategy\__init__.py:
⋮
│__all__ = [
│    'PositionDirection',
│    'StrategyMapping',
│    'TradeDetails',
│    'STRATEGY_REVERSAL',
│    'STRATEGY_BREAKOUT',
│    'GBPStrategyMapping',
│    'GBPConditionStrategy',
│    'GBPCloseStrategy',
│
⋮

strategy\base.py:
⋮
│logger = logging.getLogger(__name__)
│
│class PositionDirection(Enum):
⋮
│@dataclass
│class TradeDetails:
⋮
│STRATEGY_REVERSAL = "REVERSAL"
│STRATEGY_BREAKOUT = "BREAKOUT"
│
│class StrategyMapping:
│    @classmethod
│    def get_trade_details(cls) -> Dict[str, TradeDetails]:
│        return {
│            'reversal_buy': TradeDetails(
│                direction='buy',
│                strategy=STRATEGY_REVERSAL,
│                position_direction=PositionDirection.BUY
│            ),
│            'reversal_sell': TradeDetails(
│                direction='sell',
⋮
│class BaseConditionStrategy(ABC):
│    def __init__(self):
⋮
│    @abstractmethod
│    def calculate_conditions(self, price_data: dict, candle_date) -> dict:
⋮
│    def log_price_data(self, price_data: dict, candle_date):
⋮
│class BaseCloseStrategy(ABC):
│    def __init__(self):
⋮
│    @abstractmethod
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
⋮

strategy\base_strategy_retry.py:
⋮
│class BaseStrategyWithRetry:
│    def __init__(self):
⋮
│    async def get_valid_price_data(
│        self,
│        candle_data_service: CandleDataService,
│        symbol: str,
│        max_retries: int = 3,
│        retry_delay: float = 1.0
⋮
│    def _extract_timeframes(self, data: Dict[str, Any]) -> List[str]:
⋮
│    def _validate_data(self, data: Dict[str, Any], timeframes: List[str]) -> Dict[str, Any]:
⋮
│    def _attempt_data_repair(self, data: Dict[str, Any], missing_fields: Dict[str, List[str]]) -> O
⋮

strategy\btc_strategy.py:
⋮
│class BTCStrategyMapping(StrategyMapping):
⋮
│class BTCConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "BTCUSDT"
│        self.timeframes = ["5m"]
│        self.tf = "5m"
│        self.prefix = 'btc'
⋮
│    def _log_target_data(self, targets, title=""):
⋮
│    def print_data(self, data, title=""):
⋮
│    def _extract_triggers(self) -> Dict[str, Any]:
⋮
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date
⋮
│    def reversal_buy(self, price_data, targets, triggers):
⋮
│    def reversal_sell(self, price_data, targets, triggers):
⋮
│    def breakout_buy(self, price_data, triggers):
⋮
│    def breakout_sell(self, price_data, triggers):
⋮
│class BTCCloseStrategy(BaseCloseStrategy):
│    def __init__(self):
│        super().__init__()
│        self.tf = "5m"
⋮
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
│
⋮
│    def _check_reversal_close(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
⋮
│    def _check_breakout_close(self, position_data: dict, price_data: dict) -> tuple[bool, str]:
⋮

strategy\eth_strategy.py:
⋮
│class ETHStrategyMapping(StrategyMapping):
⋮
│class ETHConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "ETHUSDT"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
│        self.prefix = 'eth'
⋮
│    def _log_target_data(self, targets, title=""):
⋮
│    def print_data(self, data, title=""):
⋮
│    def log_data_every_1_hour(self, data, title=""):
⋮
│    def _extract_triggers(self) -> Dict[str, Any]:
⋮
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date
⋮
│    def reversal_buy(self, price_data, targets, triggers):
⋮
│    def reversal_sell(self, price_data, targets, triggers):
⋮
│    def breakout_buy(self, price_data, triggers):
⋮
│    def breakout_sell(self, price_data, triggers):
⋮
│class ETHCloseStrategy(BaseCloseStrategy):
│    def __init__(self):
│        super().__init__()
│        self.tf = "1h"
⋮
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
│
⋮
│    def _check_reversal_close(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
⋮
│    def _check_breakout_close(self, position_data: dict, price_data: dict) -> tuple[bool, str]:
⋮

strategy\eur_strategy.py:
⋮
│class EURStrategyMapping(StrategyMapping):
⋮
│def get_default_strategy_parameters() -> dict:
⋮
│class EURConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "EURUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
⋮
│    def _log_target_data(self, targets, title=""):
⋮
│    def print_data(self, data, title=""):
⋮
│    def _get_strategy_params(self) -> dict:
⋮
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date
⋮
│class EURCloseStrategy(BaseCloseStrategy):
│    def __init__(self):
│        super().__init__()
│        self.tf = "1h"
⋮
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict = None) ->
⋮
│    def _check_breakout_close(self, position_data: dict, price_data: dict) -> tuple[bool, str]:
⋮
│    def _validate_price_data(self, price_data: dict, position_data: dict = None) -> bool:
⋮
│    def _check_buy_close_conditions(
│        self, current_price: float, current_high: float,
│        take_profit: float, stop_loss: float, candle_time_check: bool
⋮
│    def _check_sell_close_conditions(
│        self, current_price: float, current_low: float,
│        take_profit: float, stop_loss: float, candle_time_check: bool
⋮

strategy\gbp_strategy.py:
⋮
│class GBPStrategyMapping(StrategyMapping):
⋮
│def get_default_strategy_parameters() -> dict:
⋮
│class GBPConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "GBPUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
⋮
│    def _log_target_data(self, targets, title=""):
⋮
│    def print_data(self, data, title=""):
⋮
│    def _get_strategy_params(self) -> dict:
⋮
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date
⋮
│class GBPCloseStrategy(BaseCloseStrategy):
│    def __init__(self):
│        super().__init__()
│        self.tf = "1h"
⋮
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict = None) ->
⋮
│    def _check_breakout_close(self, position_data: dict, price_data: dict) -> tuple[bool, str]:
⋮
│    def _validate_price_data(self, price_data: dict, position_data: dict = None) -> bool:
⋮
│    def _check_buy_close_conditions(
│        self, current_price: float, current_high: float,
│        take_profit: float, stop_loss: float, candle_time_check: bool
⋮
│    def _check_sell_close_conditions(
│        self, current_price: float, current_low: float,
│        take_profit: float, stop_loss: float, candle_time_check: bool
⋮

strategy\price_target_calculator.py:
⋮
│logger = logging.getLogger(__name__)
│
│@dataclass
│class TargetPrice:
⋮
│@dataclass
│class CombinedTarget:
⋮
│class TargetResult(NamedTuple):
⋮
│class TargetsCalculator:
│    def __init__(self, symbol: str, config_data: dict, current_time: Optional[datetime] = None):
│        self.symbol = symbol
│        self.config_data = config_data
│
│        # Use the appropriate database based on mode
│        mode_manager = ModeManager()
│        if mode_manager.is_backtest_mode():
│            db_path = mode_manager.get_backtest_db_path()
│            log_database_operation("connection", f"TargetsCalculator using backtest database: {db_p
│        else:
⋮
│    def _get_candlestick_data(self, interval: Interval) -> Optional[pd.DataFrame]:
⋮
│    def _process_interval(self, interval: Interval) -> TargetResult:
⋮
│    def _find_buy_targets(self, data: pd.DataFrame) -> List[TargetPrice]:
⋮
│    def _find_sell_targets(self, data: pd.DataFrame) -> List[TargetPrice]:
⋮
│    def _calculate_volume_averages(self, data: pd.DataFrame) -> Tuple[float, float]:
⋮
│    def _combine_buy_levels(self, targets: List[TargetPrice], avg_volume: float) -> List[CombinedTa
⋮
│    def _combine_sell_levels(self, targets: List[TargetPrice], avg_volume: float) -> List[CombinedT
⋮
│    def calculate_targets(self) -> Dict:
⋮
│def process_all_symbols() -> Dict[str, Dict]:
⋮

strategy\trigger_strategy.py:
⋮
│class TriggerStrategy:
│    def __init__(self, trigger_repository: TriggerRepository):
│        self.trigger_repository = trigger_repository
│        self.trigger_repository.trigger_strategy = self  # Set self as the strategy
⋮
│    def manage_triggers(self, symbol: str, targets_data: Dict, price_data: Dict, symbols_config: Di
⋮
│    def _extract_interval_data(self, interval_suffix: str, targets_data: Dict, price_data: Dict) ->
⋮
│    def _create_initial_timeframes(self, symbol: str, symbol_config: Dict) -> List[TriggerTimeframe
⋮
│    def _handle_trigger_update(self, symbol: str, timeframes_data: List[Dict], symbol_config: Dict)
⋮
│    def _merge_timeframes(
│        self,
│        symbol: str,
│        symbol_config: Dict,
│        current_timeframes: List,
│        new_timeframes: List[Dict]
⋮
│    def _did_timeframe_change(self, old_tf, new_tf: Dict) -> bool:
⋮
│    def _should_update_trigger(
│        self,
│        symbol: str,
│        to_bt: float,
│        to_st: float,
│        symbol_config: Dict
⋮
│    def _timeframe_to_dict(self, trigger: Trigger) -> Dict:
⋮
│    def _timeframe_obj_to_dict(self, tf_obj) -> Dict:
⋮
│    def initialize_symbols(self, symbols_config: Dict):
⋮

strategy\xau_strategy.py:
⋮
│class XAUStrategyMapping(StrategyMapping):
⋮
│class XAUConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "XAUUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
│        self.prefix = 'gold'
⋮
│    def _log_target_data(self, targets, title=""):
⋮
│    def print_data(self, data, title=""):
⋮
│    def _extract_triggers(self) -> Dict[str, Any]:
⋮
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date
⋮
│    def reversal_buy(self, price_data, targets, triggers):
⋮
│    def reversal_sell(self, price_data, targets, triggers):
⋮
│    def breakout_buy(self, price_data, triggers):
⋮
│    def breakout_sell(self, price_data, triggers):
⋮
│class XAUCloseStrategy(BaseCloseStrategy):
│    def __init__(self):
│        super().__init__()
│        self.tf = "1h"
⋮
│    def should_close_position(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
│
⋮
│    def _check_reversal_close(self, position_data: dict, price_data: dict, targets: dict) -> tuple[
⋮
│    def _check_breakout_close(self, position_data: dict, price_data: dict) -> tuple[bool, str]:
⋮

tests\test_backtest.py:
⋮
│class TestBacktest(unittest.TestCase):
│    """Test cases for the backtesting module"""
│
│    def setUp(self):
⋮
│    async def test_mode_manager(self):
⋮
│    async def test_clock(self):
⋮
│    async def test_order_simulator(self):
⋮
│    async def run_all_tests(self):
⋮
│async def main():
⋮

tests\test_event_system_coroutines.py:
⋮
│async def async_callback(arg1, arg2):
⋮
│def sync_callback(arg1, arg2):
⋮
│async def test_event_system():
⋮

tests\test_live_indicator_storage.py:
⋮
│async def test_live_indicator_storage():
│    """Test the live indicator storage functionality"""
⋮
│    try:
│        # Initialize event system
│        event_system = DataEventSystem()
│
⋮
│        class MinimalPriceManager:
│            def __init__(self):
⋮
│            def check_price_cache(self):
⋮

trade_management\__init__.py

trade_management\__pycache__\__init__.cpython-312.pyc

trade_management\__pycache__\position_closer.cpython-312.pyc

trade_management\__pycache__\position_entry_manager.cpython-312.pyc

trade_management\__pycache__\position_exit_manager.cpython-312.pyc

trade_management\__pycache__\position_manager.cpython-312.pyc

trade_management\__pycache__\position_opener.cpython-312.pyc

trade_management\__pycache__\price_manager.cpython-312.pyc

trade_management\position_entry_manager.py:
⋮
│@dataclass
│class TradeSetup:
⋮
│class PositionOpener:
│    def __init__(
│        self,
│        session: Session,
│        telegram_manager: TelegramManager,
│        order_executor: TradeExecutor,
│        cash_manager,
│        engine,
│        candle_data_service: CandleDataService,
│        target_service: TargetService,  # Make target_service required
│        technical_analysis_service=None,  # Optional for backward compatibility
⋮
│    def print_position_status(self, force_print: bool = False):
⋮
│    async def process_all_positions(self, app, balance: float) -> None:
⋮
│    def _get_symbol_configs(self, amount_to_risk: float, max_allowed: float) -> Dict:
⋮
│    def _get_current_prices(self) -> Dict:
⋮
│    async def _get_price_data(self, symbol: str, setup: Dict) -> Optional[Dict]:
⋮
│    async def _fetch_targets(self, symbol: str) -> Optional[Dict]:
⋮
│    async def _process_symbol(self, app, symbol: str, setup: Dict, symbol_prices: Dict) -> None:
⋮
│    def _get_existing_position(self, symbol: str) -> Optional[Position]:
⋮
│    async def _analyze_trading_conditions(
│        self,
│        app,
│        symbol: str,
│        setup: Dict,
│        price_data: Dict
⋮
│    def _is_valid_trade_condition(self, condition: Dict) -> bool:
⋮
│    def _calculate_position_quantity(
│        self,
│        amount_to_risk: float,
│        entry_price: float,
│        stop_loss: float,
│        contract_size: float,
│        max_allowed: float,
│        symbol: str
⋮
│    async def _execute_valid_trades(self, app, symbol: str, valid_trades: List[TradeSetup], existin
⋮
│    def _update_position_record(
│        self,
│        position: Position,
│        trade: TradeSetup,
│        volume: float,
│        entry_price: float,
│        stop_loss: float,
│        take_profit: float,
│        price_data: Dict
⋮

trade_management\position_exit_manager.py:
⋮
│class ErrorHandler:
│    def __init__(self, telegram_manager):
│        self.telegram_manager = telegram_manager
│        self.last_notification_time = {}
⋮
│    async def handle_error(self, app, symbol, error_message, log_message):
⋮
│    def reset_error_count(self, symbol):
⋮
│class PositionCloser:
│    def __init__(self, session, order_executor, telegram_manager, cash_manager, technical_analysis_
│        self.session = session
│        self.order_executor = order_executor
│        self.telegram_manager = telegram_manager
│        self.cash_manager = cash_manager
│        self.candle_service = CandleDataService(cash_manager)
│        self.technical_analysis_service = technical_analysis_service
│        self.target_service = target_service
│        self.manual_position_handler = PositionAdjustments(telegram_manager, session, order_executo
│        self.close_strategies = {}
⋮
│    async def close_position_based_on_conditions(self, app):
⋮

utils\__init__.py

utils\backtest_results copy.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
│    """
│    def __init__(self, start_date: datetime, end_date: datetime, initial_balance: float = 10000.0):
⋮
│    def add_trade(self, trade: Dict[str, Any]):
⋮
│    def _calculate_drawdown(self, current_equity: float) -> float:
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮
│    def _generate_plots(self, output_dir: str):
⋮
│    def _create_static_equity_plot(self, equity_df: pd.DataFrame, output_dir: str):
⋮
│    def _create_interactive_equity_plot(self, equity_df: pd.DataFrame, output_dir: str):
⋮
│    def _create_static_equity_plot_new_format(self, output_dir: str):
⋮
│    def _create_interactive_equity_plot_new_format(self, output_dir: str):
⋮
│    def _generate_html_report(self, output_dir: str):
⋮

utils\backtest_results.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
│    """
│    def __init__(self, start_date: datetime, end_date: datetime, initial_balance: float = 10000.0):
⋮
│    def add_trade(self, trade: Dict[str, Any]):
⋮
│    def _calculate_drawdown(self, current_equity: float) -> float:
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮
│    def _generate_plots(self, output_dir: str):
⋮
│    def _generate_interactive_equity_curve(self, equity_values, output_dir):
⋮
│    def _generate_html_report(self, output_dir: str):
⋮
│    def _generate_trades_table_rows(self) -> str:
⋮

utils\clock.py:
⋮
│class TradingClock:
│    """
│    Provides a consistent time interface for both live and backtest modes.
│    In live mode, returns the actual system time.
│    In backtest mode, returns a simulated time that can be advanced programmatically.
│    """
│    def __init__(self):
⋮
│    def now(self) -> datetime:
⋮
│    def advance_time(self, seconds: float):
⋮
│    def set_time(self, new_time: datetime):
⋮
│    def is_market_open(self) -> bool:
⋮

utils\components_registry.py:
⋮
│logger = logging.getLogger(__name__)
│
⋮
│_components: Dict[str, Any] = {}
│
│def register_component(name: str, component: Any) -> None:
⋮
│def get_component(name: str) -> Optional[Any]:
⋮
│def get_all_components() -> Dict[str, Any]:
⋮
│def has_component(name: str) -> bool:
⋮
│def get_component_names() -> list:
⋮
│def clear_components() -> None:
⋮

utils\config_manager.py:
⋮
│class ConfigManager:
│    """
│    A class to manage configuration settings for the trading system
│    """
│    def __init__(self):
⋮
│    def load_config(self) -> Dict[str, Any]:
⋮
│    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
⋮
│    def get(self, key: str, default: Any = None) -> Any:
⋮
│    def set(self, key: str, value: Any) -> bool:
⋮
│    def reset(self) -> bool:
⋮
│    def _deep_update(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
⋮
│    def update_from_args(self, args) -> None:
⋮
│config_manager = ConfigManager()

utils\config_utils.py:
⋮
│class ConfigStore:
│    """Singleton class to store and manage symbol configurations"""
⋮
│    def __new__(cls):
⋮
│    @classmethod
│    def set_config(cls, config: Dict) -> None:
⋮
│    @classmethod
│    def get_symbol_config(cls, symbol: str) -> Optional[Dict]:
⋮
│    @classmethod
│    def resolve_symbols(cls, symbol: str) -> Tuple[str, str, str]:
⋮
│    @classmethod
│    def get_price_key(cls, symbol: str) -> str:
⋮
│def init_config_store(symbols_config: Dict) -> None:
⋮

utils\dashboard.py:
⋮
│class TradingDashboard:
│    """
│    A real-time dashboard for displaying critical trading information
│    """
│    def __init__(self):
⋮
│    def start(self):
⋮
│    def stop(self):
⋮
│    def _update_loop(self):
⋮
│    def _render(self):
⋮
│    def _print_header(self):
⋮
│    def _print_errors(self):
⋮
│    def _print_account_info(self):
⋮
│    def _print_positions(self):
⋮
│    def _print_symbols(self):
⋮
│    def _print_trades(self):
⋮
│    def _print_uptime(self):
⋮
│    def _print_footer(self):
⋮
│    def update_account(self, balance, equity, margin, free_margin):
⋮
│    def update_positions(self, positions):
⋮
│    def update_symbol(self, symbol, bid, ask, trend, signal):
⋮
│    def update_status(self, status):
⋮
│    def add_error(self, error_message):
⋮
│    def clear_errors(self):
⋮
│    def clear_initialization_errors(self):
⋮
│    def update_uptime(self, uptime, start_time):
⋮
│    def update_trades(self, trades):
⋮
│    def update_indicators(self, indicators):
⋮
│    def _print_indicators(self):
⋮
│dashboard = TradingDashboard()

utils\dashboard_manager.py:
⋮
│class DashboardManager:
│    """
│    Manager class to handle dashboard operations and data collection
⋮
│    def __init__(self):
⋮
│    def start(self, symbols=None, mode=None, debug=False):
⋮
│    def stop(self):
⋮
│    def set_operation_mode(self, mode: str):
⋮
│    def is_enabled(self) -> bool:
⋮
│    def get_operation_mode(self) -> str:
⋮
│    def is_debug_mode(self) -> bool:
⋮
│    def _start_data_collection(self):
⋮
│    def _update_loop(self):
⋮
│    def _update_account_info(self):
⋮
│    def _update_positions(self):
⋮
│    def _update_symbols(self):
⋮
│    def _update_uptime(self):
⋮
│    def _initialize_dashboard_data(self):
⋮
│    def _update_trades(self):
⋮
│    def _update_indicators(self):
⋮
│dashboard_manager = DashboardManager()
│
│def handle_dashboard_command():
│    """Handle dashboard command line arguments"""
⋮
│    def signal_handler(sig, frame):
⋮

utils\data_flow_logger.py:
⋮
│class DataFlowLogger:
│    """
│    A specialized logger for tracking data flow through the system.
│    Provides clean, structured logging of market data updates, processing steps,
│    and indicator calculations with enhanced traceability.
⋮
│    def __init__(self, log_file='logs/data_flow.log'):
⋮
│    def _format_value(self, value):
⋮
│    def _format_dict(self, data_dict):
⋮
│    def generate_correlation_id(self, symbol, interval):
⋮
│    def get_correlation_id(self, symbol, interval):
⋮
│    def log_data_update(self, symbol, interval, timestamp, source="TradingView"):
⋮
│    def log_data_received(self, symbol, interval, data_summary):
⋮
│    def log_database_save(self, symbol, interval, table_name, success, row_count=None):
⋮
│    def log_indicator_calculation(self, symbol, interval, timestamp, indicators, service="indicator
⋮
│    def log_candle_data(self, symbol, interval, candle_data, source="Unknown", is_latest=False, ser
⋮
│    def log_event_published(self, event_name, symbol, interval, data=None):
⋮
│    def log_calculation_start(self, service, symbol, interval):
⋮
│    def log_scheduler_update(self, symbol, interval, next_update_time, current_time, in_progress):
⋮
│    def log_error(self, symbol, interval, error_message, context=None):
⋮
│    def log_market_regime(self, symbol, interval, regime, direction, strength, momentum, timestamp=
⋮
│    def log_data_flow_summary(self, symbol, interval, process_time, status, details=None):
⋮
│    def log_database_connection(self, db_path, connection_type, status, details=None):
⋮
│    def log_data_retrieval(self, symbol, interval, source, row_count, time_range=None, query=None):
⋮
│    def log_calculation_input(self, symbol, interval, calculation_type, data_summary, source=None):
⋮
│    def log_verification(self, verification_type, status, details=None):
⋮
│    def log_target_calculation(self, symbol, interval, targets, service="price_targets_calculator",
⋮
│data_flow_logger = DataFlowLogger()

utils\debug_utils.py:
⋮
│class DebugManager:
│    """
│    Manager class to handle debugging operations when dashboard is disabled
│    """
│    def __init__(self):
⋮
│    def start(self):
⋮
│    def stop(self):
⋮
│    def is_enabled(self) -> bool:
⋮
│    def update_account_info(self):
⋮
│    def update_positions(self):
⋮
│    def update_symbol_info(self, symbol: str):
⋮
│    def update_system_info(self, uptime: str):
⋮
│    def add_error(self, error_message: str):
⋮
│    def save_debug_data(self):
⋮
│    def log_trade(self, trade_data: Dict[str, Any]):
⋮
│debug_manager = DebugManager()

utils\indicator_data_utils.py:
⋮
│def get_recent_indicator_data(limit: int = 1) -> List[Dict]:
⋮

utils\logger.py:
⋮
│class SyncedTimedRotatingFileHandler(TimedRotatingFileHandler):
│    """
│    A custom TimedRotatingFileHandler that ensures log data is flushed
│    and synced to disk immediately after each write.
│    """
│    def emit(self, record):
⋮
│    def doRollover(self):
⋮
│def setup_logging(log_directory='log'):
│    """
│    Setup logging configuration
│    Args:
│        log_directory (str): Directory to store log files
⋮
│    class MessageFilter(logging.Filter):
│        def filter(self, record):
│            # List of patterns to filter out
│            patterns = [
│                'creating websocket connection',
│                'getting data for',
│                'connect_tcp.started',
│                'connect_tcp.complete',
│                'start_tls.started',
│                'start_tls.complete',
│                'send_request_headers',
⋮
│def log(message, level='info'):
⋮
│_last_log_times = {}
│
│def log_every_1_hour(message, level='info', context=None):
⋮
│def print_and_log(message, level='info'):
⋮
│def log_error(e, context=""):
⋮
│def log_trade(symbol, action, price, volume, status="executed", sl=None, tp=None):
⋮
│def log_execution(symbol, status, details):
⋮
│def log_position(symbol, ticket, strategy, direction, volume, entry_price=None, sl=None, tp=None):
⋮
│async def notify_critical(self, app, message):
⋮
│async def notify_position(self, app, message):
⋮
│def log_strategy_result(symbol, strategy_name, conditions):
⋮
│def log_target_data(symbol, targets):
⋮
│def log_technical_data(symbol, indicators):
⋮
│def log_market_regime(symbol, regime_data):
⋮
│def log_database_operation(operation, details, level='debug'):
⋮

utils\mode_manager.py:
⋮
│class ModeManager:
│    """
│    Controls whether the system runs in live or backtest mode.
│    Implemented as a singleton to ensure consistent mode across all components.
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def set_backtest_mode(self, start_date: datetime, end_date: datetime, db_path: Optional[str] =
⋮
│    def set_live_mode(self):
⋮
│    def is_backtest_mode(self) -> bool:
⋮
│    def get_backtest_dates(self) -> Tuple[Optional[datetime], Optional[datetime]]:
⋮
│    def get_backtest_start_date(self) -> Optional[datetime]:
⋮
│    def get_backtest_end_date(self) -> Optional[datetime]:
⋮
│    def get_backtest_db_path(self) -> str:
⋮
│    def set_backtest_speed(self, speed_multiplier: float):
⋮
│    def get_backtest_speed(self) -> float:
⋮

utils\mt5_utils.py:
⋮
│class MT5Manager:
│    def __init__(self):
│        self._monitor_task = None
│        self._is_running = False
⋮
│    def initialize_mt5(self) -> bool:
⋮
│    def ensure_connection(self) -> bool:
⋮
│    async def _monitor_connection(self):
⋮
│    def start_monitoring(self):
⋮
│    async def stop_monitoring(self):
⋮
│    def get_account_balance(self):
⋮
│    def get_symbol_prices(self, symbols_config):
⋮
│mt5_manager = MT5Manager()
│
⋮
│def initialize_mt5():
⋮
│def get_account_balance():
⋮
│def get_symbol_prices():
⋮
│async def stop_monitoring():
⋮

utils\network_utils.py:
⋮
│def is_internet_available():
⋮

utils\notification_service.py:
⋮
│def log(message):
⋮
│class TelegramManager:
│    def __init__(self, bot_token, chat_id):
│        self.bot_token = bot_token
│        self.chat_id = chat_id
│        self.bot = telegram.Bot(token=bot_token)
│        self.message_sent_w = False
│        self.message_sent = False
│        self.message_sent_positions = False
⋮
│    async def send_message(self, app, message, max_retries=3):
⋮
│    async def notify_success(self, app, message):
⋮
│    async def notify_failure(self, app, message):
⋮
│    async def position_update(self, app, message):
⋮
│    async def working(self, app):
⋮
│    async def targets(self, app, entry_price_eth):
⋮
│    async def send_to_bt_st_values(self, app, symbol, targets, timeframes):
⋮
│    def initialize_telegram(self):
⋮

utils\performance_analyzer.py:
⋮
│class PerformanceAnalyzer:
│    """
│    Advanced performance metrics calculator for trading strategies
⋮
│    def __init__(self, initial_balance: float = 10000.0):
⋮
│    def calculate_metrics_from_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Union[str, f
⋮
│    def calculate_metrics_from_db(self, start_date: datetime, end_date: datetime) -> Dict[str, Unio
⋮
│    def generate_equity_curve_plot(self,
│                                  equity_curve: List[float],
│                                  equity_dates: List[datetime],
│                                  metrics: Dict[str, Any],
│                                  output_dir: str,
│                                  file_name: str,
│                                  start_date: datetime,
⋮
│    def generate_html_report(self,
│                            equity_curve: List[float],
│                            equity_dates: List[datetime],
│                            metrics: Dict[str, Any],
│                            output_dir: str,
│                            file_name: str,
│                            start_date: datetime,
⋮
│    def _calculate_max_consecutive(self, returns: List[float], winning: bool) -> int:
⋮

utils\progress.py:
⋮
│class ProgressIndicator:
│    """
│    A class to display progress indicators in the terminal
│    """
│    def __init__(self):
⋮
│    def start_spinner(self, message: str, callback: Optional[Callable] = None):
⋮
│    def start_progress_bar(self, message: str, total: int, callback: Optional[Callable] = None):
⋮
│    def update_progress(self, current: int, message: Optional[str] = None):
⋮
│    def stop(self):
⋮
│    def _spinner_loop(self):
⋮
│    def _progress_bar_loop(self):
⋮
│progress = ProgressIndicator()

utils\terminal_logger.py:
⋮
│class LogLevel(Enum):
⋮
│class TerminalLogger:
│    """
│    Enhanced terminal logger with improved formatting and organization.
│
│    Features:
│    - Different log levels with color coding
│    - Structured output for complex data
│    - Section headers and separators
│    - Configurable verbosity
⋮
│    def __init__(self, name: str = "TradingSystem", level: LogLevel = LogLevel.INFO):
⋮
│    def _format_message(self, level: LogLevel, message: str) -> str:
⋮
│    def _format_data(self, data: Union[Dict[str, Any], List], indent: int = 0) -> str:
⋮
│    def section(self, title: str, level: LogLevel = LogLevel.INFO):
⋮
│    def subsection(self, title: str, level: LogLevel = LogLevel.INFO):
⋮
│    def log(self, level: LogLevel, message: str):
⋮
│    def data(self, title: str, data: Dict[str, Any], level: LogLevel = LogLevel.INFO):
⋮
│    def trade_setup(self, symbol: str, trade_type: str, entry_price: float,
⋮
│    def order_result(self, symbol: str, direction: str, result: Dict[str, Any]):
⋮
│    def position_status(self, symbol: str, data: Dict[str, Any]):
⋮
│    def market_data(self, symbol: str, data: Dict[str, Any]):
⋮
│    def critical(self, message: str):
⋮
│    def error(self, message: str):
⋮
│    def warning(self, message: str):
⋮
│    def info(self, message: str):
⋮
│    def success(self, message: str):
⋮
│    def trade(self, message: str):
⋮
│    def strategy(self, message: str):
⋮
│    def target(self, message: str):
⋮
│    def technical(self, message: str):
⋮
│    def debug(self, message: str):
⋮
│    def trace(self, message: str):
⋮
│terminal_logger = TerminalLogger()
│
│def set_log_level(level: LogLevel):
⋮
│def _set_level_method(self, level):
⋮

utils\theme_manager.py:
⋮
│class ThemeManager:
│    """
│    A class to manage color themes for the terminal output
│    """
│    def __init__(self):
⋮
│    def set_theme(self, theme_name: str):
⋮
│    def get_color(self, category: str) -> Tuple[str, str]:
⋮
│    def get_reset(self) -> str:
⋮
│    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
⋮
│theme_manager = ThemeManager()

utils\trade_log_debug.py:
⋮
│def check_trade_log_integrity():
⋮
│def fix_trade_log_issues():
⋮
│def print_recent_trades(days=1):
⋮

utils\trade_log_utils.py:
⋮
│def get_trade_logs(symbol: Optional[str] = None,
│                  days: Optional[int] = None,
⋮
│def print_trade_summary(symbol: Optional[str] = None,
⋮
│def plot_trade_performance(symbol: Optional[str] = None,
│                          days: Optional[int] = None,
⋮
│def print_trade_logs(symbol: Optional[str] = None,
│                    days: Optional[int] = None,
│                    status: Optional[str] = None,
⋮
│def print_trade_executions(trade_id: str) -> None:
⋮

utils\trades_dashboard.py:
⋮
│def format_trade_status(status: str) -> str:
⋮
│def format_number(num: float) -> str:
⋮
│def format_pnl(pnl: Optional[float]) -> str:
⋮
│def get_recent_trades(days: int = 7, limit: int = 10) -> List[TradeLog]:
⋮
│def get_open_trades() -> List[TradeLog]:
⋮
│def display_trades_table(trades: List[TradeLog], title: str = "Recent Trades"):
⋮
│def display_trades_summary():
⋮
│def display_trades_dashboard():
⋮

utils\uptime_tracker.py:
⋮
│class UptimeTracker:
│    """
│    Tracks system uptime since initialization
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def start(self):
⋮
│    def stop(self):
⋮
│    def _update_loop(self):
⋮
│    def get_uptime(self):
⋮
│    def get_uptime_formatted(self):
⋮
│    def get_start_time(self):
⋮
│uptime_tracker = UptimeTracker()

utils\web_dashboard_manager.py:
⋮
│class WebDashboardManager:
│    """
│    Manager class to handle web dashboard operations (FastAPI version)
│    """
│    def __init__(self):
⋮
│    def initialize(self, event_system):
⋮
│    async def handle_data_update(self, symbol: str, interval: str):
⋮
│    def start(self, host=None, port=None, update_interval=None, **kwargs): # Removed unused args fo
│        """Start the web dashboard using FastAPI/Uvicorn."""
│        if not self.enabled:
│            self.enabled = True
⋮
│            try:
│                import asyncio
⋮
│                if loop and loop.is_running():
⋮
│                else:
│                    # If no running loop, start a new event loop in a new thread
│                    import threading
│
│                    def run_loop():
⋮
│    def stop(self):
⋮
│web_dashboard_manager = WebDashboardManager()
│
│def handle_web_dashboard_command():
⋮

web\README.md

web\__init__.py

web\cli.py:
⋮
│def main():
⋮

web\server.py:
⋮
│app = Flask(__name__,
│            template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
⋮
│socketio = SocketIO(app,
│                   cors_allowed_origins="*",
│                   async_mode='threading',
│                   ping_timeout=10,
│                   ping_interval=5,
│                   logger=False,
⋮
│DEFAULT_CONFIG = {
│    'host': '0.0.0.0',
│    'port': 5000,
│    'debug': False,
│    'username': 'admin',
│    'password': 'trading123',  # Default password, should be changed
│    'update_interval': 1.0,  # seconds
│    'enable_authentication': True,
│    'indicator_db_path': 'database/indicator_data.db'
⋮
│config = DEFAULT_CONFIG.copy()
│
⋮
│update_thread = None
│running = False
│
│def get_indicator_data_from_db():
⋮
│debug_data_provider = None
│
⋮
│def set_debug_data_provider(provider):
⋮
│def update_clients():
⋮
│@app.route('/')
│def index():
⋮
│@app.route('/login', methods=['GET', 'POST'])
│def login():
⋮
│@app.route('/logout')
│def logout():
⋮
│@app.route('/api/indicators')
│def get_indicators_api():
⋮
│@app.route('/api/dashboard')
│def api_dashboard():
⋮
│@socketio.on('connect')
│def handle_connect():
⋮
│@socketio.on('disconnect')
│def handle_disconnect():
⋮
│def start_server(host=None, port=None, debug=None, username=None, password=None,
⋮
│def stop_server():
⋮

web\server_fastapi.py:
⋮
│SERVER_HOST = "0.0.0.0"
│SERVER_PORT = 5000
│UPDATE_INTERVAL = 1.0  # seconds
│ENABLE_AUTHENTICATION = True
│USERNAME = "admin"
│PASSWORD = "trading123" # Default password, should be changed
│INDICATOR_DB_PATH = 'database/indicator_data.db'
│TRADE_LOG_DB_PATH = 'database/live_trade_logs.db' # Updated path to match actual file name
│SESSION_SECRET_KEY = secrets.token_hex(16)
│
⋮
│app = FastAPI(title="Trading Dashboard API", debug=True)
│
⋮
│static_dir = os.path.join(os.path.dirname(__file__), 'static')
⋮
│template_dir = os.path.join(os.path.dirname(__file__), 'templates')
│# --- ADD THIS CHECK ---
│login_template_path = os.path.join(template_dir, 'login.html')
│dashboard_template_path = os.path.join(template_dir, 'dashboard.html')
⋮
│templates = Jinja2Templates(directory=template_dir)
│
⋮
│active_connections: Set[WebSocket] = set()
│_dashboard_update_task: Optional[asyncio.Task] = None
│
⋮
│async def get_db_connection(db_path: str) -> aiosqlite.Connection:
⋮
│async def get_indicator_data_from_db_async(limit: int = 10) -> List[Dict[str, Any]]:
⋮
│async def get_trades_from_db_async(days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
⋮
│def serialize_value(value: Any) -> Any:
⋮
│async def get_dashboard_data() -> Dict[str, Any]:
⋮
│async def get_current_user(request: Request) -> Optional[str]:
⋮
│@app.get("/", response_class=HTMLResponse)
│async def read_root(request: Request):
⋮
│@app.get("/login")
│async def login_get(request: Request):
⋮
│@app.post("/login", response_class=HTMLResponse)
│async def login_post(request: Request, username: str = Form(...), password: str = Form(...)):
⋮
│@app.get("/logout")
│async def logout(request: Request):
⋮
│@app.get("/api/dashboard")
│async def api_dashboard_data(user: Optional[str] = Depends(get_current_user)):
⋮
│async def broadcast(message: Dict[str, Any]):
⋮
│async def dashboard_update_task():
⋮
│@app.websocket("/ws/dashboard")
│async def websocket_dashboard_endpoint(websocket: WebSocket, user: Optional[str] = Depends(get_curr
⋮
│_uvicorn_server: Optional[uvicorn.Server] = None
│
│async def start_server_async(host: str = SERVER_HOST, port: int = SERVER_PORT, update_interval: flo
⋮
│async def stop_server_async():
⋮

web\simple_server.py:
⋮
│app = Flask(__name__,
│            template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
⋮
│DEFAULT_CONFIG = {
│    'host': '0.0.0.0',
│    'port': 5000,
│    'debug': False,
│    'username': 'admin',
│    'password': 'trading123',  # Default password, should be changed
│    'update_interval': 1.0,  # seconds
│    'enable_authentication': True
⋮
│config = DEFAULT_CONFIG.copy()
│
│def get_indicator_data_from_db():
⋮
│@app.route('/')
│def index():
⋮
│@app.route('/login', methods=['GET', 'POST'])
│def login():
⋮
│@app.route('/logout')
│def logout():
⋮
│@app.route('/api/dashboard')
│def api_dashboard():
⋮
│def start_server(host=None, port=None, debug=None, username=None, password=None,
⋮
│def stop_server():
⋮

web\static\css\styles.css

web\static\js\dashboard.js:
⋮
│function startPolling() {
│    if (!pollingInterval) {
│        pollingInterval = setInterval(fetchDashboardData, 1000);
│        fetchDashboardData(); // Initial fetch
│    }
⋮
│function fetchDashboardData() {
│    // First fetch the main dashboard data
│    fetch('/api/dashboard')
│        .then(response => {
│            if (!response.ok) {
│                throw new Error('Network response was not ok');
│            }
│            return response.json();
│        })
│        .then(data => {
⋮
│function formatCurrency(value) {
│    return new Intl.NumberFormat('en-US', {
│        style: 'currency',
│        currency: 'USD'
│    }).format(value);
⋮
│function formatNumber(value, decimals = 5) {
│    return parseFloat(value).toFixed(decimals);
⋮
│function updateStatusClass(status) {
│    systemStatusElement.className = '';
│
│    if (status.startsWith('Error')) {
│        systemStatusElement.classList.add('status-error');
│    } else if (status.startsWith('Initializing')) {
│        systemStatusElement.classList.add('status-initializing');
│    } else {
│        systemStatusElement.classList.add('status-running');
│    }
⋮
│function updateErrors(errors) {
│    if (errors && errors.length > 0) {
│        errorsContainer.classList.remove('hidden');
│        errorsList.innerHTML = '';
│
│        errors.forEach(error => {
│            const errorItem = document.createElement('div');
│            errorItem.className = 'error-item';
│            errorItem.textContent = error;
│            errorsList.appendChild(errorItem);
⋮
│function updateAccount(account) {
│    balanceElement.textContent = formatCurrency(account.balance);
│    equityElement.textContent = formatCurrency(account.equity);
│    marginElement.textContent = formatCurrency(account.margin);
│    freeMarginElement.textContent = formatCurrency(account.free_margin);
⋮
│function updatePositions(positions) {
│    if (positions && positions.length > 0) {
│        positionsBody.innerHTML = '';
│
│        positions.forEach(position => {
│            const row = document.createElement('tr');
│
│            // Determine P/L class
│            const plClass = position.pl >= 0 ? 'trend-bullish' : 'trend-bearish';
│
⋮
│function updateSymbols(symbols) {
│    if (symbols && Object.keys(symbols).length > 0) {
│        symbolsBody.innerHTML = '';
│
│        Object.keys(symbols).forEach(symbol => {
│            const data = symbols[symbol];
│            const row = document.createElement('tr');
│
│            // Calculate spread
│            const spread = ((data.ask - data.bid) * 10000).toFixed(1);
│
⋮
│function updateUptime(uptime, startTime) {
│    uptimeElement.textContent = `Uptime: ${uptime} | Started: ${startTime}`;
⋮
│function updateIndicators(indicators) {
│    console.log('updateIndicators called with:', indicators);
│
│    // Debug: Check what's in the indicators array
│    if (indicators) {
│        console.log('Indicators type:', typeof indicators);
│        console.log('Indicators length:', indicators.length);
│        if (indicators.length > 0) {
│            console.log('First indicator:', indicators[0]);
│        }
⋮
│function updateTrades(trades) {
│    console.log('updateTrades called with:', trades);
│
│    // Debug: Check what's in the trades array
│    if (trades) {
│        console.log('Trades type:', typeof trades);
│        console.log('Trades length:', trades.length);
│        if (trades.length > 0) {
│            console.log('First trade:', trades[0]);
│        }
⋮
│function updateDashboard(data) {
│    console.log('updateDashboard called with data:', data);
│
│    // Check if data contains trades
│    if (data.trades) {
│        console.log('Data contains trades array with length:', data.trades.length);
│        if (data.trades.length > 0) {
│            console.log('First trade in data:', data.trades[0]);
│        }
│    } else {
⋮

web\templates\dashboard.html

web\templates\login.html

web\templates\simple_dashboard.html

================================================================================
Map slice stats: 94663 characters, 10265 words
================================================================================
MAP_REQUEST processed - Repository map generated and sent to LLM
📍 **MAP_REQUEST Response**
**Keywords**: compute_next_boundary, boundary, next, compute, calculate, edge, limit
**Type**: implementation
**Max Results**: 8

**Files Found**: 1

**Repository Map Preview**:
```
# Focused Repository Map
**Search Keywords**: compute_next_boundary, boundary, next, compute, calculate, edge, limit
**Files Found**: 1
⚠️  **CRITICAL INSTRUCTION**: This map shows file structure and symbols only. DO NOT analyze or explain functionality based on
this map alone. Your NEXT response must be EXACTLY this format:
```json
{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition",      
"name": "ACTUAL_SYMBOL_NAME_FROM_MAP", "directory_name": "folder_name", "file_name": "file.py"}]}}
```
🚨 **CRITICAL**: Use ONLY the EXACT symbol names shown in the repository map below. DO NOT make assumptions about
class/function names (like assuming 'Backtest' class exists). If you don't see the exact symbol name in the map, you CANNOT   
request it, and always follow the protocol if you faild to find the exact name(s). PROTOCOL: MAP_REQUEST → Get the exact      
name(s) → Wait → CONTEXT_REQUEST
**IMPORTANT**: If you see the same method/function name in multiple files, request context from ALL of them:
```json
{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition",      
"name": "ACTUAL_SYMBOL_NAME", "directory_name": "folder1", "file_name": "file1.py"}, {"type": "method_definition", "name":    
"ACTUAL_SYMBOL_NAME", "directory_name": "folder2", "file_name": "file2.py"}]}}
```
**WARNING: If you don't follow this protocol, you will NOT be able to assist the user properly.**
## Search Results
1. **C:\Users\<USER>\Documents\____live_backtest_dashboard_____\strategy\price_target_calculator.py** (score: 15.0)
... (truncated, full map sent to LLM)
```


=== FORMAT_CHAT_CHUNKS PROCESSING ===
Using full system prompt
System prompt length: 6613 characters
System prompt first 100 chars: 🎮 YOU ARE THE PLAYER in the CODEBASE EXPLORATION GAME!

**YOUR MISSION:**
🏆 **YOU need to WIN the ga...
Using examples as separate messages
Number of example messages: 0
Created ChatChunks object
Using system prompt
Added 0 example messages to chunks
Added 0 done messages to chunks
Added 2 repo messages to chunks
Added 0 readonly file messages to chunks
Added 2 chat file messages to chunks
No system reminder message
Added 3 current messages to chunks
Initialized empty reminder list

=== CHAT_CHUNKS ALL_MESSAGES ===
System messages: 1
Example messages: 0
Readonly file messages: 0
Repo messages: 2
Done messages: 0
Chat file messages: 2
Current messages: 3
Reminder messages: 0
Total messages: 8
Message roles:
  Message 1: system
  Message 2: user
  Message 3: assistant
  Message 4: user
  Message 5: assistant
  Message 6: user
  Message 7: assistant
  Message 8: user
Role counts:
  system: 1
  user: 4
  assistant: 3
=== END OF CHAT_CHUNKS ALL_MESSAGES ===

Messages tokens: 29429
Reminder tokens: 3
Current messages tokens: 27278
Total tokens: 56710
Final message role: user
Max input tokens: 40960
Not adding reminder prompt (token limit or no reminder)
=== END OF FORMAT_CHAT_CHUNKS PROCESSING ===



=== CHAT_CHUNKS ALL_MESSAGES ===
System messages: 1
Example messages: 0
Readonly file messages: 0
Repo messages: 2
Done messages: 0
Chat file messages: 2
Current messages: 3
Reminder messages: 0
Total messages: 8
Message roles:
  Message 1: system
  Message 2: user
  Message 3: assistant
  Message 4: user
  Message 5: assistant
  Message 6: user
  Message 7: assistant
  Message 8: user
Role counts:
  system: 1
  user: 4
  assistant: 3
=== END OF CHAT_CHUNKS ALL_MESSAGES ===