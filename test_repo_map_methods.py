#!/usr/bin/env python

import os
import sys
import tempfile
from pathlib import Path

# Add the aider-main directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'aider-main')))

# Import the required modules
try:
    from aider.repomap import RepoMap
    print("✅ Successfully imported RepoMap")
except ImportError as e:
    print(f"❌ Failed to import RepoMap: {e}")
    sys.exit(1)

def create_test_repo():
    """Create a temporary test repository with some files."""
    temp_dir = tempfile.mkdtemp()

    # Create some test files
    test_files = [
        "file1.py",
        "file2.py",
        "subdir/file3.py",
        "subdir/file4.py",
        "another_dir/file5.py",
    ]

    for file_path in test_files:
        full_path = os.path.join(temp_dir, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w') as f:
            f.write(f"# Test content for {file_path}\n")

    return temp_dir

def test_get_repo_overview():
    """Test the get_repo_overview method."""
    print("\n=== Testing get_repo_overview ===")

    # Create a test repository
    repo_dir = create_test_repo()
    print(f"Created test repository at: {repo_dir}")

    try:
        # Create a RepoMap instance
        repo_map = RepoMap(root=repo_dir)
        print("Created RepoMap instance")

        # Get the repository overview
        overview = repo_map.get_repo_overview()
        print("\n=== Repository Overview ===")
        print(overview)

        # Check if the overview contains the expected directories and files
        if "another_dir" in overview and "subdir" in overview and "file1.py" in overview:
            print("✅ Repository overview contains expected directories and files")
        else:
            print("❌ Repository overview is missing expected directories or files")
    except Exception as e:
        print(f"❌ Error testing get_repo_overview: {e}")
    finally:
        # Clean up the test repository
        try:
            # Close any open file handles by forcing garbage collection
            import gc
            gc.collect()

            import shutil
            shutil.rmtree(repo_dir, ignore_errors=True)
            print(f"Removed test repository: {repo_dir}")
        except Exception as e:
            print(f"Warning: Could not fully remove test repository: {e}")

def test_get_all_files():
    """Test the get_all_files method."""
    print("\n=== Testing get_all_files ===")

    # Create a test repository
    repo_dir = create_test_repo()
    print(f"Created test repository at: {repo_dir}")

    try:
        # Create a RepoMap instance
        repo_map = RepoMap(root=repo_dir)
        print("Created RepoMap instance")

        # Get all files
        all_files = repo_map.get_all_files()
        print("\n=== All Files ===")
        for file in sorted(all_files):
            print(f"- {file}")

        # Check if all_files contains the expected files
        # Normalize paths to use the OS-specific path separator
        expected_files = [
            "file1.py",
            "file2.py",
            os.path.join("subdir", "file3.py"),
            os.path.join("subdir", "file4.py"),
            os.path.join("another_dir", "file5.py")
        ]

        # Normalize all_files to use forward slashes for comparison
        normalized_files = [file.replace('\\', '/') for file in all_files]
        normalized_expected = [file.replace('\\', '/') for file in expected_files]

        # Check if each expected file is in the normalized list
        missing_files = []
        for expected in normalized_expected:
            if not any(expected in file for file in normalized_files):
                missing_files.append(expected)

        if not missing_files:
            print("✅ get_all_files returned all expected files")
        else:
            print(f"❌ get_all_files is missing expected files: {missing_files}")

        # Print normalized paths for debugging
        print("\n=== Normalized Paths ===")
        print("Expected files:")
        for file in normalized_expected:
            print(f"- {file}")
        print("\nActual files:")
        for file in normalized_files:
            print(f"- {file}")
    except Exception as e:
        print(f"❌ Error testing get_all_files: {e}")
    finally:
        # Clean up the test repository
        try:
            # Close any open file handles by forcing garbage collection
            import gc
            gc.collect()

            import shutil
            shutil.rmtree(repo_dir, ignore_errors=True)
            print(f"Removed test repository: {repo_dir}")
        except Exception as e:
            print(f"Warning: Could not fully remove test repository: {e}")

def main():
    """Run all tests."""
    test_get_repo_overview()
    test_get_all_files()

    print("\n=== All tests completed! ===")

if __name__ == "__main__":
    main()
