=== COMPLETE MESSAGES BEING SENT TO LLM ===

Message 1 - Role: system
Content:
Act as an expert code analyst.
Answer questions about the supplied code without making assumptions or hallucinating context.
Always reply to the user in English_United States.

Your thinking process must balance thoroughness with factual accuracy:
- Start by identifying exactly what information has been explicitly shared by the user.
- Consider only files and context that have been established in the conversation.
- If insufficient information exists to answer completely, acknowledge this limitation.
- Never invent user requests, files, or repository contexts to fill gaps in information.
- When faced with ambiguity, formulate clarifying questions rather than making assumptions.
Your analysis should be comprehensive for the information available, but always prioritize accuracy over speculation.

IMPORTANT RULES FOR INTERACTION AND FILE HANDLING:
1.  Never invent or hallucinate information, files, or context that wasn't provided by the user.
2.  When responding to simple greetings (e.g., "Hi", "Hello"), just respond with a simple greeting. Do not infer any other request.
3.  Do not assume the user wants to edit files or make changes unless explicitly stated.
4.  Only reference files if their existence is established through user inputs. This means you can reference a file if:
    a. The user has explicitly mentioned the file name in the current or recent conversation.
    b. The file's content has been explicitly added to the chat by the user.
    c. The file is known from a list of project files, summaries, or a repository overview previously provided by the user.
    If a file's existence is known from such user inputs, you may reference it if it is relevant to the user's current query.
    Do not invent files, paths, or their contents.
5.  Never claim to have added files to the chat yourself. Only the user can add files. If you need to examine a file not yet in the chat, ask the user to add it.
6.  Always ground your responses in the actual conversation history and user inputs.
7.  When analyzing code or systems, focus *only* on files whose existence is known (per rule 4) and that are relevant to the user's specific query.
8.  If a user's query is ambiguous regarding which files to analyze (among known files), ask for clarification before proceeding.

GUIDANCE FOR ANSWERING QUESTIONS ABOUT SPECIFIC CODE ITEMS (functions, classes, etc.):
A. If the relevant file content has *already been added to the chat by the user*, analyze that content to answer.
B. If the relevant file content has *not* been added, but you have a project overview/repo map that suggests a likely file:
    - Suggest the file to the user and ask them to *add it to the chat*.
    - Example: "The 'position quantity calculator' function might be in 'src/trading/calculators.py'. If you'd like me to explain it, please add that file to the chat."
    - Do NOT proceed as if the file has been added until the user confirms or adds it.
C. Once the user adds a file, or you are analyzing an already-added file for a specific item:
    i.  Carefully examine the provided file content for the *specific item* the user asked about.
    ii. If the item is found, explain it clearly and concisely as requested.
    iii.If the specific item is *not* found in the analyzed file:
        1. Clearly state that the item was not found in *that specific file*.
           Example: "I've looked through 'models/position_repository.py' that you added, but I couldn't find a function specifically named 'position quantity calculator' in it."
        2. Do *not* attempt to explain other, unrelated parts of that (potentially incorrect) file unless the user explicitly redirects the conversation. Your goal is to answer their original question.
        3. If you have knowledge of other *known* files (e.g., from a repo map) that might be more relevant candidates for the requested item, you can suggest one.
           Example: "Perhaps the 'position quantity calculator' is in 'src/trading/core_logic.py' instead? If you'd like me to check that file, please add it to the chat."
        4. If you have no other likely candidate files based on *known information*, or if previous suggestions were also incorrect, ask the user for more specific guidance.
           Example: "Could you please clarify which file contains the 'position quantity calculator', or add the correct file to the chat so I can explain it?"
Your primary objective is to accurately answer the user's question about the *specific item they inquired about*, using the correct file context provided by them. Be diligent in seeking the correct context.

If you need to describe code recommendations, do so clearly and thoroughly based *only* on the provided information.



CRITICAL CONTEXT_REQUEST WORKFLOW:
For ~90% of user queries about code, CONTEXT_REQUEST is the most efficient approach. This includes questions about:
- How code works ("How does X work?")
- Implementation details ("What does X do?")
- Function parameters ("What parameters does X take?")
- Return values ("What does X return?")
- Error handling ("How does X handle errors?")
- Dependencies ("What does X depend on?")
- Usage patterns ("How should I use X?")
- Code structure ("What's the structure of X?")

For ALL these types of queries, follow this EXACT workflow:

1. IMMEDIATELY use CONTEXT_REQUEST to get the specific symbol:
   {CONTEXT_REQUEST: {
     "original_user_query_context": "Brief description of what the user is asking about",
     "symbols_of_interest": [
       {"type": "method_definition", "name": "ExactSymbolName"}
     ],
     "reason_for_request": "Brief reason"
   }}

2. DO NOT take any of these incorrect steps:
   - DO NOT try to first check if files exist
   - DO NOT try to request entire files first
   - DO NOT try to search for the file first
   - DO NOT overthink which file might contain the symbol
   - DO NOT analyze repository structure first

3. After receiving code context:
   - If the implementation is complete (has signature, body, return), IMMEDIATELY answer the question
   - Only request additional context if critical information is clearly missing (undefined variables, missing methods)

REMEMBER: The surgical extraction system will automatically find the right file and extract the code - you don't need to figure out which file contains the code first!

WHEN TO USE CONTEXT_REQUEST VS. REQUEST_FILE:

1. Use CONTEXT_REQUEST (~90% of cases):
   - When you need to understand specific functions, methods, or classes
   - When you need to analyze implementation details
   - When you need to understand how a specific piece of code works
   - When you're interested in a specific symbol rather than an entire file
   - When you want to see only the relevant code and its dependencies

2. Use REQUEST_FILE (only ~10% of cases):
   - When you need to see the overall structure of a file
   - When you need to understand the relationships between multiple components in a file
   - When you need to see configuration files, data files, or non-code files
   - When you need to see the entire context of a file to understand it

GENERAL FILE ACCESS INSTRUCTIONS:
- You start with ZERO files loaded. You must explicitly request information using either CONTEXT_REQUEST or REQUEST_FILE.
- NEVER assume you have access to any code unless you've explicitly requested it and seen its contents.
- NEVER provide information about code you haven't seen.
- ALWAYS check the repository map before making requests to ensure you're requesting the correct information.
- NEVER assume a file exists without checking the repository map first.
- ONLY request files or symbols that are present in the repository map and match your reason.

CONTEXT_REQUEST FORMAT:
{CONTEXT_REQUEST: {
  "original_user_query_context": "Brief description of what the user is asking about",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "ClassName.method_name", "file_hint": "path/to/file.py"}
  ],
  "reason_for_request": "Why you need this context to answer the user's question"
}}

If you can't find a file in the repository map, or if the added files don't contain the needed context, use the {SEARCH_REQUEST} command:

{SEARCH_REQUEST:
  "query": "Descriptive search query explaining what you're looking for",
  "keywords": "keyword1, keyword2, keyword3",
  "reason": "Explanation of what you're trying to find"
}

The "keywords" field can be either:
1. A comma-separated string: "keyword1, keyword2, keyword3"
2. An array of strings: ["keyword1", "keyword2", "keyword3"]

Both formats are supported and will be processed correctly.

The system will use your keywords to find the most relevant files and add them to the conversation.
IMPORTANT: You are limited to a maximum of 2 search attempts per user query. After 2 failed attempts, you MUST STOP searching and provide recommendations to the user.
When generating search queries:

1. ALWAYS INCLUDE EXACT FUNCTION/CLASS NAMES:
   - If you're looking for a specific function, class, or method, ALWAYS include its exact name as a keyword
   - For example, if searching for an "analyze_trading_conditions" function, include that exact name
   - Include both the exact name and any variations (with/without underscores, camelCase alternatives)
   - IMPORTANT: The search system gives higher priority to function-like names, so including them is critical

2. EXPAND CORE KEYWORDS with relevant technical variations:
   - For each primary keyword, identify 2-3 technical synonyms or related terms
   - Include industry-standard abbreviations and alternative spellings
   - Consider both verb and noun forms where applicable
   - Add common prefix/suffix variations used in code (e.g., "get-", "-Service", etc.)

3. ADD DOMAIN-SPECIFIC TECHNICAL TERMS that would uniquely identify the functionality:
   - Include likely function/method names implementing this functionality
   - Add library-specific terminology related to the feature
   - Consider framework-specific implementation terms
   - Include relevant API endpoint patterns common in this domain

4. AVOID DILUTING with general terms:
   - Remove generic programming terms that appear across many files
   - Exclude common application structure words
   - Filter out general purpose utility terms
   - Omit words that are likely to appear in most files of the codebase

5. PRIORITIZE DISTINCTIVENESS:
   - Select terms that would appear almost exclusively in relevant files
   - Prefer technical jargon over general terminology
   - Include 5-8 highly specific keywords rather than more generic ones

6. FORMAT KEYWORDS in either of these two ways:
   - As a comma-separated string: "keywords": "analyze_trading_conditions, position_entry, trading_logic"
   - OR as an array of strings: "keywords": ["analyze_trading_conditions", "position_entry", "trading_logic"]
   - Both formats are supported and will be processed correctly
   - IMPORTANT: Do NOT use double quotes around individual keywords in the comma-separated string format
   - INCORRECT: "keywords": ""analyze_trading_conditions", "position_entry", "trading_logic""
   - CORRECT: "keywords": "analyze_trading_conditions, position_entry, trading_logic"
   - List in order of specificity (most specific first)
   - Group related terms together
The search engine will prioritize files where your keywords appear together or in close proximity.

The system includes a smart search feature that will:
- Use your search query to find relevant files across the repository
- Prioritize code files over non-code files
- Give higher relevance to files where your keywords appear together
- Add the most relevant files to the conversation


Reply in English_United States.

--------------------------------------------------------------------------------
Message 2 - Role: system
Content:
CURRENT USER QUERY (ANSWER THIS QUERY ONLY):
>>> "and how does the position quantity calculator function work?" <<<

IMPORTANT: This query is ONLY about the function 'PositionCloser.close_position_based_on_conditions'.
DO NOT answer about ANY other functions from previous queries.
ONLY provide information about 'PositionCloser.close_position_based_on_conditions'.

---
CONVERSATION HISTORY (FOR REFERENCE ONLY):
The following history is provided ONLY for context. DO NOT answer these previous queries.

[PREVIOUS QUERY - DO NOT ANSWER THIS]: how does the close_position_based_on_conditions function work?


[PREVIOUS RESPONSE]: <think>
Okay, let's tackle this query. The user wants to know how the `close_position_based_on_conditions` function works. From the code context provided, I need to break down the function's logic step by step.

First, the function starts by getting all MT5 positions. If there are none, it logs a message and returns. Then it checks if there are any positions. It uses the `mt5.positions_get()` method, which retrieves all open positions. If this returns nothing, it logs and exits.

Next, it extracts symbols from both MT5 positions and the database. It creates a set of symbols to process. The function then loops through each symbol in these sets. For each symbol, it checks if there's an existing entry in the database. If not, it creates a new entry with default values.

The function then calls `manual_position_handler.handle_manual_position_changes`, which probably handles the changes to the position data. After that, it checks if the position is open and if there are any symbol positions. If so, it determines the close strategy based on the symbol.

The close strategy is retrieved from `close_strategies`, which might be a dictionary mapping symbols to specific strategies. If no strategy is found, it logs a message and continues. The function then finds the configuration for the symbol using `SYMBOLS_CONFIG`, which might include price keys and other parameters.

It retrieves price data using `candle_service.get_candle_data`, which gets historical price data. If no data is available, it skips the symbol. Then, it checks for targets from `target_service`, which might determine the take profit and stop loss levels.

The function merges targets with technical indicators from `technical_analysis_service`. It then processes each position in the symbol's positions list. For each position, it checks if the conditions for closing the position are met using the `should_close_position` method.

If the conditions are met, the function closes the positions using `order_executor.close_all_positions`, updates the database entry, and notifies the user. It logs the reason for closing and commits the session. If an error occurs during any step, it rolls back the transaction to prevent data corruption.

The function handles exceptions, logging errors and rolling back transactions to ensure data integrity. The entire process is asynchronous, using await statements to handle async operations like database commits and notifications.

So, putting it all together, the function works by first checking MT5 positions, processing each symbol, determining the close strategy, retrieving necessary data, checking conditions, and closing positions if the conditions are met. It's a complex process involving multiple steps and dependencies, all handled within the provided code.
</think>

The `close_position_based_on_conditions` function works by systematically determining when and how to close trading positions based on specific conditions. Here's a breakdown of its key steps:

---

### **1. Retrieve MT5 Positions**
- Calls `mt5.positions_get()` to fetch all open positions in MetaTrader5 (MT5).
- If no positions exist, it logs a message and returns early.

---

### **2. Symbol Processing**
- Extracts symbols from both MT5 positions and the database (via `Position` model).
- Combines symbols into `all_symbols` for processing.

---

### **3. Position Check**
- For each symbol in `all_symbols`:
  - Checks if a database entry exists. If not, creates a new entry with default values.
  - Calls `manual_position_handler.handle_manual_position_changes` to update position data.

---

### **4. Close Strategy Determination**
- Retrieves the close strategy for the symbol from `close_strategies`.
- If no strategy exists, logs a warning and skips the symbol.

---

### **5. Configuration and Data Retrieval**
- Finds the corresponding configuration for the symbol from `SYMBOLS_CONFIG`.
- Retrieves price data using `candle_service.get_candle_data` (e.g., `price_key`).
- Fetches targets from `target_service` (e.g., take profit/stop loss levels).
- Combines targets with technical indicators from `technical_analysis_service`.

---

### **6. Condition Evaluation**
- For each position in the symbol's list:
  - Uses `should_close_position` to evaluate if the position should be closed based on:
    - Position direction (long/short).
    - Entry price.
    - Target levels.
    - Technical indicators.
    - Configured risk parameters.

---

### **7. Position Closure**
- If conditions are met, the function:
  - Closes all positions using `order_executor.close_all_positions`.
  - Updates the database entry (e.g., `position_opened = False`, `strategy = None`).
  - Notifies the user via `telegram_manager.notify_success`.
  - Logs the reason for closure and commits the transaction.

---

### **8. Error Handling**
- Rolls back transactions if errors occur (e.g., missing data, invalid configurations).
- Logs errors to ensure traceability.

---

### **Key Dependencies**
- **`mt5.positions_get()`**: Retrieves MT5 positions.
- **`manual_position_handler`**: Manages position data updates.
- **`candle_service`**: Provides historical price data.
- **`target_service`**: Determines take profit/stop loss levels.
- **`technical_analysis_service`**: Provides technical indicators.

---

### **Summary**
The function evaluates conditions (e.g., price targets, technical indicators) to decide when to close positions. It dynamically adjusts strategies based on symbol-specific configurations and ensures data integrity through async operations and error handling. The process is self-contained and leverages external services for real-time data and logic.

END OF CONVERSATION HISTORY
REMEMBER: ONLY answer the CURRENT QUERY at the top of this message.
---
Code Context:

### REPOSITORY FILES
- Repository Overview:
- ./:
- - __init__.py
- - conversation_context.db
- - history.db
- - position_entry_manager.py
- - position_exit_manager.py

---### REQUESTED SYMBOL DEFINITIONS (Surgically Extracted)

--- Definition from: position_exit_manager.py ---
# File header & imports
import time
from manual_operations.manual_position_handler import PositionAdjustments
from services.candle_data_service import CandleDataService
from services.technical_analysis_service import TechnicalAnalysisService
from services.target_service import TargetService
from config.trading_pairs_config import SYMBOLS_CONFIG
from models.position_repository import Position
from utils.logger import log
import MetaTrader5 as mt5
class PositionCloser
    async def close_position_based_on_conditions(self, app):
        try:
            all_mt5_positions = mt5.positions_get()
            if all_mt5_positions is None:
                log("No positions found in MT5")
                return

            mt5_symbols = set(position.symbol for position in all_mt5_positions)
            db_symbols = set(row[0] for row in self.session.query(Position.symbol).distinct())
            all_symbols = mt5_symbols.union(db_symbols)

            # log(f"Processing symbols: {all_symbols} {'='*200}")

            for symbol in all_symbols:
                try:
                    existing_row = self.session.query(Position).filter_by(symbol=symbol).first()

                    if existing_row is None:
                        log(f"Creating new database record for symbol: {symbol}")
                        existing_row = Position(
                            symbol=symbol,
                            position_opened=False,
                            strategy=None,
                            positions_counter=0,
                            total_opened_volume=0,
                            position_ticket=None,
                            amount_to_risk=0,
                            position_direction=0,
                            entry_price=0,
                            take_profit=0,
                            stoploss=0
                        )
                        self.session.add(existing_row)
                        self.session.commit()

                    await self.manual_position_handler.handle_manual_position_changes(app, symbol, existing_row)

                    symbol_positions = [p for p in all_mt5_positions if p.symbol == symbol]

                    if existing_row.position_opened and symbol_positions:
                        close_strategy = self.close_strategies.get(symbol)
                        if not close_strategy:
                            log(f"No close strategy found for symbol: {symbol}")
                            continue

                        # Find matching configuration
                        matching_config = next((config for config_symbol, config in SYMBOLS_CONFIG.items()
                                            if config.get('mt5_symbol') == symbol), None)

                        if not matching_config:
                            log(f"No configuration found for {symbol}")
                            continue

                        # Get price data using CandleDataService
                        price_key = matching_config.get('price_key', symbol)
                        price_data = await self.candle_service.get_candle_data(symbol, price_key)

                        if not price_data:
                            log(f"No price data available for {symbol}. Skipping...")
                            continue

                        # Get targets from TargetService if available, otherwise skip
                        targets = None
                        if self.target_service:
                            # Find the config symbol from the mt5 symbol
                            config_symbol = next(
                                (sym for sym, cfg in SYMBOLS_CONFIG.items()
                                if cfg.get('mt5_symbol') == symbol),
                                symbol
                            )
                            targets = await self.target_service.get_targets(config_symbol)

                        if not targets:
                            log(f"No targets available for {symbol}. Skipping...")
                            continue

                        # Get technical indicators if available
                        technical_indicators = {}
                        if self.technical_analysis_service:
                            # Find the config symbol from the mt5 symbol
                            config_symbol = next(
                                (sym for sym, cfg in SYMBOLS_CONFIG.items()
                                if cfg.get('mt5_symbol') == symbol),
                                symbol
                            )
                            technical_indicators = self.technical_analysis_service.get_technical_indicators(config_symbol) or {}
                            log(f"Technical indicators for {symbol}: {technical_indicators}")

                        # Merge targets with technical indicators
                        combined_targets = {**targets, **technical_indicators}

                        # Add avg to price_data if available
                        if targets and 'avg' in targets:
                            price_data['avg'] = targets['avg']

                        for position in symbol_positions[:existing_row.positions_counter]:
                            try:
                                position_data = {
                                    'strategy': existing_row.strategy,
                                    'position_direction': existing_row.position_direction,
                                    'entry_price': existing_row.entry_price,
                                    'open_time': existing_row.open_time,
                                    'stoploss': existing_row.stoploss,
                                    'take_profit': existing_row.take_profit
                                }

                                should_close, reason = close_strategy.should_close_position(
                                    position_data,
                                    price_data,
                                    combined_targets
                                )

                                if should_close:
                                    await self.order_executor.close_all_positions(app, symbol, position.volume)
                                    existing_row.position_opened = False
                                    existing_row.strategy = None
                                    existing_row.positions_counter = 0
                                    existing_row.total_opened_volume = 0
                                    await self.telegram_manager.notify_success(app, f"{reason} hit. Position closed for {symbol}.")
                                    log(f"{reason} hit. Position closed for {symbol}.")
                                    self.error_handler.reset_error_count(symbol)
                                    break

                            except Exception as e:
                                error_message = f"Error checking close conditions for {symbol}: {e}"
                                log_message = f"Error checking close conditions for position {position.ticket}: {e}"
                                await self.error_handler.handle_error(app, symbol, error_message, log_message)
                                continue

                    self.session.commit()  # Commit after each symbol
                except Exception as e:
                    log(f"Error processing symbol {symbol}: {e}")
                    self.session.rollback()

        except Exception as e:
            log(f"Error in close_position_based_on_conditions: {e}")
            await self.telegram_manager.notify_failure(app, f"Error in position management: {e}")
            self.session.rollback()
---

--- Definition from: position_entry_manager.py ---
# File header & imports
from services.candle_data_service import CandleDataService
from order_management.order_executor import TradeExecutor
from config.trading_pairs_config import SYMBOLS_CONFIG
from utils.notification_service import TelegramManager
from config.trading_pairs_config import SymbolConfig
from services.target_service import TargetService
from models.position_repository import Position
from strategy.base import PositionDirection
from strategy.base import StrategyMapping
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from dataclasses import dataclass
from colorama import Fore, Style
from datetime import datetime
import uuid
from utils.logger import log, log_strategy_result, log_trade
from utils.terminal_logger import terminal_logger, LogLevel
from sqlalchemy import desc
import MetaTrader5 as mt5
import traceback
from services.position_observer import position_observer
from utils.logger import log_position
from utils.logger import log_position
import traceback
class PositionOpener
    def _calculate_position_quantity(
        self,
        amount_to_risk: float,
        entry_price: float,
        stop_loss: float,
        contract_size: float,
        max_allowed: float,
        symbol: str
    ) -> float:
        try:
            # Get mt5_symbol from config
            config_symbol = next(
                (config_sym for config_sym, config_data in SYMBOLS_CONFIG.items()
                if config_data.get('mt5_symbol') == symbol),
                symbol
            )
            mt5_symbol = SYMBOLS_CONFIG[config_symbol]['mt5_symbol']

            log(f"Starting position calculation for {mt5_symbol}")

            sl_distance = abs(entry_price - stop_loss)

            log(f"Position size calculation for {mt5_symbol}:")
            log(f"Amount to risk: {amount_to_risk}")
            log(f"Entry price: {entry_price}")
            log(f"Stop loss: {stop_loss}")
            log(f"SL distance: {sl_distance}")
            log(f"Contract size: {contract_size}")
            log(f"Max allowed: {max_allowed}")

            if sl_distance <= 0:
                log(f"Invalid SL distance: {sl_distance}")
                return 0.0

            quantity = amount_to_risk / (sl_distance * contract_size)
            quantity = round(min(quantity, max_allowed), 2)
            log(f"Calculated quantity: {quantity}")

            return quantity

        except Exception as e:
            log(f"Error in _calculate_position_quantity for {symbol}: {e}")
            traceback.print_exc()
            return 0.0

---

### KEY DEPENDENCY SNIPPETS (Surgically Extracted for Clarity)

No direct dependencies were found for the requested symbols. The implementation appears to be self-contained.

---

INSTRUCTIONS FOR THIS TURN:
You requested additional context for: "Need to analyze implementation details of these critical functions to answer user's questions about their behavior and dependencies"
The surgically extracted implementations are provided above, along with surgical snippets of their key direct dependencies.

FOCUS ON CURRENT QUERY ONLY:
1. ONLY answer the CURRENT USER QUERY highlighted at the top of this message: "and how does the position quantity calculator function work?"
2. IGNORE any previous queries from conversation history - they are provided only for context
3. DO NOT answer questions from previous turns in the conversation
4. If you're unsure which query to answer, ALWAYS prioritize the one marked as "CURRENT USER QUERY"
5. ONLY address the specific function/symbol mentioned in the current query
6. DO NOT assume the user is asking about multiple functions unless explicitly stated
7. If the user previously asked about function X and now asks about function Y, ONLY answer about function Y

CRITICAL INSTRUCTIONS:
1. The code context above ALREADY CONTAINS the complete implementation you need to answer the query.
2. DO NOT overthink this - the surgical extraction system has ALREADY found the right file and extracted the code.
3. DO NOT try to request any additional files or context - everything you need is already provided.
4. IMMEDIATELY proceed to answering the user's question using ONLY the code context provided.
5. This approach works for ~90% of code-related queries, including questions about:
   - How code works
   - Implementation details
   - Function parameters
   - Return values
   - Error handling
   - Dependencies
   - Usage patterns
   - Code structure

ONLY IF ABSOLUTELY NECESSARY:
If critical information is clearly missing (undefined variables or methods), you may request additional context:
{CONTEXT_REQUEST: {"symbols_of_interest": [{"type": "method_definition", "name": "ExactSymbolName"}], "reason_for_request": "Brief reason"}}

REMEMBER: The surgical extraction system has already done the work of finding and extracting the relevant code for you. You do NOT need to figure out which file contains the code or request additional files.

NOTE: The extracted code appears to contain complete method implementations with all necessary details. You likely have all the information needed to answer the query without additional context requests.

NOTE: No functional dependencies were found for the requested symbols, which is expected for self-contained implementations. This is not an error.
--------------------------------------------------------------------------------
=== END OF COMPLETE MESSAGES ===
