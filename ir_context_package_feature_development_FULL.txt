# USER QUERY
I want to add a new code analysis feature. How should I integrate it with the existing system?

# INTELLIGENT CONTEXT ANALYSIS PACKAGE
# Generated by IR_CONTEXT_REQUEST system

## TASK INFORMATION
- User Query: I want to add a new code analysis feature. How should I integrate it with the existing system?
- Task Description: I want to add a new code analysis feature. How should I integrate it with the existing system?
- Task Type: feature_development

## CONTEXT SUMMARY
- Total Entities Selected: 26
- Total Tokens Used: 1991
- Critical Entities: 26
- High Priority Entities: 26
- Files Involved: 4
- Token Utilization: 99.6%

## SELECTION RATIONALE
Context Selection Rationale for feature_development:

Selected 28 entities using 1991 tokens (99.6% of budget).

Priority Distribution:
  - critical: 28 entities

Criticality Distribution:
  - low: 1 entities
  - medium: 23 entities
  - high: 4 entities

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for feature_development task requirements
- Maintained token budget constraints

## IR ANALYSIS DATA (26 entities)

### 1. find_relevant_code_for_feature (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.8123529411764707
- **Priority**: critical
- **Calls**: get_intelligent_context, analyze_with_multi_turn_reasoning, _format_results_by_mode, get
- **Used By**: test_intelligent_code_discovery, demo_intelligent_code_discovery, test_output_modes, demo_actual_output_examples, demo_output_modes
- **Side Effects**: network_io, writes_log, modifies_file, modifies_state
- **Potential Errors**: ValueError, ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 2. get_symbol_references_between_files (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.7600000000000002
- **Priority**: critical
- **Calls**: get_symbols_defined_in_file, _get_repo_map, _normalize_path, relpath, get_tags, _get_module_path, append, abspath, exists, open, read, replace, basename, dirname, split
- **Used By**: aider_integration_service, surgical_context_extractor
- **Side Effects**: network_io, writes_log, modifies_file, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, PermissionError, ImportError, FileNotFoundError, TypeError, IndexError

### 3. process_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.7000000000000006
- **Priority**: critical
- **Calls**: process_context_request, get, render_augmented_prompt
- **Used By**: test_complete_function_extraction, test_partial_context_request, test_surgical_integration, test_full_aider_integration, test_context_request_end_to_end, test_partial_success_fix, test_context_request, test_no_dependencies, test_surgical_extraction_integration, test_context_request_with_repo_map
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 4. detect_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6800000000000006
- **Priority**: critical
- **Calls**: parse_context_request, join
- **Used By**: context_request_demo, test_context_request_integration, test_context_request, test_aider_context_request, test_context_request_hang, test_repo_map_compatibility, test_full_aider_integration, base_coder, base_coder_old
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 5. update_conversation_history (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6600000000000006
- **Priority**: critical
- **Calls**: get, append
- **Used By**: test_conversation_history_fix, test_context_request_integration, test_context_request_fix, test_context_request_hang, test_full_aider_integration, test_aider_context_request_integration, test_repo_map_compatibility
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 6. analyze_with_multi_turn_reasoning (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.572352941176471
- **Priority**: critical
- **Calls**: _get_iterative_engine, analyze_incrementally
- **Used By**: test_iaa_protocol, test_context_bundle_builder, aider_integration_service
- **Side Effects**: network_io, writes_log, modifies_container, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 7. get_files_that_import (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5666666666666673
- **Priority**: critical
- **Calls**: get_files_that_import
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: TypeError, KeyError, IndexError, AttributeError, ImportError

### 8. _get_repo_map (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5600000000000005
- **Priority**: critical
- **Calls**: _normalize_path, Model, InputOutput, RepoMap
- **Used By**: aider_integration_service, surgical_file_extractor
- **Side Effects**: network_io, writes_log, modifies_container, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 9. _get_context_selector (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5600000000000005
- **Priority**: critical
- **Calls**: generate_mid_level_ir, IntelligentContextSelector
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: writes_log, modifies_file, modifies_state
- **Potential Errors**: ValueError, TypeError, KeyError, AttributeError, ImportError

### 10. AiderContextRequestIntegration (class)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5533333333333337
- **Priority**: critical
- **Calls**: None
- **Used By**: test_complete_function_extraction, test_surgical_integration, test_full_aider_integration, test_partial_success_fix, test_conversation_history_fix, test_context_request, test_surgical_extraction_integration, test_context_request_availability, test_context_request_with_repo_map, test_context_request_hang
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 11. AiderIntegrationService (class)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5533333333333337
- **Priority**: critical
- **Calls**: None
- **Used By**: test_surgical_context_extractor, test_context_request_end_to_end, test_integration, surgical_extraction_demo, test_modular_pipeline, test_intelligent_code_discovery, test_mid_level_ir, test_context_request, test_context_request_root_fix, test_context_bundle_builder
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 12. _get_ir_generator (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5466666666666673
- **Priority**: critical
- **Calls**: MidLevelIRPipeline, MidLevelIRGenerator
- **Used By**: test_integration, generate_full_ir, test_mid_level_ir, aider_integration_service
- **Side Effects**: writes_log, modifies_state
- **Potential Errors**: ValueError, TypeError, KeyError, IndexError, AttributeError, ImportError

### 13. calculate_entity_confidence (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5452941176470594
- **Priority**: critical
- **Calls**: get
- **Used By**: test_iaa_protocol, iterative_analysis_engine
- **Side Effects**: network_io, modifies_container, modifies_state
- **Potential Errors**: ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 14. select_intelligent_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5400000000000005
- **Priority**: critical
- **Calls**: _get_context_selector, get, lower, select_optimal_context, analyze_context_quality, append, sort
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 15. get_context_request_summary (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.5333333333333337
- **Priority**: critical
- **Calls**: join
- **Used By**: context_request_demo, test_context_request_integration, test_context_request, test_aider_context_request_integration, test_context_request_hang, test_repo_map_compatibility, test_full_aider_integration, test_context_request_availability, base_coder, base_coder_old
- **Side Effects**: none
- **Potential Errors**: TypeError, KeyError, IndexError, AttributeError

### 16. get_files_imported_by (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get_files_imported_by
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: TypeError, KeyError, IndexError, AttributeError

### 17. get_symbols_defined_in_file (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get_symbols_defined_in_file
- **Used By**: aider_integration_service, surgical_context_extractor
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: TypeError, KeyError, IndexError, AttributeError

### 18. generate_mid_level_ir (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: _get_ir_generator, generate_ir, generate_mid_level_ir
- **Used By**: test_modular_pipeline, generate_full_ir, intelligent_context_selector, test_integration, aider_integration_service
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: AttributeError, TypeError

### 19. _generate_recommendations (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get, strip, append, extend
- **Used By**: aider_integration_service, code_generation_pipeline
- **Side Effects**: network_io, modifies_global
- **Potential Errors**: KeyError, AttributeError, ImportError, TypeError, IndexError

### 20. _update_entity_usage_simple (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: get, append
- **Used By**: aider_integration_service
- **Side Effects**: network_io, writes_log, modifies_container
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 21. _detect_architectural_layers (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: lower, get, append, items
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 22. generate_system_overview_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, items, _sanitize_name, get, defaultdict, _get_module_group, _is_internal_module, add, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, ZeroDivisionError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 23. generate_dependency_graph_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, get, items, _sanitize_name, next, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, writes_log, modifies_container, modifies_state
- **Potential Errors**: ValueError, ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 24. generate_component_architecture_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, _sanitize_name, title, get, defaultdict, items, _is_internal_module, _get_module_layer, add, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, ZeroDivisionError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 25. generate_critical_path_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, _sanitize_name, get, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 26. match (variable)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.3933333333333335
- **Priority**: critical
- **Calls**: None
- **Used By**: benchmark, simple_enhanced_test, recording_audio, clean_metadata, versionbump, simple_extraction_test, surgical_context_extractor, help, enhanced_surgical_extractor, aider_integration_service
- **Side Effects**: none
- **Potential Errors**: RuntimeError

## SOURCE CODE IMPLEMENTATIONS (25 implementations)

### 1. find_relevant_code_for_feature
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.8123529411764707

```python
    def find_relevant_code_for_feature(self, project_path: str,
                                     feature_description: str,
                                     focus_areas: list = None,
                                     output_mode: str = "concise") -> dict:
        """
        Find relevant code for a new feature with user-friendly output.

        This is the main entry point for the Intelligent Code Discovery feature.
        It combines intelligent context selection and multi-turn reasoning to provide
        organized, actionable results for feature development.

        Args:
            project_path: Path to the project
            feature_description: Natural language description of the feature
            focus_areas: Key areas/keywords to focus on (optional)
            output_mode: Output verbosity mode:
                - "concise": Essential info only (default)
                - "detailed": Full analysis results
                - "llm_friendly": Optimized for LLM consumption
                - "summary": High-level overview only

        Returns:
            Dictionary with organized, actionable results. Content varies by output_mode:
            - critical_entities: High-risk entities that need careful handling
            - related_entities: Relevant entities that may need modification
            - safe_entities: Safe integration points to start with
            - implementation_guidance: Step-by-step guidance
            - dependency_map: Relationship mapping (detailed/llm_friendly only)
            - recommendations: Actionable recommendations
        """
        try:
            print(f"🔍 Starting Intelligent Code Discovery")
            print(f"   Feature: {feature_description}")
            print(f"   Focus areas: {focus_areas}")

            # Use existing intelligent context selection
            context_result = self.get_intelligent_context(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas
            )

            # Check if context selection was successful
            if 'error' in context_result:
                return {
                    'error': f"Context selection failed: {context_result['error']}",
                    'fallback': 'Use traditional code exploration methods'
                }

            # Use existing multi-turn reasoning for deeper analysis
            analysis_results = self.analyze_with_multi_turn_reasoning(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas,
                max_iterations=3
            )

            # Check if analysis was successful
            if 'error' in analysis_results:
                print(f"⚠️ Multi-turn analysis failed, using single-turn results")
                analysis_results = {'global_insights': [], 'overall_confidence': 0.5}

            # Format into user-friendly structure based on output mode
            result = self._format_results_by_mode(
                context_result, analysis_results, feature_description,
                focus_areas, output_mode
            )

            print(f"✅ Code Discovery Complete")
            # Handle different output modes for logging
            if output_mode == "summary":
                print(f"   Critical: {result.get('critical_count', 0)}")
                print(f"   Safe: {result.get('safe_count', 0)}")
                print(f"   Related: {result.get('related_count', 0)}")
            else:
                print(f"   Critical entities: {len(result.get('critical_entities', []))}")
                print(f"   Safe entities: {len(result.get('safe_entities', []))}")
                print(f"   Related entities: {len(result.get('related_entities', []))}")

            return result

        except Exception as e:
            print(f"⚠️ Error in intelligent code discovery: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional code exploration methods'
            }

```

### 2. get_symbol_references_between_files
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.7600000000000002

```python
    def get_symbol_references_between_files(self, project_path: str, source_file: str, target_file: str, model_name: str = "gpt-3.5-turbo") -> Dict[str, List[str]]:
        """
        Get detailed information about symbols referenced between two files.

        Args:
            project_path: Path to the project root
            source_file: Path to the source file that references symbols
            target_file: Path to the target file that defines symbols
            model_name: Name of the model to use

        Returns:
            Dictionary with symbol types as keys and lists of referenced symbol names as values
        """
        # Get symbols defined in the target file
        target_symbols = self.get_symbols_defined_in_file(project_path, target_file, model_name)

        # Get all symbols in the source file
        repo_map = self.project_manager._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize file paths
            source_file = self.project_manager._normalize_path(source_file)
            target_file = self.project_manager._normalize_path(target_file)

            source_rel_path = os.path.relpath(source_file, project_path)
            target_rel_path = os.path.relpath(target_file, project_path)

            # Get tags for the source file
            source_tags = list(repo_map.get_tags(source_file, source_rel_path))

            # Find references to symbols defined in the target file
            referenced_symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            # Check for direct imports
            for tag in source_tags:
                if tag.kind == 'import':
                    # Check if this import references the target file
                    target_module = self.project_manager._get_module_path(target_rel_path)
                    if target_module in tag.name:
                        referenced_symbols['imports'].append(tag.name)

            # Check for references to symbols defined in the target file
            for tag in source_tags:
                if tag.kind == 'ref':
                    # Check if this reference is to a symbol defined in the target file
                    if tag.name in target_symbols['functions']:
                        if tag.name not in referenced_symbols['functions']:
                            referenced_symbols['functions'].append(tag.name)
                    elif tag.name in target_symbols['classes']:
                        if tag.name not in referenced_symbols['classes']:
                            referenced_symbols['classes'].append(tag.name)
                    elif tag.name in target_symbols['variables']:
                        if tag.name not in referenced_symbols['variables']:
                            referenced_symbols['variables'].append(tag.name)

            # If we have access to the source file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(source_file)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Check for imports from the target file
                    target_basename = os.path.basename(target_file).replace('.py', '')
                    target_dirname = os.path.basename(os.path.dirname(target_file))

                    import_patterns = [
                        rf'from\s+{target_dirname}\.{target_basename}\s+import\s+(.+)',
                        rf'from\s+{target_basename}\s+import\s+(.+)',
                        rf'import\s+{target_dirname}\.{target_basename}',
                        rf'import\s+{target_basename}'
                    ]

                    for pattern in import_patterns:
                        for line in file_content.split('\n'):
                            match = re.search(pattern, line.strip())
                            if match and match.group(1):
                                imports = [imp.strip() for imp in match.group(1).split(',')]
                                for imp in imports:
                                    if imp in target_symbols['functions'] and imp not in referenced_symbols['functions']:
                                        referenced_symbols['functions'].append(imp)
                                    elif imp in target_symbols['classes'] and imp not in referenced_symbols['classes']:
                                        referenced_symbols['classes'].append(imp)
                                    elif imp in target_symbols['variables'] and imp not in referenced_symbols['variables']:
                                        referenced_symbols['variables'].append(imp)
            except Exception as e:
                print(f"Error extracting additional references from file: {e}")

            return referenced_symbols
        except Exception as e:
            print(f"Error getting symbol references between files: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}


if __name__ == "__main__":
    # Example usage
    service = AiderIntegrationService()
    project_path = "aider-main"

    print("\n=== DEPENDENCY ANALYSIS DEMO ===")
    print("\n1. Basic File Dependencies")

    # Get top central files
    top_files = service.get_top_central_files(project_path, count=10)
    print("\nTop Central Files:")
    for file in top_files:
        print(f"- {file['file']}: referenced by {file['references']} files")

    # Get files that import a specific file
    target_file = "aider-main/aider/repomap.py"
    importing_files = service.get_files_that_import(project_path, target_file)
    print(f"\nFiles that import {target_file}:")
    for file in importing_files:
        print(f"- {file}")

    # Get files imported by a specific file
    imported_files = service.get_files_imported_by(project_path, target_file)
    print(f"\nFiles imported by {target_file}:")
    for file in imported_files:
        print(f"- {file}")

    print("\n2. Class Inheritance Analysis")

    # Get base classes of a specific class
    class_name = "RepoMap"
    base_classes = service.get_base_classes_of(project_path, class_name)
    print(f"\nBase classes of {class_name}:")
    if base_classes:
        for base_class in base_classes:
            print(f"- {base_class['class_name']} in {base_class['file_path']} ({base_class['module_path']})")
    else:
        print("- No base classes found")

    # Get derived classes of a specific class
    class_name = "Coder"
    derived_classes = service.get_derived_classes_of(project_path, class_name)
    print(f"\nDerived classes of {class_name}:")
    for derived_class in derived_classes:
        print(f"- {derived_class['class_name']} in {derived_class['file_path']} ({derived_class['module_path']})")

    print("\n3. Symbol Analysis")

    # Get symbols defined in a file
    file_path = "aider-main/aider/repomap.py"
    symbols = service.get_symbols_defined_in_file(project_path, file_path)
    print(f"\nSymbols defined in {file_path}:")
    for symbol_type, symbol_list in symbols.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list[:5]:  # Show only first 5 symbols of each type
                print(f"    - {symbol}")
            if len(symbol_list) > 5:
                print(f"    - ... and {len(symbol_list) - 5} more")

    # Find file defining a symbol
    symbol_name = "RepoMap"
    defining_file = service.find_file_defining_symbol(project_path, symbol_name)
    print(f"\nFile defining symbol '{symbol_name}':")
    print(f"- {defining_file if defining_file else 'Not found'}")

    print("\n4. Detailed Symbol References (New Feature)")

    # Get symbol references between files
    source_file = "aider-main/aider/coders/base_coder.py"
    target_file = "aider-main/aider/repomap.py"

    print(f"\nSymbols from {target_file} referenced in {source_file}:")
    symbol_refs = service.get_symbol_references_between_files(project_path, source_file, target_file)

    for symbol_type, symbol_list in symbol_refs.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list:
                print(f"    - {symbol}")

    print("\n5. Surgical Context Extraction (New Feature)")

    # Get contextual dependencies for a file
    primary_file = "aider-main/aider/coders/base_coder.py"
    print(f"\nContextual dependencies for {primary_file}:")

    try:
        context_map = service.get_contextual_dependencies(project_path, primary_file, max_snippets=6)

        print(f"\nPrimary file: {context_map['primary_file']}")
        print(f"Related files: {', '.join(context_map['related_files'][:3]) if context_map['related_files'] else 'None'}...")

        # Show usage contexts
        print("\nUsage contexts:")
        for i, usage_ctx in enumerate(context_map['usage_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {usage_ctx['symbol_name']} ({usage_ctx['usage_type']})")
            print(f"     File: {usage_ctx['file_path']}, Lines: {usage_ctx['start_line']}-{usage_ctx['end_line']}")
            print(f"     Surrounding function: {usage_ctx['surrounding_function'] or 'N/A'}")

            # Show a snippet of the content (first 3 lines)
            content_lines = usage_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show definition contexts
        print("\nDefinition contexts:")
        for i, def_ctx in enumerate(context_map['definition_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {def_ctx['symbol_name']} ({def_ctx['definition_type']})")
            print(f"     File: {def_ctx['file_path']}, Lines: {def_ctx['start_line']}-{def_ctx['end_line']}")
            if def_ctx['signature']:
                print(f"     Signature: {def_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = def_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating contextual dependencies: {e}")

    # Get focused inheritance context
    class_name = "Coder"
    file_path = "aider-main/aider/coders/base_coder.py"
    print(f"\nFocused inheritance context for {class_name}:")

    try:
        inheritance_ctx = service.get_focused_inheritance_context(project_path, class_name, file_path)

        # Show base classes
        print("\nBase classes:")
        for i, base_ctx in enumerate(inheritance_ctx['base_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {base_ctx['symbol_name']}")
            print(f"     File: {base_ctx['file_path']}, Lines: {base_ctx['start_line']}-{base_ctx['end_line']}")
            if base_ctx['signature']:
                print(f"     Signature: {base_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = base_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show derived classes
        print("\nDerived classes:")
        for i, derived_ctx in enumerate(inheritance_ctx['derived_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {derived_ctx['symbol_name']}")
            print(f"     File: {derived_ctx['file_path']}, Lines: {derived_ctx['start_line']}-{derived_ctx['end_line']}")

            # Show a snippet of the content (first 3 lines)
            content_lines = derived_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating inheritance context: {e}")

    print("\nSurgical Context Extraction successfully implemented!")

    print("\n6. Intelligent Context Selection (Phase 2 Feature)")

    # Test the new intelligent context selection
    print("\nTesting Intelligent Context Selection...")

    try:
        # Test different task scenarios
        test_scenarios = [
            {
                'task': 'Fix a bug in the file processing logic',
                'type': 'debugging',
                'focus': ['file', 'process']
            },
            {
                'task': 'Add a new feature for dependency analysis',
                'type': 'feature_development',
                'focus': ['dependency', 'analysis']
            }
        ]

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📋 Scenario {i}: {scenario['task']}")

            context_result = service.select_intelligent_context(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['type'],
                focus_entities=scenario['focus'],
                max_tokens=2000  # Smaller budget for demo
            )

            if 'error' not in context_result:
                print(f"✅ Context selected successfully!")
                print(f"   Selected entities: {context_result['total_entities']}")
                print(f"   Token utilization: {context_result['quality_metrics']['token_utilization']:.1f}%")
                print(f"   Average relevance: {context_result['quality_metrics']['average_relevance_score']:.2f}")

                # Show top 3 entities
                top_entities = context_result['entities'][:3]
                print(f"   Top entities:")
                for j, entity in enumerate(top_entities, 1):
                    print(f"     {j}. {entity['module_name']}.{entity['entity_name']} "
                          f"(score: {entity['relevance_score']:.2f})")
            else:
                print(f"⚠️ Context selection failed: {context_result['error']}")

        print("\n✅ Intelligent Context Selection successfully demonstrated!")

    except Exception as e:
        print(f"⚠️ Intelligent Context Selection test failed: {e}")

    print("\n7. Mid-Level IR Generation (Foundation Feature)")

    # Generate Mid-Level IR for a subset of files (to avoid overwhelming output)
    print("\nGenerating Mid-Level IR for the project...")

    try:
        # Generate the complete IR
        ir_data = service.generate_mid_level_ir(project_path)

        print(f"\n✅ Mid-Level IR generated successfully!")
        print(f"   Total modules analyzed: {ir_data['metadata']['total_modules']}")
        print(f"   Generated at: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ir_data['metadata']['generated_at']))}")

        # Show a sample of the IR structure
        print("\n📋 Sample IR Structure (first 2 modules):")

        sample_modules = ir_data['modules'][:2]  # Show first 2 modules

        for i, module in enumerate(sample_modules, 1):
            print(f"\n  Module {i}: {module['name']}")
            print(f"    File: {module['file']}")
            print(f"    Lines of Code: {module['loc']}")
            print(f"    Dependencies: {len(module['dependencies'])}")
            print(f"    Entities: {len(module['entities'])}")

            # Show dependencies
            if module['dependencies']:
                print("    Dependencies:")
                for dep in module['dependencies'][:3]:  # Show first 3 dependencies
                    print(f"      - {dep['module']} (strength: {dep['strength']})")
                if len(module['dependencies']) > 3:
                    print(f"      - ... and {len(module['dependencies']) - 3} more")

            # Show entities
            if module['entities']:
                print("    Entities:")
                for entity in module['entities'][:2]:  # Show first 2 entities
                    print(f"      - {entity['type']}: {entity['name']}")
                    print(f"        Doc: {entity['doc'][:50]}...")
                    print(f"        Params: {entity['params']}")
                    print(f"        Returns: {entity['returns']}")
                    print(f"        Calls: {entity['calls'][:3]}")  # Show first 3 calls
                    print(f"        Side effects: {entity['side_effects']}")
                    print(f"        Criticality: {entity['criticality']}")
                    print(f"        Change risk: {entity['change_risk']}")
                if len(module['entities']) > 2:
                    print(f"      - ... and {len(module['entities']) - 2} more entities")

        # Save the IR to a file for inspection
        output_file = "mid_level_ir_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Complete Mid-Level IR saved to: {output_file}")
        print("   You can inspect the full structure in this file.")

        # Show some statistics
        total_entities = sum(len(module['entities']) for module in ir_data['modules'])
        total_dependencies = sum(len(module['dependencies']) for module in ir_data['modules'])

        print(f"\n📊 IR Statistics:")
        print(f"   Total modules: {len(ir_data['modules'])}")
        print(f"   Total entities: {total_entities}")
        print(f"   Total dependencies: {total_dependencies}")

        # Show criticality distribution
        criticality_counts = {'high': 0, 'medium': 0, 'low': 0}
        for module in ir_data['modules']:
            for entity in module['entities']:
                criticality = entity.get('criticality', 'medium')
                criticality_counts[criticality] = criticality_counts.get(criticality, 0) + 1

        print(f"   Criticality distribution:")
        print(f"     High: {criticality_counts['high']}")
        print(f"     Medium: {criticality_counts['medium']}")
        print(f"     Low: {criticality_counts['low']}")

    except Exception as e:
        print(f"❌ Error generating Mid-Level IR: {e}")
        import traceback
        traceback.print_exc()

    print("\n7. Cache Management")
    print("\nClearing cache...")
    service.clear_cache()
    print("Cache cleared successfully.")

    print("\nSetting cache TTL to 1 hour...")
    service.set_cache_ttl(3600)
    print("Cache TTL set successfully.")
```

### 3. process_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.7000000000000006

```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

```

### 4. detect_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.6800000000000006

```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

```

### 5. update_conversation_history
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.6600000000000006

```python
    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

```

### 6. analyze_with_multi_turn_reasoning
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.572352941176471

```python
    def analyze_with_multi_turn_reasoning(self, project_path: str, task_description: str,
                                        task_type: str = "general_analysis",
                                        focus_entities: list = None, max_iterations: int = 5,
                                        max_tokens: int = 8000):
        """
        Perform multi-turn reasoning analysis using the IAA Protocol.

        This method implements the Iterative Analysis Accumulation protocol that builds
        understanding across multiple iterations, using intelligent context selection
        and analysis memory to progressively improve understanding.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the analysis task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_iterations: Maximum number of analysis iterations
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing comprehensive multi-turn analysis results
        """
        try:
            print(f"🧠 Starting Multi-Turn Reasoning Analysis")
            print(f"   Task: {task_description}")
            print(f"   Type: {task_type}")
            print(f"   Max iterations: {max_iterations}")

            # Get the iterative analysis engine
            engine = self._get_iterative_engine(project_path, max_tokens)

            if engine is None:
                return {
                    'error': 'Iterative Analysis Engine not available',
                    'fallback': 'Use single-turn intelligent context selection'
                }

            # Update max iterations if specified
            if max_iterations != engine.max_iterations:
                engine.max_iterations = max_iterations

            # Perform iterative analysis
            analysis_results = engine.analyze_incrementally(
                task=task_description,
                task_type=task_type,
                focus_entities=focus_entities
            )

            # Add metadata about the analysis
            analysis_results['analysis_metadata'] = {
                'project_path': project_path,
                'max_tokens': max_tokens,
                'engine_version': 'IAA Protocol v1.0',
                'analysis_type': 'multi_turn_reasoning'
            }

            print(f"✅ Multi-Turn Analysis Complete")
            print(f"   Iterations: {analysis_results['total_iterations']}")
            print(f"   Confidence: {analysis_results['overall_confidence']:.2f}")
            print(f"   Entities: {len(analysis_results['entity_summaries'])}")

            return analysis_results

        except Exception as e:
            print(f"⚠️ Error in multi-turn reasoning analysis: {e}")
            return {
                'error': str(e),
                'fallback': 'Use single-turn intelligent context selection'
            }

```

### 7. get_files_that_import
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5666666666666673

```python
    def get_files_that_import(self, project_path: str, model_name: str, target_file_path: str) -> List[str]:
        """
        Get a list of files that import (reference) the target file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            target_file_path: Path to the target file

        Returns:
            A list of file paths that import the target file
        """
        # Try to use RepoMap for more accurate results
        repo_map = self._get_repo_map(project_path, model_name)
        if repo_map:
            try:
                # Normalize the target file path
                target_file_path = self._normalize_path(target_file_path)
                rel_path = os.path.relpath(target_file_path, project_path)

                # Get all Python files in the project
                python_files = []
                for root, _, files in os.walk(project_path):
                    for file in files:
                        if file.endswith('.py'):
                            python_files.append(os.path.join(root, file))

                # Find files that import the target file
                importing_files = []

                # Get tags for the target file to find defined symbols
                target_tags = list(repo_map.get_tags(target_file_path, rel_path))
                defined_symbols = [tag.name for tag in target_tags if tag.kind == 'def' or tag.kind == 'class']

                # Check each file for references to the defined symbols
                for file_path in python_files:
                    if file_path == target_file_path:
                        continue

                    file_rel_path = os.path.relpath(file_path, project_path)
                    file_tags = list(repo_map.get_tags(file_path, file_rel_path))

                    # Check if any of the defined symbols are referenced in this file
                    for tag in file_tags:
                        if tag.kind == 'ref' and tag.name in defined_symbols:
                            importing_files.append(file_rel_path)
                            break

                return importing_files
            except Exception as e:
                print(f"Error using RepoMap for get_files_that_import: {e}")
                # Fall back to static analysis

        # Fall back to static analysis if RepoMap is not available or fails
        if not self.dependency_data:
            return []

        # Get the dependencies from the dependency data
        dependencies = self.dependency_data["dependencies"]

        # Find files that import the target file
        importing_files = []
        target_file_rel = os.path.basename(target_file_path)

        for file, deps in dependencies.items():
            for dep_file, _ in deps:
                if dep_file == target_file_rel or dep_file.endswith('/' + target_file_rel):
                    importing_files.append(file)

        return importing_files

```

### 8. _get_repo_map
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5600000000000005

```python
    def _get_repo_map(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Optional[Any]:
        """
        Get or create a RepoMap instance for the given project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use

        Returns:
            RepoMap instance or None if RepoMap is not available
        """
        if not REPOMAP_AVAILABLE:
            return None

        # Normalize the project path
        project_path = self._normalize_path(project_path)

        # Check if we already have a RepoMap for this project
        if project_path in self.repo_maps:
            return self.repo_maps[project_path]

        try:
            # Create a simple model instance for token counting
            model = Model(model_name)

            # Create an InputOutput instance
            io = InputOutput()

            # Initialize the RepoMap with a higher token limit for better coverage
            repo_map = RepoMap(
                map_tokens=8192,  # Increased token limit for better coverage
                root=project_path,
                main_model=model,
                io=io,
                repo_content_prefix="# Repository Map\n\nThis map shows the structure and dependencies of the codebase:\n\n",
                verbose=False
            )

            self.repo_maps[project_path] = repo_map
            return repo_map
        except Exception as e:
            print(f"Error creating RepoMap: {e}")
            return None

```

### 9. _get_context_selector
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5600000000000005

```python
    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

```

### 10. AiderContextRequestIntegration
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.5533333333333337

```python
class AiderContextRequestIntegration:
    """
    Integrates the context request handler with the Aider system.
    """

```

### 11. AiderIntegrationService
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5533333333333337

```python
class AiderIntegrationService:
    """
    Service for integrating with Aider and exposing its functionality.

    This service provides methods to analyze and understand the dependencies
    between files and classes in a codebase. It uses Aider's RepoMap for
    dynamic analysis when available, and falls back to static analysis
    when necessary.

    It also provides surgical context extraction capabilities to extract
    focused code snippets around dependency interaction points.

    Enhanced with Multi-Turn Reasoning Loop (IAA Protocol) for complex
    iterative analysis workflows.
    """

```

### 12. _get_ir_generator
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5466666666666673

```python
    def _get_ir_generator(self):
        """Get the Mid-Level IR generator, initializing it if necessary."""
        if self.ir_generator is None:
            # Try to use the new modular pipeline first
            try:
                from mid_level_ir import MidLevelIRPipeline

                # Configure the modular pipeline
                config = {
                    'verbose': False,  # Keep quiet for integration
                    'file_scanner': {
                        'exclude_dirs': ['__pycache__', '.git', '.pytest_cache', 'node_modules'],
                        'max_file_size_mb': 10
                    },
                    'entity_extractor': {
                        'extract_variables': True,
                        'extract_constants': True,
                        'min_function_lines': 1
                    },
                    'call_graph_builder': {
                        'max_calls_per_entity': 15,
                        'include_builtin_calls': False
                    },
                    'dependency_analyzer': {
                        'include_stdlib': False,
                        'include_external': True
                    },
                    'ir_builder': {
                        'include_metadata': True,
                        'include_ast_info': False,  # Keep output size manageable
                        'pretty_print': True
                    }
                }

                self.ir_generator = MidLevelIRPipeline(config)
                print("✅ Using enhanced modular Mid-Level IR pipeline")

            except ImportError:
                # Fall back to the original implementation
                self.ir_generator = MidLevelIRGenerator(self.project_manager)
                print("⚠️  Using legacy Mid-Level IR generator (modular pipeline not available)")

        return self.ir_generator

```

### 13. calculate_entity_confidence
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.5452941176470594

```python
    def calculate_entity_confidence(self, entity_id: str, analysis_data: Dict[str, Any]) -> float:
        """
        Calculate confidence score for an entity based on multiple factors.
        
        Args:
            entity_id: The entity being analyzed
            analysis_data: Data about the entity and its analysis
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        factors = {}
        
        # Factor 1: Documentation completeness
        doc_coverage = analysis_data.get('doc_coverage', 0.0)
        factors['documentation'] = min(doc_coverage, 1.0)
        
        # Factor 2: Code complexity (inverse relationship)
        complexity = analysis_data.get('complexity', 5)
        factors['complexity'] = max(0.0, 1.0 - (complexity - 5) / 10.0)
        
        # Factor 3: Dependency clarity
        dependency_count = len(analysis_data.get('dependencies', []))
        factors['dependencies'] = 1.0 if dependency_count < 5 else max(0.3, 1.0 - dependency_count / 20.0)
        
        # Factor 4: Analysis depth
        insights_count = len(analysis_data.get('insights', []))
        factors['analysis_depth'] = min(insights_count / 3.0, 1.0)
        
        # Factor 5: Issue resolution
        open_issues = len(analysis_data.get('open_issues', []))
        factors['issue_resolution'] = max(0.0, 1.0 - open_issues / 5.0)
        
        # Weighted average
        weights = {
            'documentation': 0.2,
            'complexity': 0.25,
            'dependencies': 0.2,
            'analysis_depth': 0.2,
            'issue_resolution': 0.15
        }
        
        confidence = sum(factors[factor] * weights[factor] for factor in factors)

        # Ensure confidence is within valid range [0.0, 1.0]
        confidence = max(0.0, min(1.0, confidence))

        # Store factors for debugging
        self.confidence_factors[entity_id] = factors
        self.entity_confidences[entity_id] = confidence

        return confidence
    
```

### 14. select_intelligent_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5400000000000005

```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

```

### 15. get_context_request_summary
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.5333333333333337

```python
    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request for logging purposes.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"
```

### 16. get_files_imported_by
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def get_files_imported_by(self, project_path: str, target_file_path: str, model_name: str = "gpt-3.5-turbo") -> List[str]:
        """
        Get a list of files that are imported by the target file.

        Args:
            project_path: Path to the project root
            target_file_path: Path to the target file
            model_name: Name of the model to use

        Returns:
            A list of file paths that are imported by the target file
        """
        return self.project_manager.get_files_imported_by(project_path, model_name, target_file_path)

```

### 17. get_symbols_defined_in_file
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def get_symbols_defined_in_file(self, project_path: str, model_name: str, file_path: str) -> Dict[str, List[str]]:
        """
        Get all symbols (functions, classes, variables) defined in a file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            file_path: Path to the file to analyze

        Returns:
            Dictionary with symbol types as keys and lists of symbol names as values
        """
        # Check if we have this information in the cache
        cache_key = (project_path, file_path, 'symbols')
        if cache_key in self.symbol_cache and self._is_cache_valid(cache_key):
            return self.symbol_cache[cache_key]

        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize the file path
            file_path = self._normalize_path(file_path)
            rel_path = os.path.relpath(file_path, project_path)

            # Get tags for the file
            tags = list(repo_map.get_tags(file_path, rel_path))

            # Categorize symbols by type
            symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            for tag in tags:
                if tag.kind == 'def':
                    if tag.name.startswith('class '):
                        class_name = tag.name.split(' ')[1]
                        if class_name not in symbols['classes']:
                            symbols['classes'].append(class_name)
                    elif tag.name.startswith('def '):
                        func_name = tag.name.split(' ')[1]
                        if func_name not in symbols['functions']:
                            symbols['functions'].append(func_name)
                    else:
                        if tag.name not in symbols['functions']:
                            symbols['functions'].append(tag.name)
                elif tag.kind == 'class':
                    if tag.name not in symbols['classes']:
                        symbols['classes'].append(tag.name)
                elif tag.kind == 'variable':
                    if tag.name not in symbols['variables']:
                        symbols['variables'].append(tag.name)
                elif tag.kind == 'import':
                    if tag.name not in symbols['imports']:
                        symbols['imports'].append(tag.name)

            # If we have access to the file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(file_path)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Extract imports
                    import_pattern = r'^(?:from\s+(\S+)\s+import\s+(.+)|import\s+(.+))$'
                    for line in file_content.split('\n'):
                        match = re.match(import_pattern, line.strip())
                        if match:
                            if match.group(1) and match.group(2):  # from X import Y
                                module = match.group(1)
                                imports = [imp.strip() for imp in match.group(2).split(',')]
                                for imp in imports:
                                    import_str = f"{module}.{imp}"
                                    if import_str not in symbols['imports']:
                                        symbols['imports'].append(import_str)
                            elif match.group(3):  # import X
                                imports = [imp.strip() for imp in match.group(3).split(',')]
                                for imp in imports:
                                    if imp not in symbols['imports']:
                                        symbols['imports'].append(imp)
            except Exception as e:
                print(f"Error extracting additional symbols from file: {e}")

            # Update the cache
            self.symbol_cache[cache_key] = symbols
            self._update_cache_timestamp(cache_key)

            return symbols
        except Exception as e:
            print(f"Error getting symbols from file: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}

```

### 18. generate_mid_level_ir
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def generate_mid_level_ir(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Dict:
        """
        Generate Mid-Level Intermediate Representation for the project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use for analysis

        Returns:
            Dictionary containing the Mid-Level IR structure
        """
        generator = self._get_ir_generator()

        # Check if we're using the new modular pipeline
        if hasattr(generator, 'generate_ir'):
            # New modular pipeline
            return generator.generate_ir(project_path)
        else:
            # Legacy generator
            return generator.generate_mid_level_ir(project_path, model_name)

```

### 19. _generate_recommendations
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def _generate_recommendations(self, analysis_results: dict, context_result: dict) -> list:
        """Generate actionable recommendations."""
        recommendations = []

        # Extract global insights from multi-turn analysis
        global_insights = analysis_results.get('global_insights', [])
        for insight in global_insights[:3]:  # Top 3 insights
            if isinstance(insight, str) and len(insight.strip()) > 0:
                recommendations.append(insight.strip())

        # Add confidence-based recommendations
        overall_confidence = analysis_results.get('overall_confidence', 0.0)
        if overall_confidence < 0.7:
            recommendations.append("Consider running additional analysis iterations for better confidence")

        # Add entity-specific recommendations
        total_entities = context_result.get('total_entities', 0)
        if total_entities > 30:
            recommendations.append("Large number of entities selected - consider breaking feature into smaller parts")
        elif total_entities < 5:
            recommendations.append("Few entities selected - verify feature scope is comprehensive")

        # Add quality-based recommendations
        quality_metrics = context_result.get('quality_metrics', {})
        token_utilization = quality_metrics.get('token_utilization', 0)
        if token_utilization < 50:
            recommendations.append("Low token utilization - consider expanding analysis scope")
        elif token_utilization > 95:
            recommendations.append("High token utilization - consider reducing scope or increasing token budget")

        # Add general best practices
        recommendations.extend([
            "Start with safe integration points to minimize risk",
            "Test critical entities thoroughly before modification",
            "Document changes to maintain code clarity"
        ])

        return recommendations[:10]  # Limit to top 10 recommendations

```

### 20. _update_entity_usage_simple
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def _update_entity_usage_simple(self, module: Dict, all_modules: List[Dict]) -> None:
        """Update entity usage with a simpler approach."""
        try:
            # For each entity in this module, find which other modules might use it
            for entity in module.get("entities", []):
                used_by = []

                # Check other modules for potential usage
                for other_module in all_modules:
                    if other_module["name"] == module["name"]:
                        continue

                    # Simple heuristic: if entity name appears in other module's entities' calls
                    for other_entity in other_module.get("entities", []):
                        if entity["name"] in other_entity.get("calls", []):
                            if other_module["name"] not in used_by:
                                used_by.append(other_module["name"])

                entity["used_by"] = used_by[:5]  # Limit to first 5

        except Exception as e:
            print(f"   Error updating simple entity usage for {module['name']}: {e}")


```

### 21. _detect_architectural_layers
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def _detect_architectural_layers(self):
        """Detect architectural layers based on naming patterns and dependencies."""
        print("🏗️  Detecting architectural layers...")
        
        layers = {
            'presentation': [],
            'service': [],
            'core': [],
            'data': [],
            'utility': [],
            'test': [],
            'script': []
        }
        
        for module in self.modules:
            module_name = module['name'].lower()
            file_path = module.get('file', '').lower()
            
            # Classify based on naming patterns
            if any(keyword in module_name for keyword in ['ui', 'view', 'template', 'render', 'display']):
                layers['presentation'].append(module)
            elif any(keyword in module_name for keyword in ['service', 'api', 'endpoint', 'handler']):
                layers['service'].append(module)
            elif any(keyword in module_name for keyword in ['core', 'main', 'engine', 'pipeline']):
                layers['core'].append(module)
            elif any(keyword in module_name for keyword in ['data', 'model', 'entity', 'storage', 'db']):
                layers['data'].append(module)
            elif any(keyword in module_name for keyword in ['util', 'helper', 'tool', 'common']):
                layers['utility'].append(module)
            elif any(keyword in file_path for keyword in ['test', 'spec']):
                layers['test'].append(module)
            elif any(keyword in file_path for keyword in ['script', 'example', 'demo']):
                layers['script'].append(module)
            else:
                # Default to core if unclear
                layers['core'].append(module)
        
        # Remove empty layers
        self.architectural_layers = {k: v for k, v in layers.items() if v}
        print(f"   Detected {len(self.architectural_layers)} architectural layers")
    
```

### 22. generate_system_overview_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_system_overview_diagram(self) -> str:
        """Generate a high-level system overview diagram."""
        print("📊 Generating system overview diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% System Overview - Top Level Architecture")
        mermaid.append("")

        # Add module groups as subgraphs
        for group_name, modules in self.module_groups.items():
            if len(modules) < 2:  # Skip single-module groups for clarity
                continue

            sanitized_group = self._sanitize_name(group_name)
            mermaid.append(f"    subgraph {sanitized_group}[{group_name}]")

            # Add top modules from this group (limit for readability)
            top_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:5]

            for module in top_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Color code by entity count
                if entity_count > 50:
                    style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # Red for large
                elif entity_count > 20:
                    style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Yellow for medium
                else:
                    style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Blue for small

                mermaid.append(f"        {sanitized_name}[\"{module_name}<br/>({entity_count} entities)\"]")
                mermaid.append(f"        style {sanitized_name} {style}")

            mermaid.append("    end")
            mermaid.append("")

        # Add key dependencies between groups
        group_connections = defaultdict(set)
        for module in self.modules:
            module_group = self._get_module_group(module['name'])
            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if self._is_internal_module(dep_module):
                    dep_group = self._get_module_group(dep_module)
                    if module_group != dep_group and dep_group:
                        group_connections[module_group].add(dep_group)

        # Add group-level connections
        mermaid.append("    %% Group Dependencies")
        for source_group, target_groups in group_connections.items():
            for target_group in target_groups:
                if source_group in self.module_groups and target_group in self.module_groups:
                    source_sanitized = self._sanitize_name(source_group)
                    target_sanitized = self._sanitize_name(target_group)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

```

### 23. generate_dependency_graph_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_dependency_graph_diagram(self, max_modules: int = 30) -> str:
        """Generate a detailed dependency graph diagram."""
        print("🔗 Generating dependency graph diagram...")

        mermaid = ["graph LR"]
        mermaid.append("    %% Module Dependency Graph")
        mermaid.append("")

        # Get most connected modules
        module_scores = {}
        for module in self.modules:
            module_name = module['name']
            outgoing = len(module.get('dependencies', []))
            incoming = sum(1 for m in self.modules
                          for dep in m.get('dependencies', [])
                          if dep.get('module') == module_name)
            module_scores[module_name] = outgoing + incoming

        # Select top modules by connectivity
        top_modules = sorted(module_scores.items(), key=lambda x: x[1], reverse=True)[:max_modules]
        selected_modules = {name for name, _ in top_modules}

        # Add nodes with styling
        for module_name, score in top_modules:
            sanitized_name = self._sanitize_name(module_name)

            # Find the actual module data
            module_data = next((m for m in self.modules if m['name'] == module_name), None)
            if not module_data:
                continue

            entity_count = len(module_data.get('entities', []))

            # Style based on connectivity and size
            if score > 10:
                style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # High connectivity
            elif score > 5:
                style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Medium connectivity
            else:
                style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Low connectivity

            # Truncate long names
            display_name = module_name if len(module_name) <= 20 else module_name[:17] + "..."
            mermaid.append(f"    {sanitized_name}[\"{display_name}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies
        mermaid.append("    %% Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in selected_modules:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in selected_modules:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Style arrows by dependency strength
                    if strength == 'strong':
                        arrow_style = "stroke:#d63031,stroke-width:3px"
                    elif strength == 'medium':
                        arrow_style = "stroke:#ff9ff3,stroke-width:2px"
                    else:
                        arrow_style = "stroke:#74b9ff,stroke-width:1px"

                    mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    # Note: Mermaid doesn't support per-edge styling in this syntax

        return "\n".join(mermaid)

```

### 24. generate_component_architecture_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_component_architecture_diagram(self) -> str:
        """Generate a component architecture diagram showing layers."""
        print("🏗️  Generating component architecture diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% Component Architecture - Layered View")
        mermaid.append("")

        # Define layer order (top to bottom)
        layer_order = ['presentation', 'service', 'core', 'data', 'utility']

        # Add layers as subgraphs
        for layer_name in layer_order:
            if layer_name not in self.architectural_layers:
                continue

            modules = self.architectural_layers[layer_name]
            if not modules:
                continue

            sanitized_layer = self._sanitize_name(layer_name)
            layer_display = layer_name.title() + " Layer"
            mermaid.append(f"    subgraph {sanitized_layer}[{layer_display}]")

            # Add key modules from this layer (limit for readability)
            key_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:8]

            for module in key_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Truncate long names
                display_name = module_name if len(module_name) <= 15 else module_name[:12] + "..."

                mermaid.append(f"        {sanitized_name}[\"{display_name}<br/>({entity_count})\"]")

            mermaid.append("    end")
            mermaid.append("")

        # Add layer dependencies
        mermaid.append("    %% Layer Dependencies")
        layer_deps = defaultdict(set)

        for layer_name, modules in self.architectural_layers.items():
            for module in modules:
                for dep in module.get('dependencies', []):
                    dep_module = dep.get('module', '')
                    if self._is_internal_module(dep_module):
                        dep_layer = self._get_module_layer(dep_module)
                        if dep_layer and dep_layer != layer_name:
                            layer_deps[layer_name].add(dep_layer)

        # Add layer connections
        for source_layer, target_layers in layer_deps.items():
            for target_layer in target_layers:
                if source_layer in self.architectural_layers and target_layer in self.architectural_layers:
                    source_sanitized = self._sanitize_name(source_layer)
                    target_sanitized = self._sanitize_name(target_layer)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

```

### 25. generate_critical_path_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_critical_path_diagram(self) -> str:
        """Generate a diagram showing critical modules and their relationships."""
        print("⚠️  Generating critical path diagram...")

        mermaid = ["graph TD"]
        mermaid.append("    %% Critical Path - High Impact Components")
        mermaid.append("")

        # Take top critical modules
        top_critical = self.critical_modules[:15]
        critical_names = {module['name'] for module in top_critical}

        # Add critical modules with styling
        for module_info in top_critical:
            module_name = module_info['name']
            sanitized_name = self._sanitize_name(module_name)
            score = module_info['score']
            high_crit = module_info['high_crit_entities']
            incoming = module_info['incoming_deps']

            # Style based on criticality score
            if score > 1.0:
                style = "fill:#ff4757,stroke:#ff3742,color:#fff"  # Critical
            elif score > 0.7:
                style = "fill:#ff6348,stroke:#ff4757,color:#fff"  # High
            elif score > 0.5:
                style = "fill:#ffa502,stroke:#ff9ff3,color:#000"  # Medium
            else:
                style = "fill:#fffa65,stroke:#ffdd59,color:#000"  # Low

            # Truncate long names
            display_name = module_name if len(module_name) <= 18 else module_name[:15] + "..."

            mermaid.append(f"    {sanitized_name}[\"{display_name}<br/>Score: {score:.2f}<br/>Critical: {high_crit}<br/>Used by: {incoming}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies between critical modules
        mermaid.append("    %% Critical Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in critical_names:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in critical_names:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Add dependency with strength indicator
                    if strength == 'strong':
                        mermaid.append(f"    {sanitized_source} ==> {sanitized_target}")
                    elif strength == 'medium':
                        mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    else:
                        mermaid.append(f"    {sanitized_source} -.-> {sanitized_target}")

        return "\n".join(mermaid)

```

## INSTRUCTIONS FOR LLM
You have been provided with intelligent context selection based on IR (Intermediate Representation) analysis and ICD (Intelligent Code Discovery).

### Context Quality
- This context was selected using task-specific algorithms
- Entities are prioritized by criticality and relevance
- Dependencies and risk factors have been analyzed
- Token budget has been optimized for maximum value

### Your Task
Please analyze the provided context and respond to the user's query: "I want to add a new code analysis feature. How should I integrate it with the existing system?"

Use the IR analysis data to understand:
1. **Entity Criticality**: Focus on high-criticality components
2. **Change Risk**: Consider risk factors when making recommendations
3. **Dependencies**: Understand how components interact
4. **Side Effects**: Be aware of potential impacts
5. **Error Patterns**: Identify potential issues

Provide a comprehensive, accurate response based on this intelligent context selection.
