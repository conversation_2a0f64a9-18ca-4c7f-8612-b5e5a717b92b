#!/usr/bin/env python

import os
import re
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any

from aider_integration_service import AiderIntegrationService
from aider.repomap import RepoMap
from aider.io import InputOutput


class ContextType(Enum):
    """Enum for different types of code context."""
    USAGE = "usage"
    DEFINITION = "definition"
    CALL_SITE = "call_site"
    INHERITANCE = "inheritance"
    IMPLEMENTATION = "implementation"


class UsageType(Enum):
    """Enum for different types of symbol usage."""
    FUNCTION_CALL = "function_call"
    CLASS_INSTANTIATION = "class_instantiation"
    INHERITANCE = "inheritance"
    IMPORT = "import"
    VARIABLE_REFERENCE = "variable_reference"
    ATTRIBUTE_ACCESS = "attribute_access"
    METHOD_CALL = "method_call"
    UNKNOWN = "unknown"


class DefinitionType(Enum):
    """Enum for different types of symbol definitions."""
    FUNCTION = "function"
    CLASS = "class"
    VARIABLE = "variable"
    CONSTANT = "constant"
    METHOD = "method"
    MODULE = "module"
    UNKNOWN = "unknown"


@dataclass
class CodeSnippet:
    """Represents a focused code snippet with metadata."""
    content: str
    file_path: str
    start_line: int
    end_line: int
    context_type: ContextType
    symbol_name: str
    surrounding_function: Optional[str] = None
    relevance_score: float = 1.0

    def __post_init__(self):
        if isinstance(self.context_type, str):
            self.context_type = ContextType(self.context_type)


@dataclass
class UsageContext:
    """Represents how a symbol is used in a file."""
    snippet: CodeSnippet
    usage_type: UsageType
    referenced_symbol: str
    defining_file: Optional[str] = None

    def __post_init__(self):
        if isinstance(self.usage_type, str):
            self.usage_type = UsageType(self.usage_type)


@dataclass
class DefinitionContext:
    """Represents the definition of a symbol with its surrounding context."""
    snippet: CodeSnippet
    definition_type: DefinitionType
    signature: Optional[str] = None
    docstring: Optional[str] = None

    def __post_init__(self):
        if isinstance(self.definition_type, str):
            self.definition_type = DefinitionType(self.definition_type)


@dataclass
class ContextualDependencyMap:
    """Map of contextual dependencies between files."""
    primary_file: str
    usage_contexts: List[UsageContext] = field(default_factory=list)
    definition_contexts: List[DefinitionContext] = field(default_factory=list)
    related_files: List[str] = field(default_factory=list)


@dataclass
class InheritanceContextMap:
    """Map of inheritance relationships for a class."""
    class_name: str
    file_path: str
    base_classes: List[DefinitionContext] = field(default_factory=list)
    derived_classes: List[DefinitionContext] = field(default_factory=list)
    implementation_contexts: List[CodeSnippet] = field(default_factory=list)


class SurgicalContextExtractor:
    """
    Extracts focused code snippets around dependency interaction points.

    This class provides methods to extract highly targeted, relevant code snippets
    around specific points where files interact, enabling better understanding of
    code relationships while minimizing token usage.
    """

    def __init__(self, aider_service: AiderIntegrationService, cache_ttl: int = 3600):
        """
        Initialize the surgical context extractor.

        Args:
            aider_service: The AiderIntegrationService instance to use for dependency analysis
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.aider_service = aider_service
        self.cache_ttl = cache_ttl
        self.context_cache = {}
        self.cache_timestamps = {}
        self.io = InputOutput()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if a cache entry is still valid based on TTL."""
        if cache_key not in self.cache_timestamps:
            return False

        timestamp = self.cache_timestamps[cache_key]
        return time.time() - timestamp < self.cache_ttl

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value and timestamp."""
        self.context_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """Get a value from the cache if it exists and is valid."""
        if cache_key in self.context_cache and self._is_cache_valid(cache_key):
            return self.context_cache[cache_key]
        return None

    def _read_file_content(self, project_path: str, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        abs_path = os.path.join(project_path, file_path)
        return self.io.read_text(abs_path)

    def _determine_context_window_size(self, code: str, line_num: int, symbol_type: str) -> int:
        """
        Determine an appropriate context window size based on code structure and symbol type.

        Args:
            code: The code content
            line_num: The line number to center the context around
            symbol_type: The type of symbol (function, class, etc.)

        Returns:
            The number of lines to include above and below the target line
        """
        # Default window sizes based on symbol type
        if symbol_type in ['class', 'classes']:
            default_size = 20  # Larger window for classes
        elif symbol_type in ['function', 'functions', 'method', 'methods']:
            default_size = 10  # Medium window for functions/methods
        else:
            default_size = 5   # Small window for simple expressions

        # Try to find semantic boundaries (complete functions/methods)
        lines = code.splitlines()
        if 0 <= line_num < len(lines):
            # Look for function/method definition patterns
            if re.search(r'^\s*(def|class)\s+\w+', lines[line_num]):
                # This is a definition line, expand to capture the whole definition

                # Find the indentation level of the definition
                match = re.match(r'^(\s*)', lines[line_num])
                base_indent = match.group(1) if match else ''

                # Look for the end of the definition (next line with same or less indentation)
                end_line = line_num
                for i in range(line_num + 1, min(len(lines), line_num + 100)):
                    if lines[i].strip() and not lines[i].startswith(base_indent + ' '):
                        end_line = i - 1
                        break
                    end_line = i

                # Calculate appropriate window size
                return max(default_size, end_line - line_num + 5)

        return default_size

    def _extract_code_snippet(self, project_path: str, file_path: str,
                             line_num: int, context_window: int,
                             context_type: ContextType, symbol_name: str) -> Optional[CodeSnippet]:
        """
        Extract a code snippet from a file centered around a specific line.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to extract from
            line_num: The line number to center the snippet around (1-based)
            context_type: The type of context being extracted
            symbol_name: The name of the symbol being referenced

        Returns:
            A CodeSnippet object or None if extraction failed
        """
        content = self._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return None

        # Adjust line_num to 0-based for internal calculations
        line_idx = line_num - 1

        # For class definitions, extract the complete class body
        if context_type == ContextType.DEFINITION and re.search(r'^\s*class\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_class_body(lines, line_idx)
        # For function/method definitions, extract the complete function body
        elif context_type == ContextType.DEFINITION and re.search(r'^\s*def\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_function_body(lines, line_idx)
        else:
            # Use context window for other cases
            start_idx = max(0, line_idx - context_window)
            end_idx = min(len(lines) - 1, line_idx + context_window)

        # Extract the surrounding function/method name if possible
        surrounding_function = self._find_surrounding_function(lines, line_idx)

        # Extract the snippet content
        snippet_lines = lines[start_idx:end_idx + 1]
        snippet_content = '\n'.join(snippet_lines)

        return CodeSnippet(
            content=snippet_content,
            file_path=file_path,
            start_line=start_idx + 1,  # Convert back to 1-based
            end_line=end_idx + 1,      # Convert back to 1-based
            context_type=context_type,
            symbol_name=symbol_name,
            surrounding_function=surrounding_function
        )

    def _find_complete_class_body(self, lines: List[str], class_line_idx: int) -> Tuple[int, int]:
        """
        Find the complete body of a class definition.

        Args:
            lines: List of all lines in the file
            class_line_idx: 0-based index of the class definition line

        Returns:
            Tuple of (start_idx, end_idx) for the complete class body
        """
        # Find the indentation level of the class
        class_line = lines[class_line_idx]
        match = re.match(r'^(\s*)', class_line)
        class_indent = match.group(1) if match else ''

        # Start from the class definition line
        start_idx = class_line_idx

        # Find the end of the class by looking for the next line with same or less indentation
        # that is not empty and not a comment
        end_idx = class_line_idx
        for i in range(class_line_idx + 1, len(lines)):
            line = lines[i]

            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                end_idx = i
                continue

            # Check if this line has same or less indentation than the class
            if not line.startswith(class_indent + ' ') and line.strip():
                # This line is at the same level or less indented than the class
                # The class body ends at the previous line
                break
            else:
                # This line is part of the class body
                end_idx = i

        return start_idx, end_idx

    def _find_complete_function_body(self, lines: List[str], func_line_idx: int) -> Tuple[int, int]:
        """
        Find the complete body of a function/method definition.

        Args:
            lines: List of all lines in the file
            func_line_idx: 0-based index of the function definition line

        Returns:
            Tuple of (start_idx, end_idx) for the complete function body
        """
        # Find the indentation level of the function
        func_line = lines[func_line_idx]
        match = re.match(r'^(\s*)', func_line)
        func_indent = match.group(1) if match else ''

        # Start from the function definition line
        start_idx = func_line_idx

        # Find the end of the function by looking for the next line with same or less indentation
        # that is not empty and not a comment
        end_idx = func_line_idx
        for i in range(func_line_idx + 1, len(lines)):
            line = lines[i]

            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                end_idx = i
                continue

            # Check if this line has same or less indentation than the function
            if not line.startswith(func_indent + ' ') and line.strip():
                # This line is at the same level or less indented than the function
                # The function body ends at the previous line
                break
            else:
                # This line is part of the function body
                end_idx = i

        return start_idx, end_idx

    def _find_surrounding_function(self, lines: List[str], line_idx: int) -> Optional[str]:
        """
        Find the name of the function or method that contains the given line.

        Args:
            lines: The lines of code
            line_idx: The index of the line to find the surrounding function for (0-based)

        Returns:
            The name of the surrounding function/method, or None if not found
        """
        # Look backwards for the nearest function or method definition
        for i in range(line_idx, -1, -1):
            # Match function or method definition
            match = re.search(r'^\s*(def|class)\s+(\w+)', lines[i])
            if match:
                return match.group(2)

        return None

    def _find_symbol_line_numbers(self, project_path: str, file_path: str, symbol_name: str) -> List[int]:
        """
        Find line numbers where a symbol is used in a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to search in
            symbol_name: The name of the symbol to find

        Returns:
            A list of line numbers (1-based) where the symbol is used
        """
        content = self._read_file_content(project_path, file_path)
        if not content:
            return []

        lines = content.splitlines()
        line_numbers = []

        # Simple pattern matching for the symbol
        pattern = r'\b' + re.escape(symbol_name) + r'\b'
        for i, line in enumerate(lines):
            if re.search(pattern, line):
                line_numbers.append(i + 1)  # Convert to 1-based

        return line_numbers

    def _calculate_relevance_score(self, snippet: CodeSnippet, primary_file: str,
                                  symbol_importance: Dict[str, int]) -> float:
        """
        Calculate a relevance score for a code snippet.

        Args:
            snippet: The code snippet to score
            primary_file: The primary file being analyzed
            symbol_importance: Dictionary mapping symbols to their importance scores

        Returns:
            A relevance score between 0.0 and 1.0
        """
        base_score = 0.5

        # Adjust score based on context type
        if snippet.context_type == ContextType.DEFINITION:
            base_score += 0.2
        elif snippet.context_type == ContextType.USAGE:
            base_score += 0.1

        # Adjust score based on symbol importance
        if snippet.symbol_name in symbol_importance:
            base_score += min(0.3, symbol_importance[snippet.symbol_name] / 10)

        # Adjust score based on file relationship
        if snippet.file_path == primary_file:
            base_score += 0.1

        # Ensure score is between 0 and 1
        return min(1.0, max(0.0, base_score))

    def extract_dependency_contexts(self, project_path: str, primary_file: str,
                                  context_window: int = 10) -> Dict[str, List[CodeSnippet]]:
        """
        Extract focused code snippets from all dependencies of a primary file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            context_window: Default number of lines to include above and below each reference

        Returns:
            Dictionary mapping file paths to lists of code snippets
        """
        cache_key = f"dependency_contexts:{project_path}:{primary_file}:{context_window}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # Get files that import the primary file
        importing_files = self.aider_service.get_files_that_import(project_path, primary_file)

        # Combine all related files
        related_files = list(set(imported_files + importing_files))

        # Initialize result dictionary
        result = {file_path: [] for file_path in related_files + [primary_file]}

        # Process each related file
        for file_path in related_files:
            # Get symbol references between the files
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, file_path)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in symbol_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is defined in the target file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.DEFINITION, symbol)

                        if snippet:
                            result[file_path].append(snippet)

            # Also check references from the related file to the primary file
            reverse_refs = self.aider_service.get_symbol_references_between_files(
                project_path, file_path, primary_file)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in reverse_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is used in the source file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.USAGE, symbol)

                        if snippet:
                            result[file_path].append(snippet)

        # Cache the result
        self._update_cache(cache_key, result)

        return result

    def extract_usage_contexts(self, project_path: str, symbol_name: str,
                              defining_file: str) -> List[UsageContext]:
        """
        Find all places where a specific symbol is used and extract the context around each usage.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to find usages of
            defining_file: Path to the file that defines the symbol

        Returns:
            A list of UsageContext objects
        """
        cache_key = f"usage_contexts:{project_path}:{symbol_name}:{defining_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Find files that might use this symbol
        files_to_check = self.aider_service.get_files_that_import(project_path, defining_file)

        # Add the defining file itself (for self-references)
        if defining_file not in files_to_check:
            files_to_check.append(defining_file)

        usage_contexts = []

        # Check each file for usages of the symbol
        for file_path in files_to_check:
            # Find line numbers where the symbol is used
            line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol_name)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, file_path)
                if not content:
                    continue

                # Determine the usage type
                usage_type = self._determine_usage_type(content, line_num, symbol_name)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1, "variable" if usage_type == UsageType.VARIABLE_REFERENCE else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, file_path, line_num, adjusted_window,
                    ContextType.USAGE, symbol_name)

                if snippet:
                    usage_context = UsageContext(
                        snippet=snippet,
                        usage_type=usage_type,
                        referenced_symbol=symbol_name,
                        defining_file=defining_file
                    )
                    usage_contexts.append(usage_context)

        # Cache the result
        self._update_cache(cache_key, usage_contexts)

        return usage_contexts

    def _determine_usage_type(self, content: str, line_num: int, symbol_name: str) -> UsageType:
        """
        Determine how a symbol is being used in a specific line.

        Args:
            content: The file content
            line_num: The line number where the symbol is used (1-based)
            symbol_name: The name of the symbol

        Returns:
            The usage type
        """
        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return UsageType.UNKNOWN

        line = lines[line_num - 1]

        # Check for different usage patterns
        if re.search(rf'{symbol_name}\s*\(', line):
            return UsageType.FUNCTION_CALL
        elif re.search(rf'class\s+\w+\s*\(\s*{symbol_name}', line):
            return UsageType.INHERITANCE
        elif re.search(rf'from\s+\S+\s+import\s+.*{symbol_name}|import\s+.*{symbol_name}', line):
            return UsageType.IMPORT
        elif re.search(rf'\b{symbol_name}\s*=\s*', line):
            return UsageType.VARIABLE_REFERENCE
        elif re.search(rf'\.\s*{symbol_name}\s*\(', line):
            return UsageType.METHOD_CALL
        elif re.search(rf'\.\s*{symbol_name}\b', line):
            return UsageType.ATTRIBUTE_ACCESS
        elif re.search(rf'{symbol_name}\s*=\s*\w+\s*\(', line):
            return UsageType.CLASS_INSTANTIATION
        else:
            return UsageType.UNKNOWN

    def extract_definition_contexts(self, project_path: str, symbols: List[str],
                                   source_file: str) -> List[DefinitionContext]:
        """
        For symbols referenced by a file, extract their definitions with surrounding context.

        Args:
            project_path: Path to the project root
            symbols: List of symbol names to find definitions for
            source_file: Path to the file that references these symbols

        Returns:
            A list of DefinitionContext objects
        """
        cache_key = f"definition_contexts:{project_path}:{','.join(symbols)}:{source_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        definition_contexts = []

        for symbol in symbols:
            # Find the file that defines this symbol
            defining_file = self.aider_service.find_file_defining_symbol(project_path, symbol)
            if not defining_file:
                continue

            # Find line numbers where the symbol is defined
            line_numbers = self._find_definition_line_numbers(project_path, defining_file, symbol)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, defining_file)
                if not content:
                    continue

                # Determine the definition type
                definition_type, signature, docstring = self._extract_definition_info(
                    content, line_num, symbol)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1,
                    "class" if definition_type == DefinitionType.CLASS else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, defining_file, line_num, adjusted_window,
                    ContextType.DEFINITION, symbol)

                if snippet:
                    definition_context = DefinitionContext(
                        snippet=snippet,
                        definition_type=definition_type,
                        signature=signature,
                        docstring=docstring
                    )
                    definition_contexts.append(definition_context)

        # Cache the result
        self._update_cache(cache_key, definition_contexts)

        return definition_contexts

    def _find_definition_line_numbers(self, project_path: str, file_path: str, symbol_name: str) -> List[int]:
        """
        Find line numbers where a symbol is defined in a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to search in
            symbol_name: The name of the symbol to find

        Returns:
            A list of line numbers (1-based) where the symbol is defined
        """


    def _extract_definition_info(self, content: str, line_num: int, symbol_name: str) -> Tuple[DefinitionType, Optional[str], Optional[str]]:
        """
        Extract information about a symbol definition.

        Args:
            content: The file content
            line_num: The line number where the symbol is defined (1-based)
            symbol_name: The name of the symbol

        Returns:
            A tuple of (definition_type, signature, docstring)
        """
        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return DefinitionType.UNKNOWN, None, None

        line = lines[line_num - 1]

        # Determine definition type
        if re.search(rf'class\s+{re.escape(symbol_name)}', line):
            definition_type = DefinitionType.CLASS
            # Extract class signature
            signature = line.strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'def\s+{re.escape(symbol_name)}', line):
            # Check if this is a method (indented and/or has self parameter)
            if line.startswith(' ') or re.search(r'\(\s*self\s*[,)]', line):
                definition_type = DefinitionType.METHOD
            else:
                definition_type = DefinitionType.FUNCTION

            # Extract function signature
            signature = line.strip()
            if not signature.endswith(':'):
                # Look for continuation lines
                i = line_num
                while i < len(lines) and not signature.endswith(':'):
                    i += 1
                    if i < len(lines):
                        signature += ' ' + lines[i].strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'{re.escape(symbol_name)}\s*=', line):
            # Check if this is a constant (all uppercase)
            if symbol_name.isupper():
                definition_type = DefinitionType.CONSTANT
            else:
                definition_type = DefinitionType.VARIABLE

            # Extract variable assignment
            signature = line.strip()

            return definition_type, signature, None

        else:
            return DefinitionType.UNKNOWN, None, None

    def _extract_docstring(self, lines: List[str], line_num: int) -> Optional[str]:
        """
        Extract a docstring following a definition.

        Args:
            lines: The lines of code
            line_num: The line number of the definition (1-based)

        Returns:
            The docstring if found, None otherwise
        """
        if line_num >= len(lines):
            return None

        # Find the first non-empty line after the definition
        i = line_num
        while i < len(lines) and (not lines[i].strip() or not lines[i].strip().endswith(':')):
            i += 1

        if i >= len(lines):
            return None

        # Check for docstring (triple quotes)
        i += 1
        if i < len(lines):
            line = lines[i].strip()
            if line.startswith('"""') or line.startswith("'''"):
                # Single line docstring
                if line.endswith('"""') or line.endswith("'''"):
                    return line

                # Multi-line docstring
                docstring_lines = [line]
                i += 1
                while i < len(lines):
                    line = lines[i]
                    docstring_lines.append(line)
                    if line.strip().endswith('"""') or line.strip().endswith("'''"):
                        break
                    i += 1

                return '\n'.join(docstring_lines)

        return None

    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> ContextualDependencyMap:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A ContextualDependencyMap object
        """
        # Extract dependency contexts
        dependency_contexts = self.extract_dependency_contexts(project_path, primary_file)

        # Get all symbols defined in the primary file
        symbols_in_primary = self.aider_service.get_symbols_defined_in_file(project_path, primary_file)

        # Flatten the symbols list
        all_symbols = []
        for symbol_type, symbols in symbols_in_primary.items():
            all_symbols.extend(symbols)

        # Get usage contexts for symbols defined in the primary file
        usage_contexts = []
        for symbol in all_symbols:
            symbol_usages = self.extract_usage_contexts(project_path, symbol, primary_file)
            usage_contexts.extend(symbol_usages)

        # Get definition contexts for symbols used in the primary file
        definition_contexts = []

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # For each imported file, get symbols referenced from the primary file
        for imported_file in imported_files:
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, imported_file)

            # Flatten the symbols list
            referenced_symbols = []
            for symbol_type, symbols in symbol_refs.items():
                referenced_symbols.extend(symbols)

            # Get definition contexts for these symbols
            if referenced_symbols:
                symbol_definitions = self.extract_definition_contexts(
                    project_path, referenced_symbols, primary_file)
                definition_contexts.extend(symbol_definitions)

        # Sort contexts by relevance and limit to max_snippets
        usage_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)
        definition_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)

        # Ensure we have a balanced mix of usage and definition contexts
        max_each = max_snippets // 2
        usage_contexts = usage_contexts[:max_each]
        definition_contexts = definition_contexts[:max_each]

        # Get related files
        related_files = list(set(
            [ctx.snippet.file_path for ctx in usage_contexts] +
            [ctx.snippet.file_path for ctx in definition_contexts]
        ))

        return ContextualDependencyMap(
            primary_file=primary_file,
            usage_contexts=usage_contexts,
            definition_contexts=definition_contexts,
            related_files=related_files
        )

    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> InheritanceContextMap:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            An InheritanceContextMap object
        """
        # Get base classes
        base_classes_info = self.aider_service.get_base_classes_of(project_path, class_name, file_path)

        # Get derived classes
        derived_classes_info = self.aider_service.get_derived_classes_of(project_path, class_name, file_path)

        # Extract definition contexts for base classes
        base_class_contexts = []
        for base_class_info in base_classes_info:
            base_class_name = base_class_info.get('class_name')
            base_class_file = base_class_info.get('file_path')

            if base_class_name and base_class_file:
                # Find line numbers where the base class is defined
                line_numbers = self._find_definition_line_numbers(project_path, base_class_file, base_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, base_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, base_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, base_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, base_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        base_class_contexts.append(definition_context)

        # Extract definition contexts for derived classes
        derived_class_contexts = []
        for derived_class_info in derived_classes_info:
            derived_class_name = derived_class_info.get('class_name')
            derived_class_file = derived_class_info.get('file_path')

            if derived_class_name and derived_class_file:
                # Find line numbers where the derived class is defined
                line_numbers = self._find_definition_line_numbers(project_path, derived_class_file, derived_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, derived_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, derived_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, derived_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, derived_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        derived_class_contexts.append(definition_context)

        # Extract implementation contexts (method overrides, etc.)
        implementation_contexts = []

        # Find methods in the class
        class_line_numbers = self._find_definition_line_numbers(project_path, file_path, class_name)
        if class_line_numbers:
            content = self._read_file_content(project_path, file_path)
            if content:
                lines = content.splitlines()
                class_line = class_line_numbers[0] - 1  # Convert to 0-based

                # Find the indentation level of the class
                match = re.match(r'^(\s*)', lines[class_line])
                class_indent = match.group(1) if match else ''
                method_indent = class_indent + '    '  # Assuming 4 spaces for indentation

                # Look for method definitions
                for i in range(class_line + 1, len(lines)):
                    line = lines[i]
                    if line.startswith(method_indent) and 'def ' in line:
                        # This is a method definition
                        method_match = re.search(r'def\s+(\w+)', line)
                        if method_match:
                            method_name = method_match.group(1)

                            # Check if this method overrides a base class method
                            for base_class_info in base_classes_info:
                                base_class_name = base_class_info.get('class_name')
                                base_class_file = base_class_info.get('file_path')

                                if base_class_name and base_class_file:
                                    # Check if the base class has this method
                                    base_content = self._read_file_content(project_path, base_class_file)
                                    if base_content and re.search(rf'def\s+{method_name}\s*\(', base_content):
                                        # This is an overridden method
                                        adjusted_window = self._determine_context_window_size(
                                            content, i, "method")

                                        # Extract the code snippet
                                        snippet = self._extract_code_snippet(
                                            project_path, file_path, i + 1, adjusted_window,
                                            ContextType.IMPLEMENTATION, method_name)

                                        if snippet:
                                            implementation_contexts.append(snippet)

        return InheritanceContextMap(
            class_name=class_name,
            file_path=file_path,
            base_classes=base_class_contexts,
            derived_classes=derived_class_contexts,
            implementation_contexts=implementation_contexts
        )




    def _extract_definition_info(self, content: str, line_num: int, symbol_name: str) -> Tuple[DefinitionType, Optional[str], Optional[str]]:
        """
        Extract information about a symbol definition.

        Args:
            content: The file content
            line_num: The line number where the symbol is defined (1-based)
            symbol_name: The name of the symbol

        Returns:
            A tuple of (definition_type, signature, docstring)
        """
        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return DefinitionType.UNKNOWN, None, None

        line = lines[line_num - 1]

        # Determine definition type
        if re.search(rf'class\s+{re.escape(symbol_name)}', line):
            definition_type = DefinitionType.CLASS
            # Extract class signature
            signature = line.strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'def\s+{re.escape(symbol_name)}', line):
            # Check if this is a method (indented and/or has self parameter)
            if line.startswith(' ') or re.search(r'\(\s*self\s*[,)]', line):
                definition_type = DefinitionType.METHOD
            else:
                definition_type = DefinitionType.FUNCTION

            # Extract function signature
            signature = line.strip()
            if not signature.endswith(':'):
                # Look for continuation lines
                i = line_num
                while i < len(lines) and not signature.endswith(':'):
                    i += 1
                    if i < len(lines):
                        signature += ' ' + lines[i].strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'{re.escape(symbol_name)}\s*=', line):
            # Check if this is a constant (all uppercase)
            if symbol_name.isupper():
                definition_type = DefinitionType.CONSTANT
            else:
                definition_type = DefinitionType.VARIABLE

            # Extract variable assignment
            signature = line.strip()

            return definition_type, signature, None

        else:
            return DefinitionType.UNKNOWN, None, None

    def _extract_docstring(self, lines: List[str], line_num: int) -> Optional[str]:
        """
        Extract a docstring following a definition.

        Args:
            lines: The lines of code
            line_num: The line number of the definition (1-based)

        Returns:
            The docstring if found, None otherwise
        """
        if line_num >= len(lines):
            return None

        # Find the first non-empty line after the definition
        i = line_num
        while i < len(lines) and (not lines[i].strip() or not lines[i].strip().endswith(':')):
            i += 1

        if i >= len(lines):
            return None

        # Check for docstring (triple quotes)
        i += 1
        if i < len(lines):
            line = lines[i].strip()
            if line.startswith('"""') or line.startswith("'''"):
                # Single line docstring
                if line.endswith('"""') or line.endswith("'''"):
                    return line

                # Multi-line docstring
                docstring_lines = [line]
                i += 1
                while i < len(lines):
                    line = lines[i]
                    docstring_lines.append(line)
                    if line.strip().endswith('"""') or line.strip().endswith("'''"):
                        break
                    i += 1

                return '\n'.join(docstring_lines)

        return None

    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> ContextualDependencyMap:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A ContextualDependencyMap object
        """
        # Extract dependency contexts
        dependency_contexts = self.extract_dependency_contexts(project_path, primary_file)

        # Get all symbols defined in the primary file
        symbols_in_primary = self.aider_service.get_symbols_defined_in_file(project_path, primary_file)

        # Flatten the symbols list
        all_symbols = []
        for symbol_type, symbols in symbols_in_primary.items():
            all_symbols.extend(symbols)

        # Get usage contexts for symbols defined in the primary file
        usage_contexts = []
        for symbol in all_symbols:
            symbol_usages = self.extract_usage_contexts(project_path, symbol, primary_file)
            usage_contexts.extend(symbol_usages)

        # Get definition contexts for symbols used in the primary file
        definition_contexts = []

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # For each imported file, get symbols referenced from the primary file
        for imported_file in imported_files:
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, imported_file)

            # Flatten the symbols list
            referenced_symbols = []
            for symbol_type, symbols in symbol_refs.items():
                referenced_symbols.extend(symbols)

            # Get definition contexts for these symbols
            if referenced_symbols:
                symbol_definitions = self.extract_definition_contexts(
                    project_path, referenced_symbols, primary_file)
                definition_contexts.extend(symbol_definitions)

        # Sort contexts by relevance and limit to max_snippets
        usage_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)
        definition_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)

        # Ensure we have a balanced mix of usage and definition contexts
        max_each = max_snippets // 2
        usage_contexts = usage_contexts[:max_each]
        definition_contexts = definition_contexts[:max_each]

        # Get related files
        related_files = list(set(
            [ctx.snippet.file_path for ctx in usage_contexts] +
            [ctx.snippet.file_path for ctx in definition_contexts]
        ))

        return ContextualDependencyMap(
            primary_file=primary_file,
            usage_contexts=usage_contexts,
            definition_contexts=definition_contexts,
            related_files=related_files
        )
