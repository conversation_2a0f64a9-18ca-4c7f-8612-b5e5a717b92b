#!/usr/bin/env python3
"""
Test script to verify the LLM provides a roadmap instead of immediately using MAP_REQUEST
"""

import sys
import os

def test_roadmap_prompts():
    """Test that the prompts encourage roadmap behavior"""
    print("🧪 Testing Roadmap Behavior Implementation")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test the assistant reply content
        assistant_reply = prompts.smart_map_request_assistant_reply
        print("📝 Smart Map Request Assistant Reply:")
        print(f"   Length: {len(assistant_reply)} characters")
        print(f"   Content: {assistant_reply}")
        
        # Check for roadmap-encouraging elements
        roadmap_checks = [
            ("I understand", "✅" if "I understand" in assistant_reply else "❌"),
            ("ZERO knowledge", "✅" if "ZERO knowledge" in assistant_reply else "❌"),
            ("PLAY DUMB", "✅" if "PLAY DUMB" in assistant_reply else "❌"),
            ("provide a roadmap", "✅" if "provide a roadmap" in assistant_reply else "❌"),
            ("what information I need to gather", "✅" if "what information I need to gather" in assistant_reply else "❌"),
            ("systematically collect", "✅" if "systematically collect" in assistant_reply else "❌"),
            ("not make any assumptions", "✅" if "not make any assumptions" in assistant_reply else "❌")
        ]
        
        print("\n🔍 Roadmap Behavior Checks:")
        for check, status in roadmap_checks:
            print(f"   {status} {check}")
        
        # Check that it doesn't immediately commit to MAP_REQUEST
        immediate_action_checks = [
            ("Does NOT say 'always start with MAP_REQUEST'", "✅" if "always start with MAP_REQUEST" not in assistant_reply else "❌"),
            ("Does NOT say 'will start with MAP_REQUEST'", "✅" if "will start with MAP_REQUEST" not in assistant_reply else "❌"),
            ("Does NOT say 'must start with MAP_REQUEST'", "✅" if "must start with MAP_REQUEST" not in assistant_reply else "❌")
        ]
        
        print("\n🚫 Immediate Action Avoidance Checks:")
        for check, status in immediate_action_checks:
            print(f"   {status} {check}")
        
        # Overall assessment
        all_roadmap_checks_pass = all(status == "✅" for _, status in roadmap_checks)
        all_avoidance_checks_pass = all(status == "✅" for _, status in immediate_action_checks)
        
        if all_roadmap_checks_pass and all_avoidance_checks_pass:
            print("\n🎉 SUCCESS: Assistant will provide roadmap instead of immediate MAP_REQUEST!")
            return True
        else:
            print("\n❌ ISSUES: Assistant may still jump to immediate actions")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_llm_behavior():
    """Test what the expected LLM behavior should be"""
    print("\n🤖 Expected LLM Behavior Analysis")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Simulate what the LLM should see
        user_instruction = prompts.smart_map_request_user_prompt
        assistant_commitment = prompts.smart_map_request_assistant_reply
        
        print("📋 What the LLM sees in conversation history:")
        print(f"\n👤 USER: {user_instruction}")
        print(f"\n🤖 ASSISTANT: {assistant_commitment}")
        
        print(f"\n🎯 Expected LLM behavior when user asks about a function:")
        print(f"   1. ✅ Acknowledge that it has ZERO knowledge")
        print(f"   2. ✅ Provide a roadmap of information needed")
        print(f"   3. ✅ List what it needs to discover")
        print(f"   4. ✅ Then use MAP_REQUEST to start gathering info")
        print(f"   5. ❌ Should NOT immediately jump to MAP_REQUEST")
        print(f"   6. ❌ Should NOT make assumptions about code structure")
        
        print(f"\n📝 Example expected response to 'How does the login function work?':")
        expected_response = """I have ZERO knowledge about this codebase and need to gather information systematically. Here's my roadmap:

**Information I need to gather:**
1. Explore the repository structure to find authentication-related files
2. Locate files that might contain login functionality
3. Identify the specific login function implementation
4. Understand the login process flow and dependencies

**Step 1: Repository exploration**
Let me start by exploring the codebase structure to find relevant files:

{MAP_REQUEST: {"keywords": ["login", "auth", "authentication", "user"], "type": "implementation", "scope": "all", "max_results": 8}}"""
        
        print(expected_response)
        
        print(f"\n🔍 Key differences from old behavior:")
        print(f"   ✅ NEW: Provides explicit roadmap first")
        print(f"   ✅ NEW: Explains what information is needed")
        print(f"   ✅ NEW: Shows systematic approach")
        print(f"   ❌ OLD: Immediately jumped to MAP_REQUEST")
        print(f"   ❌ OLD: No explanation of approach")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_conversation_flow():
    """Test the complete conversation flow"""
    print("\n🔄 Testing Complete Conversation Flow")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Simulate the repo_messages that get added to conversation
        repo_messages = [
            dict(role="user", content=prompts.smart_map_request_user_prompt),
            dict(role="assistant", content=prompts.smart_map_request_assistant_reply),
        ]
        
        print("📋 Conversation history that LLM sees:")
        for i, msg in enumerate(repo_messages):
            role = msg['role'].upper()
            content = msg['content']
            print(f"\n{i+1}. {role}:")
            print(f"   {content}")
        
        print(f"\n🎯 What this conversation history teaches the LLM:")
        print(f"   1. ✅ You have ZERO knowledge about the codebase")
        print(f"   2. ✅ You must PLAY DUMB and not make assumptions")
        print(f"   3. ✅ You should provide a roadmap first")
        print(f"   4. ✅ You should systematically collect information")
        print(f"   5. ✅ You must discover everything through MAP_REQUEST")
        
        print(f"\n🧠 LLM's internal understanding after seeing this:")
        print(f"   - 'I previously agreed to play dumb'")
        print(f"   - 'I previously agreed to provide roadmaps'")
        print(f"   - 'I previously agreed to gather info systematically'")
        print(f"   - 'I should follow this pattern I established'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Roadmap Behavior Implementation")
    print("=" * 80)
    
    success1 = test_roadmap_prompts()
    success2 = test_expected_llm_behavior()
    success3 = test_conversation_flow()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: LLM should now provide roadmaps!")
        print("\n📋 Summary of changes:")
        print("   ✅ Assistant commits to providing roadmaps first")
        print("   ✅ Systematic information gathering approach")
        print("   ✅ Still maintains PLAY DUMB behavior")
        print("   ✅ No immediate jumping to MAP_REQUEST")
        print("   ✅ Clear explanation of approach before action")
        print("\n🎯 Expected behavior:")
        print("   When asked about code, LLM should:")
        print("   1. Acknowledge zero knowledge")
        print("   2. Provide roadmap of needed information")
        print("   3. Then systematically use MAP_REQUEST")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
