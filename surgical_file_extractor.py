#!/usr/bin/env python

import os
import re
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any

from aider_integration_service import AiderIntegrationService
from aider.repomap import RepoMap, Tag
from aider.io import InputOutput


@dataclass
class ExtractionRange:
    """Represents a range of lines to extract from a file."""
    start_line: int
    end_line: int
    total_lines: int
    symbol_name: str
    file_path: str


@dataclass
class SymbolInfo:
    """Information about a symbol in the codebase."""
    name: str
    start_line: int
    file_path: str
    symbol_type: str  # 'function', 'class', 'method'


class SurgicalFileExtractor:
    """
    Extracts complete function or method bodies from source files using line number boundaries.

    This class provides methods to surgically extract only the required code sections with
    perfect precision, leveraging existing repository mapping data to determine symbol boundaries.
    """

    def __init__(self, aider_service: AiderIntegrationService, cache_ttl: int = 3600):
        """
        Initialize the surgical file extractor.

        Args:
            aider_service: The AiderIntegrationService instance to use for repository mapping
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.aider_service = aider_service
        self.cache_ttl = cache_ttl
        self.extraction_cache = {}
        self.cache_timestamps = {}
        self.io = InputOutput()

    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.extraction_cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.extraction_cache[cache_key]
        return None

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.extraction_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def _read_file_content(self, project_path: str, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        try:
            full_path = os.path.join(project_path, file_path)
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None

    def _get_file_line_count(self, project_path: str, file_path: str) -> int:
        """Get the total number of lines in a file."""
        content = self._read_file_content(project_path, file_path)
        if content is None:
            return 0
        return len(content.splitlines())

    def _get_repo_map(self, project_path: str) -> Optional[RepoMap]:
        """Get the repository map for a project."""
        try:
            return self.aider_service.coder.repo_map
        except AttributeError:
            print("Repository map not available")
            return None

    def get_symbols_in_file(self, project_path: str, file_path: str) -> List[SymbolInfo]:
        """
        Get all symbols defined in a file with their line numbers.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze

        Returns:
            List of SymbolInfo objects for symbols defined in the file
        """
        repo_map = self._get_repo_map(project_path)
        if not repo_map:
            return []

        # Get all tags for the file
        abs_file_path = os.path.join(project_path, file_path)
        rel_file_path = repo_map.get_rel_fname(abs_file_path)

        tags = repo_map.get_tags(abs_file_path, rel_file_path)
        if not tags:
            return []

        # Filter for definition tags only
        def_tags = [tag for tag in tags if tag.kind == "def"]

        # Convert to SymbolInfo objects
        symbols = []
        for tag in def_tags:
            # Determine symbol type based on naming conventions or context
            symbol_type = "function"  # Default
            if tag.name[0].isupper():
                symbol_type = "class"
            elif tag.name.startswith("__") and tag.name.endswith("__"):
                symbol_type = "method"

            symbols.append(SymbolInfo(
                name=tag.name,
                start_line=tag.line + 1,  # Convert to 1-based line numbers
                file_path=file_path,
                symbol_type=symbol_type
            ))

        # Sort by line number
        symbols.sort(key=lambda s: s.start_line)
        return symbols

    def extract_symbol_range(self, target_symbol: str, file_path: str, project_path: str) -> Optional[ExtractionRange]:
        """
        Extract the line range for a specific symbol in a file.

        Args:
            target_symbol: Name of the symbol to extract
            file_path: Path to the file containing the symbol
            project_path: Path to the project root

        Returns:
            ExtractionRange object with the line range to extract, or None if not found
        """
        cache_key = f"symbol_range:{project_path}:{file_path}:{target_symbol}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get all symbols in the file
        file_symbols = self.get_symbols_in_file(project_path, file_path)
        if not file_symbols:
            return None

        # Find target symbol
        target = next((s for s in file_symbols if s.name == target_symbol), None)
        if not target:
            return None

        # Find next symbol boundary
        next_symbols = [s for s in file_symbols if s.start_line > target.start_line]

        if next_symbols:
            # Extract until next symbol
            next_boundary = min(next_symbols, key=lambda s: s.start_line)
            end_line = next_boundary.start_line - 1
        else:
            # Extract to end of file
            end_line = self._get_file_line_count(project_path, file_path)

        result = ExtractionRange(
            start_line=target.start_line,
            end_line=end_line,
            total_lines=end_line - target.start_line + 1,
            symbol_name=target_symbol,
            file_path=file_path
        )

        # Cache the result
        self._update_cache(cache_key, result)

        return result

    def extract_symbol_content(self, target_symbol: str, file_path: str, project_path: str) -> Optional[str]:
        """
        Extract the complete content of a symbol from a file.

        Args:
            target_symbol: Name of the symbol to extract
            file_path: Path to the file containing the symbol
            project_path: Path to the project root

        Returns:
            String containing the complete symbol implementation, or None if not found
        """
        extraction_range = self.extract_symbol_range(target_symbol, file_path, project_path)
        if not extraction_range:
            return None

        content = self._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if extraction_range.start_line > len(lines) or extraction_range.start_line < 1:
            return None

        # Adjust end_line to not exceed file length
        end_line = min(extraction_range.end_line, len(lines))

        # Extract the lines (adjusting for 0-based indexing)
        extracted_lines = lines[extraction_range.start_line - 1:end_line]
        return '\n'.join(extracted_lines)
