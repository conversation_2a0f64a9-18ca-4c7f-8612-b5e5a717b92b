#!/usr/bin/env python3
"""
Debug script to check if repo_messages are being properly generated and used
"""

import sys
import os

def debug_repo_messages():
    """Debug the repo_messages generation"""
    print("🔍 Debugging Repo Messages Generation")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import SMART_MAP_REQUEST_AVAILABLE
        from aider.coders.base_prompts import CoderPrompts

        print(f"📋 SMART_MAP_REQUEST_AVAILABLE: {SMART_MAP_REQUEST_AVAILABLE}")

        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System is NOT available!")
            print("   This means the LLM will get legacy behavior or no repo messages")
            return False

        print("✅ Smart Map Request System is available")

        # Test the prompts directly
        prompts = CoderPrompts()

        user_prompt = prompts.smart_map_request_user_prompt
        assistant_reply = prompts.smart_map_request_assistant_reply

        print(f"\n📝 User Prompt ({len(user_prompt)} chars):")
        print(f"   {user_prompt}")

        print(f"\n📝 Assistant Reply ({len(assistant_reply)} chars):")
        print(f"   {assistant_reply}")

        # Check for key elements
        key_elements = [
            ("ZERO knowledge", user_prompt),
            ("PLAY DUMB", user_prompt),
            ("provide a roadmap", assistant_reply),
            ("systematically collect", assistant_reply)
        ]

        print(f"\n🔍 Key Elements Check:")
        for element, text in key_elements:
            status = "✅" if element in text else "❌"
            print(f"   {status} {element}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_coder_integration():
    """Debug the coder integration"""
    print("\n🔗 Debugging Coder Integration")
    print("=" * 60)

    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Cannot test coder integration - Smart Map Request not available")
            return False

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")

        coder = Coder.create(
            main_model=model,
            edit_format="informative",  # Use valid edit format
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )

        print("✅ Coder instance created")

        # Test get_repo_messages directly
        repo_messages = coder.get_repo_messages()

        print(f"\n📋 Repo Messages Generated: {len(repo_messages)} messages")

        if not repo_messages:
            print("❌ No repo messages generated!")
            return False

        for i, msg in enumerate(repo_messages):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            print(f"\n--- Message {i+1} ({role}) ---")
            print(f"Length: {len(content)} characters")
            print(f"Preview: {content[:200]}...")

            # Check for our key elements
            if role == 'user' and 'PLAY DUMB' in content:
                print("✅ Found PLAY DUMB rule in user message")
            elif role == 'assistant' and 'roadmap' in content:
                print("✅ Found roadmap commitment in assistant message")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_message_flow():
    """Debug the complete message flow"""
    print("\n🔄 Debugging Complete Message Flow")
    print("=" * 60)

    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Cannot test message flow - Smart Map Request not available")
            return False

        # Create coder
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")

        coder = Coder.create(
            main_model=model,
            edit_format="informative",  # Use valid edit format
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )

        # Test format_chat_chunks
        chunks = coder.format_chat_chunks()

        print(f"✅ Chat chunks formatted")
        print(f"   System messages: {len(chunks.system) if chunks.system else 0}")
        print(f"   Examples: {len(chunks.examples) if chunks.examples else 0}")
        print(f"   Repo messages: {len(chunks.repo) if chunks.repo else 0}")
        print(f"   Done messages: {len(chunks.done) if chunks.done else 0}")
        print(f"   Current messages: {len(chunks.cur) if chunks.cur else 0}")

        # Check repo messages specifically
        if chunks.repo:
            print(f"\n📋 Repo Messages in Chunks:")
            for i, msg in enumerate(chunks.repo):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                print(f"   {i+1}. {role}: {len(content)} chars")

                # Check for our roadmap elements
                if 'roadmap' in content.lower():
                    print(f"      ✅ Contains 'roadmap'")
                if 'PLAY DUMB' in content:
                    print(f"      ✅ Contains 'PLAY DUMB'")
        else:
            print("❌ No repo messages in chunks!")

        # Test all_messages
        all_messages = chunks.all_messages()
        print(f"\n📨 Total messages to LLM: {len(all_messages)}")

        # Find repo messages in the complete flow
        repo_msg_positions = []
        for i, msg in enumerate(all_messages):
            content = msg.get('content', '')
            if 'PLAY DUMB' in content or 'roadmap' in content.lower():
                repo_msg_positions.append(i)
                print(f"   Position {i}: Found repo message ({msg.get('role', 'unknown')})")

        if repo_msg_positions:
            print(f"✅ Repo messages found at positions: {repo_msg_positions}")
        else:
            print("❌ Repo messages NOT found in final message flow!")

        return len(repo_msg_positions) > 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Debugging Repo Messages System")
    print("=" * 80)

    success1 = debug_repo_messages()
    success2 = debug_coder_integration()
    success3 = debug_message_flow()

    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL DEBUG CHECKS PASSED!")
        print("\n📋 Summary:")
        print("   ✅ Smart Map Request System is available")
        print("   ✅ Prompts contain Play Dumb and roadmap elements")
        print("   ✅ Coder generates repo messages correctly")
        print("   ✅ Repo messages are included in final message flow")
        print("\n🤔 If LLM is still not following the rules, the issue might be:")
        print("   1. System prompts overriding repo messages")
        print("   2. Model-specific behavior ignoring conversation history")
        print("   3. Message ordering issues")
        print("   4. The LLM model itself not respecting the instructions")
    else:
        print("❌ SOME DEBUG CHECKS FAILED!")
        print("   This explains why the LLM is not following the Play Dumb rules")

    print("=" * 80)
