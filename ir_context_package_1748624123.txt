# IR CONTEXT PACKAGE
# Generated: 2025-05-30 19:55:23
# Query: How does position management work?
# Task: Test caching system

============================================================

# USER QUERY
How does position management work?

# INTELLIGENT CONTEXT ANALYSIS
## Task: general_analysis
## Focus: Test caching system

## CRITICAL ENTITIES (5 most important)

### 1. process_all_positions (async_function)
- File: backtest\backtest_position_manager.py


- Criticality: high | Risk: medium
- **Calls**: ["items", "get", "strategy_class", "process_symbol", "SymbolConfig", "..."] (total: 19)
- **Used by**: ["backtest_runner"] (total: 1)
- **Side Effects**: modifies_state, network_io, modifies_container

### 2. open_position (async_function)
- File: order_management\order_executor.py


- Criticality: medium | Risk: medium
- **Calls**: ["subsection", "info", "lower", "ValueError", "symbol_info_tick", "..."] (total: 17)
- **Used by**: ["manual_position_handler", "position_entry_manager"] (total: 2)
- **Side Effects**: writes_log, database_io, modifies_file

### 3. close_position (function)
- File: order_management\order_simulator.py


- Criticality: medium | Risk: medium
- **Calls**: ["is_backtest_mode", "log", "get_instance", "_get_current_price", "_apply_slippage", "..."] (total: 11)
- **Used by**: ["test_backtest", "order_simulator"] (total: 2)
- **Side Effects**: modifies_container, writes_log, modifies_state

### 4. _analyze_trading_conditions (async_function)
- File: backtest\backtest_position_manager.py


- Criticality: medium | Risk: medium
- **Calls**: ["get", "_fetch_targets", "get_technical_indicators", "next", "items", "..."] (total: 16)
- **Used by**: ["backtest_position_manager", "position_entry_manager"] (total: 2)
- **Side Effects**: modifies_container, writes_log, modifies_state

### 5. handle_manual_position_changes (async_function)
- File: manual_operations\manual_position_handler.py


- Criticality: medium | Risk: medium
- **Calls**: ["positions_get", "log", "commit", "open_position", "notify_failure", "..."] (total: 7)
- **Used by**: ["position_exit_manager"] (total: 1)
- **Side Effects**: modifies_state, network_io, writes_log

## KEY IMPLEMENTATIONS (5 functions)
Complete code available on request for any function.

### 1. process_all_positions
```python
    async def process_all_positions(self, app, balance: float, current_prices=None) -> List[Dict]:
        """Process all symbols for potential position opening in backtest mode"""
        try:
            print("\n==== PROCESSING POSITION ENTRIES ====\n")

            # Update current prices if provided
            if current_prices:
                self.current_prices = current_prices
                print(f"Updated current prices: {self.current_prices}")

            symbol_configs = {}
            results = []

            for symbol, config in SYMBOLS_CONFIG.items():
                # Skip disabled symbols
                if not config.get('symbol_allowed', True):
                    print(f"Skipping {symbol} - trading disabled")
                    continue

                # Get strategy class
    # ... (implementation continues)
```

### 2. open_position
```python
    async def open_position(self, app, symbol: str, direction: str, volume: float, stop_loss: float = None, take_profit: float = None, risk_amount: float = None, strategy: str = None) -> TradeResult:
        """
        Open a trading position with comprehensive error handling and logging
        """
        try:
            from utils.terminal_logger import terminal_logger, LogLevel

            terminal_logger.subsection("ORDER PLACEMENT", LogLevel.INFO)
            terminal_logger.info(f"Symbol: {symbol}, Direction: {direction}, Volume: {volume}")

            # Validate direction
            if direction.lower() not in ['buy', 'sell']:
                raise ValueError(f"Invalid direction: {direction}")

    # ... (implementation continues)
```

### 3. close_position
```python
    def close_position(self, ticket: int, volume: Optional[float] = None, close_price: Optional[float] = None) -> Dict:
        """
        Simulate closing a position

        Args:
            ticket: Position ticket
            volume: Volume to close (None for full position)
            close_price: Price at which to close the position (optional)

        Returns:
            Simulated close result
        """
        if not self.mode_manager.is_backtest_mode():
            log("Order simulator can only be used in backtest mode")
            return {"retcode": -1, "comment": "Not in backtest mode"}

        # Check if position exists
        if ticket not in self.positions:
            log(f"Position {ticket} not found")
    # ... (implementation continues)
```

### 4. _analyze_trading_conditions
```python
    async def _analyze_trading_conditions(
        self,
        symbol: str,
        setup: Dict,
        price_data: Dict
    ) -> List[TradeSetup]:
        """Analyze trading conditions for a symbol"""
        try:
            print(f"\n==== ANALYZING TRADING CONDITIONS FOR {symbol} ====\n")

            print(f"Price data: {price_data}")

            current_time = price_data.get('candle_date_1h')
            print(f"Using current time for targets: {current_time}")

            targets = await self._fetch_targets(symbol, current_time)
            if not targets:
                print(f"No targets available for {symbol}")
                return []

            print(f"Targets for {symbol}: {targets}")

            technical_indicators = {}
            if self.technical_analysis_service:
    # ... (implementation continues)
```

### 5. handle_manual_position_changes
```python
    async def handle_manual_position_changes(self, app, symbol, existing_row):
        """
        Handles all manual position changes including restoring closed positions and removing unauthorized manual positions
        """
        try:
            ALLOW_MANUAL_TRADING = False
            # Get current MT5 positions for the symbol
            all_positions = mt5.positions_get()
            if all_positions is None:
                log(f"No positions found in MT5")
                return

            # Filter positions for current symbol
            positions = [pos for pos in all_positions if pos.symbol == symbol]
            mt5_position_count = len(positions)
            mt5_total_volume = sum(position.volume for position in positions)

            # Get database state
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 5 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: How does position management work?

Provide specific, actionable insights based on this focused context.
