#!/usr/bin/env python3
"""
Performance test for MAP_REQUEST optimization.

This script tests the performance improvements made to the Smart Map Request System
by comparing execution times and cache effectiveness.
"""

import os
import sys
import time
import json
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

try:
    from aider.smart_map_request_handler import SmartMapRequestHandler
    from aider.repomap import RepoMap
    from aider.models import Model
    from aider.io import InputOutput
    print("✅ Successfully imported optimized SmartMapRequestHandler")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MapRequestPerformanceTester:
    """Test performance improvements in MAP_REQUEST processing."""

    def __init__(self, project_path: str = "aider-main"):
        self.project_path = project_path
        self.io = InputOutput()

        # Create model and repo map
        self.model = Model("gpt-3.5-turbo")
        self.repo_map = RepoMap(
            map_tokens=8192,
            root=project_path,
            main_model=self.model,
            io=self.io,
            verbose=True
        )

        # Create smart map request handler
        self.handler = SmartMapRequestHandler(
            repo_map=self.repo_map,
            root_dir=project_path,
            io=self.io
        )

    def test_cold_start_performance(self):
        """Test performance with cold caches."""
        print("\n" + "="*60)
        print("🧪 TESTING COLD START PERFORMANCE (Complete Map Generation)")
        print("="*60)

        # Clear all caches
        self.handler.clear_caches()

        # Test request
        test_request = {
            "keywords": ["map", "request", "handler"],
            "type": "implementation",
            "scope": "all",
            "max_results": 8
        }

        print(f"📝 Test request: {json.dumps(test_request, indent=2)}")
        print("🔄 This will generate the complete repository map for the first time...")

        start_time = time.time()
        result = self.handler.handle_map_request(test_request)
        end_time = time.time()

        cold_start_time = (end_time - start_time) * 1000
        print(f"⏱️  Cold start time (includes complete map generation): {cold_start_time:.1f}ms")

        # Check if complete map was generated
        cache_stats = self.handler.get_cache_stats()
        if cache_stats['complete_repository_map_cached']:
            print(f"✅ Complete repository map generated in {cache_stats['complete_map_generation_time_ms']:.1f}ms")
        else:
            print("❌ Complete repository map was not generated")

        return cold_start_time, result

    def test_warm_cache_performance(self):
        """Test performance with warm caches."""
        print("\n" + "="*60)
        print("🔥 TESTING WARM CACHE PERFORMANCE (Using Pre-Generated Map)")
        print("="*60)

        # Test same request again (should use caches)
        test_request = {
            "keywords": ["map", "request", "handler"],
            "type": "implementation",
            "scope": "all",
            "max_results": 8
        }

        print(f"📝 Test request: {json.dumps(test_request, indent=2)}")
        print("🚀 This should use the pre-generated complete repository map...")

        start_time = time.time()
        result = self.handler.handle_map_request(test_request)
        end_time = time.time()

        warm_cache_time = (end_time - start_time) * 1000
        print(f"⏱️  Warm cache time (using pre-generated map): {warm_cache_time:.1f}ms")

        # Verify that complete map was used from cache
        cache_stats = self.handler.get_cache_stats()
        if cache_stats['complete_repository_map_cached']:
            print(f"✅ Used cached complete repository map (age: {cache_stats['complete_map_age_seconds']:.1f}s)")
        else:
            print("❌ Complete repository map cache was not used")

        return warm_cache_time, result

    def test_different_keywords_performance(self):
        """Test performance with different keywords (partial cache hit)."""
        print("\n" + "="*60)
        print("🔄 TESTING DIFFERENT KEYWORDS PERFORMANCE")
        print("="*60)

        # Test with different keywords
        test_request = {
            "keywords": ["coder", "base", "prompt"],
            "type": "implementation",
            "scope": "all",
            "max_results": 8
        }

        print(f"📝 Test request: {json.dumps(test_request, indent=2)}")

        start_time = time.time()
        result = self.handler.handle_map_request(test_request)
        end_time = time.time()

        different_keywords_time = (end_time - start_time) * 1000
        print(f"⏱️  Different keywords time: {different_keywords_time:.1f}ms")

        return different_keywords_time, result

    def analyze_cache_effectiveness(self):
        """Analyze cache statistics."""
        print("\n" + "="*60)
        print("📊 CACHE EFFECTIVENESS ANALYSIS")
        print("="*60)

        cache_stats = self.handler.get_cache_stats()

        print("Cache Status:")
        print(f"   Repository files cached: {cache_stats['repository_files_cached']}")
        print(f"   Repository terms cached: {cache_stats['repository_terms_cached']}")
        print(f"   Ranked tags cached: {cache_stats['ranked_tags_cached']}")
        print(f"   Embedding cache size: {cache_stats['embedding_cache_size']}")
        print(f"   Search cache size: {cache_stats['search_cache_size']}")

        if cache_stats['cache_age_seconds']:
            print(f"   Cache age: {cache_stats['cache_age_seconds']:.1f} seconds")

        return cache_stats

    def run_performance_tests(self):
        """Run all performance tests."""
        print("🚀 Starting MAP_REQUEST Performance Tests")
        print(f"📁 Project path: {self.project_path}")

        # Test 1: Cold start
        cold_time, cold_result = self.test_cold_start_performance()

        # Test 2: Warm cache
        warm_time, warm_result = self.test_warm_cache_performance()

        # Test 3: Different keywords
        diff_time, diff_result = self.test_different_keywords_performance()

        # Analyze caches
        cache_stats = self.analyze_cache_effectiveness()

        # Performance summary
        print("\n" + "="*60)
        print("📈 PERFORMANCE SUMMARY")
        print("="*60)

        speedup = cold_time / max(warm_time, 1)  # Avoid division by zero

        print(f"Cold start time:      {cold_time:.1f}ms")
        print(f"Warm cache time:      {warm_time:.1f}ms")
        print(f"Different keywords:   {diff_time:.1f}ms")
        print(f"Cache speedup:        {speedup:.1f}x")

        if speedup > 2:
            print("✅ Excellent cache performance!")
        elif speedup > 1.5:
            print("✅ Good cache performance!")
        else:
            print("⚠️  Cache performance could be improved")

        # Validate results
        if cold_result and warm_result:
            print("✅ All tests produced valid results")
        else:
            print("❌ Some tests failed to produce results")

        return {
            "cold_start_ms": cold_time,
            "warm_cache_ms": warm_time,
            "different_keywords_ms": diff_time,
            "cache_speedup": speedup,
            "cache_stats": cache_stats
        }


def main():
    """Main test function."""
    # Check if aider-main directory exists
    if not os.path.exists("aider-main"):
        print("❌ aider-main directory not found. Please run this script from the correct location.")
        return 1

    try:
        # Create tester and run tests
        tester = MapRequestPerformanceTester("aider-main")
        results = tester.run_performance_tests()

        print(f"\n🎯 Test completed successfully!")
        print(f"📊 Results: {json.dumps(results, indent=2, default=str)}")

        return 0

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
