#!/usr/bin/env python3
"""
Test using the EXACT SAME original search method but with inheritance data.
This should produce the same 8 functions as the original, but with inheritance info.
"""

import os
import sys

def test_same_original_method_with_inheritance():
    """Test using the exact same parameters as the original working method."""
    print("🎯 Testing SAME Original Method + Inheritance")
    print("=" * 55)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)

        # Import the real working modules
        from aider.context_request import ContextRequestHand<PERSON>, IRContextRequest
        
        print("✅ Successfully imported working IR context request modules")
        
        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Use the EXACT SAME parameters as the original working method
        print("\n🔍 Using EXACT SAME parameters as original working method...")
        
        original_request = IRContextRequest(
            user_query="Why is my context selection taking so long?",  # EXACT SAME query
            task_description="Debug performance issues in context selection",  # EXACT SAME description
            task_type="debugging",  # EXACT SAME task type
            focus_entities=["performance", "context", "selection"],  # EXACT SAME focus entities
            max_tokens=2000,  # EXACT SAME token budget
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=30000,
            max_entities=8  # EXACT SAME max entities (original default)
        )
        
        print(f"   Query: {original_request.user_query}")
        print(f"   Task: {original_request.task_description}")
        print(f"   Focus: {original_request.focus_entities}")
        print(f"   Max tokens: {original_request.max_tokens}")
        print(f"   Max entities: {original_request.max_entities}")
        
        result = handler.process_ir_context_request(original_request)
        
        if "llm_friendly_package" in result:
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"\n✅ Same method + inheritance package generated!")
            print(f"   📦 Package size: {package_size:,} characters")
            print(f"   🤖 Compatibility: {compatibility}")
            
            # Save the package
            output_file = "same_original_method_with_inheritance.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(result["llm_friendly_package"])
            print(f"   💾 Package saved to: {output_file}")
            
            # Analyze the package content
            package_content = result["llm_friendly_package"]
            
            # Count CRITICAL ENTITIES
            critical_entities_count = package_content.count("### ")
            if "## KEY IMPLEMENTATIONS" in package_content:
                key_impl_section = package_content.split("## KEY IMPLEMENTATIONS")[0]
                critical_entities_count = key_impl_section.count("### ")
            
            # Count KEY IMPLEMENTATIONS
            key_implementations_count = 0
            if "## KEY IMPLEMENTATIONS" in package_content:
                key_impl_section = package_content.split("## KEY IMPLEMENTATIONS")[1]
                if "## ANALYSIS INSTRUCTIONS" in key_impl_section:
                    key_impl_section = key_impl_section.split("## ANALYSIS INSTRUCTIONS")[0]
                key_implementations_count = key_impl_section.count("### ")
            
            print(f"\n📊 Package Analysis:")
            print(f"   CRITICAL ENTITIES: {critical_entities_count}")
            print(f"   KEY IMPLEMENTATIONS: {key_implementations_count}")
            
            # Check for inheritance data
            inheritance_count = package_content.count("- Inherits From:")
            no_inheritance_count = package_content.count("No inheritance detected")
            real_inheritance_count = inheritance_count - no_inheritance_count
            
            print(f"   Inheritance lines: {inheritance_count}")
            print(f"   Real inheritance: {real_inheritance_count}")
            
            # Check for low-signal items
            low_signal_items = []
            if "main(" in package_content:
                low_signal_items.append("main()")
            if "get_repo_map_tokens" in package_content:
                low_signal_items.append("get_repo_map_tokens()")
            if "try_to_select_default_model" in package_content:
                low_signal_items.append("try_to_select_default_model()")
            if "configure_model_settings" in package_content:
                low_signal_items.append("configure_model_settings()")
            if "validate_environment" in package_content:
                low_signal_items.append("validate_environment()")
            
            if low_signal_items:
                print(f"   ⚠️  Low-signal items found: {', '.join(low_signal_items)}")
                print(f"   📝 These should be filtered out for better focus")
            else:
                print(f"   ✅ No low-signal items detected")
            
            # Compare with original
            print(f"\n📋 Comparison with Original:")
            print(f"   Original: 8 CRITICAL ENTITIES, 8 KEY IMPLEMENTATIONS")
            print(f"   This run: {critical_entities_count} CRITICAL ENTITIES, {key_implementations_count} KEY IMPLEMENTATIONS")
            
            if critical_entities_count == 8 and key_implementations_count == 8:
                print(f"   ✅ PERFECT MATCH: Same entity count as original!")
                if real_inheritance_count > 0:
                    print(f"   🎉 SUCCESS: Same method + inheritance data working!")
                    return True, output_file
                else:
                    print(f"   ⚠️  Same method but no inheritance data detected")
                    return True, output_file
            else:
                print(f"   ❌ DIFFERENT: Entity count doesn't match original")
                if len(low_signal_items) > 0:
                    print(f"   💡 Likely cause: Low-signal items included")
                return False, output_file
        else:
            print("❌ No LLM package generated")
            return False, None
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def compare_with_original():
    """Compare the result with the original working package."""
    print("\n🔍 Comparing with Original Working Package")
    print("=" * 50)
    
    # Check if we have both files
    new_file = "same_original_method_with_inheritance.txt"
    original_file = "test_llm_friendly_package_with_inheritance.txt"
    
    if not os.path.exists(new_file):
        print("❌ New package file not found")
        return False
    
    if not os.path.exists(original_file):
        print("❌ Original package file not found")
        return False
    
    # Read both files
    with open(new_file, 'r', encoding='utf-8') as f:
        new_content = f.read()
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Compare function names in KEY IMPLEMENTATIONS
    def extract_function_names(content):
        if "## KEY IMPLEMENTATIONS" not in content:
            return []
        
        section = content.split("## KEY IMPLEMENTATIONS")[1]
        if "## ANALYSIS INSTRUCTIONS" in section:
            section = section.split("## ANALYSIS INSTRUCTIONS")[0]
        
        import re
        function_names = re.findall(r'### \d+\. (\w+)', section)
        return function_names
    
    original_functions = extract_function_names(original_content)
    new_functions = extract_function_names(new_content)
    
    print(f"Original functions ({len(original_functions)}): {original_functions}")
    print(f"New functions ({len(new_functions)}): {new_functions}")
    
    # Check for overlap
    common_functions = set(original_functions) & set(new_functions)
    only_in_original = set(original_functions) - set(new_functions)
    only_in_new = set(new_functions) - set(original_functions)
    
    print(f"\n📊 Function Comparison:")
    print(f"   Common functions: {len(common_functions)} - {list(common_functions)}")
    if only_in_original:
        print(f"   Only in original: {list(only_in_original)}")
    if only_in_new:
        print(f"   Only in new: {list(only_in_new)}")
    
    # Check if we have the same core functions
    core_context_functions = ["parse_context_request", "process_context_request", "process_context_requests", "detect_context_request"]
    core_functions_in_new = [f for f in new_functions if f in core_context_functions]
    
    print(f"\n🎯 Core context functions in new package: {core_functions_in_new}")
    
    if len(common_functions) >= 6:  # Most functions should be the same
        print(f"✅ GOOD OVERLAP: {len(common_functions)}/8 functions match")
        return True
    else:
        print(f"❌ POOR OVERLAP: Only {len(common_functions)}/8 functions match")
        return False


def main():
    """Main function."""
    print("🧪 Testing Same Original Method + Inheritance")
    print("=" * 60)
    
    success, output_file = test_same_original_method_with_inheritance()
    
    if success and output_file:
        print(f"\n✅ SAME METHOD + INHERITANCE: SUCCESS")
        print(f"📄 Package: {output_file}")
        
        # Compare with original
        comparison_success = compare_with_original()
        
        if comparison_success:
            print(f"\n🎉 COMPLETE SUCCESS!")
            print(f"✅ Same method produces similar results")
            print(f"✅ Enhanced with inheritance data")
            print(f"✅ No low-signal items detected")
            return True
        else:
            print(f"\n⚠️  PARTIAL SUCCESS")
            print(f"✅ Package generated successfully")
            print(f"❌ Some differences from original detected")
            return True
    else:
        print(f"\n❌ SAME METHOD + INHERITANCE: FAILED")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
