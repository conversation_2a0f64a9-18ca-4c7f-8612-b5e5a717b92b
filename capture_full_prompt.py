#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to capture the full prompt/context sent to the LLM.
This intercepts the actual messages before they're sent to the model.
"""

import sys
import os
import json

# Add aider to path
sys.path.insert(0, "aider-main")

def capture_full_llm_prompt():
    """Capture the complete prompt sent to the LLM."""
    
    print("🔍 CAPTURING FULL LLM PROMPT")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use external project path
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        repo = GitRepo(io, [], external_project)
        
        print(f"📁 Using project path: {external_project}")
        
        # Create coder
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192,
            verbose=True,
            dry_run=True
        )
        
        # Create a custom coder that captures the full prompt
        class PromptCapturingCoder(coder.__class__):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.captured_messages = None
                self.captured_prompt_text = None
            
            def send_message(self, inp):
                """Intercept send_message to capture the full prompt."""
                print(f"🎯 Intercepting send_message with input: '{inp[:100]}...'")
                
                # Let the normal flow happen to build the messages
                self.init_before_message()
                
                # Process the direct IR context if enabled
                enable_new_flow = getattr(self, 'enable_direct_ir_context', True)
                if enable_new_flow:
                    ir_context_result = self.process_direct_ir_context(inp)
                    if ir_context_result:
                        print("✅ IR context was injected")
                
                # Format the messages for the LLM
                try:
                    # This is the key method that formats all messages for the LLM
                    chat_chunks = self.format_chat_chunks(self.cur_messages)
                    all_messages = chat_chunks.all_messages
                    
                    print(f"📨 Captured {len(all_messages)} messages for LLM")
                    
                    # Save the complete message structure
                    self.captured_messages = all_messages
                    
                    # Create a human-readable version
                    prompt_parts = []
                    prompt_parts.append("=" * 80)
                    prompt_parts.append("FULL PROMPT SENT TO LLM")
                    prompt_parts.append("=" * 80)
                    
                    for i, msg in enumerate(all_messages, 1):
                        role = msg.get('role', 'unknown')
                        content = msg.get('content', '')
                        
                        prompt_parts.append(f"\n{'='*20} MESSAGE {i}: {role.upper()} {'='*20}")
                        prompt_parts.append(f"Length: {len(content)} characters")
                        prompt_parts.append("-" * 60)
                        prompt_parts.append(content)
                        prompt_parts.append("-" * 60)
                    
                    prompt_parts.append(f"\n{'='*20} END OF PROMPT {'='*20}")
                    prompt_parts.append(f"Total messages: {len(all_messages)}")
                    prompt_parts.append(f"Total characters: {sum(len(msg.get('content', '')) for msg in all_messages)}")
                    
                    self.captured_prompt_text = "\n".join(prompt_parts)
                    
                    # Save to files
                    with open("full_llm_prompt.txt", "w", encoding="utf-8") as f:
                        f.write(self.captured_prompt_text)
                    
                    with open("full_llm_messages.json", "w", encoding="utf-8") as f:
                        json.dump(all_messages, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 Saved full prompt to: full_llm_prompt.txt")
                    print(f"💾 Saved messages JSON to: full_llm_messages.json")
                    
                    # Show summary
                    print(f"\n📊 PROMPT SUMMARY:")
                    for i, msg in enumerate(all_messages, 1):
                        role = msg.get('role', 'unknown')
                        content_len = len(msg.get('content', ''))
                        content_preview = msg.get('content', '')[:100].replace('\n', ' ')
                        print(f"   {i}. {role}: {content_len} chars - {content_preview}...")
                    
                    # Check for IR context
                    ir_context_found = False
                    for msg in all_messages:
                        if 'Intelligent Context for Your Query' in msg.get('content', ''):
                            ir_context_found = True
                            print(f"   ✅ IR Context found in message")
                            break
                    
                    if not ir_context_found:
                        print(f"   ❌ No IR Context found in messages")
                    
                except Exception as e:
                    print(f"❌ Error capturing prompt: {e}")
                    import traceback
                    traceback.print_exc()
                
                # Don't actually send to LLM
                print("🛑 Stopping here to avoid LLM calls")
                return iter([])  # Return empty generator
        
        # Replace the coder's class
        coder.__class__ = PromptCapturingCoder
        coder.captured_messages = None
        coder.captured_prompt_text = None
        
        # Test with a query
        test_query = "How does position management work in the trading system?"
        print(f"\n📝 Testing with query: '{test_query}'")
        
        # Use run_stream to simulate GUI mode
        result_generator = coder.run_stream(test_query)
        list(result_generator)  # Consume the generator
        
        return True
        
    except Exception as e:
        print(f"❌ Error during prompt capture: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """Show how to use the captured files."""
    
    print("\n📋 HOW TO USE THE CAPTURED FILES")
    print("=" * 60)
    
    print("📄 **full_llm_prompt.txt**:")
    print("   • Human-readable version of the complete prompt")
    print("   • Shows each message with role and content")
    print("   • Easy to read and understand")
    print()
    
    print("📄 **full_llm_messages.json**:")
    print("   • Machine-readable JSON format")
    print("   • Exact structure sent to LLM API")
    print("   • Can be used for debugging or replay")
    print()
    
    print("🔍 **What to look for:**")
    print("   • System messages with IR context")
    print("   • User messages with your query")
    print("   • Repository map information")
    print("   • Trading system context vs aider context")
    print()
    
    print("📊 **Expected structure:**")
    print("   1. System message (basic instructions)")
    print("   2. System message (IR context with trading functions)")
    print("   3. User message (your query)")
    print("   4. Possibly more messages from conversation history")

if __name__ == "__main__":
    print("🔍 FULL LLM PROMPT CAPTURE TOOL")
    print("This will capture the exact prompt sent to the LLM\n")
    
    success = capture_full_llm_prompt()
    
    if success:
        print("\n🎉 Prompt capture completed successfully!")
        show_usage_instructions()
        
        print(f"\n📁 Files created:")
        print(f"   • full_llm_prompt.txt - Human-readable full prompt")
        print(f"   • full_llm_messages.json - Machine-readable messages")
        print(f"\nOpen these files to see exactly what the LLM receives!")
    else:
        print("\n❌ Prompt capture failed.")
        print("Check the error messages above.")
