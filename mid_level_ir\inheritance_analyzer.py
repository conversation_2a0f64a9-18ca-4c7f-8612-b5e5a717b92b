"""
Inheritance Analyzer - Analyzes class inheritance relationships and method overrides.

This module provides comprehensive analysis of object-oriented programming patterns,
including inheritance hierarchies, method overrides, and super() call detection.
"""

import ast
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict

from .ir_context import IRContext, EntityInfo


class InheritanceAnalyzer:
    """
    Analyzes inheritance relationships and method overrides in code.
    
    This analyzer performs comprehensive OOP analysis including:
    - Class inheritance hierarchy mapping
    - Method override detection across inheritance chains
    - Super() call detection within method implementations
    - Cross-module inheritance tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the inheritance analyzer with configuration.
        
        Args:
            config: Configuration dictionary for analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.track_cross_module = config.get('track_cross_module', True)
        
        # Analysis state
        self.class_hierarchy: Dict[str, List[str]] = defaultdict(list)  # class -> parents
        self.method_signatures: Dict[str, Dict[str, str]] = defaultdict(dict)  # class -> {method: signature}
        self.class_methods: Dict[str, List[str]] = defaultdict(list)  # class -> methods
        self.method_overrides: Dict[str, Dict[str, List[str]]] = defaultdict(dict)  # class -> {method: overridden_methods}
    
    def analyze(self, context: IRContext) -> IRContext:
        """
        Analyze inheritance relationships in the context.
        
        Args:
            context: The IR context containing parsed modules
            
        Returns:
            Updated context with inheritance analysis
        """
        if self.verbose:
            print(f"   Analyzing inheritance for {len(context.modules)} modules")
        
        # Step 1: Build class hierarchy map
        self._build_class_hierarchy(context)
        
        # Step 2: Analyze method relationships
        self._analyze_method_relationships(context)
        
        # Step 3: Detect super() calls
        self._detect_super_calls(context)
        
        # Step 4: Update entity inheritance data
        self._update_entity_inheritance_data(context)
        
        if self.verbose:
            total_classes = len(self.class_hierarchy)
            total_methods = sum(len(methods) for methods in self.class_methods.values())
            print(f"   Analyzed {total_classes} classes with {total_methods} methods")
        
        return context
    
    def _build_class_hierarchy(self, context: IRContext) -> None:
        """Build the complete class hierarchy map."""
        for module_info in context.modules.values():
            for entity in module_info.entities:
                if entity.type == "class":
                    class_name = entity.name
                    
                    # Extract base classes from parameters (stored during extraction)
                    base_classes = []
                    for param in entity.params:
                        if param.type_hint == "base_class":
                            base_classes.append(param.name)
                    
                    self.class_hierarchy[class_name] = base_classes
                    
                    if self.verbose and base_classes:
                        print(f"     Found class {class_name} inherits from: {base_classes}")
    
    def _analyze_method_relationships(self, context: IRContext) -> None:
        """Analyze method relationships across inheritance hierarchy."""
        # First pass: collect all methods for each class
        for module_info in context.modules.values():
            current_class = None
            
            for entity in module_info.entities:
                if entity.type == "class":
                    current_class = entity.name
                elif entity.type in ["function", "async_function"] and current_class:
                    # This is a method within a class
                    method_name = entity.name
                    self.class_methods[current_class].append(method_name)
                    
                    # Store method signature for override detection
                    signature = self._extract_method_signature(entity)
                    self.method_signatures[current_class][method_name] = signature
                    
                    # Set class_name for the method entity
                    entity.class_name = current_class
        
        # Second pass: detect overrides
        self._detect_method_overrides()
    
    def _extract_method_signature(self, entity: EntityInfo) -> str:
        """Extract a comparable method signature from an entity."""
        params = []
        for param in entity.params:
            param_str = param.name
            if param.type_hint:
                param_str += f": {param.type_hint}"
            if param.default_value:
                param_str += f" = {param.default_value}"
            params.append(param_str)
        
        return f"{entity.name}({', '.join(params)})"
    
    def _detect_method_overrides(self) -> None:
        """Detect method overrides across the inheritance hierarchy."""
        for class_name, methods in self.class_methods.items():
            parent_classes = self._get_all_parent_classes(class_name)
            
            for method_name in methods:
                # Check if this method exists in any parent class
                overridden_methods = []
                for parent_class in parent_classes:
                    if method_name in self.class_methods.get(parent_class, []):
                        overridden_methods.append(f"{parent_class}.{method_name}")
                
                if overridden_methods:
                    # Store override information
                    self.method_overrides[class_name][method_name] = overridden_methods
    
    def _get_all_parent_classes(self, class_name: str) -> List[str]:
        """Get all parent classes (including grandparents) for a class."""
        parents = []
        visited = set()
        
        def collect_parents(cls_name: str):
            if cls_name in visited:
                return  # Avoid circular inheritance
            visited.add(cls_name)
            
            direct_parents = self.class_hierarchy.get(cls_name, [])
            for parent in direct_parents:
                if parent not in parents:
                    parents.append(parent)
                    collect_parents(parent)  # Recursively collect grandparents
        
        collect_parents(class_name)
        return parents
    
    def _detect_super_calls(self, context: IRContext) -> None:
        """Detect super() calls within method implementations."""
        for module_info in context.modules.values():
            for entity in module_info.entities:
                if entity.type in ["function", "async_function"] and entity.ast_node:
                    # Check if this method contains super() calls
                    has_super = self._contains_super_call(entity.ast_node)
                    entity.calls_super = has_super
    
    def _contains_super_call(self, node: ast.AST) -> bool:
        """Check if an AST node contains a super() call."""
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                if isinstance(child.func, ast.Name) and child.func.id == "super":
                    return True
        return False
    
    def _update_entity_inheritance_data(self, context: IRContext) -> None:
        """Update entity objects with complete inheritance data."""
        for module_info in context.modules.values():
            for entity in module_info.entities:
                if entity.type == "class":
                    # Update class inheritance data
                    entity.inherits_from = self.class_hierarchy.get(entity.name, [])
                    
                elif entity.type in ["function", "async_function"] and entity.class_name:
                    # Update method inheritance data
                    class_name = entity.class_name
                    method_name = entity.name
                    
                    # Set method overrides
                    overrides = self.method_overrides.get(class_name, {}).get(method_name, [])
                    entity.method_overrides = overrides
                    
                    # Set overridden_by (methods in child classes that override this one)
                    overridden_by = self._find_overriding_methods(class_name, method_name)
                    entity.overridden_by = overridden_by
    
    def _find_overriding_methods(self, class_name: str, method_name: str) -> List[str]:
        """Find child classes that override a specific method."""
        overriding = []
        
        # Find all classes that inherit from this class
        for child_class, parents in self.class_hierarchy.items():
            if class_name in self._get_all_parent_classes(child_class):
                # Check if child class has this method
                if method_name in self.class_methods.get(child_class, []):
                    overriding.append(f"{child_class}.{method_name}")
        
        return overriding
