---
parent: Example chat transcripts
nav_order: 2
---

# Modify an open source 2048 game with aider

In this chat transcript, the user explores and modifies an open source [javascript 2048 game repo](https://github.com/gabrielecirulli/2048).

Notice that the LLM asks to see a particular source file, and aider automatically includes it in the chat session after getting permission from the user.

<div class="chat-transcript" markdown="1">

> $ git clone https://github.com/gabrielecirulli/2048  
> $ cd 2048  
> $ aider  

#### what is this repo?  

Based on the file names you provided, it seems like this repository is for a web-based game, possibly a 2048 game clone. The files include JavaScript files for game logic, HTML and CSS files for the user interface, and various meta files for icons and touch startup images.

If you need help with any specific part of the code or want to add new features, please let me know, and I'll be happy to assist you.


</div>
<div class="chat-transcript" markdown="1">
#### how does the scoring work?  

To help you with the scoring mechanism, I need to see the relevant code. Please provide the contents of the `js/game_manager.js` file, as it is likely to contain the scoring logic.


> js/game_manager.js  
> Add these files to the chat? y


Apologies for the oversight. I see the contents of the `js/game_manager.js` file now.

The scoring in this 2048 game works as follows:

1. When two tiles with the same value merge, they create a new tile with a value equal to the sum of the merged tiles' values.
2. The score is updated by adding the value of the new merged tile.

Here's the relevant code snippet from the `js/game_manager.js` file:

```javascript
if (next && next.value === tile.value && !next.mergedFrom) {
  var merged = new Tile(positions.next, tile.value * 2);
  merged.mergedFrom = [tile, next];

  self.grid.insertTile(merged);
  self.grid.removeTile(tile);

  // Converge the two tiles' positions
  tile.updatePosition(positions.next);

  // Update the score
  self.score += merged.value;

  // The mighty 2048 tile
  if (merged.value === 2048) self.won = true;
}
```

In this code snippet, when two tiles with the same value (`tile` and `next`) are found, they are merged into a new tile (`merged`). The value of the new tile is added to the current score (`self.score += merged.value`). If the merged tile's value is 2048, the game is considered won (`self.won = true`).


</div>
<div class="chat-transcript" markdown="1">
#### can we add some randomness? maybe 10% of the time give the user a 10x bonus?  

Sure, we can add a 10% chance of giving the user a 10x bonus when two tiles merge. To implement this, we'll modify the scoring logic in the `js/game_manager.js` file. Here's the step-by-step plan:

1. Generate a random number between 0 and 1.
2. Check if the random number is less than or equal to 0.1 (10% chance).
3. If the condition is met, multiply the merged tile's value by 10 before adding it to the score.

Here's the code change for the `js/game_manager.js` file:

```javascript
js/game_manager.js
<<<<<<< ORIGINAL
  // Update the score
  self.score += merged.value;
=======
  // Update the score with a 10% chance of 10x bonus
  var bonus = Math.random() <= 0.1 ? 10 : 1;
  self.score += merged.value * bonus;
>>>>>>> UPDATED
```

This change will give the user a 10x bonus 10% of the time when two tiles merge.

> Applied edit to js/game_manager.js  
> Commit eb9d882 aider: Added a 10% chance of giving the user a 10x bonus when two tiles merge.  

</div>
