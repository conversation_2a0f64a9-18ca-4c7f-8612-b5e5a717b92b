# USER QUERY
How does the intelligent context selection algorithm work? I need to understand the implementation.

# INTELLIGENT CONTEXT ANALYSIS
## Task: general_analysis
## Focus: How does the intelligent context selection algorithm work? I need to understand the implementation.

## CRITICAL ENTITIES (4 most important)

### 1. parse_context_request (function)
- File: context_request_handler.py
- Criticality: high | Risk: medium
- Calls: 13 | Used by: 6
- Side Effects: network_io, writes_log

### 2. process_context_requests (function)
- File: aider-main\aider\coders\base_coder.py
- Criticality: high | Risk: high
- Calls: 15 | Used by: 9
- Side Effects: network_io, writes_log, modifies_state

### 3. process_context_request (function)
- File: context_request_handler.py
- Criticality: high | Risk: high
- Calls: 9 | Used by: 10
- Side Effects: network_io, writes_log, database_io

### 4. process_context_requests (function)
- File: aider-main\aider\coders\base_coder_old.py
- Criticality: high | Risk: high
- Calls: 13 | Used by: 9
- Side Effects: network_io, writes_log, modifies_state

## KEY IMPLEMENTATIONS (5 functions)

### 1. parse_context_request
```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
    # ... (implementation continues)
```

### 2. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 3. process_context_request
```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
    # ... (implementation continues)
```

### 4. process_context_request
```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
    # ... (implementation continues)
```

### 5. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 4 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: How does the intelligent context selection algorithm work? I need to understand the implementation.

Provide specific, actionable insights based on this focused context.
