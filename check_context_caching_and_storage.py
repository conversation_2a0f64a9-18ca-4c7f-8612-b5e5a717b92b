#!/usr/bin/env python3
"""
Check where IR context packages are cached and stored for review.
"""

import sys
import os
import json
import time
from pathlib import Path

# Add aider to path
sys.path.insert(0, "aider-main")

def check_context_caching_system():
    """Check the IR context caching and storage system."""
    
    print("🔍 CHECKING IR CONTEXT CACHING AND STORAGE")
    print("=" * 60)
    
    try:
        from aider.context_request import <PERSON>textRequest<PERSON><PERSON><PERSON>, IRContextRequest
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        print(f"📁 Project path: {external_project}")
        
        # Create handler
        handler = ContextRequestHandler(external_project)
        
        # Check current cache state
        print(f"\n📊 CURRENT CACHE STATE:")
        print(f"   IR cache entries: {len(handler._ir_cache)}")
        print(f"   Context cache entries: {len(handler.cache)}")
        
        if handler._ir_cache:
            for project_path, timestamp in handler._ir_cache_timestamps.items():
                age = time.time() - timestamp
                print(f"   - {project_path}: {age:.1f}s old")
        
        # Generate a context request to see caching in action
        print(f"\n🎯 GENERATING CONTEXT REQUEST TO TEST CACHING")
        print("-" * 50)
        
        request = IRContextRequest(
            user_query="How does position management work?",
            task_description="Test caching system",
            task_type="general_analysis",
            focus_entities=["position", "management"],
            max_tokens=1000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=5
        )
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        print(f"✅ Generated context result")
        
        # Check cache state after generation
        print(f"\n📊 CACHE STATE AFTER GENERATION:")
        print(f"   IR cache entries: {len(handler._ir_cache)}")
        print(f"   Context cache entries: {len(handler.cache)}")
        
        # Check what's in the cache
        if handler.cache:
            print(f"\n📄 CONTEXT CACHE CONTENTS:")
            for cache_key in handler.cache.keys():
                print(f"   - {cache_key}")
        
        # Check if there are any saved files
        print(f"\n📁 CHECKING FOR SAVED CONTEXT FILES")
        print("-" * 50)
        
        current_dir = Path(".")
        
        # Look for IR-related files
        ir_files = list(current_dir.glob("*ir*.json"))
        context_files = list(current_dir.glob("*context*.json"))
        context_txt_files = list(current_dir.glob("*context*.txt"))
        llm_package_files = list(current_dir.glob("*llm*.txt"))
        
        print(f"📊 FOUND FILES:")
        print(f"   IR files: {len(ir_files)}")
        for f in ir_files:
            size = f.stat().st_size / 1024  # KB
            print(f"     - {f.name} ({size:.1f} KB)")
        
        print(f"   Context JSON files: {len(context_files)}")
        for f in context_files:
            size = f.stat().st_size / 1024  # KB
            print(f"     - {f.name} ({size:.1f} KB)")
        
        print(f"   Context text files: {len(context_txt_files)}")
        for f in context_txt_files:
            size = f.stat().st_size / 1024  # KB
            print(f"     - {f.name} ({size:.1f} KB)")
        
        print(f"   LLM package files: {len(llm_package_files)}")
        for f in llm_package_files:
            size = f.stat().st_size / 1024  # KB
            print(f"     - {f.name} ({size:.1f} KB)")
        
        # Save the current context result for review
        if "llm_friendly_package" in result:
            timestamp = int(time.time())
            filename = f"ir_context_package_{timestamp}.txt"
            
            with open(filename, "w", encoding="utf-8") as f:
                f.write("# IR CONTEXT PACKAGE\n")
                f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Query: {request.user_query}\n")
                f.write(f"# Task: {request.task_description}\n")
                f.write("\n" + "="*60 + "\n\n")
                f.write(result["llm_friendly_package"])
            
            print(f"\n💾 SAVED CURRENT CONTEXT PACKAGE:")
            print(f"   File: {filename}")
            print(f"   Size: {len(result['llm_friendly_package'])} characters")
        
        # Check if the system automatically saves context
        print(f"\n🔍 CHECKING AUTOMATIC CONTEXT SAVING")
        print("-" * 50)
        
        # Look for any automatic saving mechanisms
        # Check if there's a logs or cache directory
        possible_cache_dirs = [
            Path(".aider"),
            Path("cache"),
            Path("logs"),
            Path("context_cache"),
            Path("ir_cache"),
            Path.home() / ".aider",
            Path.home() / ".cache" / "aider"
        ]
        
        found_cache_dirs = []
        for cache_dir in possible_cache_dirs:
            if cache_dir.exists():
                found_cache_dirs.append(cache_dir)
                print(f"   Found cache directory: {cache_dir}")
                
                # List contents
                try:
                    contents = list(cache_dir.iterdir())
                    print(f"     Contents: {len(contents)} items")
                    for item in contents[:5]:  # Show first 5
                        if item.is_file():
                            size = item.stat().st_size / 1024  # KB
                            print(f"       - {item.name} ({size:.1f} KB)")
                        else:
                            print(f"       - {item.name}/ (directory)")
                    if len(contents) > 5:
                        print(f"       ... and {len(contents) - 5} more items")
                except PermissionError:
                    print(f"     Permission denied to list contents")
        
        if not found_cache_dirs:
            print(f"   No automatic cache directories found")
        
        # Summary and recommendations
        print(f"\n📊 CONTEXT CACHING SUMMARY")
        print("=" * 60)
        print(f"✅ IR data is cached in memory (TTL: {handler._ir_cache_ttl}s)")
        print(f"✅ Context requests are cached in memory (TTL: {handler.cache_ttl}s)")
        print(f"✅ Generated context packages can be manually saved")
        
        if ir_files or context_files or context_txt_files or llm_package_files:
            print(f"✅ Found existing context files for review")
        else:
            print(f"⚠️  No existing context files found")
        
        print(f"\n💡 RECOMMENDATIONS FOR CONTEXT REVIEW:")
        print(f"   1. Context packages are cached in memory during the session")
        print(f"   2. For permanent storage, manually save important packages")
        print(f"   3. Check these file patterns for saved context:")
        print(f"      - *ir*.json (IR data)")
        print(f"      - *context*.txt (context packages)")
        print(f"      - *llm*.txt (LLM packages)")
        print(f"   4. Current session context is available in handler.cache")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking context caching: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_cache_access_methods():
    """Show how to access cached context programmatically."""
    
    print(f"\n🔧 HOW TO ACCESS CACHED CONTEXT PROGRAMMATICALLY")
    print("=" * 60)
    
    print("""
# 1. Access IR cache (class-level, shared across instances)
from aider.context_request import ContextRequestHandler

# Check what's in the IR cache
print("IR Cache:", ContextRequestHandler._ir_cache.keys())

# Get cached IR data for a project
project_path = "your_project_path"
ir_data = ContextRequestHandler._get_ir_from_cache(project_path)

# 2. Access context request cache (instance-level)
handler = ContextRequestHandler(project_path)

# Check what's in the context cache
print("Context Cache:", handler.cache.keys())

# Get a specific cached result
cache_key = "ir_context_request:your_hash"
cached_result = handler._get_from_cache(cache_key)

# 3. Save context packages manually
def save_context_package(result, filename):
    if "llm_friendly_package" in result:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(result["llm_friendly_package"])
        print(f"Saved to {filename}")

# 4. List all cached context
def list_all_cached_context():
    handler = ContextRequestHandler(".")
    print("Cached context requests:")
    for key, value in handler.cache.items():
        if "llm_friendly_package" in value:
            size = len(value["llm_friendly_package"])
            print(f"  {key}: {size} characters")
""")

if __name__ == "__main__":
    success = check_context_caching_system()
    
    if success:
        show_cache_access_methods()
        print("\n✅ Context caching check completed!")
    else:
        print("\n❌ Context caching check failed.")
