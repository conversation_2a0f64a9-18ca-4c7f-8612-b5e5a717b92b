"""
IR Context - Shared data structures for the Mid-Level IR pipeline.

This module defines the core data structures that flow through the analysis pipeline,
providing a clean interface between modules and ensuring type safety.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set, Any, Union
from pathlib import Path
import ast


@dataclass
class ParameterInfo:
    """Information about a function parameter."""
    name: str
    type_hint: Optional[str] = None
    default_value: Optional[str] = None
    is_optional: bool = False


@dataclass
class ReturnInfo:
    """Information about a function's return value."""
    type_hint: Optional[str] = None
    description: Optional[str] = None


@dataclass
class EntityInfo:
    """Detailed information about a code entity (function, class, etc.)."""
    type: str  # "function", "class", "variable", "constant"
    name: str
    doc: Optional[str] = None

    # Enhanced parameter/return information
    params: List[ParameterInfo] = field(default_factory=list)
    returns: Optional[ReturnInfo] = None

    # Behavioral analysis
    calls: List[str] = field(default_factory=list)
    used_by: List[str] = field(default_factory=list)
    side_effects: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)

    # Risk assessment
    criticality: str = "low"  # "low", "medium", "high"
    change_risk: str = "low"  # "low", "medium", "high"

    # Inheritance and OOP analysis
    class_name: Optional[str] = None  # For methods: which class they belong to
    inherits_from: List[str] = field(default_factory=list)  # For classes: parent classes
    method_overrides: List[str] = field(default_factory=list)  # Methods this method overrides
    calls_super: bool = False  # Whether this method calls super()
    overridden_by: List[str] = field(default_factory=list)  # Child classes that override this

    # Metadata
    line_start: Optional[int] = None
    line_end: Optional[int] = None
    complexity: Optional[float] = None
    decorators: List[str] = field(default_factory=list)

    # AST node for further analysis
    ast_node: Optional[ast.AST] = field(default=None, repr=False)


@dataclass
class DependencyInfo:
    """Information about a module dependency."""
    module: str
    strength: str = "weak"  # "weak", "medium", "strong"
    import_type: str = "import"  # "import", "from_import", "relative"
    alias: Optional[str] = None
    imported_names: List[str] = field(default_factory=list)


@dataclass
class ModuleMetadata:
    """Metadata about a module."""
    doc_coverage: float = 0.0  # Percentage of entities with docstrings
    comment_density: float = 0.0  # Comments per line of code
    avg_complexity: float = 0.0  # Average cyclomatic complexity
    function_count: int = 0
    class_count: int = 0
    variable_count: int = 0
    most_critical_entity: Optional[str] = None
    test_coverage: Optional[float] = None


@dataclass
class ModuleInfo:
    """Complete information about a module."""
    name: str
    file: str
    loc: int = 0  # Lines of code
    
    # Dependencies
    dependencies: List[DependencyInfo] = field(default_factory=list)
    
    # Entities
    entities: List[EntityInfo] = field(default_factory=list)
    
    # Metadata
    metadata: Optional[ModuleMetadata] = None
    
    # AST for further analysis
    ast_tree: Optional[ast.AST] = field(default=None, repr=False)
    
    # File content for analysis
    source_code: Optional[str] = field(default=None, repr=False)


@dataclass
class IRContext:
    """
    Central context object that flows through the analysis pipeline.
    
    This object accumulates information as it passes through each analysis module,
    providing a clean interface for data sharing and state management.
    """
    
    # Project information
    project_path: Path
    project_name: Optional[str] = None
    
    # Modules being analyzed
    modules: Dict[str, ModuleInfo] = field(default_factory=dict)
    
    # Global analysis state
    all_files: List[Path] = field(default_factory=list)
    processed_files: Set[Path] = field(default_factory=set)
    
    # Cross-module analysis
    global_call_graph: Dict[str, Set[str]] = field(default_factory=dict)
    global_usage_map: Dict[str, Set[str]] = field(default_factory=dict)
    
    # Analysis configuration
    config: Dict[str, Any] = field(default_factory=dict)
    
    # Analysis results
    analysis_metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_module(self, module_info: ModuleInfo) -> None:
        """Add a module to the context."""
        self.modules[module_info.name] = module_info
    
    def get_module(self, name: str) -> Optional[ModuleInfo]:
        """Get a module by name."""
        return self.modules.get(name)
    
    def get_all_entities(self) -> List[EntityInfo]:
        """Get all entities across all modules."""
        entities = []
        for module in self.modules.values():
            entities.extend(module.entities)
        return entities
    
    def get_entities_by_type(self, entity_type: str) -> List[EntityInfo]:
        """Get all entities of a specific type."""
        return [entity for entity in self.get_all_entities() 
                if entity.type == entity_type]
    
    def find_entity(self, name: str, module_name: Optional[str] = None) -> Optional[EntityInfo]:
        """Find an entity by name, optionally within a specific module."""
        if module_name:
            module = self.get_module(module_name)
            if module:
                for entity in module.entities:
                    if entity.name == name:
                        return entity
        else:
            for entity in self.get_all_entities():
                if entity.name == name:
                    return entity
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get overall statistics about the analysis."""
        total_entities = len(self.get_all_entities())
        total_functions = len(self.get_entities_by_type("function"))
        total_classes = len(self.get_entities_by_type("class"))
        total_loc = sum(module.loc for module in self.modules.values())
        total_dependencies = sum(len(module.dependencies) for module in self.modules.values())
        
        return {
            "total_modules": len(self.modules),
            "total_entities": total_entities,
            "total_functions": total_functions,
            "total_classes": total_classes,
            "total_loc": total_loc,
            "total_dependencies": total_dependencies,
            "processed_files": len(self.processed_files),
        }


# Utility functions for working with IR data structures

def create_parameter_info(name: str, annotation: Optional[ast.AST] = None, 
                         default: Optional[ast.AST] = None) -> ParameterInfo:
    """Create a ParameterInfo from AST components."""
    type_hint = None
    if annotation:
        type_hint = ast.unparse(annotation) if hasattr(ast, 'unparse') else str(annotation)
    
    default_value = None
    is_optional = default is not None
    if default:
        try:
            default_value = ast.unparse(default) if hasattr(ast, 'unparse') else str(default)
        except:
            default_value = "<complex_default>"
    
    return ParameterInfo(
        name=name,
        type_hint=type_hint,
        default_value=default_value,
        is_optional=is_optional
    )


def create_return_info(annotation: Optional[ast.AST] = None) -> Optional[ReturnInfo]:
    """Create a ReturnInfo from AST annotation."""
    if not annotation:
        return None
    
    type_hint = None
    try:
        type_hint = ast.unparse(annotation) if hasattr(ast, 'unparse') else str(annotation)
    except:
        type_hint = "<complex_type>"
    
    return ReturnInfo(type_hint=type_hint)
