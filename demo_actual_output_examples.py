#!/usr/bin/env python3
"""
Demo showing ACTUAL output examples for each mode in the terminal.
This shows what developers and LLMs will actually see.
"""

import os
import sys
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

from aider_integration_service import AiderIntegrationService


def show_actual_usage_examples():
    """Show actual running examples of each output mode."""
    
    print("🚀 ACTUAL USAGE EXAMPLES")
    print("=" * 60)
    print("Here's what you actually get when using each mode:")
    print()
    
    service = AiderIntegrationService()
    
    feature_description = "Add user authentication with JWT tokens"
    focus_areas = ["user", "auth", "token"]
    
    # Example 1: Default (Concise) - for daily development
    print("📋 EXAMPLE 1: Default Developer Workflow")
    print("Code: service.find_relevant_code_for_feature(...)")
    print("-" * 50)
    
    try:
        # Suppress processing output, show only results
        import contextlib
        import io
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            results = service.find_relevant_code_for_feature(
                project_path=".",
                feature_description=feature_description,
                focus_areas=focus_areas
                # output_mode="concise" is default
            )
        
        print("ACTUAL OUTPUT:")
        print(json.dumps(results, indent=2)[:1000] + "...")  # Show first 1000 chars
        print(f"\nSize: {len(json.dumps(results))/1024:.1f} KB")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*60)
    
    # Example 2: LLM-Friendly - for AI agents
    print("\n📋 EXAMPLE 2: LLM-Friendly Mode (for AI agents)")
    print('Code: service.find_relevant_code_for_feature(..., output_mode="llm_friendly")')
    print("-" * 50)
    
    try:
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            results = service.find_relevant_code_for_feature(
                project_path=".",
                feature_description=feature_description,
                focus_areas=focus_areas,
                output_mode="llm_friendly"
            )
        
        print("ACTUAL OUTPUT:")
        print(json.dumps(results, indent=2))
        print(f"\nSize: {len(json.dumps(results))/1024:.1f} KB")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*60)
    
    # Example 3: Summary - for dashboards
    print("\n📋 EXAMPLE 3: Summary Mode (for dashboards)")
    print('Code: service.find_relevant_code_for_feature(..., output_mode="summary")')
    print("-" * 50)
    
    try:
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            results = service.find_relevant_code_for_feature(
                project_path=".",
                feature_description=feature_description,
                focus_areas=focus_areas,
                output_mode="summary"
            )
        
        print("ACTUAL OUTPUT:")
        print(json.dumps(results, indent=2))
        print(f"\nSize: {len(json.dumps(results))/1024:.1f} KB")
        
    except Exception as e:
        print(f"Error: {e}")


def show_practical_comparison():
    """Show side-by-side comparison of what each mode gives you."""
    
    print("\n\n🔍 PRACTICAL COMPARISON")
    print("=" * 60)
    print("Same feature request, different output modes:")
    print()
    
    service = AiderIntegrationService()
    
    feature_description = "Add file upload functionality"
    focus_areas = ["file", "upload"]
    
    modes = [
        ("summary", "Dashboard/Quick View"),
        ("llm_friendly", "AI Agent Consumption"), 
        ("concise", "Developer Daily Use"),
    ]
    
    results_data = {}
    
    for mode, purpose in modes:
        try:
            import contextlib
            import io
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                results = service.find_relevant_code_for_feature(
                    project_path=".",
                    feature_description=feature_description,
                    focus_areas=focus_areas,
                    output_mode=mode
                )
            
            results_data[mode] = results
            
        except Exception as e:
            results_data[mode] = {"error": str(e)}
    
    # Show comparison
    for mode, purpose in modes:
        print(f"\n📊 {mode.upper()} MODE ({purpose})")
        print("-" * 40)
        
        if "error" not in results_data[mode]:
            result = results_data[mode]
            
            if mode == "summary":
                print(f"Task: {result.get('task', 'N/A')}")
                print(f"Entities Found: {result.get('entities_found', 0)}")
                print(f"Confidence: {result.get('confidence', 0)}")
                print(f"Critical: {result.get('critical_count', 0)}")
                print(f"Safe: {result.get('safe_count', 0)}")
                print(f"Recommendation: {result.get('quick_recommendation', 'N/A')}")
                
            elif mode == "llm_friendly":
                print(f"Task: {result.get('task', 'N/A')}")
                analysis = result.get('analysis', {})
                print(f"Entities: {analysis.get('entities_analyzed', 0)}")
                print(f"Confidence: {analysis.get('confidence', 0)}")
                
                critical = result.get('critical_entities', [])
                if critical:
                    print(f"Top Critical: {critical[0].get('name', 'N/A')} ({critical[0].get('type', 'N/A')})")
                
                safe = result.get('safe_entities', [])
                if safe:
                    print(f"Top Safe: {safe[0].get('name', 'N/A')} ({safe[0].get('type', 'N/A')})")
                
                print(f"Guidance: {result.get('guidance', 'N/A')}")
                
            elif mode == "concise":
                summary = result.get('analysis_summary', {})
                print(f"Task: {summary.get('task', 'N/A')}")
                print(f"Entities: {summary.get('entities_selected', 0)}")
                print(f"Confidence: {summary.get('confidence', 0)}")
                print(f"Critical Entities: {len(result.get('critical_entities', []))}")
                print(f"Safe Entities: {len(result.get('safe_entities', []))}")
                print(f"Related Entities: {len(result.get('related_entities', []))}")
            
            # Show size
            size_kb = len(json.dumps(result)) / 1024
            print(f"Output Size: {size_kb:.1f} KB")
            
        else:
            print(f"Error: {results_data[mode]['error']}")


if __name__ == "__main__":
    print("🎯 INTELLIGENT CODE DISCOVERY - ACTUAL OUTPUT EXAMPLES")
    print("=" * 70)
    print("This shows the REAL output you get from each mode")
    print("(not just documentation examples)")
    print()
    
    show_actual_usage_examples()
    show_practical_comparison()
    
    print("\n\n🎉 NOW YOU CAN SEE THE ACTUAL OUTPUTS!")
    print("=" * 50)
    print("Key Takeaways:")
    print("• Summary mode: Ultra-compact for quick decisions")
    print("• LLM-friendly: Structured data perfect for AI agents")
    print("• Concise mode: Essential info for developers")
    print("• Each mode serves a specific real-world use case")
