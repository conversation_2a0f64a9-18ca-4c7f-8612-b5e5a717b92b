#!/usr/bin/env python

import os
import sys
import time
import re
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""

    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []

    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")

    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


class MockRepoMap:
    """Mock RepoMap class for testing."""

    def __init__(self):
        self.files = {
            "position_entry_manager.py": "content1",
            "position_calculator.py": "content2",
            "trading_system.py": "content3"
        }

    def get_repo_overview(self):
        """Get an overview of the repository."""
        return """
position_entry_manager.py:
│class PositionEntryManager:
│    def calculate_position_quantity(self, account_balance, risk_percentage, entry_price, stop_loss_price):
│    def validate_position_parameters(self, entry_price, stop_loss_price, risk_percentage):
│    def execute_position_entry(self, symbol, quantity, entry_price):
position_calculator.py:
│class PositionCalculator:
│    def calculate_position_size(self, account_balance, risk_amount, entry_price, stop_loss_price):
│    def calculate_risk_amount(self, account_balance, risk_percentage):
│    def calculate_price_difference(self, entry_price, stop_loss_price):
trading_system.py:
│class TradingSystem:
│    def place_trade(self, symbol, quantity, entry_price, stop_loss_price):
│    def calculate_trade_parameters(self, symbol, account_balance, risk_percentage, entry_price, stop_loss_price):
│    def execute_trade(self, trade_parameters):
"""


class MockAiderIntegrationService:
    """Mock AiderIntegrationService class for testing."""

    def __init__(self):
        pass

    def find_file_defining_symbol(self, project_path, symbol_name):
        """Find the file that defines the given symbol."""
        if symbol_name == "PositionEntryManager.calculate_position_quantity":
            return "position_entry_manager.py"
        elif symbol_name == "PositionCalculator.calculate_position_size":
            return "position_calculator.py"
        else:
            return None

    def process_context_request(self, request):
        """
        Process a context request, extracting the requested symbols and their dependencies.
        """
        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            # Extract the symbol name (handle class.method format)
            symbol_parts = symbol.name.split('.')
            if len(symbol_parts) > 1:
                # This is a class.method format
                class_name = symbol_parts[0]
                method_name = symbol_parts[1]

                # Find the file containing the symbol
                file_path = self.find_file_defining_symbol("", symbol.name)
                if not file_path:
                    continue

                # Extract the method content
                content = self.extract_symbol_content(method_name, file_path, "")
                if not content:
                    continue

                # Extract essential imports
                essential_imports = self.extract_essential_imports("", file_path)

                # Extract containing class
                containing_class = self.extract_containing_class("", file_path, method_name)

                # Extract usage contexts
                usage_contexts = self.extract_usage_contexts("", method_name, file_path)

                # Add the extracted symbol to the result
                result["extracted_symbols"].append({
                    "symbol_name": symbol.name,
                    "file_path": file_path,
                    "content": content,
                    "essential_imports": essential_imports,
                    "containing_class": containing_class
                })

                # Add dependency snippets
                for usage in usage_contexts:
                    result["dependency_snippets"].append({
                        "file_path": usage.get("file_path", ""),
                        "symbol_name": usage.get("symbol_name", ""),
                        "content": usage.get("content", ""),
                        "usage_type": usage.get("usage_type", "unknown")
                    })

        return result

    def extract_symbol_content(self, symbol_name, file_path, project_path):
        """Extract the content of a symbol from a file."""
        if symbol_name == "calculate_position_quantity" and file_path == "position_entry_manager.py":
            return """
    def calculate_position_quantity(self, account_balance, risk_percentage, entry_price, stop_loss_price):
        # Calculate the position quantity based on account balance, risk percentage, entry price, and stop loss price.
        #
        # Args:
        #     account_balance (float): The account balance
        #     risk_percentage (float): The risk percentage (0-100)
        #     entry_price (float): The entry price
        #     stop_loss_price (float): The stop loss price
        #
        # Returns:
        #     float: The position quantity
        # Validate the position parameters
        if not self.validate_position_parameters(entry_price, stop_loss_price, risk_percentage):
            return 0

        # Calculate the risk amount
        risk_amount = account_balance * (risk_percentage / 100)

        # Calculate the price difference
        price_difference = abs(entry_price - stop_loss_price)

        # Calculate the position quantity
        if price_difference == 0:
            return 0

        position_quantity = risk_amount / price_difference

        return position_quantity
"""
        elif symbol_name == "calculate_position_size" and file_path == "position_calculator.py":
            return """
    def calculate_position_size(self, account_balance, risk_amount, entry_price, stop_loss_price):
        # Calculate the position size based on account balance, risk amount, entry price, and stop loss price.
        #
        # Args:
        #     account_balance (float): The account balance
        #     risk_amount (float): The risk amount
        #     entry_price (float): The entry price
        #     stop_loss_price (float): The stop loss price
        #
        # Returns:
        #     float: The position size
        # Calculate the price difference
        price_difference = self.calculate_price_difference(entry_price, stop_loss_price)

        # Calculate the position size
        if price_difference == 0:
            return 0

        position_size = risk_amount / price_difference

        return position_size
"""
        else:
            return None

    def extract_essential_imports(self, project_path, file_path):
        """Extract essential imports from a file."""
        if file_path == "position_entry_manager.py":
            return "from position_calculator import PositionCalculator"
        elif file_path == "position_calculator.py":
            return "import math"
        else:
            return None

    def extract_containing_class(self, project_path, file_path, symbol_name):
        """Extract the containing class of a method."""
        if file_path == "position_entry_manager.py" and symbol_name == "calculate_position_quantity":
            return "PositionEntryManager"
        elif file_path == "position_calculator.py" and symbol_name == "calculate_position_size":
            return "PositionCalculator"
        else:
            return None

    def extract_usage_contexts(self, project_path, symbol_name, file_path):
        """Extract usage contexts of a symbol."""
        if symbol_name == "calculate_position_quantity" and file_path == "position_entry_manager.py":
            return [
                {
                    "file_path": "trading_system.py",
                    "symbol_name": "calculate_trade_parameters",
                    "content": """
    def calculate_trade_parameters(self, symbol, account_balance, risk_percentage, entry_price, stop_loss_price):
        # Calculate trade parameters.
        #
        # Args:
        #     symbol (str): The trading symbol
        #     account_balance (float): The account balance
        #     risk_percentage (float): The risk percentage
        #     entry_price (float): The entry price
        #     stop_loss_price (float): The stop loss price
        #
        # Returns:
        #     dict: The trade parameters
        position_manager = PositionEntryManager()
        quantity = position_manager.calculate_position_quantity(account_balance, risk_percentage, entry_price, stop_loss_price)

        return {
            "symbol": symbol,
            "quantity": quantity,
            "entry_price": entry_price,
            "stop_loss_price": stop_loss_price
        }
""",
                    "usage_type": "function_call"
                }
            ]
        elif symbol_name == "calculate_position_size" and file_path == "position_calculator.py":
            return [
                {
                    "file_path": "position_entry_manager.py",
                    "symbol_name": "calculate_position_quantity",
                    "content": """
    def calculate_position_quantity(self, account_balance, risk_percentage, entry_price, stop_loss_price):
        # Calculate the risk amount
        risk_amount = account_balance * (risk_percentage / 100)

        # Use the position calculator to calculate the position size
        calculator = PositionCalculator()
        position_size = calculator.calculate_position_size(account_balance, risk_amount, entry_price, stop_loss_price)

        return position_size
""",
                    "usage_type": "function_call"
                }
            ]
        else:
            return []


def main():
    print("\n=== Testing Augmented Prompt Content ===")

    # Create a mock integration service
    aider_service = MockAiderIntegrationService()

    # Get the project path
    project_path = os.getcwd()

    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)

    # Create a sample context request for the position quantity calculator
    context_request = ContextRequest(
        original_user_query_context="User is asking about the position quantity calculator",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="PositionEntryManager.calculate_position_quantity",
                file_hint="position_entry_manager.py"
            )
        ],
        reason_for_request="To analyze the implementation of the position quantity calculator function"
    )

    # Get the repository overview
    repo_map = MockRepoMap()
    repo_overview = repo_map.get_repo_overview()

    # Process the context request using the mock service
    print("\nProcessing context request...")
    extracted_context = aider_service.process_context_request(context_request)

    # Generate the augmented prompt using the template renderer
    from aider.context_request.aider_template_renderer import AiderTemplateRenderer
    template_renderer = AiderTemplateRenderer()
    augmented_prompt = template_renderer.render_augmented_prompt(
        original_query="How does the position quantity calculator work?",
        repo_overview=repo_overview,
        extracted_context=extracted_context
    )

    # Print the augmented prompt
    print("\n=== Augmented Prompt Content ===")
    print(augmented_prompt)

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
