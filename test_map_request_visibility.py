#!/usr/bin/env python3
"""
Test to demonstrate the enhanced MAP_REQUEST visibility showing exactly
what repository map slice gets sent to the LLM.
"""

import os
import sys
import json

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_map_request_visibility():
    """Test MAP_REQUEST with enhanced visibility to see the actual map slice sent to LLM."""
    print("🔍 Testing MAP_REQUEST Visibility")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Smart Map Request modules imported successfully")
        
        # Create components with verbose output
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=True
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )
        
        print("✅ Smart Map Request Handler created successfully")
        
        # Test 1: Simple function search (like the user's original query)
        print("\n🧪 Test 1: Function Search - 'close_position_based_on_conditions'")
        print("-" * 80)
        
        map_request_1 = {
            "keywords": ["close_position_based_on_conditions", "position", "close", "conditions", "trading", "executor"],
            "type": "implementation",
            "scope": "all",
            "max_results": 8
        }
        
        print(f"📝 MAP_REQUEST: {json.dumps(map_request_1, indent=2)}")
        
        # Process the request - this will show all the debugging output
        result_1 = handler.handle_map_request(map_request_1)
        
        print(f"\n📊 Result 1 Summary:")
        print(f"   Characters: {len(result_1)}")
        print(f"   Lines: {len(result_1.split(chr(10)))}")
        print(f"   Words: {len(result_1.split())}")
        
        # Test 2: Class search
        print("\n🧪 Test 2: Class Search - 'Coder'")
        print("-" * 80)
        
        map_request_2 = {
            "keywords": ["Coder", "base_coder", "class", "implementation"],
            "type": "implementation", 
            "scope": "all",
            "max_results": 5
        }
        
        print(f"📝 MAP_REQUEST: {json.dumps(map_request_2, indent=2)}")
        
        # Process the request
        result_2 = handler.handle_map_request(map_request_2)
        
        print(f"\n📊 Result 2 Summary:")
        print(f"   Characters: {len(result_2)}")
        print(f"   Lines: {len(result_2.split(chr(10)))}")
        print(f"   Words: {len(result_2.split())}")
        
        # Test 3: Broad search
        print("\n🧪 Test 3: Broad Search - 'repository mapping'")
        print("-" * 80)
        
        map_request_3 = {
            "keywords": ["repository", "mapping", "repomap", "tags"],
            "type": "implementation",
            "scope": "all", 
            "max_results": 10
        }
        
        print(f"📝 MAP_REQUEST: {json.dumps(map_request_3, indent=2)}")
        
        # Process the request
        result_3 = handler.handle_map_request(map_request_3)
        
        print(f"\n📊 Result 3 Summary:")
        print(f"   Characters: {len(result_3)}")
        print(f"   Lines: {len(result_3.split(chr(10)))}")
        print(f"   Words: {len(result_3.split())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in MAP_REQUEST visibility test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_map_request_efficiency_analysis():
    """Analyze the efficiency of different MAP_REQUEST strategies."""
    print("\n🔍 MAP_REQUEST Efficiency Analysis")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False  # Less verbose for analysis
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )
        
        # Test different keyword strategies
        test_cases = [
            {
                "name": "Specific Function Name",
                "keywords": ["close_position_based_on_conditions"],
                "expected_efficiency": "High"
            },
            {
                "name": "Function + Context",
                "keywords": ["close_position_based_on_conditions", "position", "close", "conditions"],
                "expected_efficiency": "High"
            },
            {
                "name": "Generic Terms",
                "keywords": ["function", "method", "implementation"],
                "expected_efficiency": "Low"
            },
            {
                "name": "Domain Specific",
                "keywords": ["trading", "position", "executor", "manager"],
                "expected_efficiency": "Medium"
            },
            {
                "name": "Class Focused",
                "keywords": ["Coder", "base_coder", "class"],
                "expected_efficiency": "High"
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n🧪 Testing: {test_case['name']}")
            print(f"   Keywords: {test_case['keywords']}")
            print(f"   Expected: {test_case['expected_efficiency']} efficiency")
            
            map_request = {
                "keywords": test_case['keywords'],
                "type": "implementation",
                "scope": "all",
                "max_results": 8
            }
            
            import time
            start_time = time.time()
            result = handler.handle_map_request(map_request)
            end_time = time.time()
            
            # Analyze result quality
            char_count = len(result)
            line_count = len(result.split('\n'))
            word_count = len(result.split())
            processing_time = (end_time - start_time) * 1000
            
            # Simple efficiency score based on result size and processing time
            if char_count > 0:
                efficiency_score = min(100, (char_count / processing_time) * 10)
            else:
                efficiency_score = 0
            
            results.append({
                "name": test_case['name'],
                "keywords": test_case['keywords'],
                "char_count": char_count,
                "line_count": line_count,
                "word_count": word_count,
                "processing_time": processing_time,
                "efficiency_score": efficiency_score,
                "expected": test_case['expected_efficiency']
            })
            
            print(f"   📊 Results: {char_count} chars, {line_count} lines, {processing_time:.1f}ms")
            print(f"   📈 Efficiency Score: {efficiency_score:.1f}")
        
        # Summary
        print(f"\n📊 EFFICIENCY ANALYSIS SUMMARY")
        print("=" * 80)
        
        for result in sorted(results, key=lambda x: x['efficiency_score'], reverse=True):
            print(f"🏆 {result['name']}: {result['efficiency_score']:.1f} points")
            print(f"   Keywords: {result['keywords']}")
            print(f"   Output: {result['char_count']} chars in {result['processing_time']:.1f}ms")
            print(f"   Expected: {result['expected']} efficiency")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error in efficiency analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run MAP_REQUEST visibility and efficiency tests."""
    print("🚀 MAP_REQUEST Visibility & Efficiency Testing")
    print("=" * 100)
    
    tests = [
        ("MAP_REQUEST Visibility", test_map_request_visibility),
        ("MAP_REQUEST Efficiency Analysis", test_map_request_efficiency_analysis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 MAP_REQUEST TESTING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 MAP_REQUEST VISIBILITY ENHANCED!")
        print("\n📋 Now you can see:")
        print("  ✅ Exact MAP_REQUEST parameters")
        print("  ✅ Search results with relevance scores")
        print("  ✅ Complete repository map slice sent to LLM")
        print("  ✅ Map slice statistics (characters, words, lines)")
        print("  ✅ Processing time and efficiency metrics")
        print("\n🎯 You can now evaluate Smart Map Request efficiency!")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("   Check the output above for details")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
