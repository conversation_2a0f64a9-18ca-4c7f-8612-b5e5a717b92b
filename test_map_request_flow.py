#!/usr/bin/env python3
"""
Test script to verify MAP_REQUEST flow and automatic response handling.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

from aider.coders.base_coder import Coder
from aider.io import InputOutput
from aider.models import Model
from aider import utils

def test_map_request_flow():
    """Test the complete MAP_REQUEST flow to see if augmented prompt is sent automatically."""

    print("🧪 Testing MAP_REQUEST Flow")
    print("=" * 60)

    # Create a mock model for testing
    class TestModel(Model):
        def __init__(self):
            self.name = "test-model"
            self.use_repo_map = True
            self.streaming = True
            self.edit_format = "informative"
            self.cache_control = False
            self.weak_model = None
            self.max_chat_history_tokens = 1000
            self.reasoning_tag = "reasoning"
            self.system_prompt_prefix = ""
            self.info = {
                "max_input_tokens": 8000,
                "max_tokens": 4000
            }

        def commit_message_models(self):
            return [self]

        def token_count(self, text):
            return len(text.split())

        def send_completion(self, messages, functions=None, stream=False, temperature=0):
            # Mock completion that returns a simple response
            class MockCompletion:
                def __init__(self, content):
                    self.content = content

                def __iter__(self):
                    yield {"choices": [{"delta": {"content": self.content}}]}

            # Return a hash and the completion
            import hashlib
            hash_obj = hashlib.md5(str(messages).encode())
            return hash_obj, MockCompletion("Test response from LLM")

    # Create IO and coder
    io = InputOutput(pretty=False, yes=True)
    model = TestModel()

    try:
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io
        )

        print("✅ Coder created successfully")

        # Test content with MAP_REQUEST
        test_content = '''I need to understand the repository structure.

{MAP_REQUEST: {"keywords": ["backtest", "trading", "strategy"], "type": "implementation", "scope": "all", "max_results": 5}}'''

        user_message = "How does the backtesting system work?"

        print(f"\n🔍 Testing MAP_REQUEST processing...")
        print(f"📝 Test content: {test_content}")
        print(f"👤 User message: {user_message}")

        # Process the MAP_REQUEST
        cleaned_content, map_prompt = coder.process_map_requests(test_content, user_message)

        if map_prompt:
            print("\n✅ MAP_REQUEST processed successfully")
            print(f"📝 Cleaned content length: {len(cleaned_content)}")
            print(f"📋 Map prompt length: {len(map_prompt)}")

            # Show preview of the map prompt
            print(f"\n📋 Map prompt preview (first 500 chars):")
            print("-" * 50)
            print(map_prompt[:500])
            print("-" * 50)

            # Check if the map prompt contains the expected structure
            if "Based on your map request" in map_prompt:
                print("✅ Map prompt has correct structure")
            else:
                print("❌ Map prompt structure is incorrect")

            if "Now please answer the original user query" in map_prompt:
                print("✅ Map prompt includes original query instruction")
            else:
                print("❌ Map prompt missing original query instruction")

            # Test the recursive send_message call
            print(f"\n🔄 Testing recursive send_message call...")
            try:
                # This should simulate what happens in the actual flow
                response_chunks = list(coder.send_message(map_prompt))
                print(f"✅ Recursive call completed, got {len(response_chunks)} chunks")

                if response_chunks:
                    print(f"📝 Response preview: {str(response_chunks[0])[:200]}...")
                else:
                    print("⚠️  No response chunks received")

            except Exception as e:
                print(f"❌ Error in recursive call: {e}")

        else:
            print("❌ MAP_REQUEST not processed")

    except Exception as e:
        print(f"❌ Error creating coder: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_map_request_flow()
