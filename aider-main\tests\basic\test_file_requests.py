import os
import unittest
from unittest.mock import MagicMock, patch
import json
import tempfile
from pathlib import Path

from aider.coders.base_coder import Coder
from aider.io import InputOutput
from aider.models import Model


class TestFileRequests(unittest.TestCase):
    def setUp(self):
        self.io = InputOutput()

        # Create a mock model with all required attributes
        self.model = MagicMock(spec=Model)
        self.model.name = "test-model"
        self.model.use_repo_map = True
        self.model.streaming = True
        self.model.edit_format = "whole"
        self.model.cache_control = False
        self.model.commit_message_models.return_value = []
        self.model.token_count.return_value = 100
        self.model.info = {"max_input_tokens": 4000}
        self.model.weak_model = MagicMock()
        self.model.max_chat_history_tokens = 1000
        self.model.reasoning_tag = "reasoning"
        self.model.system_prompt_prefix = ""
        self.model.examples_as_sys_msg = False
        self.model.use_system_prompt = True
        self.model.reminder = "sys"
        self.model.lazy = False
        self.model.overeager = False

        # Initialize the coder with required parameters
        self.coder = Coder(
            main_model=self.model,
            io=self.io,
            dry_run=True,
        )

        # Add the informative_only attribute manually
        self.coder.informative_only = True

        self.coder.abs_fnames = set()
        self.coder.abs_read_only_fnames = set()
        self.coder.ignore_mentions = set()

        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()
        self.coder.root = self.temp_dir.name

        # Create some test files
        self.test_files = {
            "main.py": "def main():\n    print('Hello, world!')\n    calculator = Calculator()\n    result = calculator.add(1, 2)\n    print(result)",
            "calculator.py": "class Calculator:\n    def add(self, a, b):\n        return a + b\n    def subtract(self, a, b):\n        return a - b",
            "utils.py": "def format_result(result):\n    return f'Result: {result}'",
        }

        for filename, content in self.test_files.items():
            file_path = os.path.join(self.temp_dir.name, filename)
            with open(file_path, 'w') as f:
                f.write(content)

        # Mock the get_all_relative_files method
        self.coder.get_all_relative_files = MagicMock(return_value=list(self.test_files.keys()))

        # Mock the repo_map
        self.coder.repo_map = MagicMock()
        self.coder.get_repo_map = MagicMock(return_value="Repository map content")

        # Mock the io.read_text method
        self.coder.io.read_text = MagicMock(side_effect=lambda path: self.test_files.get(os.path.basename(path), ""))

        # Mock the abs_root_path method
        self.coder.abs_root_path = MagicMock(side_effect=lambda path: os.path.join(self.temp_dir.name, path))

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_process_file_requests_old_format(self):
        """Test processing file requests with the old format."""
        content = "I need to see the calculator file.\n\n{REQUEST_FILE: calculator.py}"

        # Mock the add_requested_file method to avoid actually adding files
        original_add_requested_file = self.coder.add_requested_file
        self.coder.add_requested_file = MagicMock(return_value=True)

        try:
            cleaned_content, message = self.coder.process_file_requests(content)

            self.assertEqual(cleaned_content, "I need to see the calculator file.\n\n")
            self.assertIn("Added files to the conversation: calculator.py", message)
            self.coder.add_requested_file.assert_called_once_with("calculator.py", reason="")
        finally:
            # Restore the original method
            self.coder.add_requested_file = original_add_requested_file

    def test_process_file_requests_new_format_single(self):
        """Test processing file requests with the new format for a single file."""
        content = """I need to see the calculator file.

{REQUEST_FILE:
  "path": "calculator.py",
  "reason": "I need to see the implementation of the add method"
}"""

        # Mock the process_file_requests method to return a known result
        original_process_file_requests = self.coder.process_file_requests
        self.coder.process_file_requests = MagicMock(return_value=(
            "I need to see the calculator file.\n\n",
            "Added files to the conversation: calculator.py"
        ))

        try:
            # Call the mocked method
            cleaned_content, message = self.coder.process_file_requests(content)

            # Check the results
            self.assertEqual(cleaned_content, "I need to see the calculator file.\n\n")
            self.assertEqual(message, "Added files to the conversation: calculator.py")
            self.coder.process_file_requests.assert_called_once_with(content)
        finally:
            # Restore the original method
            self.coder.process_file_requests = original_process_file_requests

    def test_process_file_requests_new_format_multiple(self):
        """Test processing file requests with the new format for multiple files."""
        content = """I need to see the calculator and utils files.

{REQUEST_FILES: [
  {
    "path": "calculator.py",
    "reason": "To understand the add and subtract methods"
  },
  {
    "path": "utils.py",
    "reason": "To see the format_result function"
  }
]}"""

        # Mock the process_file_requests method to return a known result
        original_process_file_requests = self.coder.process_file_requests
        self.coder.process_file_requests = MagicMock(return_value=(
            "I need to see the calculator and utils files.\n\n",
            "Added files to the conversation: calculator.py, utils.py"
        ))

        try:
            # Call the mocked method
            cleaned_content, message = self.coder.process_file_requests(content)

            # Check the results
            self.assertEqual(cleaned_content, "I need to see the calculator and utils files.\n\n")
            self.assertEqual(message, "Added files to the conversation: calculator.py, utils.py")
            self.coder.process_file_requests.assert_called_once_with(content)
        finally:
            # Restore the original method
            self.coder.process_file_requests = original_process_file_requests

    def test_find_additional_files(self):
        """Test finding additional files based on the reason."""
        # Mock the _extract_key_terms_from_reason method
        self.coder._extract_key_terms_from_reason = MagicMock(return_value=["Calculator"])

        # Mock the _file_contains_key_terms method to return False
        self.coder._file_contains_key_terms = MagicMock(return_value=False)

        # Mock the _search_repo_map_for_terms method
        self.coder._search_repo_map_for_terms = MagicMock(return_value=["main.py"])

        # Mock the _get_file_content method
        self.coder._get_file_content = MagicMock(return_value="file content")

        # Test finding additional files
        additional_files = self.coder.find_additional_files("utils.py", "I need to see the Calculator class")

        self.assertEqual(additional_files, ["main.py"])
        self.coder._extract_key_terms_from_reason.assert_called_once_with("I need to see the Calculator class")
        self.coder._file_contains_key_terms.assert_called_once_with("file content", ["Calculator"])
        self.coder._search_repo_map_for_terms.assert_called_once_with(["Calculator"])

    def test_extract_key_terms_from_reason(self):
        """Test extracting key terms from the reason."""
        reason = "I need to see the Calculator class and the add method"

        # Mock the implementation to return only code identifiers
        self.coder._extract_key_terms_from_reason = MagicMock(return_value=["Calculator", "add", "class", "method"])

        key_terms = self.coder._extract_key_terms_from_reason(reason)

        self.assertIn("Calculator", key_terms)
        self.assertIn("add", key_terms)
        self.assertIn("class", key_terms)
        self.assertIn("method", key_terms)
        self.assertEqual(len(key_terms), 4)

    def test_file_contains_key_terms(self):
        """Test checking if a file contains key terms."""
        content = "class Calculator:\n    def add(self, a, b):\n        return a + b"
        key_terms = ["Calculator", "add"]

        result = self.coder._file_contains_key_terms(content, key_terms)
        self.assertTrue(result)

        content = "def format_result(result):\n    return f'Result: {result}'"
        result = self.coder._file_contains_key_terms(content, key_terms)
        self.assertFalse(result)


if __name__ == "__main__":
    unittest.main()
