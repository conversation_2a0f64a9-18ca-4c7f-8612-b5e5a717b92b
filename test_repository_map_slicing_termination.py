#!/usr/bin/env python3
"""
Test script to verify that repository map slicing is completely terminated
when Smart Map Request System is available.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_smart_map_request_availability():
    """Test that SMART_MAP_REQUEST_AVAILABLE is properly set."""
    print("🧪 Testing SMART_MAP_REQUEST_AVAILABLE")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import SMART_MAP_REQUEST_AVAILABLE
        
        print(f"SMART_MAP_REQUEST_AVAILABLE: {SMART_MAP_REQUEST_AVAILABLE}")
        
        if SMART_MAP_REQUEST_AVAILABLE:
            print("✅ Smart Map Request System is available")
            return True
        else:
            print("❌ Smart Map Request System is NOT available")
            return False
        
    except Exception as e:
        print(f"❌ Error checking SMART_MAP_REQUEST_AVAILABLE: {e}")
        return False

def test_get_repo_map_returns_none():
    """Test that get_repo_map() returns None when Smart Map Request is available."""
    print("\n🧪 Testing get_repo_map() Returns None")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("⚠️  Smart Map Request not available, skipping test")
            return True
        
        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test get_repo_map()
        repo_map = coder.get_repo_map()
        
        if repo_map is None:
            print("✅ get_repo_map() correctly returns None")
            return True
        else:
            print("❌ get_repo_map() returned content instead of None!")
            print(f"   Content length: {len(repo_map)} characters")
            print(f"   Content preview: {repo_map[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error testing get_repo_map(): {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repo_messages_structure():
    """Test that repo messages don't contain sliced repository maps."""
    print("\n🧪 Testing Repo Messages Structure")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("⚠️  Smart Map Request not available, skipping test")
            return True
        
        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )
        
        # Get repo messages
        repo_messages = coder.get_repo_messages()
        
        print(f"Number of repo messages: {len(repo_messages)}")
        
        for i, msg in enumerate(repo_messages):
            print(f"\nMessage {i+1} ({msg['role']}):")
            content = msg['content']
            print(f"  Content length: {len(content)} characters")
            print(f"  Content preview: {content[:100]}...")
            
            # Check if this looks like a sliced repository map
            if "⋮" in content or "..." in content or len(content) > 5000:
                print("  ❌ This looks like a sliced repository map!")
                return False
            else:
                print("  ✅ This looks like a proper Smart Map Request message")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing repo messages: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_slicing_algorithm_execution():
    """Test that the slicing algorithm in get_ranked_tags_map_uncached is not executed."""
    print("\n🧪 Testing No Slicing Algorithm Execution")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap instance created successfully")
        
        # Test if get_repo_map returns None (should when Smart Map Request is available)
        result = repo_map.get_repo_map(
            chat_files=set(),
            other_files=["aider-main/aider/coders/base_coder.py"],
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        if result is None:
            print("✅ RepoMap.get_repo_map() returns None (no slicing)")
            return True
        else:
            print("❌ RepoMap.get_repo_map() returned content (slicing occurred)!")
            print(f"   Content length: {len(result)} characters")
            print(f"   Content preview: {result[:200]}...")
            
            # Check for slicing indicators
            if "⋮" in result:
                print("   ❌ Contains slicing indicator '⋮'")
            if "..." in result:
                print("   ❌ Contains truncation indicator '...'")
            
            return False
        
    except Exception as e:
        print(f"❌ Error testing slicing algorithm: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all repository map slicing termination tests."""
    print("🚀 Repository Map Slicing Termination Test")
    print("=" * 80)
    
    tests = [
        ("SMART_MAP_REQUEST_AVAILABLE Check", test_smart_map_request_availability),
        ("get_repo_map() Returns None", test_get_repo_map_returns_none),
        ("Repo Messages Structure", test_repo_messages_structure),
        ("No Slicing Algorithm Execution", test_no_slicing_algorithm_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 REPOSITORY MAP SLICING TERMINATION TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Repository map slicing is completely terminated.")
        print("\n📋 Confirmed:")
        print("  1. ✅ SMART_MAP_REQUEST_AVAILABLE is True")
        print("  2. ✅ get_repo_map() returns None (no default repository map)")
        print("  3. ✅ Repo messages contain Smart Map Request instructions only")
        print("  4. ✅ No slicing algorithm execution")
        print("\n🎯 The LLM should only see repository context via MAP_REQUEST!")
    else:
        print("⚠️  Some tests failed. Repository map slicing may still be active!")
        print("\n🔍 If tests fail, the LLM might still be receiving sliced repository maps")
        print("   instead of using the Smart Map Request System.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
