#!/usr/bin/env python3

"""
Test to verify that the context request root path fix works correctly.
This test verifies that the context request system uses the correct project root
when external project files are referenced in the repository map.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_context_request_root_fix():
    """Test that context request system uses the correct root path."""
    
    print("🧪 Testing Context Request Root Path Fix")
    print("=" * 50)
    
    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create the aider directory structure
        aider_dir = os.path.join(temp_dir, "aider")
        aider_main_dir = os.path.join(aider_dir, "aider-main")
        os.makedirs(aider_main_dir)
        
        # Create a mock dashboard_repo_map.txt with external references
        dashboard_map_content = '''
# Repository Map

config\\base_config.py:
⋮
│BASE_SYMBOLS_CONFIG = {
│    'EURUSD': {
│        'mt5_symbol': 'EURUSD',
│        'price_key': 'EURUSD',
│        'symbol_allowed': True,
│    }
⋮

..\\trading_project\\services\\position_observer.py:
⋮
│class PositionObserver:
│    def close_position_based_on_conditions(self, app):
│        return True
⋮

..\\trading_project\\trade_management\\position_exit_manager.py:
⋮
│class PositionCloser:
│    def close_position_based_on_conditions(self, app):
│        return False
⋮
'''
        
        dashboard_map_path = os.path.join(aider_main_dir, "dashboard_repo_map.txt")
        with open(dashboard_map_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_map_content)
        
        # Create the external trading project structure
        trading_dir = os.path.join(temp_dir, "trading_project")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)
        
        # Create the actual trading files
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        return self._evaluate_market_conditions(app)
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")
        
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path}")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📄 {dashboard_map_path}")
        
        # Change to the aider directory to simulate running aider from there
        original_cwd = os.getcwd()
        try:
            os.chdir(aider_dir)
            print(f"📁 Changed working directory to: {aider_dir}")
            
            # Test the context request root resolution
            try:
                # Import the required modules
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.context_request.context_request_handler import ContextRequestHandler
                from aider.context_request.aider_integration_service import AiderIntegrationService
                
                print("✅ Successfully imported context request modules")
                
                # Test the root resolution logic
                # Simulate what the Coder class does
                root = aider_dir  # This would be self.root in the Coder class
                context_root = root
                
                # Apply the fix logic
                if (os.path.basename(root) == 'aider' and 
                    os.path.exists(os.path.join(root, 'aider-main'))):
                    # Check if there's a dashboard_repo_map.txt that indicates external project access
                    dashboard_map_path = os.path.join(root, 'aider-main', 'dashboard_repo_map.txt')
                    if os.path.exists(dashboard_map_path):
                        try:
                            with open(dashboard_map_path, 'r', encoding='utf-8') as f:
                                map_content = f.read()
                            # If the map contains references to external directories (..\ paths),
                            # use the parent directory as the context root
                            if '..\\' in map_content or '../' in map_content:
                                context_root = os.path.dirname(root)
                        except Exception:
                            pass  # Fall back to original root
                
                print(f"📁 Original root: {root}")
                print(f"📁 Context root after fix: {context_root}")
                
                # Verify the fix worked
                if context_root == temp_dir:
                    print("✅ SUCCESS: Context root correctly resolved to parent directory!")
                    
                    # Test that the context request handler can find the files
                    aider_service = AiderIntegrationService()
                    handler = ContextRequestHandler(context_root, aider_service)
                    
                    # Test file resolution
                    from aider.context_request.context_request_handler import SymbolRequest
                    
                    symbol1 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="trading_project/services/position_observer.py"
                    )
                    
                    symbol2 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions", 
                        file_hint="trading_project/trade_management/position_exit_manager.py"
                    )
                    
                    # Test file resolution
                    file1 = handler._find_file_for_symbol(symbol1)
                    file2 = handler._find_file_for_symbol(symbol2)
                    
                    print(f"🔍 File 1 resolution: {file1}")
                    print(f"🔍 File 2 resolution: {file2}")
                    
                    if file1 and file2:
                        print("✅ SUCCESS: Both trading files found with the fixed context root!")
                        return True
                    else:
                        print("❌ FAILED: Files not found even with fixed context root")
                        return False
                else:
                    print(f"❌ FAILED: Context root not correctly resolved")
                    print(f"   Expected: {temp_dir}")
                    print(f"   Got: {context_root}")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import context request modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

def main():
    """Main test function."""
    success = test_context_request_root_fix()
    
    if success:
        print("\n🎉 CONTEXT REQUEST ROOT PATH FIX: PASSED")
        print("The fix correctly resolves project paths for external trading files.")
    else:
        print("\n❌ CONTEXT REQUEST ROOT PATH FIX: FAILED")
        print("The fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
