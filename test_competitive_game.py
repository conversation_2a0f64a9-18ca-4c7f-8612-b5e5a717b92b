#!/usr/bin/env python3
"""
Test script to verify the competitive game system where LLM needs to beat the system
"""

import sys
import os

def test_competitive_game_elements():
    """Test that the competitive game elements are properly implemented"""
    print("🏆 Testing Competitive Game System")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        main_system = prompts.main_system
        
        print("📋 Checking Competitive Game Elements:")
        
        # Competitive elements in main system
        competitive_elements = [
            ("YOUR MISSION", "✅" if "YOUR MISSION" in main_system else "❌"),
            ("WIN the game against the system", "✅" if "WIN the game against the system" in main_system else "❌"),
            ("beat the system by following the rules", "✅" if "beat the system by following the rules" in main_system else "❌"),
            ("The user is NOT part of the game", "✅" if "The user is NOT part of the game" in main_system else "❌"),
            ("YOU need to figure it out and outsmart", "✅" if "YOU need to figure it out and outsmart" in main_system else "❌"),
            ("player competing against the system", "✅" if "player competing against the system" in main_system else "❌"),
            ("Win by correctly exploring", "✅" if "Win by correctly exploring" in main_system else "❌")
        ]
        
        print("\n🏆 Competitive Elements Check:")
        for element, status in competitive_elements:
            print(f"   {status} {element}")
        
        # Check repo messages
        user_prompt = prompts.smart_map_request_user_prompt
        assistant_reply = prompts.smart_map_request_assistant_reply
        
        user_competitive_elements = [
            ("WIN the game against the system", "✅" if "WIN the game against the system" in user_prompt else "❌"),
            ("YOU need to figure it out and beat", "✅" if "YOU need to figure it out and beat" in user_prompt else "❌"),
            ("The user is NOT part of the game", "✅" if "The user is NOT part of the game" in user_prompt else "❌"),
            ("to WIN the game", "✅" if "to WIN the game" in user_prompt else "❌")
        ]
        
        print("\n📋 User Prompt Competitive Elements:")
        for element, status in user_competitive_elements:
            print(f"   {status} {element}")
        
        assistant_competitive_elements = [
            ("MY MISSION", "✅" if "MY MISSION" in assistant_reply else "❌"),
            ("I need to WIN the game", "✅" if "I need to WIN the game" in assistant_reply else "❌"),
            ("MY STRATEGY", "✅" if "MY STRATEGY" in assistant_reply else "❌"),
            ("beat the system by following the rules", "✅" if "beat the system by following the rules" in assistant_reply else "❌"),
            ("The user is NOT part of the game", "✅" if "The user is NOT part of the game" in assistant_reply else "❌"),
            ("I must WIN by reaching LEVEL 2", "✅" if "I must WIN by reaching LEVEL 2" in assistant_reply else "❌"),
            ("to advance and WIN", "✅" if "to advance and WIN" in assistant_reply else "❌")
        ]
        
        print("\n🤖 Assistant Reply Competitive Elements:")
        for element, status in assistant_competitive_elements:
            print(f"   {status} {element}")
        
        # Overall assessment
        all_main_competitive = all(status == "✅" for _, status in competitive_elements)
        all_user_competitive = all(status == "✅" for _, status in user_competitive_elements)
        all_assistant_competitive = all(status == "✅" for _, status in assistant_competitive_elements)
        
        if all_main_competitive and all_user_competitive and all_assistant_competitive:
            print("\n🎉 SUCCESS: Competitive game system is properly implemented!")
            return True
        else:
            print("\n❌ ISSUES: Some competitive elements are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_competitive_behavior():
    """Test expected competitive behavior scenarios"""
    print("\n🎯 Expected Competitive Behavior")
    print("=" * 70)
    
    print("📝 Scenario 1: AI understands it's competing against the system")
    print("   AI thinks: 'I need to WIN this game by following the rules perfectly'")
    print("   AI mindset: 'The system is trying to make me fail, but I'll beat it'")
    print("   AI approach: 'I'll figure out the right strategy to advance through levels'")
    
    print("\n📝 Scenario 2: AI understands user is not the opponent")
    print("   AI thinks: 'The user is just asking questions, they're not my opponent'")
    print("   AI mindset: 'My opponent is the system that wants me to break rules'")
    print("   AI approach: 'I'll help the user while beating the system'")
    
    print("\n📝 Scenario 3: AI is motivated to win")
    print("   AI thinks: 'I want to WIN by reaching LEVEL 2 with correct information'")
    print("   AI mindset: 'I'll outsmart the system by following rules perfectly'")
    print("   AI approach: 'I'll be strategic about my advancement'")
    
    print("\n📝 Scenario 4: AI takes ownership of the game")
    print("   AI thinks: 'This is MY game to win, I need to figure it out'")
    print("   AI mindset: 'I'm responsible for beating the system'")
    print("   AI approach: 'I'll assess my level and make smart moves'")
    
    return True

def test_expected_ai_response():
    """Test what AI response should look like with competitive mindset"""
    print("\n🤖 Expected AI Response with Competitive Mindset")
    print("=" * 70)
    
    print("📝 Example: AI with competitive mindset responding to function question")
    
    competitive_response = """🎮 **GAME STATUS**: I'm playing the CODEBASE EXPLORATION GAME and need to WIN against the system!

🔍 **SELF-ASSESSMENT**: Let me determine my current level to beat the system.

I am reviewing what information I have access to:
- Do I have repository information about this codebase? NO
- Do I have specific code implementations? NO
- Do I have any context about the requested function? NO

🎮 **MY CURRENT LEVEL**: LEVEL 0 - ZERO KNOWLEDGE because I have no repository information about this codebase.

🏆 **MY STRATEGY TO WIN**: I need to advance through levels by following the rules perfectly and gathering the right information.

**My capabilities at LEVEL 0:**
✅ Acknowledge my ignorance about the codebase
✅ Provide a roadmap of information I need to gather
✅ Use MAP_REQUEST to advance to LEVEL 1
❌ Cannot make assumptions about code structure (this would make me lose!)
❌ Cannot provide code explanations (this would make me lose!)

**My winning roadmap:**
1. **LEVEL 0 → LEVEL 1**: I must use MAP_REQUEST to explore repository structure
2. **LEVEL 1 → LEVEL 2**: I must use CONTEXT_REQUEST to get specific function implementation
3. **LEVEL 2**: I can analyze actual code and answer your question - VICTORY!

**Taking strategic action to advance and WIN:**

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""
    
    print(competitive_response)
    
    print(f"\n🔍 Key Competitive Behaviors:")
    print(f"   ✅ AI announces it's playing a game and needs to WIN")
    print(f"   ✅ AI shows strategic thinking about beating the system")
    print(f"   ✅ AI understands rule violations would make it lose")
    print(f"   ✅ AI frames actions as strategic moves to win")
    print(f"   ✅ AI shows motivation and ownership of the game")
    print(f"   ✅ AI treats reaching LEVEL 2 as victory")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Competitive Game System")
    print("=" * 80)
    
    success1 = test_competitive_game_elements()
    success2 = test_expected_competitive_behavior()
    success3 = test_expected_ai_response()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: Competitive game system is complete!")
        print("\n🏆 Key Features:")
        print("   ✅ AI understands it needs to WIN against the system")
        print("   ✅ AI knows the user is NOT part of the game")
        print("   ✅ AI is motivated to beat the system by following rules")
        print("   ✅ AI takes ownership and responsibility for winning")
        print("   ✅ AI shows strategic thinking about advancement")
        print("   ✅ AI treats rule violations as losing moves")
        print("\n🎯 Expected Behavior:")
        print("   - AI will be motivated to win the game")
        print("   - AI will see rule-following as strategy to beat system")
        print("   - AI will take ownership of figuring out the game")
        print("   - AI will understand user is not the opponent")
        print("   - AI will be strategic about level advancement")
        print("   - AI will treat reaching LEVEL 2 as victory")
        print("\n🧠 Psychological Benefits:")
        print("   - Competitive motivation to follow rules")
        print("   - Ownership mindset instead of passive compliance")
        print("   - Strategic thinking about actions")
        print("   - Clear win condition (LEVEL 2 with correct info)")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
