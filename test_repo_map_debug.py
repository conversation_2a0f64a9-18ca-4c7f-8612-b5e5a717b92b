#!/usr/bin/env python3
"""
Debug why the repository map is empty.
"""

import os
import sys
import subprocess
import tempfile

def test_show_repo_map():
    """Test the --show-repo-map to see if it works."""
    print("🔍 Testing --show-repo-map to debug the issue")
    print("=" * 80)
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--show-repo-map"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        # Check if there's any repo map content
        if "No repository map generated" in result.stdout:
            print("❌ Repository map generation is failing")
            return False
        elif len(result.stdout.strip()) > 200:  # Some substantial content
            print("✅ Repository map is being generated")
            return True
        else:
            print("⚠️  Repository map output is minimal")
            return False
            
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_with_verbose():
    """Test with verbose output to see more details."""
    print("\n🔍 Testing with --verbose to see more details")
    print("=" * 80)
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command with verbose
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--show-repo-map",
            "--verbose"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        return True
            
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_with_files():
    """Test with specific files to see if that helps."""
    print("\n🔍 Testing with specific files")
    print("=" * 80)
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command with specific files
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--show-repo-map",
            "aider/main.py",
            "aider/repomap.py"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        return True
            
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_generate_with_files():
    """Test generate repo map with specific files."""
    print("\n🔍 Testing --generate-repo-map with specific files")
    print("=" * 80)
    
    # Create a temporary file for the output
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command with specific files
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--generate-repo-map", temp_filename,
            "aider/main.py",
            "aider/repomap.py",
            "aider/args.py"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        # Check if the file was created and has content
        if os.path.exists(temp_filename):
            file_size = os.path.getsize(temp_filename)
            print(f"✅ Repository map file created: {temp_filename}")
            print(f"📊 File size: {file_size} bytes")
            
            if file_size > 0:
                # Read and show a preview of the content
                with open(temp_filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    print(f"📊 Content stats:")
                    print(f"   Lines: {len(lines)}")
                    print(f"   Characters: {len(content)}")
                    print(f"   Words: {len(content.split())}")
                    
                    print(f"\n📋 Content preview (first 10 lines):")
                    print("-" * 60)
                    for i, line in enumerate(lines[:10]):
                        print(f"{i+1:3d}: {line}")
                    if len(lines) > 10:
                        print(f"... and {len(lines) - 10} more lines")
                    print("-" * 60)
                    
                    return True
            else:
                print("❌ Repository map file is empty")
                return False
        else:
            print(f"❌ Repository map file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)
        
        # Clean up temporary file
        try:
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
                print(f"🧹 Cleaned up temporary file: {temp_filename}")
        except Exception as e:
            print(f"⚠️  Could not clean up temporary file: {e}")

def main():
    """Run debugging tests."""
    print("🚀 Debugging Repository Map Generation")
    print("=" * 100)
    
    tests = [
        ("Show Repo Map Test", test_show_repo_map),
        ("Verbose Output Test", test_with_verbose),
        ("With Specific Files Test", test_with_files),
        ("Generate with Files Test", test_generate_with_files),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 DEBUGGING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
