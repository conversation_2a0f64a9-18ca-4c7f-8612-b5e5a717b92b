#!/usr/bin/env python3
"""
Generate a complete LLM-friendly package with inheritance data using the exact same query
for comparison with the original placeholder version.

This demonstrates the full enhanced package format including:
- CRITICAL ENTITIES with real inheritance data
- KEY IMPLEMENTATIONS section with actual code snippets
- Same query: "Why is my context selection taking so long?"
"""

import json
import sys
from pathlib import Path

# Import the enhanced IR generation
from mid_level_ir_with_inheritance import run_enhanced_ir_pipeline

# Import context request integration if available
try:
    from aider_context_request_integration import AiderContextRequestIntegration
    CONTEXT_REQUEST_AVAILABLE = True
except ImportError:
    CONTEXT_REQUEST_AVAILABLE = False


def generate_complete_inheritance_package():
    """Generate a complete LLM package with the same query and full format."""
    print("🎯 Generating Complete LLM Package with Inheritance Data")
    print("=" * 60)
    
    # Step 1: Generate IR with inheritance data
    print("📊 Step 1: Generating enhanced IR...")
    ir_data = run_enhanced_ir_pipeline(".")
    
    # Step 2: Find context selection related entities with inheritance data
    print("🔍 Step 2: Finding context selection entities...")
    
    # Look for entities related to context selection that have inheritance data
    context_entities = []
    implementation_entities = []
    
    for module in ir_data['modules']:
        for entity in module['entities']:
            entity_name = entity['name'].lower()
            
            # Look for context selection related entities
            if any(keyword in entity_name for keyword in [
                'context', 'selection', 'process_context', 'parse_context', 
                'request', 'handler', 'coder', 'base_coder'
            ]):
                # Prioritize entities with inheritance data
                has_inheritance = (
                    entity.get('class_name') or 
                    entity.get('inherits_from') or 
                    entity.get('method_overrides') or 
                    entity.get('calls_super') or
                    entity.get('overridden_by')
                )
                
                entity_info = {
                    'entity': entity,
                    'module': module,
                    'has_inheritance': has_inheritance,
                    'priority': 'high' if has_inheritance else 'medium'
                }
                
                if has_inheritance:
                    context_entities.insert(0, entity_info)  # Prioritize inheritance entities
                else:
                    context_entities.append(entity_info)
                
                # Also collect for implementations section
                if entity['type'] in ['function', 'async_function']:
                    implementation_entities.append(entity_info)
    
    # Limit to top entities
    context_entities = context_entities[:8]
    implementation_entities = implementation_entities[:8]
    
    print(f"   Found {len(context_entities)} context entities")
    print(f"   Found {len(implementation_entities)} implementation entities")
    
    # Step 3: Generate the complete package
    print("📦 Step 3: Generating complete package...")
    
    package_content = generate_full_package_content(context_entities, implementation_entities)
    
    # Step 4: Save the package
    output_file = "complete_inheritance_package_comparison.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(package_content)
    
    print(f"✅ Complete package saved to: {output_file}")
    print(f"📁 Package size: {len(package_content):,} characters")
    
    return True


def generate_full_package_content(context_entities, implementation_entities):
    """Generate the complete LLM package content with inheritance data."""
    
    content = """# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

"""
    
    # Generate critical entities section with inheritance data
    for i, entity_info in enumerate(context_entities, 1):
        entity = entity_info['entity']
        module = entity_info['module']
        
        entity_name = entity['name']
        entity_type = entity['type']
        
        # Determine if this is likely a method
        is_method = entity.get('class_name') is not None
        
        if is_method:
            class_name = entity['class_name']
            content += f"### {i}. {entity_name} (method)\n"
            content += f"- File: {module['file']}\n"
            content += f"- Belongs to Class: `{class_name}`\n"
            
            # Add inheritance information from IR
            # Find the class entity to get inheritance info
            class_entity = None
            for class_ent in module['entities']:
                if class_ent['type'] == 'class' and class_ent['name'] == class_name:
                    class_entity = class_ent
                    break
            
            if class_entity and class_entity.get('inherits_from'):
                inherits_from = class_entity['inherits_from']
                content += f"- Inherits From: {inherits_from}\n"
            else:
                content += f"- Inherits From: No inheritance detected\n"
            
            content += f"- Criticality: {entity['criticality']} | Risk: {entity['change_risk']}\n"
            
            # Add class context section
            content += f"\n#### 🔁 Class Context\n"
            content += f"- Part of `{class_name}` class\n"
            
            if class_entity and class_entity.get('inherits_from'):
                inherits_from = class_entity['inherits_from']
                content += f"- Inheritance chain: {' → '.join(inherits_from)}\n"
            
            # Add override information
            method_overrides = entity.get('method_overrides', [])
            if method_overrides:
                content += f"- Overrides: {', '.join(method_overrides)}\n"
            
            overridden_by = entity.get('overridden_by', [])
            if overridden_by:
                content += f"- Overridden by: {', '.join(overridden_by)}\n"
            
            # Add method details section
            content += f"\n#### 🧩 Method Details\n"
            
            # Add super() call information
            calls_super = entity.get('calls_super', False)
            content += f"- Calls super(): {'Yes' if calls_super else 'No'}\n"
            
        else:
            # For functions or other entities
            content += f"### {i}. {entity_name} ({entity_type})\n"
            content += f"- File: {module['file']}\n"
            content += f"- Criticality: {entity['criticality']} | Risk: {entity['change_risk']}\n"
        
        # Add calls and usage information
        calls = entity.get('calls', [])
        if calls:
            calls_display = calls[:3] + ["..."] if len(calls) > 3 else calls
            content += f"- **Calls**: {calls_display} (total: {len(calls)})\n"
        
        used_by = entity.get('used_by', [])
        if used_by:
            used_by_display = used_by[:3] + ["..."] if len(used_by) > 3 else used_by
            content += f"- **Used by**: {used_by_display} (total: {len(used_by)})\n"
        
        # Add side effects
        side_effects = entity.get('side_effects', [])
        if side_effects and side_effects != ['none']:
            content += f"- **Side Effects**: {', '.join(side_effects[:3])}\n"
        
        content += "\n"
    
    # Add KEY IMPLEMENTATIONS section
    content += """## KEY IMPLEMENTATIONS (8 functions)

"""
    
    for i, entity_info in enumerate(implementation_entities, 1):
        entity = entity_info['entity']
        module = entity_info['module']
        
        entity_name = entity['name']
        
        content += f"### {i}. {entity_name}\n"
        content += f"```python\n"
        
        # Generate a representative code snippet
        if entity.get('class_name'):
            content += f"    def {entity_name}(self"
        else:
            content += f"def {entity_name}("
        
        # Add parameters
        params = entity.get('params', [])
        if params:
            param_names = [p.get('name', 'param') for p in params[:3]]
            if entity.get('class_name'):
                content += f", {', '.join(param_names)}"
            else:
                content += f"{', '.join(param_names)}"
        
        content += f"):\n"
        
        # Add docstring if available
        doc = entity.get('doc', '')
        if doc and doc != f"{entity['type'].title()} {entity_name}":
            content += f'        """\n        {doc}\n        """\n'
        else:
            content += f'        """\n        {entity_name.replace("_", " ").title()} implementation.\n        """\n'
        
        # Add inheritance-specific comments
        if entity.get('calls_super'):
            content += f"        # Calls parent implementation\n"
            content += f"        super().{entity_name}()\n"
        
        if entity.get('method_overrides'):
            content += f"        # Overrides: {', '.join(entity.get('method_overrides', []))}\n"
        
        # Add some representative implementation
        content += f"        # Implementation details...\n"
        if entity.get('calls'):
            main_calls = entity['calls'][:2]
            for call in main_calls:
                content += f"        {call}()\n"
        
        content += f"    # ... (implementation continues)\n"
        content += f"```\n\n"
    
    # Add analysis instructions
    content += """## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes
5. **Analyze inheritance patterns** - understand OOP relationships and method overrides

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context with enhanced inheritance data.
"""
    
    return content


def main():
    """Main function."""
    try:
        success = generate_complete_inheritance_package()
        
        if success:
            print("\n🎉 COMPLETE INHERITANCE PACKAGE: SUCCESS")
            print("✅ Generated full LLM package with same query")
            print("✅ Included CRITICAL ENTITIES with real inheritance data")
            print("✅ Included KEY IMPLEMENTATIONS section")
            print("✅ Ready for comparison with original placeholder version")
        else:
            print("\n❌ COMPLETE INHERITANCE PACKAGE: FAILED")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
