#!/usr/bin/env python3
"""
Test script to verify automatic saving of LLM-friendly packages to ICA_package folder.
"""

import sys
import os
import time
from pathlib import Path

# Add aider to path
sys.path.insert(0, "aider-main")

def test_auto_save_ica_packages():
    """Test the automatic saving of ICA packages."""
    
    print("🔍 TESTING AUTOMATIC ICA PACKAGE SAVING")
    print("=" * 60)
    
    try:
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        print(f"📁 Project path: {external_project}")
        
        # Check if ICA_package folder exists before test
        ica_folder = Path(external_project) / "ICA_package"
        print(f"📁 ICA_package folder: {ica_folder}")
        print(f"   Exists before test: {'✅' if ica_folder.exists() else '❌'}")
        
        if ica_folder.exists():
            existing_files = list(ica_folder.glob("*.txt"))
            print(f"   Existing files: {len(existing_files)}")
        else:
            existing_files = []
        
        # Create handler
        handler = ContextRequestHandler(external_project)
        
        # Test 1: Position management query
        print(f"\n🎯 TEST 1: POSITION MANAGEMENT QUERY")
        print("-" * 40)
        
        request1 = IRContextRequest(
            user_query="How does position management work in the trading system?",
            task_description="Analyze position management workflow and components",
            task_type="general_analysis",
            focus_entities=["position", "management", "trading", "workflow"],
            max_tokens=1500,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=6
        )
        
        print(f"📝 Processing request: {request1.task_description}")
        result1 = handler.process_ir_context_request(request1)
        
        if "llm_friendly_package" in result1:
            print(f"✅ Generated LLM package ({len(result1['llm_friendly_package'])} chars)")
        else:
            print(f"❌ No LLM package generated")
        
        # Test 2: Strategy inheritance query
        print(f"\n🎯 TEST 2: STRATEGY INHERITANCE QUERY")
        print("-" * 40)
        
        request2 = IRContextRequest(
            user_query="How do strategy classes inherit from base classes?",
            task_description="Analyze strategy class inheritance patterns and OOP design",
            task_type="code_review",
            focus_entities=["strategy", "inheritance", "base", "class", "oop"],
            max_tokens=1200,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=5
        )
        
        print(f"📝 Processing request: {request2.task_description}")
        result2 = handler.process_ir_context_request(request2)
        
        if "llm_friendly_package" in result2:
            print(f"✅ Generated LLM package ({len(result2['llm_friendly_package'])} chars)")
        else:
            print(f"❌ No LLM package generated")
        
        # Test 3: Quick debugging query
        print(f"\n🎯 TEST 3: DEBUGGING QUERY")
        print("-" * 40)
        
        request3 = IRContextRequest(
            user_query="Debug trading execution issues",
            task_description="Find and analyze trading execution problems",
            task_type="debugging",
            focus_entities=["execution", "trade", "debug", "error"],
            max_tokens=800,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=False,  # Faster generation
            max_entities=4
        )
        
        print(f"📝 Processing request: {request3.task_description}")
        result3 = handler.process_ir_context_request(request3)
        
        if "llm_friendly_package" in result3:
            print(f"✅ Generated LLM package ({len(result3['llm_friendly_package'])} chars)")
        else:
            print(f"❌ No LLM package generated")
        
        # Check results
        print(f"\n📊 CHECKING AUTO-SAVE RESULTS")
        print("=" * 60)
        
        # Check if ICA_package folder was created
        if ica_folder.exists():
            print(f"✅ ICA_package folder exists: {ica_folder}")
        else:
            print(f"❌ ICA_package folder not created")
            return False
        
        # List all files in the folder
        all_files = list(ica_folder.glob("*.txt"))
        new_files = [f for f in all_files if f not in existing_files]
        
        print(f"📁 Files in ICA_package folder:")
        print(f"   Total files: {len(all_files)}")
        print(f"   New files from this test: {len(new_files)}")
        
        if new_files:
            print(f"\n📄 NEW FILES CREATED:")
            for i, file_path in enumerate(new_files, 1):
                file_size = file_path.stat().st_size
                print(f"   {i}. {file_path.name}")
                print(f"      Size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                
                # Check file content
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Verify it has the expected structure
                    has_header = "# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED" in content
                    has_metadata = "# Generated:" in content and "# User Query:" in content
                    has_separator = "="*80 in content
                    
                    print(f"      Header: {'✅' if has_header else '❌'}")
                    print(f"      Metadata: {'✅' if has_metadata else '❌'}")
                    print(f"      Separator: {'✅' if has_separator else '❌'}")
                    
                    # Show first few lines
                    lines = content.split('\n')
                    print(f"      Preview (first 5 lines):")
                    for line in lines[:5]:
                        print(f"        {line}")
                    
                except Exception as e:
                    print(f"      ❌ Error reading file: {e}")
        else:
            print(f"❌ No new files were created during the test")
            return False
        
        # Verify filename format
        print(f"\n🔍 VERIFYING FILENAME FORMATS")
        print("-" * 40)
        
        import re
        filename_pattern = r'^\d{8}_\d{6}_[a-z0-9_]+\.txt$'
        
        for file_path in new_files:
            filename = file_path.name
            matches_pattern = bool(re.match(filename_pattern, filename))
            print(f"   {filename}: {'✅' if matches_pattern else '❌'}")
            
            if matches_pattern:
                # Extract timestamp
                timestamp_part = filename[:15]  # YYYYMMDD_HHMMSS
                try:
                    from datetime import datetime
                    parsed_time = datetime.strptime(timestamp_part, "%Y%m%d_%H%M%S")
                    print(f"     Timestamp: {parsed_time.strftime('%Y-%m-%d %H:%M:%S')}")
                except ValueError as e:
                    print(f"     ❌ Invalid timestamp format: {e}")
        
        # Summary
        print(f"\n📊 AUTO-SAVE TEST SUMMARY")
        print("=" * 60)
        print(f"✅ ICA_package folder: {'Created/Exists' if ica_folder.exists() else 'Missing'}")
        print(f"✅ Files auto-saved: {len(new_files)}")
        print(f"✅ Expected files: 3 (one per test)")
        
        if len(new_files) == 3:
            print(f"🎉 SUCCESS: All 3 context packages were automatically saved!")
            print(f"📁 Location: {ica_folder}")
            print(f"💡 You can now review these packages anytime for analysis")
            return True
        else:
            print(f"⚠️  WARNING: Expected 3 files, but got {len(new_files)}")
            return False
        
    except Exception as e:
        print(f"❌ Error during auto-save test: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """Show instructions for using the auto-save feature."""
    
    print(f"\n📖 HOW TO USE AUTO-SAVE ICA PACKAGES")
    print("=" * 60)
    
    print("""
🎯 AUTOMATIC SAVING IS NOW ENABLED!

Every time you generate an IR context package, it will be automatically saved to:
📁 {project_path}/ICA_package/

📄 FILENAME FORMAT:
   YYYYMMDD_HHMMSS_task_description.txt
   
   Examples:
   - 20241201_143022_position_management_analysis.txt
   - 20241201_143045_strategy_inheritance_patterns.txt
   - 20241201_143102_debug_trading_execution.txt

📋 FILE CONTENT INCLUDES:
   ✅ Complete metadata header (timestamp, query, task, etc.)
   ✅ Full LLM-friendly package content
   ✅ Inheritance data and context analysis
   ✅ Ready for review and analysis

🔧 USAGE:
   1. Just use the IR context system normally
   2. Packages are automatically saved - no action needed
   3. Check the ICA_package folder for all saved packages
   4. Review packages anytime for analysis or debugging

💡 BENEFITS:
   - Permanent archive of all context analysis
   - Easy to review past queries and results
   - Track evolution of context understanding
   - Share packages with team members
   - Debug context selection issues
""")

if __name__ == "__main__":
    success = test_auto_save_ica_packages()
    
    if success:
        show_usage_instructions()
        print("\n✅ Auto-save ICA packages test completed successfully!")
    else:
        print("\n❌ Auto-save ICA packages test failed.")
        print("Check the implementation and try again.")
