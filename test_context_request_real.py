#!/usr/bin/env python

import os
import sys
import time
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""
    
    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []
    
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")
    
    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")
    
    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


def main():
    print("\n=== Testing CONTEXT_REQUEST with Real Code ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path)
    
    # Create a sample context request for a real function in the codebase
    context_request = ContextRequest(
        original_user_query_context="User is asking about the position quantity calculator",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="AiderContextRequestIntegration.process_context_request",
                file_hint="aider_context_request_integration.py"
            )
        ],
        reason_for_request="To analyze the implementation of the process_context_request method"
    )
    
    # Process the context request
    print("\nProcessing context request...")
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the process_context_request method work?",
        repo_overview=""
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt Content ===")
    print(augmented_prompt)
    
    # Extract the code context from the augmented prompt
    code_context_start = augmented_prompt.find("### REQUESTED SYMBOL DEFINITIONS")
    code_context_end = augmented_prompt.find("INSTRUCTIONS FOR THIS TURN")
    
    if code_context_start != -1 and code_context_end != -1:
        code_context = augmented_prompt[code_context_start:code_context_end].strip()
        print("\n=== Code Context ===")
        print(code_context)
    else:
        print("\n❌ No code context found in the augmented prompt")
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
