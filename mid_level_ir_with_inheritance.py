#!/usr/bin/env python3
"""
Enhanced Mid-Level IR Generation with Comprehensive Inheritance Analysis

This script generates Mid-Level Intermediate Representation (IR) with complete
class inheritance tracking, method override detection, and super() call analysis.

Enhanced Features:
- Complete class inheritance hierarchy mapping
- Method override detection across inheritance chains  
- Super() call detection within method implementations
- Class membership tracking for all methods
- Cross-module inheritance analysis

Usage:
    python mid_level_ir_with_inheritance.py [project_path]
"""

import sys
import time
import json
from pathlib import Path
from typing import Dict, Any

# Import the modular pipeline components
from mid_level_ir.file_scanner import FileScanner
from mid_level_ir.entity_extractor import EntityExtractor
from mid_level_ir.dependency_analyzer import DependencyAnalyzer
from mid_level_ir.call_graph_builder import CallGraphBuilder
from mid_level_ir.side_effect_analyzer import SideEffectAnalyzer
from mid_level_ir.error_analyzer import ErrorAnalyzer
from mid_level_ir.criticality_scorer import CriticalityScorer
from mid_level_ir.metadata_enricher import MetadataEnricher
from mid_level_ir.inheritance_analyzer import InheritanceA<PERSON>yzer  # New module
from mid_level_ir.ir_builder import IRBuilder
from mid_level_ir.ir_context import IRContext


def create_enhanced_config() -> Dict[str, Any]:
    """Create configuration for enhanced IR generation with inheritance analysis."""
    return {
        # File scanning
        'include_patterns': ['*.py'],
        'exclude_patterns': [
            '__pycache__/*',
            '*.pyc',
            '.git/*',
            '.venv/*',
            'venv/*',
            '.pytest_cache/*',
            'node_modules/*',
            'build/*',
            'dist/*'
        ],
        'max_file_size': 1024 * 1024,  # 1MB
        
        # Entity extraction
        'extract_variables': True,
        'extract_constants': True,
        'min_function_lines': 1,
        
        # Inheritance analysis (new)
        'track_cross_module': True,
        'detect_method_overrides': True,
        'analyze_super_calls': True,
        
        # Analysis options
        'analyze_side_effects': True,
        'analyze_errors': True,
        'analyze_criticality': True,
        'include_metadata': True,
        'pretty_print': True,
        
        # Performance
        'verbose': True,
        'max_workers': 4
    }


def run_enhanced_ir_pipeline(project_path: str) -> Dict[str, Any]:
    """
    Run the enhanced IR generation pipeline with inheritance analysis.
    
    Args:
        project_path: Path to the project to analyze
        
    Returns:
        Complete IR data with inheritance information
    """
    start_time = time.time()
    config = create_enhanced_config()
    
    print("🔍 Enhanced Mid-Level IR Generation with Inheritance Analysis")
    print("=" * 70)
    print(f"Project: {project_path}")
    print()
    
    # Initialize context
    context = IRContext(project_path=Path(project_path))
    
    # Step 1: File Discovery
    print("📁 Step 1: File Discovery")
    file_scanner = FileScanner(config)
    context = file_scanner.scan(context)
    print(f"   Found {len(context.all_files)} Python files")
    print()
    
    # Step 2: Entity Extraction (Enhanced)
    print("🔧 Step 2: Enhanced Entity Extraction")
    entity_extractor = EntityExtractor(config)
    context = entity_extractor.extract(context)

    total_entities = len(context.get_all_entities())
    total_classes = len(context.get_entities_by_type("class"))
    total_functions = len(context.get_entities_by_type("function"))
    print(f"   Extracted {total_entities} entities ({total_classes} classes, {total_functions} functions)")
    print()

    # Step 3: Inheritance Analysis (NEW)
    print("🏗️ Step 3: Inheritance Analysis")
    inheritance_analyzer = InheritanceAnalyzer(config)
    context = inheritance_analyzer.analyze(context)
    print("   ✅ Class hierarchy mapping complete")
    print("   ✅ Method override detection complete")
    print("   ✅ Super() call analysis complete")
    print()

    # Step 4: Call Graph Analysis
    print("📞 Step 4: Call Graph Analysis")
    call_graph_builder = CallGraphBuilder(config)
    context = call_graph_builder.build(context)
    print()

    # Step 5: Dependency Analysis
    print("🔗 Step 5: Dependency Analysis")
    dependency_analyzer = DependencyAnalyzer(config)
    context = dependency_analyzer.analyze(context)
    print()

    # Step 6: Side Effect Analysis
    print("⚡ Step 6: Side Effect Analysis")
    side_effect_analyzer = SideEffectAnalyzer(config)
    context = side_effect_analyzer.analyze(context)
    print()

    # Step 7: Error Analysis
    print("🚨 Step 7: Error Analysis")
    error_analyzer = ErrorAnalyzer(config)
    context = error_analyzer.analyze(context)
    print()

    # Step 8: Metadata Enrichment
    print("📊 Step 8: Metadata Enrichment")
    metadata_enricher = MetadataEnricher(config)
    context = metadata_enricher.enrich(context)
    print()

    # Step 9: Criticality Analysis
    print("🎯 Step 9: Criticality Analysis")
    criticality_scorer = CriticalityScorer(config)
    context = criticality_scorer.score(context)
    print()
    
    # Step 10: IR Building
    print("🏗️ Step 10: IR Building")
    ir_builder = IRBuilder(config)
    ir_data = ir_builder.build(context)
    print()
    
    # Performance summary
    end_time = time.time()
    duration = end_time - start_time
    
    print("📊 Enhanced Analysis Summary")
    print("-" * 30)
    stats = context.get_statistics()
    print(f"Total modules: {stats['total_modules']}")
    print(f"Total entities: {stats['total_entities']}")
    print(f"Total classes: {stats['total_classes']}")
    print(f"Total functions: {stats['total_functions']}")
    print(f"Total LOC: {stats['total_loc']:,}")
    print(f"Processing time: {duration:.1f}s")
    print()
    
    # Inheritance-specific statistics
    classes_with_inheritance = 0
    methods_with_overrides = 0
    methods_with_super = 0
    
    for entity in context.get_all_entities():
        if entity.type == "class" and entity.inherits_from:
            classes_with_inheritance += 1
        elif entity.type in ["function", "async_function"]:
            if entity.method_overrides:
                methods_with_overrides += 1
            if entity.calls_super:
                methods_with_super += 1
    
    print("🏗️ Inheritance Analysis Results")
    print("-" * 35)
    print(f"Classes with inheritance: {classes_with_inheritance}")
    print(f"Methods with overrides: {methods_with_overrides}")
    print(f"Methods calling super(): {methods_with_super}")
    print()
    
    return ir_data


def main():
    """Main function to run enhanced IR generation."""
    if len(sys.argv) > 1:
        project_path = sys.argv[1]
    else:
        project_path = "."
    
    try:
        # Generate enhanced IR with inheritance analysis
        ir_data = run_enhanced_ir_pipeline(project_path)
        
        # Save to file
        output_file = "enhanced_ir_with_inheritance.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Enhanced IR with inheritance data saved to: {output_file}")
        
        # Calculate file size
        file_size = Path(output_file).stat().st_size
        print(f"📁 Output size: {file_size / 1024 / 1024:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during enhanced IR generation: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
