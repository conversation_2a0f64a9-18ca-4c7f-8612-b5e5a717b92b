{"structure": {"keys": ["modules", "metadata"], "modules_count": 120, "metadata": {"generated_at": 1748623594.356733, "project_path": "C:\\Users\\<USER>\\Documents\\____live_backtest_dashboard_____", "project_name": null, "generator_version": "2.0.0", "total_modules": 120, "total_entities": 3998, "total_functions": 613, "total_classes": 120, "total_loc": 20428, "total_dependencies": 694}}, "sample_module": {"keys": ["name", "file", "loc", "dependencies", "entities", "metadata"], "entities_count": 13, "module_name": "unknown"}, "sample_entity": {"type": "class", "name": "BacktestConfig", "doc": "Class BacktestConfig", "params": [], "returns": {"type": "class_instance"}, "calls": [], "used_by": [], "side_effects": ["none"], "errors": ["RuntimeError"], "criticality": "low", "change_risk": "low", "class_name": null, "inherits_from": [], "method_overrides": [], "calls_super": false, "overridden_by": []}}