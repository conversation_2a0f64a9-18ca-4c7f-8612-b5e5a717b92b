#!/usr/bin/env python3
"""
Test the error handling fix for CONTEXT_REQUEST failures.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_context_request_error_handling():
    """Test that CONTEXT_REQUEST errors are properly communicated to the LLM."""
    print("🔍 Testing CONTEXT_REQUEST Error Handling")
    print("=" * 80)

    try:
        from aider.context_request import ContextRequestHandler, ContextRequest, SymbolRequest, AiderTemplateRenderer

        print("✅ Context request modules imported successfully")

        # Create a context request handler
        handler = ContextRequestHandler(project_path="aider-main")
        renderer = AiderTemplateRenderer()

        print("✅ Handler and renderer created successfully")

        # Test 1: Symbol not found scenario
        print("\n🧪 Test 1: Symbol not found scenario")
        print("-" * 60)

        # Create a context request for a non-existent symbol
        context_request = ContextRequest(
            original_user_query_context="how does the close_position_based_on_conditions function work?",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="position_observer.py"
                )
            ],
            reason_for_request="To retrieve the definition of the 'close_position_based_on_conditions' method"
        )

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"   Extracted symbols: {len(result.get('extracted_symbols', []))}")
        print(f"   Not found symbols: {len(result.get('not_found_symbols', []))}")

        # Check if not_found_symbols are properly tracked
        not_found_symbols = result.get('not_found_symbols', [])
        if not_found_symbols:
            print("   ✅ Not found symbols properly tracked:")
            for symbol in not_found_symbols:
                symbol_name = symbol.get('symbol_name', '')
                reason = symbol.get('reason', '')
                print(f"     - {symbol_name}: {reason}")
        else:
            print("   ❌ Not found symbols not tracked")
            return False

        # Test 2: Template renderer formats errors properly
        print("\n🧪 Test 2: Template renderer error formatting")
        print("-" * 60)

        # Generate the augmented prompt
        augmented_prompt = renderer.render_augmented_prompt(
            original_query="how does the close_position_based_on_conditions function work?",
            repo_overview="Test repository overview",
            extracted_context=result
        )

        # Check if the error is included in the prompt
        if "⚠️ SYMBOLS NOT FOUND ⚠️" in augmented_prompt:
            print("   ✅ Error section properly included in prompt")

            # Check if suggestions are included
            if "SUGGESTIONS:" in augmented_prompt:
                print("   ✅ Helpful suggestions included")
            else:
                print("   ❌ Suggestions missing")
                return False

            # Check if the specific symbol is mentioned
            if "close_position_based_on_conditions" in augmented_prompt:
                print("   ✅ Specific symbol mentioned in error")
            else:
                print("   ❌ Specific symbol not mentioned")
                return False

        else:
            print("   ❌ Error section not included in prompt")
            print("   Prompt preview:")
            print(augmented_prompt[:500] + "...")
            return False

        # Test 3: Check that LLM gets actionable guidance
        print("\n🧪 Test 3: Actionable guidance for LLM")
        print("-" * 60)

        guidance_keywords = [
            "REQUEST_FILE",
            "MAP_REQUEST",
            "spelled correctly",
            "different file",
            "alternative approaches"
        ]

        guidance_found = 0
        for keyword in guidance_keywords:
            if keyword in augmented_prompt:
                guidance_found += 1
                print(f"   ✅ Found guidance: '{keyword}'")
            else:
                print(f"   ❌ Missing guidance: '{keyword}'")

        if guidance_found >= 3:
            print(f"   ✅ Sufficient actionable guidance provided ({guidance_found}/{len(guidance_keywords)})")
        else:
            print(f"   ❌ Insufficient actionable guidance ({guidance_found}/{len(guidance_keywords)})")
            return False

        return True

    except Exception as e:
        print(f"❌ Error in context request error handling test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixed_success_failure_scenario():
    """Test scenario where some symbols are found and others are not."""
    print("\n🔍 Testing Mixed Success/Failure Scenario")
    print("=" * 80)

    try:
        from aider.context_request import ContextRequestHandler, ContextRequest, SymbolRequest, AiderTemplateRenderer

        # Create a context request handler
        handler = ContextRequestHandler(project_path="aider-main")
        renderer = AiderTemplateRenderer()

        # Test mixed scenario: one symbol exists, one doesn't
        print("\n🧪 Testing mixed success/failure scenario")
        print("-" * 60)

        context_request = ContextRequest(
            original_user_query_context="how do these functions work?",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",  # Doesn't exist
                    file_hint="position_observer.py"
                ),
                SymbolRequest(
                    type="class_definition",
                    name="Coder",  # Might exist
                    file_hint="base_coder.py"
                )
            ],
            reason_for_request="To analyze multiple functions"
        )

        # Process the context request
        result = handler.process_context_request(context_request)

        extracted_symbols = result.get('extracted_symbols', [])
        not_found_symbols = result.get('not_found_symbols', [])

        print(f"   Extracted symbols: {len(extracted_symbols)}")
        print(f"   Not found symbols: {len(not_found_symbols)}")

        # Generate the augmented prompt
        augmented_prompt = renderer.render_augmented_prompt(
            original_query="how do these functions work?",
            repo_overview="Test repository overview",
            extracted_context=result
        )

        # Check that both success and failure are handled
        has_extracted_content = len(extracted_symbols) > 0
        has_not_found_content = "⚠️ SYMBOLS NOT FOUND ⚠️" in augmented_prompt

        print(f"   Has extracted content: {has_extracted_content}")
        print(f"   Has not found content: {has_not_found_content}")

        if has_not_found_content:
            print("   ✅ Mixed scenario properly handled")
            return True
        else:
            print("   ❌ Mixed scenario not properly handled")
            return False

    except Exception as e:
        print(f"❌ Error in mixed scenario test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive error handling tests."""
    print("🚀 CONTEXT_REQUEST Error Handling Fix Verification")
    print("=" * 100)

    tests = [
        ("Context Request Error Handling", test_context_request_error_handling),
        ("Mixed Success/Failure Scenario", test_mixed_success_failure_scenario),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 100)
    print("📊 ERROR HANDLING FIX VERIFICATION SUMMARY")
    print("=" * 100)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ERROR HANDLING FIX SUCCESSFUL!")
        print("\n📋 Confirmed:")
        print("  ✅ CONTEXT_REQUEST errors are properly tracked")
        print("  ✅ Error messages are formatted and sent to LLM")
        print("  ✅ Actionable suggestions are provided")
        print("  ✅ Mixed success/failure scenarios handled")
        print("\n🎯 LLM will now be notified when symbols are not found!")
        print("🎯 LLM can adapt and try alternative approaches!")
    else:
        print("⚠️  ERROR HANDLING FIX INCOMPLETE!")
        print("   Additional fixes may be needed")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
