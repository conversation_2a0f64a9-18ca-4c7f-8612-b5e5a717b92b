#!/usr/bin/env python3
"""
Test script to verify the AI understands it is the player in the game
"""

import sys
import os

def test_ai_as_player_clarity():
    """Test that it's clear the AI is the player, not the user"""
    print("🎮 Testing AI as Player Clarity")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test the main system prompt
        main_system = prompts.main_system
        print("🎯 Main System Prompt Analysis:")
        
        # Check for AI-as-player elements
        ai_player_elements = [
            ("YOU ARE THE PLAYER", "✅" if "YOU ARE THE PLAYER" in main_system else "❌"),
            ("YOU (the AI) are the player", "✅" if "YOU (the AI) are the player" in main_system else "❌"),
            ("YOUR GAME STATUS", "✅" if "YOUR GAME STATUS" in main_system else "❌"),
            ("YOUR LEVEL RESTRICTIONS", "✅" if "YOUR LEVEL RESTRICTIONS" in main_system else "❌"),
            ("YOUR LEVEL SYSTEM", "✅" if "YOUR LEVEL SYSTEM" in main_system else "❌"),
            ("YOUR LEVEL 0", "✅" if "YOUR LEVEL 0" in main_system else "❌"),
            ("YOUR CURRENT STATUS", "✅" if "YOUR CURRENT STATUS" in main_system else "❌")
        ]
        
        print("\n🎮 AI-as-Player Elements Check:")
        for element, status in ai_player_elements:
            print(f"   {status} {element}")
        
        # Test user prompt
        user_prompt = prompts.smart_map_request_user_prompt
        print(f"\n📋 User Prompt Analysis:")
        
        user_prompt_elements = [
            ("YOUR GAME STATUS", "✅" if "YOUR GAME STATUS" in user_prompt else "❌"),
            ("YOU (the AI) are currently", "✅" if "YOU (the AI) are currently" in user_prompt else "❌"),
            ("YOUR LEVEL 0 RULES", "✅" if "YOUR LEVEL 0 RULES" in user_prompt else "❌"),
            ("YOUR LEVEL 0 CAPABILITIES", "✅" if "YOUR LEVEL 0 CAPABILITIES" in user_prompt else "❌"),
            ("FOR YOU TO ADVANCE", "✅" if "FOR YOU TO ADVANCE" in user_prompt else "❌"),
            ("YOU MUST use MAP_REQUEST", "✅" if "YOU MUST use MAP_REQUEST" in user_prompt else "❌")
        ]
        
        print("\n📍 User Prompt Elements Check:")
        for element, status in user_prompt_elements:
            print(f"   {status} {element}")
        
        # Test assistant reply
        assistant_reply = prompts.smart_map_request_assistant_reply
        print(f"\n🤖 Assistant Reply Analysis:")
        
        assistant_reply_elements = [
            ("I (the AI) am currently", "✅" if "I (the AI) am currently" in assistant_reply else "❌"),
            ("MY LEVEL 0", "✅" if "MY LEVEL 0" in assistant_reply else "❌"),
            ("MY game rules", "✅" if "MY game rules" in assistant_reply else "❌"),
            ("advance to MY LEVEL 1", "✅" if "advance to MY LEVEL 1" in assistant_reply else "❌"),
            ("Acknowledge MY LEVEL 0 status", "✅" if "Acknowledge MY LEVEL 0 status" in assistant_reply else "❌")
        ]
        
        print("\n🎯 Assistant Reply Elements Check:")
        for element, status in assistant_reply_elements:
            print(f"   {status} {element}")
        
        # Overall assessment
        all_ai_player_elements = all(status == "✅" for _, status in ai_player_elements)
        all_user_prompt_elements = all(status == "✅" for _, status in user_prompt_elements)
        all_assistant_reply_elements = all(status == "✅" for _, status in assistant_reply_elements)
        
        if all_ai_player_elements and all_user_prompt_elements and all_assistant_reply_elements:
            print("\n🎉 SUCCESS: It's crystal clear that the AI is the player!")
            return True
        else:
            print("\n❌ ISSUES: Some clarity elements are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_ai_behavior():
    """Test what the AI behavior should be now"""
    print("\n🤖 Expected AI Behavior")
    print("=" * 60)
    
    print("📝 When user asks: 'How does the close_position_based_on_conditions function work?'")
    print("\n🎯 Expected AI Response (AI acknowledges it's the player):")
    
    expected_response = """🎮 **MY CURRENT LEVEL**: I am at MY LEVEL 0 - ZERO KNOWLEDGE

As the AI player in this game, I have ZERO knowledge about this codebase and cannot provide any code explanations at my current level.

**My roadmap to help you:**
1. **MY LEVEL 0 → MY LEVEL 1**: I must use MAP_REQUEST to explore repository structure
2. **MY LEVEL 1 → MY LEVEL 2**: I must use CONTEXT_REQUEST to get specific function implementation  
3. **MY LEVEL 2**: I can analyze actual code and answer your question

**Starting my advancement from MY LEVEL 0 → MY LEVEL 1:**

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""
    
    print(expected_response)
    
    print(f"\n🔍 Key AI-as-Player Behaviors:")
    print(f"   ✅ AI says 'MY CURRENT LEVEL' (not 'your level')")
    print(f"   ✅ AI says 'I am at MY LEVEL 0' (not 'you are at level 0')")
    print(f"   ✅ AI says 'As the AI player' (acknowledges its role)")
    print(f"   ✅ AI says 'my advancement' (owns the progression)")
    print(f"   ✅ AI uses MAP_REQUEST to advance itself")
    
    return True

def test_clarity_comparison():
    """Compare old vs new clarity"""
    print("\n🆚 Clarity Comparison")
    print("=" * 60)
    
    print("❌ **OLD (Confusing)**:")
    print("   'You are at LEVEL 0' → AI thinks user is at level 0")
    print("   'You must use MAP_REQUEST' → AI thinks user must do it")
    print("   'Your capabilities' → AI thinks it's about user's capabilities")
    
    print("\n✅ **NEW (Crystal Clear)**:")
    print("   'YOU (the AI) are at YOUR LEVEL 0' → AI knows it's about AI")
    print("   'YOU MUST use MAP_REQUEST' → AI knows it must do it")
    print("   'YOUR capabilities' → AI knows it's about AI's capabilities")
    print("   'I (the AI) am currently' → AI acknowledges its role")
    print("   'MY LEVEL 0' → AI owns its level status")
    
    print(f"\n🎯 Why This Fixes the Problem:")
    print(f"   🔧 **Explicit Role Assignment**: 'YOU (the AI)' makes it unmistakable")
    print(f"   🔧 **Ownership Language**: 'MY level', 'MY rules', 'MY advancement'")
    print(f"   🔧 **Self-Acknowledgment**: 'I (the AI) am currently'")
    print(f"   🔧 **Clear Responsibility**: 'I must use MAP_REQUEST'")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing AI as Player Clarity")
    print("=" * 80)
    
    success1 = test_ai_as_player_clarity()
    success2 = test_expected_ai_behavior()
    success3 = test_clarity_comparison()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: AI should now understand it's the player!")
        print("\n🎮 Key Improvements:")
        print("   ✅ Explicit 'YOU (the AI)' language throughout")
        print("   ✅ Ownership language: 'MY level', 'MY rules', 'MY advancement'")
        print("   ✅ Self-acknowledgment: 'I (the AI) am currently'")
        print("   ✅ Clear responsibility: 'I must use MAP_REQUEST'")
        print("\n🎯 Expected Result:")
        print("   The AI should now clearly understand that:")
        print("   - IT is the player progressing through levels")
        print("   - IT has level restrictions and capabilities")
        print("   - IT must use MAP_REQUEST to advance itself")
        print("   - The user is asking questions, but AI follows game rules")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
