#!/usr/bin/env python3
"""
Debug script to check file path issues in Smart Map Request Handler.
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_file_paths():
    """Debug file path construction."""
    print("🔍 Debugging File Path Issues")
    print("=" * 60)

    root_dir = "aider-main"
    print(f"Root directory: {root_dir}")
    print(f"Root directory absolute: {os.path.abspath(root_dir)}")
    print(f"Root directory exists: {os.path.exists(root_dir)}")
    print()

    # Test find_src_files
    try:
        from aider.repomap import find_src_files
        all_files = find_src_files(root_dir)
        print(f"find_src_files returned {len(all_files)} files")

        # Show first 10 files
        print("First 10 files from find_src_files:")
        for i, file_path in enumerate(all_files[:10]):
            print(f"  {i+1}. {file_path}")

            # Test if file exists using new logic
            if file_path.startswith(root_dir):
                # Path already includes root directory, use as-is
                full_path = file_path
            elif os.path.isabs(file_path):
                # Absolute path, use as-is
                full_path = file_path
            else:
                # Relative path, join with root directory
                full_path = os.path.join(root_dir, file_path)
            exists = os.path.isfile(full_path)
            print(f"      Full path: {full_path}")
            print(f"      Exists: {exists}")
            print()

    except Exception as e:
        print(f"Error with find_src_files: {e}")

        # Fallback to basic file discovery
        print("Using fallback file discovery...")
        all_files = []
        for root, _, filenames in os.walk(root_dir):
            for filename in filenames:
                rel_path = os.path.relpath(os.path.join(root, filename), root_dir)
                all_files.append(rel_path)

        print(f"Fallback found {len(all_files)} files")

        # Show first 10 files
        print("First 10 files from fallback:")
        for i, file_path in enumerate(all_files[:10]):
            print(f"  {i+1}. {file_path}")

            # Test if file exists using new logic
            if file_path.startswith(root_dir):
                # Path already includes root directory, use as-is
                full_path = file_path
            elif os.path.isabs(file_path):
                # Absolute path, use as-is
                full_path = file_path
            else:
                # Relative path, join with root directory
                full_path = os.path.join(root_dir, file_path)
            exists = os.path.isfile(full_path)
            print(f"      Full path: {full_path}")
            print(f"      Exists: {exists}")
            print()

if __name__ == "__main__":
    debug_file_paths()
