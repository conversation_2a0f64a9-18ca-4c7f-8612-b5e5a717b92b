#!/usr/bin/env python

import os
import re
from collections import defaultdict, Counter

def main():
    # Define the input and output files
    input_file = "aider_repo_map_output/dependencies.txt"
    output_file = "aider_repo_map_output/dependency_analysis.txt"
    
    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found")
        return
    
    # Read the dependencies file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract file dependencies using regex
    dependencies = {}
    sections = re.split(r'##\s+([^\n]+)\s+depends on:', content)
    
    # Skip the first section (header)
    sections = sections[1:]
    
    # Process each section
    for i in range(0, len(sections), 2):
        if i+1 < len(sections):
            file_name = sections[i].strip()
            deps_text = sections[i+1]
            
            # Extract dependencies
            deps = []
            for line in deps_text.strip().split('\n'):
                if line.startswith('- '):
                    match = re.search(r'- ([^\(]+) \((\d+) references\)', line)
                    if match:
                        dep_file = match.group(1).strip()
                        ref_count = int(match.group(2))
                        deps.append((dep_file, ref_count))
            
            dependencies[file_name] = deps
    
    print(f"Parsed {len(dependencies)} files with dependencies")
    
    # Identify central files (files that are referenced by many other files)
    reference_counts = Counter()
    for file, deps in dependencies.items():
        for dep_file, _ in deps:
            reference_counts[dep_file] += 1
    
    central_files = sorted(reference_counts.items(), key=lambda x: x[1], reverse=True)
    print(f"Found {len(central_files)} central files")
    
    # Identify strong dependencies (files with 3+ references to other files)
    strong_deps = {}
    for file, deps in dependencies.items():
        strong_deps[file] = [dep for dep in deps if dep[1] >= 3]
    
    strong_deps_count = sum(1 for deps in strong_deps.values() if deps)
    print(f"Found {strong_deps_count} files with strong dependencies")
    
    # Identify class-related files
    class_deps = {}
    for file, deps in dependencies.items():
        if "coder" in file.lower() or "prompts" in file.lower():
            class_deps[file] = deps
    
    print(f"Found {len(class_deps)} class-related files")
    
    # Write the analysis to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Dependency Analysis\n\n")
        
        f.write("## Top Central Files\n")
        f.write("These files are referenced by many other files and are central to the codebase:\n\n")
        for file, count in central_files[:20]:  # Top 20
            f.write(f"- {file}: referenced by {count} files\n")
        
        f.write("\n## Strong Dependencies\n")
        f.write("These are files with strong dependencies (3+ references) to other files:\n\n")
        for file, deps in strong_deps.items():
            if deps:
                f.write(f"### {file} strongly depends on:\n")
                for dep_file, ref_count in sorted(deps, key=lambda x: x[1], reverse=True):
                    f.write(f"- {dep_file} ({ref_count} references)\n")
                f.write("\n")
        
        f.write("\n## Class Dependencies\n")
        f.write("These are dependencies specifically for files likely containing classes:\n\n")
        for file, deps in class_deps.items():
            if deps:
                f.write(f"### {file} depends on:\n")
                for dep_file, ref_count in sorted(deps, key=lambda x: x[1], reverse=True)[:10]:  # Limit to top 10
                    f.write(f"- {dep_file} ({ref_count} references)\n")
                f.write("\n")
    
    print(f"Analysis complete. Results saved to {output_file}")

if __name__ == "__main__":
    main()
