#!/usr/bin/env python3

"""
Test script to diagnose why the LLM is not following the Smart Map Request protocol.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
from aider.io import InputOutput
from aider import models

def test_llm_behavior():
    print("🔍 DIAGNOSING LLM BEHAVIOR ISSUE")
    print("=" * 50)
    
    # Check if Smart Map Request is available
    print(f"✅ Smart Map Request Available: {SMART_MAP_REQUEST_AVAILABLE}")
    
    # Initialize a basic coder with informative edit format
    io = InputOutput()
    model = models.Model('gpt-4')
    
    try:
        coder = Coder.create(main_model=model, io=io, edit_format='informative')
        print("✅ Coder created successfully with informative format")
    except Exception as e:
        print(f"❌ Failed to create coder: {e}")
        return False
    
    # Get the repo messages to see what the LLM receives
    repo_messages = coder.get_repo_messages()
    print(f"\n📋 Repository Messages: {len(repo_messages)} messages")
    
    for i, msg in enumerate(repo_messages):
        print(f"\n--- Message {i+1} ({msg['role']}) ---")
        content = msg['content']
        if len(content) > 300:
            print(content[:300] + "...")
        else:
            print(content)
    
    # Check the base prompts
    print(f"\n📝 Base Prompts Available: {hasattr(coder, 'gpt_prompts')}")
    if hasattr(coder, 'gpt_prompts'):
        print(f"✅ GPT Prompts initialized: {coder.gpt_prompts is not None}")
    
    # Test a simple MAP_REQUEST processing
    print(f"\n🧪 Testing MAP_REQUEST Processing...")
    
    test_content = '''I need to understand how the position quantity calculator works.

{MAP_REQUEST: {"keywords": ["position", "quantity", "calculator"], "type": "implementation", "scope": "all", "max_results": 8}}'''
    
    user_message = "How does the position quantity calculator function work?"
    
    try:
        cleaned_content, map_prompt = coder.process_map_requests(test_content, user_message)
        
        if map_prompt:
            print("✅ MAP_REQUEST processed successfully")
            print(f"📝 Cleaned content length: {len(cleaned_content)}")
            print(f"📋 Map prompt length: {len(map_prompt)}")
            
            # Check if anti-fabrication instructions are in the map prompt
            if "CRITICAL INSTRUCTION" in map_prompt:
                print("✅ Anti-fabrication instructions found in map prompt")
            else:
                print("❌ Anti-fabrication instructions NOT found in map prompt")
                
            if "Your NEXT response must be EXACTLY this format" in map_prompt:
                print("✅ Direct example format found in map prompt")
            else:
                print("❌ Direct example format NOT found in map prompt")
                
        else:
            print("❌ MAP_REQUEST not processed")
            
    except Exception as e:
        print(f"❌ Error processing MAP_REQUEST: {e}")
    
    return True

if __name__ == "__main__":
    test_llm_behavior()
