#!/usr/bin/env python3
"""
Test script to verify the integration of the modular pipeline with AiderIntegrationService.
"""

import os
import time

def test_integration():
    """Test the integration of the modular pipeline."""
    
    print("🧪 Testing Modular Pipeline Integration with AiderIntegrationService")
    print("=" * 70)
    
    try:
        # Import the service
        from aider_integration_service import AiderIntegrationService
        
        print("✅ Successfully imported AiderIntegrationService")
        
        # Create service instance
        service = AiderIntegrationService()
        print("✅ Successfully created service instance")
        
        # Test the IR generation
        print("\n🚀 Testing Mid-Level IR generation...")
        start_time = time.time()
        
        project_path = os.getcwd()
        ir_data = service.generate_mid_level_ir(project_path)
        
        generation_time = time.time() - start_time
        
        print(f"\n✅ Integration test successful!")
        print(f"   Generation time: {generation_time:.2f} seconds")
        print(f"   Modules generated: {len(ir_data.get('modules', []))}")
        print(f"   Total entities: {ir_data.get('metadata', {}).get('total_entities', 'N/A')}")
        
        # Check if we're using the modular pipeline
        generator = service._get_ir_generator()
        if hasattr(generator, 'generate_ir'):
            print("✅ Using enhanced modular Mid-Level IR pipeline")
        else:
            print("⚠️  Using legacy Mid-Level IR generator")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_integration()
    if success:
        print("\n🎉 Integration test completed successfully!")
    else:
        print("\n💥 Integration test failed!")
