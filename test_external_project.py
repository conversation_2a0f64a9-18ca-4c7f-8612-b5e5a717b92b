#!/usr/bin/env python3
"""
Test Smart Map Request Handler with an external project directory
to verify path resolution works correctly.
"""

import os
import sys
import tempfile
import json

def create_test_project():
    """Create a temporary test project with some files."""
    temp_dir = tempfile.mkdtemp(prefix="test_project_")
    
    # Create some test files
    test_files = {
        "main.py": """
def main():
    print("Hello World")
    
if __name__ == "__main__":
    main()
""",
        "utils.py": """
def helper_function():
    return "helper"
    
class UtilityClass:
    def method(self):
        pass
""",
        "config.json": """
{
    "setting1": "value1",
    "setting2": "value2"
}
""",
        "subdir/module.py": """
class SubModule:
    def process(self):
        return "processed"
"""
    }
    
    for file_path, content in test_files.items():
        full_path = os.path.join(temp_dir, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w') as f:
            f.write(content)
    
    return temp_dir

def test_external_project():
    """Test Smart Map Request Handler with external project."""
    print("🧪 Testing Smart Map Request Handler with External Project")
    print("=" * 60)
    
    # Create test project
    project_dir = create_test_project()
    print(f"📁 Created test project at: {project_dir}")
    
    try:
        # Add aider-main to path
        aider_path = os.path.join(os.path.dirname(__file__), 'aider-main')
        sys.path.insert(0, aider_path)
        
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components with external project directory
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        print(f"🔧 Creating RepoMap with root: {project_dir}")
        repo_map = RepoMap(
            map_tokens=8192,
            root=project_dir,  # Use external project directory
            main_model=model,
            io=io,
            verbose=True
        )
        
        print(f"🔧 Creating SmartMapRequestHandler with root_dir: {project_dir}")
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=project_dir,  # Use external project directory
            io=io
        )
        
        print("✅ SmartMapRequestHandler created successfully")
        
        # Test map request
        test_request = {
            "keywords": ["main", "function", "class"],
            "type": "implementation", 
            "scope": "all",
            "max_results": 5
        }
        
        print(f"📝 Testing MAP_REQUEST: {test_request}")
        
        # Execute map request
        result = handler.handle_map_request(test_request)
        
        print("✅ MAP_REQUEST executed successfully")
        print(f"📊 Result length: {len(result)} characters")
        
        # Check if result contains expected content
        if "main.py" in result:
            print("✅ Found main.py in result")
        else:
            print("❌ main.py not found in result")
            
        if "utils.py" in result:
            print("✅ Found utils.py in result")
        else:
            print("❌ utils.py not found in result")
            
        if "UtilityClass" in result or "helper_function" in result:
            print("✅ Found symbols from utils.py")
        else:
            print("❌ No symbols found from utils.py")
        
        print("\n" + "=" * 60)
        print("🎯 Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        import shutil
        shutil.rmtree(project_dir, ignore_errors=True)
        print(f"🧹 Cleaned up test project: {project_dir}")

if __name__ == "__main__":
    success = test_external_project()
    sys.exit(0 if success else 1)
