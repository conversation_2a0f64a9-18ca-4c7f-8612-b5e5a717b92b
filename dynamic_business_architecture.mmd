graph TB
    %% Dynamic Business System Architecture

    %% External Actors & Systems
    Users[👥 Users]
    style Users fill:#e1f5fe,stroke:#0277bd,color:#000
    ExternalSystems[🌐 External Systems]
    style ExternalSystems fill:#fff3e0,stroke:#f57c00,color:#000

    subgraph SystemBoundary[🏢 aider__500]
        direction TB

        subgraph UserInterfaces[User Interfaces]
            call_graph_builder[💻 Interface<br/>(22 components)]
            style call_graph_builder fill:#e8f5e8,stroke:#2e7d32,color:#000
            gui[📱 GUI<br/>(102 components)]
            style gui fill:#e8f5e8,stroke:#2e7d32,color:#000
            ir_builder[💻 Interface<br/>(42 components)]
            style ir_builder fill:#e8f5e8,stroke:#2e7d32,color:#000
            test_browser[🌐 Web Interface<br/>(4 components)]
            style test_browser fill:#e8f5e8,stroke:#2e7d32,color:#000
            test_ui_formatting[💻 Interface<br/>(29 components)]
            style test_ui_formatting fill:#e8f5e8,stroke:#2e7d32,color:#000
        end

        subgraph CoreServices[Core Business Services]
            ir_context[🔍 Context Engine<br/>(31 components)]
            style ir_context fill:#ffebee,stroke:#d32f2f,color:#000
            base_coder[✏️ Code Editor<br/>(673 components)]
            style base_coder fill:#f3e5f5,stroke:#7b1fa2,color:#000
            base_coder_old[✏️ Code Editor<br/>(607 components)]
            style base_coder_old fill:#f3e5f5,stroke:#7b1fa2,color:#000
            test_coder[✏️ Code Editor<br/>(263 components)]
            style test_coder fill:#f3e5f5,stroke:#7b1fa2,color:#000
            repomap[🗺️ Repository Mapper<br/>(247 components)]
            style repomap fill:#f3e5f5,stroke:#7b1fa2,color:#000
            surgical_context_extractor[🔍 Context Engine<br/>(232 components)]
            style surgical_context_extractor fill:#f3e5f5,stroke:#7b1fa2,color:#000
        end

        subgraph IntegrationServices[Integration Services]
            aider_integration_service[🔗 Integration Service<br/>(337 components)]
            style aider_integration_service fill:#fce4ec,stroke:#c2185b,color:#000
            smart_map_request_handler[🔗 Integration Service<br/>(164 components)]
            style smart_map_request_handler fill:#fce4ec,stroke:#c2185b,color:#000
            test_aider_integration_service[🔗 Integration Service<br/>(21 components)]
            style test_aider_integration_service fill:#fce4ec,stroke:#c2185b,color:#000
            test_full_aider_integration[🔗 Integration Service<br/>(25 components)]
            style test_full_aider_integration fill:#fce4ec,stroke:#c2185b,color:#000
            test_integration[🔗 Integration Service<br/>(8 components)]
            style test_integration fill:#fce4ec,stroke:#c2185b,color:#000
        end

        subgraph DataServices[Data Services]
            clean_metadata[🗃️ Data Service<br/>(44 components)]
            style clean_metadata fill:#e0f2f1,stroke:#00695c,color:#000
            metadata_enricher[🗃️ Data Service<br/>(22 components)]
            style metadata_enricher fill:#e0f2f1,stroke:#00695c,color:#000
            models[📊 Data Models<br/>(195 components)]
            style models fill:#e0f2f1,stroke:#00695c,color:#000
            my_models[📊 Data Models<br/>(18 components)]
            style my_models fill:#e0f2f1,stroke:#00695c,color:#000
        end

    end

    %% Primary Workflows
    Users --> UserInterfaces
    UserInterfaces --> CoreServices
    CoreServices --> IntegrationServices
    IntegrationServices <--> ExternalSystems
    CoreServices --> DataServices