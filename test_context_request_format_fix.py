#!/usr/bin/env python3
"""
Test script to verify that the CONTEXT_REQUEST format confusion is fixed.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_context_request_format_instructions():
    """Test that the CONTEXT_REQUEST format instructions are clear and unambiguous."""
    print("🧪 Testing CONTEXT_REQUEST Format Instructions")
    print("=" * 60)
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Check file_access_reminder
        file_access = prompts.file_access_reminder
        
        print("📋 Checking file_access_reminder for CONTEXT_REQUEST clarity...")
        
        context_checks = [
            ("symbols_of_interest", "✅" if "symbols_of_interest" in file_access else "❌"),
            ("OBJECTS with type/name/file_hint", "✅" if "OBJECTS with type/name/file_hint" in file_access else "❌"),
            ("NOT strings", "✅" if "NOT strings" in file_access else "❌"),
            ("CRITICAL", "✅" if "CRITICAL" in file_access else "❌"),
        ]
        
        for check, status in context_checks:
            print(f"  {status} Contains: {check}")
        
        # Check repo_content_prefix
        repo_content = prompts.repo_content_prefix
        
        print("\n📋 Checking repo_content_prefix for CONTEXT_REQUEST clarity...")
        
        repo_checks = [
            ("CRITICAL", "✅" if "CRITICAL" in repo_content else "❌"),
            ("array of OBJECTS", "✅" if "array of OBJECTS" in repo_content else "❌"),
            ("NOT a simple array of strings", "✅" if "NOT a simple array of strings" in repo_content else "❌"),
            ("WRONG", "✅" if "WRONG" in repo_content else "❌"),
            ("CORRECT", "✅" if "CORRECT" in repo_content else "❌"),
        ]
        
        for check, status in repo_checks:
            print(f"  {status} Contains: {check}")
        
        # Count how many checks passed
        all_checks = context_checks + repo_checks
        passed = sum(1 for _, status in all_checks if status == "✅")
        total = len(all_checks)
        
        print(f"\n📊 CONTEXT_REQUEST format clarity: {passed}/{total} checks passed")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST format instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_request_parsing():
    """Test that the context request parsing handles both correct and incorrect formats."""
    print("\n🧪 Testing CONTEXT_REQUEST Parsing")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        
        handler = ContextRequestHandler("aider-main")
        
        print("✅ ContextRequestHandler created successfully")
        
        # Test 1: Correct format (should work)
        print("\n🔍 Testing correct CONTEXT_REQUEST format...")
        
        correct_request = '''
        I need to understand the function implementation.
        
        {CONTEXT_REQUEST: {
          "original_user_query_context": "how does the close_position_based_on_conditions function work?",
          "symbols_of_interest": [
            {"type": "function_definition", "name": "close_position_based_on_conditions", "file_hint": "trade_management/position_exit_manager.py"}
          ],
          "reason_for_request": "To analyze the implementation of the function"
        }}
        '''
        
        parsed_correct = handler.parse_context_request(correct_request)
        
        if parsed_correct:
            print("✅ Correct format parsed successfully")
            print(f"   - Symbols count: {len(parsed_correct.symbols_of_interest)}")
            if parsed_correct.symbols_of_interest:
                symbol = parsed_correct.symbols_of_interest[0]
                print(f"   - First symbol: type='{symbol.type}', name='{symbol.name}', file_hint='{symbol.file_hint}'")
        else:
            print("❌ Correct format failed to parse")
            return False
        
        # Test 2: Incorrect format (should fail or be handled gracefully)
        print("\n🔍 Testing incorrect CONTEXT_REQUEST format (string array)...")
        
        incorrect_request = '''
        I need to understand the function implementation.
        
        {CONTEXT_REQUEST: {
          "original_user_query_context": "how does the close_position_based_on_conditions function work?",
          "symbols_of_interest": ["close_position_based_on_conditions", "PositionCloser", "position", "conditions"],
          "reason_for_request": "To analyze the implementation"
        }}
        '''
        
        parsed_incorrect = handler.parse_context_request(incorrect_request)
        
        if parsed_incorrect:
            print("⚠️  Incorrect format was parsed (system tried to handle it)")
            print(f"   - Symbols count: {len(parsed_incorrect.symbols_of_interest)}")
            if parsed_incorrect.symbols_of_interest:
                symbol = parsed_incorrect.symbols_of_interest[0]
                print(f"   - First symbol: type='{symbol.type}', name='{symbol.name}', file_hint='{symbol.file_hint}'")
        else:
            print("✅ Incorrect format correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST parsing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_examples():
    """Test that the format examples are clear and comprehensive."""
    print("\n🧪 Testing Format Examples")
    print("=" * 60)
    
    print("📋 Correct CONTEXT_REQUEST format examples:")
    
    examples = [
        {
            "description": "Function request",
            "format": {
                "original_user_query_context": "how does the close_position_based_on_conditions function work?",
                "symbols_of_interest": [
                    {"type": "function_definition", "name": "close_position_based_on_conditions", "file_hint": "trade_management/position_exit_manager.py"}
                ],
                "reason_for_request": "To analyze the implementation of the function"
            }
        },
        {
            "description": "Class and method request",
            "format": {
                "original_user_query_context": "how does authentication work?",
                "symbols_of_interest": [
                    {"type": "class_definition", "name": "AuthManager", "file_hint": "auth/manager.py"},
                    {"type": "method_definition", "name": "AuthManager.authenticate", "file_hint": "auth/manager.py"}
                ],
                "reason_for_request": "To understand the authentication system"
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print("   ✅ CORRECT format:")
        print(f"   {example['format']}")
        
        # Show what would be wrong
        wrong_symbols = [symbol['name'] for symbol in example['format']['symbols_of_interest']]
        print("   ❌ WRONG format:")
        print(f"   \"symbols_of_interest\": {wrong_symbols}")
    
    return True

def main():
    """Run all CONTEXT_REQUEST format fix tests."""
    print("🚀 CONTEXT_REQUEST Format Fix Test")
    print("=" * 80)
    
    tests = [
        ("CONTEXT_REQUEST Format Instructions", test_context_request_format_instructions),
        ("CONTEXT_REQUEST Parsing", test_context_request_parsing),
        ("Format Examples", test_format_examples),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 CONTEXT_REQUEST FORMAT FIX TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! CONTEXT_REQUEST format confusion is fixed.")
        print("\n📋 The LLM now understands:")
        print("  1. ✅ symbols_of_interest must be OBJECTS with type/name/file_hint")
        print("  2. ✅ NOT a simple array of strings like MAP_REQUEST keywords")
        print("  3. ✅ Clear examples of WRONG vs CORRECT format")
        print("  4. ✅ CRITICAL warnings about the format requirements")
        print("\n🎯 For query: 'how does close_position_based_on_conditions work?'")
        print("  ✅ Should use: [{\"type\": \"function_definition\", \"name\": \"close_position_based_on_conditions\", \"file_hint\": \"file.py\"}]")
        print("  ❌ Should NOT use: [\"close_position_based_on_conditions\", \"position\", \"conditions\"]")
    else:
        print("⚠️  Some tests failed. Please check the CONTEXT_REQUEST format instructions.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
