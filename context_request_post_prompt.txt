# Code Context Response

## Current Query: How does the close_position_based_on_conditions function work?

**Instructions for this response:**
- Answer ONLY the current query about `close_position_based_on_conditions`
- Use the provided code implementation below
- Ignore any previous conversation history
- Focus on explaining how this specific function works

---

## Function Implementation

**File:** `trade_management/position_exit_manager.py`  
**Class:** `PositionCloser`

### File Structure and Imports
```python
import time
from manual_operations.manual_position_handler import PositionAdjustments
from services.candle_data_service import CandleDataService
from services.technical_analysis_service import TechnicalAnalysisService
from services.target_service import TargetService
from config.trading_pairs_config import SYMBOLS_CONFIG
from models.position_repository import Position
from utils.logger import log
import MetaTrader5 as mt5
```

### Method Implementation
```python
async def close_position_based_on_conditions(self, app):
    try:
        all_mt5_positions = mt5.positions_get()
        if all_mt5_positions is None:
            log("No positions found in MT5")
            return

        mt5_symbols = set(position.symbol for position in all_mt5_positions)
        db_symbols = set(row[0] for row in self.session.query(Position.symbol).distinct())
        all_symbols = mt5_symbols.union(db_symbols)

        for symbol in all_symbols:
            try:
                existing_row = self.session.query(Position).filter_by(symbol=symbol).first()

                if existing_row is None:
                    log(f"Creating new database record for symbol: {symbol}")
                    existing_row = Position(
                        symbol=symbol,
                        position_opened=False,
                        strategy=None,
                        positions_counter=0,
                        total_opened_volume=0,
                        position_ticket=None,
                        amount_to_risk=0,
                        position_direction=0,
                        entry_price=0,
                        take_profit=0,
                        stoploss=0
                    )
                    self.session.add(existing_row)
                    self.session.commit()

                await self.manual_position_handler.handle_manual_position_changes(app, symbol, existing_row)

                symbol_positions = [p for p in all_mt5_positions if p.symbol == symbol]

                if existing_row.position_opened and symbol_positions:
                    close_strategy = self.close_strategies.get(symbol)
                    if not close_strategy:
                        log(f"No close strategy found for symbol: {symbol}")
                        continue

                    # Find matching configuration
                    matching_config = next((config for config_symbol, config in SYMBOLS_CONFIG.items()
                                        if config.get('mt5_symbol') == symbol), None)

                    if not matching_config:
                        log(f"No configuration found for {symbol}")
                        continue

                    # Get price data using CandleDataService
                    price_key = matching_config.get('price_key', symbol)
                    price_data = await self.candle_service.get_candle_data(symbol, price_key)

                    if not price_data:
                        log(f"No price data available for {symbol}. Skipping...")
                        continue

                    # Get targets from TargetService if available, otherwise skip
                    targets = None
                    if self.target_service:
                        config_symbol = next(
                            (sym for sym, cfg in SYMBOLS_CONFIG.items()
                            if cfg.get('mt5_symbol') == symbol),
                            symbol
                        )
                        targets = await self.target_service.get_targets(config_symbol)

                    if not targets:
                        log(f"No targets available for {symbol}. Skipping...")
                        continue

                    # Get technical indicators if available
                    technical_indicators = {}
                    if self.technical_analysis_service:
                        config_symbol = next(
                            (sym for sym, cfg in SYMBOLS_CONFIG.items()
                            if cfg.get('mt5_symbol') == symbol),
                            symbol
                        )
                        technical_indicators = self.technical_analysis_service.get_technical_indicators(config_symbol) or {}

                    # Merge targets with technical indicators
                    combined_targets = {**targets, **technical_indicators}

                    # Add avg to price_data if available
                    if targets and 'avg' in targets:
                        price_data['avg'] = targets['avg']

                    for position in symbol_positions[:existing_row.positions_counter]:
                        try:
                            position_data = {
                                'strategy': existing_row.strategy,
                                'position_direction': existing_row.position_direction,
                                'entry_price': existing_row.entry_price,
                                'open_time': existing_row.open_time,
                                'stoploss': existing_row.stoploss,
                                'take_profit': existing_row.take_profit
                            }

                            should_close, reason = close_strategy.should_close_position(
                                position_data,
                                price_data,
                                combined_targets
                            )

                            if should_close:
                                await self.order_executor.close_all_positions(app, symbol, position.volume)
                                existing_row.position_opened = False
                                existing_row.strategy = None
                                existing_row.positions_counter = 0
                                existing_row.total_opened_volume = 0
                                await self.telegram_manager.notify_success(app, f"{reason} hit. Position closed for {symbol}.")
                                log(f"{reason} hit. Position closed for {symbol}.")
                                self.error_handler.reset_error_count(symbol)
                                break

                        except Exception as e:
                            error_message = f"Error checking close conditions for {symbol}: {e}"
                            log_message = f"Error checking close conditions for position {position.ticket}: {e}"
                            await self.error_handler.handle_error(app, symbol, error_message, log_message)
                            continue

                self.session.commit()
            except Exception as e:
                log(f"Error processing symbol {symbol}: {e}")
                self.session.rollback()

    except Exception as e:
        log(f"Error in close_position_based_on_conditions: {e}")
        await self.telegram_manager.notify_failure(app, f"Error in position management: {e}")
        self.session.rollback()
```

### Class Context
This method belongs to the `PositionCloser` class and uses the following instance variables:
- `self.session`: Database session for position data
- `self.order_executor`: Service to execute position closures
- `self.telegram_manager`: Service for sending notifications
- `self.candle_service`: Service to fetch price/candle data
- `self.technical_analysis_service`: Service for technical indicators
- `self.target_service`: Service for price targets
- `self.manual_position_handler`: Handler for manual position changes
- `self.close_strategies`: Dictionary mapping symbols to their close strategies
- `self.error_handler`: Service for error handling and notifications

### Dependencies Note
The implementation is self-contained within the provided code. No additional method definitions are needed to understand how this function works.