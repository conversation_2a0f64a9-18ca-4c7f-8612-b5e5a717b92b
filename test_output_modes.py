#!/usr/bin/env python3
"""
Simple test for output modes to verify they work correctly.
"""

import os
import sys
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

from aider_integration_service import AiderIntegrationService


def test_output_modes():
    """Test all output modes with a simple feature request."""
    
    print("🧪 Testing Output Modes")
    print("=" * 40)
    
    service = AiderIntegrationService()
    
    feature_description = "Add simple logging functionality"
    focus_areas = ["log", "debug"]
    
    modes = ["summary", "concise", "llm_friendly", "detailed"]
    
    for mode in modes:
        print(f"\n📋 Testing {mode.upper()} mode...")
        
        try:
            # Suppress verbose output
            import contextlib
            import io
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                results = service.find_relevant_code_for_feature(
                    project_path=".",
                    feature_description=feature_description,
                    focus_areas=focus_areas,
                    output_mode=mode
                )
            
            if 'error' not in results:
                print(f"✅ {mode} mode works!")
                
                # Show key structure
                if mode == "summary":
                    print(f"   Keys: {list(results.keys())}")
                    print(f"   Entities found: {results.get('entities_found', 0)}")
                    print(f"   Confidence: {results.get('confidence', 0)}")
                    
                elif mode == "concise":
                    print(f"   Keys: {list(results.keys())}")
                    print(f"   Critical entities: {len(results.get('critical_entities', []))}")
                    print(f"   Safe entities: {len(results.get('safe_entities', []))}")
                    
                elif mode == "llm_friendly":
                    print(f"   Keys: {list(results.keys())}")
                    print(f"   Critical entities: {len(results.get('critical_entities', []))}")
                    print(f"   Safe entities: {len(results.get('safe_entities', []))}")
                    
                elif mode == "detailed":
                    print(f"   Keys: {list(results.keys())}")
                    print(f"   Has dependency_map: {'dependency_map' in results}")
                    print(f"   Has recommendations: {'recommendations' in results}")
                
                # Calculate size
                json_str = json.dumps(results, indent=2)
                size_kb = len(json_str) / 1024
                print(f"   Size: {size_kb:.1f} KB")
                
            else:
                print(f"❌ {mode} mode failed: {results['error']}")
                
        except Exception as e:
            print(f"❌ {mode} mode exception: {e}")
    
    print(f"\n✅ Output modes test complete!")


def show_size_comparison():
    """Show size comparison between modes."""
    
    print(f"\n📊 SIZE COMPARISON")
    print("=" * 30)
    
    service = AiderIntegrationService()
    
    feature_description = "Add error handling"
    focus_areas = ["error", "exception"]
    
    modes = ["summary", "concise", "llm_friendly", "detailed"]
    sizes = {}
    
    for mode in modes:
        try:
            import contextlib
            import io
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                results = service.find_relevant_code_for_feature(
                    project_path=".",
                    feature_description=feature_description,
                    focus_areas=focus_areas,
                    output_mode=mode
                )
            
            if 'error' not in results:
                json_str = json.dumps(results, indent=2)
                sizes[mode] = len(json_str) / 1024
            else:
                sizes[mode] = 0
                
        except Exception:
            sizes[mode] = 0
    
    # Show comparison
    print(f"{'Mode':<12} | {'Size (KB)':<10} | {'Reduction':<10}")
    print("-" * 35)
    
    detailed_size = sizes.get('detailed', 1)
    for mode in modes:
        size = sizes.get(mode, 0)
        if detailed_size > 0:
            reduction = f"{(1 - size/detailed_size)*100:.0f}%"
        else:
            reduction = "N/A"
        print(f"{mode:<12} | {size:<10.1f} | {reduction:<10}")


if __name__ == "__main__":
    print("🔍 OUTPUT MODES TEST")
    print("=" * 30)
    
    test_output_modes()
    show_size_comparison()
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print("• Use 'summary' for dashboards and quick overviews")
    print("• Use 'concise' for daily development (default)")
    print("• Use 'llm_friendly' when sending to AI agents")
    print("• Use 'detailed' for complex analysis and debugging")
