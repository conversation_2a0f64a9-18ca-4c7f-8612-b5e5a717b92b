#!/usr/bin/env python3
"""
Test script to verify MAP_REQUEST pattern matching works correctly.
"""

import sys
import os
import re
sys.path.insert(0, 'aider-main')

def test_map_request_pattern():
    """Test the MAP_REQUEST pattern matching."""
    print("🧪 Testing MAP_REQUEST Pattern Matching")
    print("=" * 60)

    # Test patterns
    map_request_patterns = [
        r'\{\{MAP_REQUEST:\s*(\{.*?\})\s*\}\}',  # Double braces format
        r'\{MAP_REQUEST:\s*(\{.*?\})\s*\}'       # Single braces format
    ]

    # Test content (what the LLM actually sends)
    test_content = '''I need to understand the repository structure.

{MAP_REQUEST: {"keywords": ["backtest", "trading", "strategy"], "type": "implementation", "scope": "all", "max_results": 5}}

How does the backtesting system work?'''

    print(f"📝 Test content:")
    print(test_content)
    print("\n" + "=" * 60)

    # Test pattern matching
    match_found = False
    for i, pattern in enumerate(map_request_patterns):
        match = re.search(pattern, test_content, re.DOTALL)
        if match:
            print(f"✅ Pattern {i+1} matched!")
            print(f"   Pattern: {pattern}")
            print(f"   Matched JSON: {match.group(1)}")

            # Try to parse the JSON
            import json
            try:
                request_json = json.loads(match.group(1))
                print(f"   ✅ JSON parsed successfully: {request_json}")
                match_found = True
                break
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON parsing failed: {e}")
        else:
            print(f"❌ Pattern {i+1} did not match")
            print(f"   Pattern: {pattern}")

    if not match_found:
        print("\n❌ No patterns matched! This is the issue.")
        print("   The LLM is sending MAP_REQUEST but the system can't detect it.")
    else:
        print("\n✅ Pattern matching works correctly!")

    return match_found

def test_process_map_requests():
    """Test the actual process_map_requests method."""
    print("\n🧪 Testing process_map_requests Method")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput

        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False

        print("✅ Smart Map Request System is available")

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput(pretty=False, yes=True)

        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io
        )

        print("✅ Coder created successfully")

        # Test content
        test_content = '''I need to understand the repository structure.

{MAP_REQUEST: {"keywords": ["backtest", "trading", "strategy"], "type": "implementation", "scope": "all", "max_results": 5}}'''

        user_message = "How does the backtesting system work?"

        print(f"\n📝 Testing with content:")
        print(f"Content: {test_content}")
        print(f"User message: {user_message}")

        # Process the MAP_REQUEST
        cleaned_content, map_prompt = coder.process_map_requests(test_content, user_message)

        if map_prompt:
            print("\n✅ MAP_REQUEST processed successfully!")
            print(f"📝 Cleaned content: {cleaned_content}")
            print(f"📋 Map prompt length: {len(map_prompt)}")
            print(f"📋 Map prompt preview (first 200 chars): {map_prompt[:200]}...")
            return True
        else:
            print("\n❌ MAP_REQUEST was not processed")
            print(f"📝 Returned content: {cleaned_content}")
            return False

    except Exception as e:
        print(f"❌ Error testing process_map_requests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 MAP_REQUEST Pattern and Processing Test")
    print("=" * 80)

    # Test 1: Pattern matching
    pattern_works = test_map_request_pattern()

    # Test 2: Actual processing
    processing_works = test_process_map_requests()

    print("\n" + "=" * 80)
    print("📊 FINAL RESULTS:")
    print(f"   Pattern matching: {'✅ PASS' if pattern_works else '❌ FAIL'}")
    print(f"   MAP_REQUEST processing: {'✅ PASS' if processing_works else '❌ FAIL'}")

    if pattern_works and processing_works:
        print("\n🎉 All tests passed! MAP_REQUEST should work correctly now.")
    else:
        print("\n⚠️  Some tests failed. MAP_REQUEST may not work properly.")
