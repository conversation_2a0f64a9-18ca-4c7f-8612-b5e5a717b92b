#!/usr/bin/env python3

"""
Test to verify that repository overview is removed for function-specific queries.
This test verifies that when a function is successfully extracted, the repository
overview is not included to reduce token waste and cognitive overload.
"""

import os
import sys
import tempfile
from pathlib import Path

def test_repository_overview_removal():
    """Test that repository overview is removed for function-specific queries."""
    
    print("🧪 Testing Repository Overview Removal")
    print("=" * 50)
    
    try:
        # Import the required modules
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        from aider.context_request.aider_template_renderer import AiderTemplateRenderer
        
        print("✅ Successfully imported template renderer")
        
        # Create a template renderer
        renderer = AiderTemplateRenderer()
        
        # Create mock data for a successful function extraction
        original_query = "how does the close_position_based_on_conditions function work?"
        
        # Large repository overview (simulating the real scenario)
        repo_overview = """Repository Overview:
./:
main.py
backtest/:
README.md
backtest_position_manager.py
backtest_runner.py
backtest_trade_logger.py
backtest_logs/:
backtest_20250201_20250202.log
backtest_20250301_20250302.log
services/:
position_observer.py
trade_logger.py
trade_management/:
position_exit_manager.py
utils/:
dashboard.py
config_manager.py
web/:
server.py
templates/:
dashboard.html"""
        
        # Mock extracted context with successful symbol extraction
        extracted_context_success = {
            "original_user_query_context": "how does the close_position_based_on_conditions function work?",
            "reason_for_request": "To answer the user's question about position closing",
            "extracted_symbols": [
                {
                    "file_path": "trade_management/position_exit_manager.py",
                    "symbol_name": "close_position_based_on_conditions",
                    "content": "async def close_position_based_on_conditions(self, app):\n    # Implementation here\n    return True",
                    "essential_imports": "import MetaTrader5 as mt5",
                    "containing_class": "PositionCloser"
                }
            ],
            "dependency_snippets": [],
            "not_found_symbols": []
        }
        
        # Mock extracted context with NO symbol extraction (failure case)
        extracted_context_failure = {
            "original_user_query_context": "how does the close_position_based_on_conditions function work?",
            "reason_for_request": "To answer the user's question about position closing",
            "extracted_symbols": [],
            "dependency_snippets": [],
            "not_found_symbols": [
                {
                    "symbol_name": "close_position_based_on_conditions",
                    "reason": "Could not find or extract the symbol content."
                }
            ]
        }
        
        print("🔍 Testing successful function extraction scenario...")
        
        # Test 1: Successful extraction - should NOT include repository overview
        augmented_prompt_success = renderer.render_augmented_prompt(
            original_query=original_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context_success,
            conversation_history=None
        )
        
        # Check if repository overview is included
        repo_indicators = [
            "### REPOSITORY FILES",
            "### REPOSITORY OVERVIEW",
            "backtest_logs/",
            "backtest_20250201_20250202.log"
        ]
        
        found_repo_indicators = []
        for indicator in repo_indicators:
            if indicator in augmented_prompt_success:
                found_repo_indicators.append(indicator)
        
        print(f"📝 Success prompt length: {len(augmented_prompt_success)} characters")
        print(f"🔍 Repository indicators found: {len(found_repo_indicators)}/{len(repo_indicators)}")
        
        if len(found_repo_indicators) == 0:
            print("✅ SUCCESS: Repository overview correctly removed for successful extraction")
            success_test_passed = True
        else:
            print("❌ FAILED: Repository overview still included for successful extraction")
            print(f"   Found indicators: {found_repo_indicators}")
            success_test_passed = False
        
        print("\n🔍 Testing failed function extraction scenario...")
        
        # Test 2: Failed extraction - should include repository overview for broader context
        augmented_prompt_failure = renderer.render_augmented_prompt(
            original_query=original_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context_failure,
            conversation_history=None
        )
        
        found_repo_indicators_failure = []
        for indicator in repo_indicators:
            if indicator in augmented_prompt_failure:
                found_repo_indicators_failure.append(indicator)
        
        print(f"📝 Failure prompt length: {len(augmented_prompt_failure)} characters")
        print(f"🔍 Repository indicators found: {len(found_repo_indicators_failure)}/{len(repo_indicators)}")
        
        if len(found_repo_indicators_failure) > 0:
            print("✅ SUCCESS: Repository overview correctly included for failed extraction")
            failure_test_passed = True
        else:
            print("❌ FAILED: Repository overview missing for failed extraction")
            failure_test_passed = False
        
        # Test 3: Check that the function implementation is still included in success case
        function_indicators = [
            "### REQUESTED SYMBOL DEFINITIONS",
            "close_position_based_on_conditions",
            "trade_management/position_exit_manager.py",
            "async def close_position_based_on_conditions"
        ]
        
        found_function_indicators = []
        for indicator in function_indicators:
            if indicator in augmented_prompt_success:
                found_function_indicators.append(indicator)
        
        print(f"\n🔍 Function implementation indicators: {len(found_function_indicators)}/{len(function_indicators)}")
        
        if len(found_function_indicators) >= 3:
            print("✅ SUCCESS: Function implementation correctly included")
            function_test_passed = True
        else:
            print("❌ FAILED: Function implementation missing or incomplete")
            print(f"   Found indicators: {found_function_indicators}")
            function_test_passed = False
        
        # Calculate token savings
        token_savings = len(augmented_prompt_failure) - len(augmented_prompt_success)
        print(f"\n💰 Token savings: {token_savings} characters ({token_savings/4:.0f} estimated tokens)")
        
        # Overall test result
        all_tests_passed = success_test_passed and failure_test_passed and function_test_passed
        
        return all_tests_passed
        
    except ImportError as e:
        print(f"❌ FAILED: Could not import template renderer: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED: Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_repository_overview_removal()
    
    if success:
        print("\n🎉 REPOSITORY OVERVIEW REMOVAL: PASSED")
        print("The fix correctly removes repository overview for successful function extractions:")
        print("  ✅ Repository overview removed when function is found")
        print("  ✅ Repository overview included when function is not found")
        print("  ✅ Function implementation still included when found")
        print("  💰 Significant token savings achieved")
        print("\nBenefits:")
        print("  - Reduced token usage and costs")
        print("  - Less cognitive overload for the LLM")
        print("  - Faster processing and response times")
        print("  - Focus on relevant code context only")
    else:
        print("\n❌ REPOSITORY OVERVIEW REMOVAL: FAILED")
        print("The fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
