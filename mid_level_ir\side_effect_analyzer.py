"""
Side Effect Analyzer - Detects side effects in functions and methods.

This module analyzes code to identify various types of side effects
including I/O operations, state modifications, and external interactions.
"""

import ast
from typing import Dict, List, Any, Set

from .ir_context import IRContext


class SideEffectAnalyzer:
    """
    Analyzes code for various types of side effects.
    
    This analyzer detects:
    - File I/O operations
    - Logging and print statements
    - Global and nonlocal variable modifications
    - Database and network operations
    - State mutations
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the side effect analyzer with configuration.
        
        Args:
            config: Configuration dictionary for side effect analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Patterns for detecting different types of side effects
        self.io_patterns = {
            'open', 'read', 'write', 'close', 'flush', 'seek', 'tell',
            'readline', 'readlines', 'writelines'
        }
        
        self.logging_patterns = {
            'print', 'log', 'debug', 'info', 'warning', 'error', 'critical',
            'logger', 'logging'
        }
        
        self.network_patterns = {
            'request', 'get', 'post', 'put', 'delete', 'patch',
            'urlopen', 'urlretrieve', 'socket', 'connect'
        }
        
        self.db_patterns = {
            'execute', 'commit', 'rollback', 'insert', 'update', 'delete',
            'query', 'cursor', 'connection'
        }
    
    def analyze(self, context: IRContext) -> IRContext:
        """
        Analyze side effects for all entities in the context.
        
        Args:
            context: The IR context containing modules with entities
            
        Returns:
            Updated context with side effect information
        """
        if self.verbose:
            print(f"   Analyzing side effects in {len(context.modules)} modules")
        
        total_analyzed = 0
        for module_info in context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'async_function') and entity.ast_node:
                    side_effects = self._analyze_entity_side_effects(entity.ast_node, module_info.source_code)
                    entity.side_effects = side_effects
                    total_analyzed += 1
        
        if self.verbose:
            print(f"   Analyzed side effects for {total_analyzed} entities")
        
        return context
    
    def _analyze_entity_side_effects(self, func_node: ast.FunctionDef, source_code: str) -> List[str]:
        """
        Analyze side effects for a single function.
        
        Args:
            func_node: AST function definition node
            source_code: Source code for additional analysis
            
        Returns:
            List of detected side effects
        """
        side_effects = set()
        
        # Use AST visitor to detect side effects
        visitor = SideEffectVisitor(
            self.io_patterns,
            self.logging_patterns,
            self.network_patterns,
            self.db_patterns
        )
        visitor.visit(func_node)
        
        side_effects.update(visitor.side_effects)
        
        # Additional pattern-based analysis on source code
        func_source = self._extract_function_source(func_node, source_code)
        if func_source:
            additional_effects = self._analyze_source_patterns(func_source)
            side_effects.update(additional_effects)
        
        return list(side_effects) if side_effects else ["none"]
    
    def _extract_function_source(self, func_node: ast.FunctionDef, source_code: str) -> str:
        """Extract the source code for a specific function."""
        try:
            lines = source_code.split('\n')
            start_line = func_node.lineno - 1  # Convert to 0-based
            end_line = getattr(func_node, 'end_lineno', start_line + 10)
            
            if end_line and start_line < len(lines):
                return '\n'.join(lines[start_line:end_line])
        except:
            pass
        
        return ""
    
    def _analyze_source_patterns(self, func_source: str) -> Set[str]:
        """Analyze source code for side effect patterns."""
        side_effects = set()
        
        # Check for global/nonlocal modifications
        if 'global ' in func_source:
            side_effects.add('modifies_global')
        if 'nonlocal ' in func_source:
            side_effects.add('modifies_nonlocal')
        
        # Check for self attribute modifications
        if 'self.' in func_source and '=' in func_source:
            side_effects.add('modifies_state')
        
        # Check for dynamic imports
        if 'import ' in func_source and ('importlib' in func_source or '__import__' in func_source):
            side_effects.add('dynamic_import')
        
        return side_effects


class SideEffectVisitor(ast.NodeVisitor):
    """AST visitor to detect side effects in function bodies."""
    
    def __init__(self, io_patterns: Set[str], logging_patterns: Set[str], 
                 network_patterns: Set[str], db_patterns: Set[str]):
        self.side_effects = set()
        self.io_patterns = io_patterns
        self.logging_patterns = logging_patterns
        self.network_patterns = network_patterns
        self.db_patterns = db_patterns
    
    def visit_Call(self, node: ast.Call) -> None:
        """Visit function calls to detect side effects."""
        call_name = self._get_call_name(node.func)
        
        if call_name:
            call_name_lower = call_name.lower()
            
            # Check for I/O operations
            if any(pattern in call_name_lower for pattern in self.io_patterns):
                self.side_effects.add('modifies_file')
            
            # Check for logging
            if any(pattern in call_name_lower for pattern in self.logging_patterns):
                self.side_effects.add('writes_log')
            
            # Check for network operations
            if any(pattern in call_name_lower for pattern in self.network_patterns):
                self.side_effects.add('network_io')
            
            # Check for database operations
            if any(pattern in call_name_lower for pattern in self.db_patterns):
                self.side_effects.add('database_io')
        
        self.generic_visit(node)
    
    def visit_Assign(self, node: ast.Assign) -> None:
        """Visit assignments to detect state modifications."""
        for target in node.targets:
            if isinstance(target, ast.Attribute):
                # Check for self.attribute = value
                if isinstance(target.value, ast.Name) and target.value.id == 'self':
                    self.side_effects.add('modifies_state')
            elif isinstance(target, ast.Subscript):
                # Check for container modifications like list[0] = value
                self.side_effects.add('modifies_container')
        
        self.generic_visit(node)
    
    def visit_Global(self, node: ast.Global) -> None:
        """Visit global statements."""
        self.side_effects.add('modifies_global')
        self.generic_visit(node)
    
    def visit_Nonlocal(self, node: ast.Nonlocal) -> None:
        """Visit nonlocal statements."""
        self.side_effects.add('modifies_nonlocal')
        self.generic_visit(node)
    
    def _get_call_name(self, func_node) -> str:
        """Extract the function name from a call node."""
        if isinstance(func_node, ast.Name):
            return func_node.id
        elif isinstance(func_node, ast.Attribute):
            return func_node.attr
        else:
            return ""
