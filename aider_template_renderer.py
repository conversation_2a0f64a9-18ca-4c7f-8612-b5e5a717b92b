#!/usr/bin/env python

import os
from typing import Dict, List, Optional, Any


class AiderTemplateRenderer:
    """
    Formats extracted context into a well-structured prompt for the LLM.
    """

    def __init__(self):
        """Initialize the template renderer."""
        pass

    def format_repository_overview(self, repo_overview: str) -> str:
        """
        Format the repository overview section.

        Args:
            repo_overview: The repository overview text

        Returns:
            Formatted repository overview section
        """
        return f"""### REPOSITORY OVERVIEW (Aider-Generated Slice)
{repo_overview}
---"""

    def format_conversation_history(self, conversation_history: List[Dict[str, str]]) -> str:
        """
        Format the conversation history section.

        Args:
            conversation_history: List of conversation messages

        Returns:
            Formatted conversation history section
        """
        if not conversation_history:
            return ""

        # Log the full conversation history
        print("\n=== FULL CONVERSATION HISTORY BEFORE FORMATTING ===")
        for i, message in enumerate(conversation_history):
            role = message.get("role", "")
            content = message.get("content", "")
            print(f"Message {i+1} - Role: {role}")
            print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
            print("-" * 50)
        print("=== END OF FULL CONVERSATION HISTORY ===\n")

        # Only include the last 2 exchanges to reduce confusion
        recent_history = conversation_history[-2:] if len(conversation_history) > 2 else conversation_history

        print(f"\n=== USING ONLY THE LAST {len(recent_history)} MESSAGES FROM CONVERSATION HISTORY ===")
        for i, message in enumerate(recent_history):
            role = message.get("role", "")
            content = message.get("content", "")
            print(f"Message {i+1} - Role: {role}")
            print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
            print("-" * 50)
        print("=== END OF SELECTED CONVERSATION HISTORY ===\n")

        result = ""
        for i, message in enumerate(recent_history):
            role = message.get("role", "")
            content = message.get("content", "")

            # Truncate very long messages to prevent overwhelming the context
            if len(content) > 300:
                content = content[:300] + "... [message truncated for brevity]"

            if role == "user":
                result += f"Previous User: {content}\n\n"
            elif role == "assistant":
                result += f"Previous Assistant: {content}\n\n"

        # Log the final formatted history
        print("\n=== FINAL FORMATTED CONVERSATION HISTORY ===")
        print(result)
        print("=== END OF FINAL FORMATTED CONVERSATION HISTORY ===\n")

        return result

    def format_extracted_symbols(self, extracted_symbols: List[Dict[str, Any]]) -> str:
        """
        Format the extracted symbols section.

        Args:
            extracted_symbols: List of extracted symbols

        Returns:
            Formatted extracted symbols section
        """
        if not extracted_symbols:
            return ""

        result = "### REQUESTED SYMBOL DEFINITIONS (Surgically Extracted)\n\n"

        for symbol in extracted_symbols:
            file_path = symbol.get("file_path", "")
            symbol_name = symbol.get("symbol_name", "")
            content = symbol.get("content", "")
            essential_imports = symbol.get("essential_imports", "")
            containing_class = symbol.get("containing_class", "")

            result += f"--- Definition from: {file_path} ---\n"

            # Add imports section if available
            if essential_imports:
                result += "# File header & imports\n"
                result += f"{essential_imports}\n"

            # Add containing class if available
            if containing_class:
                result += f"class {containing_class}\n"

            # Add the symbol content
            result += f"{content}\n"
            result += "---\n\n"

        return result

    def format_dependency_snippets(self, dependency_snippets: List[Dict[str, Any]]) -> str:
        """
        Format the dependency snippets section.

        Args:
            dependency_snippets: List of dependency snippets

        Returns:
            Formatted dependency snippets section
        """
        if not dependency_snippets:
            return ""

        result = "### KEY DEPENDENCY SNIPPETS (Surgically Extracted for Clarity)\n\n"

        for i, snippet in enumerate(dependency_snippets):
            file_path = snippet.get("file_path", "")
            symbol_name = snippet.get("symbol_name", "")
            content = snippet.get("content", "")
            usage_type = snippet.get("usage_type", "unknown")

            result += f"--- Dependency {i+1}: `{symbol_name}` ({usage_type}) from `{file_path}` ---\n"
            result += f"{content}\n"
            result += "---\n\n"

        return result

    def format_not_found_symbols(self, not_found_symbols: List[Dict[str, Any]]) -> str:
        """
        Format the not found symbols section.

        Args:
            not_found_symbols: List of symbols that could not be found

        Returns:
            Formatted not found symbols section
        """
        if not not_found_symbols:
            return ""

        result = "### ⚠️ SYMBOLS NOT FOUND ⚠️\n\n"
        result += "The following symbols could not be found or extracted:\n\n"

        for symbol in not_found_symbols:
            symbol_name = symbol.get("symbol_name", "")
            reason = symbol.get("reason", "Unknown reason")
            result += f"- **{symbol_name}**: {reason}\n"

        result += "\n**SUGGESTIONS:**\n"
        result += "1. Check if the symbol name is spelled correctly\n"
        result += "2. Try using REQUEST_FILE to get the entire file and search manually\n"
        result += "3. Use MAP_REQUEST with different keywords to find similar symbols\n"
        result += "4. The symbol might be in a different file than expected\n\n"
        result += "---\n\n"

        return result

    def format_instructions(self, original_query: str, reason_for_request: str) -> str:
        """
        Format the instructions section.

        Args:
            original_query: The original user query
            reason_for_request: The reason for the context request

        Returns:
            Formatted instructions section
        """
        return f"""You requested additional context for: "{reason_for_request}"

The code context above contains the implementations and dependencies you requested.

Your task is to answer the current user query:
"{original_query}"

If you need additional context, you may request it using the CONTEXT_REQUEST protocol.
"""

    def render_augmented_prompt(self,
                               original_query: str,
                               repo_overview: str,
                               extracted_context: Dict[str, Any],
                               conversation_history: Optional[List[Dict[str, str]]] = None) -> str:
        """
        Render the augmented prompt with the extracted context.

        Args:
            original_query: The original user query
            repo_overview: The repository overview
            extracted_context: The extracted context
            conversation_history: Optional conversation history

        Returns:
            The augmented prompt
        """
        print("\n=== RENDER_AUGMENTED_PROMPT ===")
        print(f"Original query: {original_query}")
        print(f"Repo overview length: {len(repo_overview)} characters")

        if conversation_history:
            print(f"Conversation history provided with {len(conversation_history)} messages:")
            for i, msg in enumerate(conversation_history):
                role = msg.get("role", "unknown")
                content = msg.get("content", "")
                print(f"  Message {i+1} - Role: {role}")
                print(f"  Content (first 50 chars): {content[:50]}..." if len(content) > 50 else f"  Content: {content}")
        else:
            print("No conversation history provided")

        # Extract data from the context
        original_user_query_context = extracted_context.get("original_user_query_context", "")
        reason_for_request = extracted_context.get("reason_for_request", "")
        extracted_symbols = extracted_context.get("extracted_symbols", [])
        dependency_snippets = extracted_context.get("dependency_snippets", [])

        print(f"Original user query context: {original_user_query_context}")
        print(f"Reason for request: {reason_for_request}")
        print(f"Number of extracted symbols: {len(extracted_symbols)}")
        print(f"Number of dependency snippets: {len(dependency_snippets)}")

        # Build the prompt with a clear structure - start with system instructions
        prompt = """You are an AI assistant helping with code-related questions.
Your task is to answer the CURRENT USER QUERY using the provided code context.
DO NOT answer any previous queries from the conversation history.
The conversation history is provided only for context.

"""

        # Add the current query in a clearly separated section
        prompt += f"""
======================================================================
CURRENT USER QUERY - THIS IS THE ONLY QUERY YOU SHOULD ANSWER:
======================================================================

{original_query}

======================================================================
"""

        # Add code context in a clearly separated section
        prompt += """
======================================================================
CODE CONTEXT FOR YOUR ANSWER:
======================================================================

"""

        # Add repository overview
        prompt += self.format_repository_overview(repo_overview)

        # Add extracted symbols
        prompt += self.format_extracted_symbols(extracted_symbols)

        # Add dependency snippets
        prompt += self.format_dependency_snippets(dependency_snippets)

        # Add instructions
        prompt += """
======================================================================
INSTRUCTIONS FOR YOUR ANSWER:
======================================================================

"""
        prompt += self.format_instructions(original_query, reason_for_request)

        # Add conversation history at the END of the prompt
        # This makes it less likely to be confused with the current query
        if conversation_history:
            print("\n=== CONVERSATION HISTORY IN PROMPT ===")
            print(f"Number of messages in history: {len(conversation_history)}")
            for i, msg in enumerate(conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                content = msg.get('content', '')
                print(f"Content (first 50 chars): {content[:50]}..." if len(content) > 50 else f"Content: {content}")
            print("=== END OF CONVERSATION HISTORY IN PROMPT ===\n")

            prompt += """
======================================================================
PREVIOUS CONVERSATION HISTORY (FOR REFERENCE ONLY):
======================================================================

"""
            prompt += self.format_conversation_history(conversation_history)
            prompt += "======================================================================\n"

        print("=== END OF RENDER_AUGMENTED_PROMPT ===\n")

        # Save the prompt to a file for detailed analysis
        try:
            import os
            import datetime

            # Create a logs directory if it doesn't exist
            logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
            os.makedirs(logs_dir, exist_ok=True)

            # Create a filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(logs_dir, f"augmented_prompt_{timestamp}.txt")

            # Write the prompt to the file
            with open(filename, "w", encoding="utf-8") as f:
                f.write("=== AUGMENTED PROMPT ===\n\n")
                f.write(prompt)
                f.write("\n\n=== END OF AUGMENTED PROMPT ===\n")

            print(f"Augmented prompt saved to: {filename}")
        except Exception as e:
            print(f"Error saving augmented prompt to file: {e}")

        return prompt
