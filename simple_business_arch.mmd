graph TB
    Users[👥 Users] --> UI[🖥️ User Interfaces]
    UI --> Core[⚙️ Core Services]
    Core --> Integration[🔗 Integration Services]
    Integration --> External[🌐 External Systems]
    Core --> Data[💾 Data Services]
    
    style Users fill:#e1f5fe,stroke:#0277bd
    style UI fill:#e8f5e8,stroke:#2e7d32
    style Core fill:#fff8e1,stroke:#f9a825
    style Integration fill:#fce4ec,stroke:#c2185b
    style External fill:#fff3e0,stroke:#f57c00
    style Data fill:#e0f2f1,stroke:#00695c
