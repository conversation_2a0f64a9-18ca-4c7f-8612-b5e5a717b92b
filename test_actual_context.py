#!/usr/bin/env python3
"""
Simple test to verify the actual context being generated.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_actual_context():
    """Test what context is actually being generated."""
    
    print("🔍 TESTING ACTUAL CONTEXT GENERATION")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use external project path
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        repo = GitRepo(io, [], external_project)
        
        print(f"📁 Using project path: {external_project}")
        print(f"📁 Repo root: {repo.root}")
        
        # Create coder
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192,
            verbose=True,
            dry_run=True
        )
        
        # Test the path detection method directly
        detected_path = coder._get_project_path_for_context()
        print(f"📁 Detected path: {detected_path}")
        
        # Test the direct IR context generation
        test_query = "How does position management work in the trading system?"
        print(f"\n📝 Testing query: '{test_query}'")
        
        # Call the method directly and capture result
        result = coder.process_direct_ir_context(test_query)
        print(f"🎯 Direct IR context result: {result}")
        
        # Check what was injected into cur_messages
        if hasattr(coder, 'cur_messages') and coder.cur_messages:
            print(f"\n📨 Messages in cur_messages: {len(coder.cur_messages)}")
            for i, msg in enumerate(coder.cur_messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                print(f"   {i+1}. {role}: {len(content)} chars")
                
                # Check if this is the context message
                if role == 'system' and 'Intelligent Context for Your Query' in content:
                    print(f"      🎯 Found context message!")
                    
                    # Save the actual context
                    with open("actual_context.txt", "w", encoding="utf-8") as f:
                        f.write(content)
                    
                    # Analyze the content
                    print(f"      📏 Context size: {len(content)} characters")
                    print(f"      🔍 Contains 'process_all_positions': {'process_all_positions' in content}")
                    print(f"      🔍 Contains 'Position' class: {'class Position' in content}")
                    print(f"      🔍 Contains 'BacktestTradeLogger': {'BacktestTradeLogger' in content}")
                    print(f"      🔍 Contains 'show_send_output': {'show_send_output' in content}")
                    print(f"      🔍 Contains trading files: {'backtest\\\\' in content or 'trade_management\\\\' in content}")
                    
                    # Show first few lines
                    lines = content.split('\n')[:20]
                    print(f"      📄 First 20 lines:")
                    for j, line in enumerate(lines, 1):
                        print(f"         {j:2d}: {line}")
                    
                    break
        else:
            print("❌ No messages found in cur_messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_actual_context()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("Check 'actual_context.txt' for the real context being generated.")
    else:
        print("\n❌ Test failed.")
