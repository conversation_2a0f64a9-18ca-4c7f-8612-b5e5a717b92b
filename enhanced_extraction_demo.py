#!/usr/bin/env python

import os
import sys
from pprint import pprint

from aider_integration_service import AiderIntegrationService
from enhanced_surgical_extractor import EnhancedSurgicalExtractor


def main():
    """Main function to demonstrate the Enhanced Surgical Extractor."""
    if len(sys.argv) < 4:
        print("Usage: python enhanced_extraction_demo.py <project_path> <file_path> <symbol_name>")
        sys.exit(1)

    project_path = sys.argv[1]
    file_path = sys.argv[2]
    symbol_name = sys.argv[3]

    # Initialize the AiderIntegrationService
    try:
        aider_service = AiderIntegrationService()
    except Exception as e:
        print(f"Error initializing AiderIntegrationService: {e}")
        print("Using mock service for demonstration purposes.")
        aider_service = type('MockAiderService', (), {'coder': type('MockCoder', (), {'repo_map': None})})()

    # Initialize the EnhancedSurgicalExtractor
    extractor = EnhancedSurgicalExtractor(aider_service)

    print(f"\nExtracting enhanced context for symbol: {symbol_name} in {file_path}")

    # Extract the enhanced context
    context = extractor.extract_enhanced_context(project_path, symbol_name, file_path)

    if context:
        # Format and display the output
        output = extractor.format_enhanced_context(context)
        print("\n" + output)

        # Print some statistics
        print("\nExtraction Statistics:")
        print(f"  Symbol Type: {context.symbol_type}")
        if context.extraction_range:
            print(f"  Line Range: {context.extraction_range.start_line}-{context.extraction_range.end_line}")
            print(f"  Total Lines: {context.extraction_range.total_lines}")
        if context.usage_contexts:
            print(f"  Usage Contexts: {len(context.usage_contexts)}")
    else:
        print(f"  Failed to extract enhanced context for {symbol_name}")


if __name__ == "__main__":
    main()
