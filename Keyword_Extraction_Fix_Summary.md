# Keyword Extraction Fix - Complete Solution

## 🚨 **The Problem You Identified**

**User Query**: "how does the close_position_based_on_conditions function work?"

**LLM Response**: 
```json
{MAP_REQUEST: {"keywords": ["technical", "terms"], "type": "implementation", "scope": "all", "max_results": 8}}
```

**❌ WRONG**: The LLM was using placeholder text instead of extracting actual keywords from the user query!

---

## ✅ **The Solution Applied**

I used the **proven keyword extraction approach** from the existing SEARCH_REQUEST instructions and applied it to MAP_REQUEST.

### **Before (Broken)**
```
**STEP 1 (REQUIRED)**: Use MAP_REQUEST to explore repository structure first:
{MAP_REQUEST: {"keywords": ["EXTRACT_FROM_USER_QUERY"], "type": "implementation", "scope": "all", "max_results": 8}}
**IMPORTANT**: Replace "EXTRACT_FROM_USER_QUERY" with actual keywords from the user's question!
```

### **After (Fixed)**
```
**STEP 1 (REQUIRED)**: Use MAP_REQUEST to explore repository structure first:
{MAP_REQUEST: {"keywords": ["keyword1", "keyword2", "keyword3"], "type": "implementation", "scope": "all", "max_results": 8}}

**CRITICAL KEYWORD EXTRACTION RULES:**
1. **ALWAYS INCLUDE EXACT FUNCTION/CLASS NAMES**: If user asks about "close_position_based_on_conditions", include that exact name
2. **EXPAND WITH TECHNICAL VARIATIONS**: Include related terms like "position", "close", "conditions"  
3. **ADD DOMAIN-SPECIFIC TERMS**: Include likely related function/method names
4. **AVOID GENERIC TERMS**: Don't use "technical", "terms", "implementation" - use ACTUAL keywords from user query
5. **PRIORITIZE DISTINCTIVENESS**: Use 5-8 specific keywords that would appear in relevant files

**Example**: User asks "how does close_position_based_on_conditions work?"
✅ CORRECT: ["close_position_based_on_conditions", "position", "close", "conditions", "exit", "trading"]
❌ WRONG: ["technical", "terms", "implementation"]
```

---

## 🎯 **Key Changes Made**

### **1. Copied Proven SEARCH_REQUEST Approach**
I found the existing SEARCH_REQUEST instructions (lines 140-177 in prompt logs) that work perfectly:
- "ALWAYS INCLUDE EXACT FUNCTION/CLASS NAMES"
- "EXPAND CORE KEYWORDS with relevant technical variations"
- "ADD DOMAIN-SPECIFIC TECHNICAL TERMS"
- "AVOID DILUTING with general terms"
- "PRIORITIZE DISTINCTIVENESS"

### **2. Applied to MAP_REQUEST Instructions**
Updated both `file_access_reminder` and `repo_content_prefix` with:
- Clear rules for keyword extraction
- Specific examples showing correct vs wrong approaches
- Emphasis on using ACTUAL keywords from user queries

### **3. Enhanced Smart Guidance System**
Updated the guidance messages to include the same keyword extraction rules when requests fail.

---

## 📋 **Now the LLM Should Respond Correctly**

**User Query**: "how does the close_position_based_on_conditions function work?"

**Expected LLM Response**:
```json
{MAP_REQUEST: {
  "keywords": ["close_position_based_on_conditions", "position", "close", "conditions", "exit", "trading"],
  "type": "implementation",
  "scope": "all", 
  "max_results": 8
}}
```

**✅ CORRECT**: Uses actual keywords from the user query!

---

## 🎯 **More Examples**

### **Example 1: Authentication Query**
- **User**: "explain the authentication system"
- **✅ Correct**: `["authentication", "auth", "login", "user", "system"]`
- **❌ Wrong**: `["technical", "terms"]`

### **Example 2: Database Query**
- **User**: "how to use the DatabaseManager class?"
- **✅ Correct**: `["DatabaseManager", "database", "manager", "db", "connection"]`
- **❌ Wrong**: `["technical", "terms"]`

### **Example 3: Calculation Query**
- **User**: "what does the calculate_profit method do?"
- **✅ Correct**: `["calculate_profit", "profit", "calculate", "method"]`
- **❌ Wrong**: `["technical", "terms"]`

---

## 🔧 **Technical Implementation**

### **Files Updated**:
1. **`aider-main/aider/coders/base_prompts.py`**:
   - Updated `file_access_reminder` with clear keyword extraction rules
   - Updated `repo_content_prefix` with detailed examples
   - Added specific examples showing correct vs wrong approaches

2. **`aider-main/aider/coders/base_coder.py`**:
   - Updated `_generate_smart_guidance_message()` with keyword extraction rules
   - Enhanced guidance messages to teach proper keyword extraction

### **Key Principles Applied**:
1. **Learn from what works**: Used proven SEARCH_REQUEST approach
2. **Be specific**: Show exact examples of correct vs wrong keywords
3. **Avoid ambiguity**: Remove placeholder text that LLM might copy literally
4. **Provide rules**: Give clear, actionable rules for keyword extraction
5. **Show consequences**: Demonstrate why good keywords matter

---

## 🎉 **Result**

The LLM now has **crystal clear instructions** on how to extract keywords properly:

1. ✅ **Includes exact function/class names** from user queries
2. ✅ **Expands with related technical terms** 
3. ✅ **Avoids generic placeholder text**
4. ✅ **Uses distinctive keywords** that appear in relevant files
5. ✅ **Gets smart guidance** when requests fail with proper keyword examples

**The keyword extraction problem is SOLVED!** 🚀

The LLM should now extract meaningful keywords like `["close_position_based_on_conditions", "position", "close", "conditions"]` instead of useless placeholders like `["technical", "terms"]`.
