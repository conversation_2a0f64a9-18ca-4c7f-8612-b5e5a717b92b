Search Results
strategy\gbp_strategy.py (score: 13.0)

Match types: content
Symbols: calculate_conditions, GBPConditionStrategy, _validate_price_data
strategy\eur_strategy.py (score: 13.0)

Match types: content
Symbols: calculate_conditions, EURConditionStrategy, _validate_price_data
trade_management\position_entry_manager.py (score: 8.0)

Match types: content
Symbols: _is_valid_trade_condition, TradeSetup, _analyze_trading_conditions
strategy\base.py (score: 8.0)

Match types: content
Symbols: calculate_conditions, get_trade_details, BaseConditionStrategy
trade_management\position_exit_manager.py (score: 8.0)

Match types: content
Symbols: close_position_based_on_conditions
... and 14 more files

Repository Structure
config\environment.py: ⋮ │LIVE_TRADE_LOGS_DB_URI = 'sqlite:///database/live_trade_logs.db' │BACKTEST_TRADE_LOGS_DB_URI = 'sqlite:///database/backtest_trade_logs.db' │ ⋮

market_data\realtime_data_manager.py: ⋮ │class PriceManager: │ def init(self, telegram_manager=None, max_concurrent_tasks=1): │ self.tv = TvDatafeed(TV_USERNAME, TV_PASSWORD) │ self.cache_lock = threading.Lock() │ self.price_cache = self._initialize_price_cache() │ self.internet_connected = True │ self.telegram_manager = telegram_manager │ self.update_queue = asyncio.Queue() │ self.pending_internet_restore_notification = False │ self.semaphore = asyncio.Semaphore(max_concurrent_tasks) # Add semaphore │ ⋮ │ def _initialize_price_cache(self) → dict: ⋮ │ async def _fetch_and_update_price(self, symbol, exchange, interval, timeframe): ⋮ │ async def fetch_price_data(self): ⋮ │ async def start_price_updater(self): ⋮ │ def check_price_cache(self) → bool: ⋮

models\trade_log.py: ⋮ │def get_trade_log_engine(): ⋮ │trade_log_engine = get_trade_log_engine() ⋮ │def ensure_trade_log_tables_exist(): ⋮ │def get_trade_log_session(): ⋮

services\technical_analysis_service.py: ⋮ │class TechnicalAnalysisService: │ """ │ Service for calculating technical indicators that combine data from │ both candle data and target data sources. ⋮ │ def _get_historical_price_data(self, symbol: str, interval, current_time: Optional[datetime] = ⋮ │class PriceStructureAnalyzer: │ """Analyzes market structure and price levels for CCI adjustment""" │ ⋮ │ def calculate_price_structure(self, df: pd.DataFrame) → pd.DataFrame: ⋮ │class MarketDynamicsAnalyzer: │ """Analyzes complex market dynamics including volatility, momentum, and trends""" ⋮ │ def calculate_price_targets(self, df: pd.DataFrame, n_levels: int = 3) → pd.DataFrame: ⋮ │class EnhancedCCICalculator: │ def init( │ self, │ period: int = 80, # Reduced from 100 for faster response │ use_ema: bool = True, │ weights: Dict[str, float] = None, │ ema_alphas: Dict[str, float] = None, │ min_period: int = 40, # Reduced from 50 │ max_period: int = 120, # Reduced from 150 │ volatility_sensitivity: float = 0.4 # Reduced from 0.5 ⋮ │ def calculate_typical_price(self, data: pd.DataFrame, weights: Dict[str, float]) → pd.Series: ⋮

services\trade_logger.py: ⋮ │class TradeLogger: │ """Service for logging trade information""" │ ⋮ │ def log_trade_entry(self, │ symbol: str, │ entry_price: float, │ direction: str, │ volume: float, │ stop_loss: Optional[float] = None, │ take_profit: Optional[float] = None, │ risk_amount: Optional[float] = None, │ risk_percentage: Optional[float] = None, │ strategy: Optional[str] = None, ⋮ │ def log_trade_exit(self, │ trade_id: str, │ exit_price: float, │ pnl: Optional[float] = None, │ pnl_percentage: Optional[float] = None, │ pnl_pips: Optional[float] = None, ⋮ │ def update_trade(self, │ trade_id: str, ⋮ │ def get_trades_by_symbol(self, symbol: str, status: Optional[str] = None) → List[TradeLog]: ⋮ │ def get_trade_by_id(self, trade_id: str) → Optional[TradeLog]: ⋮

strategy\base.py: ⋮ │class StrategyMapping: │ @classmethod │ def get_trade_details(cls) → Dict[str, TradeDetails]: │ return { │ 'reversal_buy': TradeDetails( │ direction='buy', │ strategy=STRATEGY_REVERSAL, │ position_direction=PositionDirection.BUY │ ), │ 'reversal_sell': TradeDetails( │ direction='sell', ⋮ │class BaseConditionStrategy(ABC): │ def init(self): ⋮ │ @abstractmethod │ def calculate_conditions(self, price_data: dict, candle_date) → dict: ⋮ │ def log_price_data(self, price_data: dict, candle_date): ⋮

strategy\btc_strategy.py: ⋮ │class BTCConditionStrategy(BaseStrategyWithRetry): │ def init(self): │ super().init() │ self.logger = logging.getLogger(name) │ self.trading_symbol = "BTCUSDT" │ self.timeframes = ["5m"] │ self.tf = "5m" │ self.prefix = 'btc' ⋮ │ def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date ⋮

strategy\eth_strategy.py: ⋮ │class ETHConditionStrategy(BaseStrategyWithRetry): │ def init(self): │ super().init() │ self.logger = logging.getLogger(name) │ self.trading_symbol = "ETHUSDT" │ self.timeframes = ["1h"] │ self.tf = "1h" │ self.prefix = 'eth' ⋮ │ def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date ⋮

strategy\eur_strategy.py: ⋮ │class EURConditionStrategy(BaseStrategyWithRetry): │ def init(self): │ super().init() │ self.logger = logging.getLogger(name) │ self.trading_symbol = "EURUSD" │ self.timeframes = ["1h"] │ self.tf = "1h" ⋮ │ def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date ⋮ │class EURCloseStrategy(BaseCloseStrategy): │ def init(self): │ super().init() │ self.tf = "1h" ⋮ │ def _validate_price_data(self, price_data: dict, position_data: dict = None) → bool: ⋮ │ def _check_buy_close_conditions( │ self, current_price: float, current_high: float, │ take_profit: float, stop_loss: float, candle_time_check: bool ⋮ │ def _check_sell_close_conditions( │ self, current_price: float, current_low: float, │ take_profit: float, stop_loss: float, candle_time_check: bool ⋮

strategy\gbp_strategy.py: ⋮ │class GBPConditionStrategy(BaseStrategyWithRetry): │ def init(self): │ super().init() │ self.logger = logging.getLogger(name) │ self.trading_symbol = "GBPUSD" │ self.timeframes = ["1h"] │ self.tf = "1h" ⋮ │ def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date ⋮ │class GBPCloseStrategy(BaseCloseStrategy): │ def init(self): │ super().init() │ self.tf = "1h" ⋮ │ def _validate_price_data(self, price_data: dict, position_data: dict = None) → bool: ⋮ │ def _check_buy_close_conditions( │ self, current_price: float, current_high: float, │ take_profit: float, stop_loss: float, candle_time_check: bool ⋮ │ def _check_sell_close_conditions( │ self, current_price: float, current_low: float, │ take_profit: float, stop_loss: float, candle_time_check: bool ⋮

strategy\xau_strategy.py: ⋮ │class XAUConditionStrategy(BaseStrategyWithRetry): │ def init(self): │ super().init() │ self.logger = logging.getLogger(name) │ self.trading_symbol = "XAUUSD" │ self.timeframes = ["1h"] │ self.tf = "1h" │ self.prefix = 'gold' ⋮ │ def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any], candle_date ⋮

trade_management\position_entry_manager.py: ⋮ │@dataclass │class TradeSetup: ⋮ │class PositionOpener: │ def init( │ self, │ session: Session, │ telegram_manager: TelegramManager, │ order_executor: TradeExecutor, │ cash_manager, │ engine, │ candle_data_service: CandleDataService, │ target_service: TargetService, # Make target_service required │ technical_analysis_service=None, # Optional for backward compatibility ⋮ │ async def _get_price_data(self, symbol: str, setup: Dict) → Optional[Dict]: ⋮ │ async def _analyze_trading_conditions( │ self, │ app, │ symbol: str, │ setup: Dict, │ price_data: Dict ⋮ │ def _is_valid_trade_condition(self, condition: Dict) → bool: ⋮ │ async def _execute_valid_trades(self, app, symbol: str, valid_trades: List[TradeSetup], existin ⋮

trade_management\position_exit_manager.py: ⋮ │class PositionCloser: │ def init(self, session, order_executor, telegram_manager, cash_manager, technical_analysis_ │ self.session = session │ self.order_executor = order_executor │ self.telegram_manager = telegram_manager │ self.cash_manager = cash_manager │ self.candle_service = CandleDataService(cash_manager) │ self.technical_analysis_service = technical_analysis_service │ self.target_service = target_service │ self.manual_position_handler = PositionAdjustments(telegram_manager, session, order_executo │ self.close_strategies = {} ⋮ │ async def close_position_based_on_conditions(self, app): ⋮

utils\dashboard.py: ⋮ │class TradingDashboard: │ """ │ A real-time dashboard for displaying critical trading information ⋮ │ def _print_trades(self): ⋮ │ def update_trades(self, trades): ⋮

utils\terminal_logger.py: ⋮ │class TerminalLogger: │ """ │ Enhanced terminal logger with improved formatting and organization. │ │ Features: │ - Different log levels with color coding │ - Structured output for complex data │ - Section headers and separators │ - Configurable verbosity ⋮ │ def trade_setup(self, symbol: str, trade_type: str, entry_price: float, ⋮ │ def trade(self, message: str): ⋮

utils\trade_log_debug.py: ⋮ │def check_trade_log_integrity(): ⋮ │def fix_trade_log_issues(): ⋮ │def print_recent_trades(days=1): ⋮

utils\trade_log_utils.py: ⋮ │def get_trade_logs(symbol: Optional[str] = None, │ days: Optional[int] = None, ⋮ │def print_trade_summary(symbol: Optional[str] = None, ⋮ │def plot_trade_performance(symbol: Optional[str] = None, │ days: Optional[int] = None, ⋮ │def print_trade_logs(symbol: Optional[str] = None, │ days: Optional[int] = None, │ status: Optional[str] = None, ⋮ │def print_trade_executions(trade_id: str) → None: ⋮

utils\trades_dashboard.py: ⋮ │def format_trade_status(status: str) → str: ⋮ │def get_recent_trades(days: int = 7, limit: int = 10) → List[TradeLog]: ⋮ │def get_open_trades() → List[TradeLog]: ⋮ │def display_trades_table(trades: List[TradeLog], title: str = "Recent Trades"): ⋮ │def display_trades_summary(): ⋮ │def display_trades_dashboard(): ⋮

web\server_fastapi.py: ⋮ │TRADE_LOG_DB_PATH = 'database/live_trade_logs.db' # Updated path to match actual file name ⋮ │async def get_trades_from_db_async(days: int = 7, limit: int = 10) → List[Dict[str, Any]]: ⋮

Now please answer the original user query: how does the close_position_based_on_conditions function work?