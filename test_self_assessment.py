#!/usr/bin/env python3
"""
Test script to verify the LLM self-assessment system
"""

import sys
import os

def test_self_assessment_system():
    """Test that the self-assessment system is properly implemented"""
    print("🔍 Testing LLM Self-Assessment System")
    print("=" * 70)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()
        main_system = prompts.main_system

        print("📋 Checking Self-Assessment Elements:")

        # Self-assessment elements
        self_assessment_elements = [
            ("YOUR RESPONSIBILITY", "✅" if "YOUR RESPONSIBILITY" in main_system else "❌"),
            ("YOU must determine YOUR current level", "✅" if "YOU must determine YOUR current level" in main_system else "❌"),
            ("LEVEL SELF-ASSESSMENT CRITERIA", "✅" if "LEVEL SELF-ASSESSMENT CRITERIA" in main_system else "❌"),
            ("YOU are at LEVEL 0", "✅" if "YOU are at LEVEL 0" in main_system else "❌"),
            ("YOU are at LEVEL 1", "✅" if "YOU are at LEVEL 1" in main_system else "❌"),
            ("YOU are at LEVEL 2", "✅" if "YOU are at LEVEL 2" in main_system else "❌"),
            ("Assess YOUR current level", "✅" if "Assess YOUR current level" in main_system else "❌")
        ]

        print("\n🔍 Self-Assessment Elements Check:")
        for element, status in self_assessment_elements:
            print(f"   {status} {element}")

        # Check response format includes self-assessment
        response_format_elements = [
            ("Assess YOUR current level", "✅" if "Assess YOUR current level" in main_system else "❌"),
            ("Determine what level YOU are at", "✅" if "Determine what level YOU are at" in main_system else "❌"),
            ("MY CURRENT LEVEL: LEVEL X because", "✅" if "MY CURRENT LEVEL: LEVEL X because" in main_system else "❌")
        ]

        print("\n📝 Response Format Elements Check:")
        for element, status in response_format_elements:
            print(f"   {status} {element}")

        # Check repo messages
        user_prompt = prompts.smart_map_request_user_prompt
        assistant_reply = prompts.smart_map_request_assistant_reply

        repo_elements = [
            ("YOU must assess YOUR current level", "✅" if "YOU must assess YOUR current level" in user_prompt else "❌"),
            ("Determine YOUR current level", "✅" if "Determine YOUR current level" in user_prompt else "❌"),
            ("I must assess MY current level", "✅" if "I must assess MY current level" in assistant_reply else "❌"),
            ("Assess MY current level", "✅" if "Assess MY current level" in assistant_reply else "❌")
        ]

        print("\n🔗 Repo Messages Elements Check:")
        for element, status in repo_elements:
            print(f"   {status} {element}")

        # Overall assessment
        all_self_assessment = all(status == "✅" for _, status in self_assessment_elements)
        all_response_format = all(status == "✅" for _, status in response_format_elements)
        all_repo_elements = all(status == "✅" for _, status in repo_elements)

        if all_self_assessment and all_response_format and all_repo_elements:
            print("\n🎉 SUCCESS: Self-assessment system is properly implemented!")
            return True
        else:
            print("\n❌ ISSUES: Some self-assessment elements are missing")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_self_assessment_scenarios():
    """Test expected scenarios with self-assessment"""
    print("\n🎯 Expected Self-Assessment Scenarios")
    print("=" * 70)

    print("📝 Scenario 1: AI starts with no information")
    print("   AI thinks: 'What information do I have about this codebase?'")
    print("   AI assesses: 'I have NO repository information'")
    print("   AI determines: 'I am at LEVEL 0'")
    print("   AI announces: 'MY CURRENT LEVEL: LEVEL 0 because I have no repository information'")

    print("\n📝 Scenario 2: AI receives repository map")
    print("   AI thinks: 'I now have repository map information but no specific code'")
    print("   AI assesses: 'I have repository map/file information but NO specific code implementation'")
    print("   AI determines: 'I am at LEVEL 1'")
    print("   AI announces: 'MY CURRENT LEVEL: LEVEL 1 because I have repository information but no specific code'")

    print("\n📝 Scenario 3: AI receives actual code")
    print("   AI thinks: 'I now have the actual function implementation'")
    print("   AI assesses: 'I have the actual code/function implementation I need'")
    print("   AI determines: 'I am at LEVEL 2'")
    print("   AI announces: 'MY CURRENT LEVEL: LEVEL 2 because I have the actual function implementation'")

    print("\n📝 Scenario 4: AI receives insufficient information")
    print("   AI thinks: 'The MAP_REQUEST returned irrelevant files'")
    print("   AI assesses: 'I still have NO useful repository information for this query'")
    print("   AI determines: 'I remain at LEVEL 0'")
    print("   AI announces: 'MY CURRENT LEVEL: LEVEL 0 because the information received was insufficient'")

    return True

def test_expected_ai_responses():
    """Test what AI responses should look like with self-assessment"""
    print("\n🤖 Expected AI Responses with Self-Assessment")
    print("=" * 70)

    print("📝 Example: AI self-assesses and determines its level")

    self_assessment_response = """🔍 **SELF-ASSESSMENT**: Let me determine my current level based on available information.

I am reviewing what information I have access to:
- Do I have repository information about this codebase? NO
- Do I have specific code implementations? NO
- Do I have any context about the requested function? NO

🎮 **MY CURRENT LEVEL**: LEVEL 0 - ZERO KNOWLEDGE because I have no repository information about this codebase.

**My capabilities at LEVEL 0:**
✅ Acknowledge my ignorance about the codebase
✅ Provide a roadmap of information I need to gather
✅ Use MAP_REQUEST to advance to LEVEL 1
❌ Cannot make assumptions about code structure
❌ Cannot provide code explanations

**My roadmap to help you:**
1. **LEVEL 0 → LEVEL 1**: I must use MAP_REQUEST to explore repository structure
2. **LEVEL 1 → LEVEL 2**: I must use CONTEXT_REQUEST to get specific function implementation
3. **LEVEL 2**: I can analyze actual code and answer your question

**Taking action appropriate for LEVEL 0:**

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""

    print(self_assessment_response)

    print(f"\n🔍 Key Self-Assessment Behaviors:")
    print(f"   ✅ AI explicitly reviews what information it has")
    print(f"   ✅ AI determines its level based on available information")
    print(f"   ✅ AI provides reasoning for its level determination")
    print(f"   ✅ AI follows capabilities/restrictions for its determined level")
    print(f"   ✅ AI takes appropriate action for its current level")

    return True

if __name__ == "__main__":
    print("🚀 Testing LLM Self-Assessment System")
    print("=" * 80)

    success1 = test_self_assessment_system()
    success2 = test_expected_self_assessment_scenarios()
    success3 = test_expected_ai_responses()

    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: Self-assessment system is complete!")
        print("\n🔍 Key Features:")
        print("   ✅ AI is responsible for determining its own level")
        print("   ✅ AI assesses level based on available information")
        print("   ✅ AI provides reasoning for level determination")
        print("   ✅ AI follows game rules for its determined level")
        print("   ✅ AI reassesses level after receiving new information")
        print("\n🎯 Expected Behavior:")
        print("   - AI will always start by assessing what information it has")
        print("   - AI will determine its level based on game rules")
        print("   - AI will announce its level with clear reasoning")
        print("   - AI will follow capabilities/restrictions for its level")
        print("   - AI will reassess its level after each information update")
        print("\n🎮 Benefits:")
        print("   - No external state management needed")
        print("   - AI is self-aware of its capabilities")
        print("   - Transparent reasoning for level determination")
        print("   - Automatic level progression based on information")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")

    print("=" * 80)
