# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-05-30 21:32:00
# Project: C:\Users\<USER>\Documents\aider_project\aider__500
# User Query: Why is my context selection taking so long?
# Task Description: Debug performance issues in context selection
# Task Type: debugging
# Max Tokens: 2000
# Focus Entities: performance, context, selection
# Package Size: 11,757 characters

================================================================================

# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

### 1. process_direct_ir_context (method)
- File: aider-main\aider\coders\base_coder.py
- Belongs to Class: `Coder`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "split", "startswith", "tool_output", "_get_project_path_for_context", "..."] (total: 10)
- **Used by**: ["debug_direct_ir_flow", "simple_prompt_capture", "test_gui_direct_ir_flow", "test_actual_context", "base_coder", "..."] (total: 7)
- **Side Effects**: writes_log, network_io, database_io

### 2. process_direct_ir_context (function)
- File: capture_llm_context.py


- Criticality: high | Risk: high
- **Calls**: ["process_direct_ir_context", "super", "get"] (total: 3)
- **Used by**: ["debug_direct_ir_flow", "simple_prompt_capture", "test_gui_direct_ir_flow", "test_actual_context", "base_coder", "..."] (total: 7)
- **Side Effects**: network_io, modifies_state, writes_log

### 3. parse_context_request (function)
- File: context_request_handler.py


- Criticality: high | Risk: medium
- **Calls**: ["search", "strip", "group", "rstrip", "startswith", "..."] (total: 13)
- **Used by**: ["test_directory_file_format", "test_json_parsing_fix", "aider_context_request_integration", "test_aider_context_request_integration", "test_context_request_format_fix", "..."] (total: 6)
- **Side Effects**: network_io, writes_log

### 4. process_context_requests (method)
- File: aider-main\aider\coders\base_coder.py
- Belongs to Class: `Coder`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "getcwd", "basename", "exists", "join", "..."] (total: 20)
- **Used by**: ["test_aider_coder_path_fix", "test_context_request_fix", "test_context_request_code_block_fix", "test_llm_workflow_understanding", "test_full_aider_integration", "..."] (total: 9)
- **Side Effects**: writes_log, network_io, database_io

### 5. process_context_request (method)
- File: context_request_handler.py
- Belongs to Class: `ContextRequestHandler`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `ContextRequestHandler` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["join", "_get_from_cache", "_extract_symbol_content", "_extract_essential_imports", "SymbolInfo", "..."] (total: 9)
- **Used by**: ["test_surgical_extraction_integration", "test_context_request_end_to_end", "test_complete_function_extraction", "test_error_handling_fix", "test_partial_context_request", "..."] (total: 10)
- **Side Effects**: network_io, database_io, modifies_state

### 6. select_optimal_context (function)
- File: intelligent_context_selector.py


- Criticality: medium | Risk: medium
- **Calls**: ["_score_entities_for_task", "_select_entities_within_budget", "_enhance_with_dependency_context", "_build_context_bundle", "get_critical_entities"] (total: 5)
- **Used by**: ["iterative_analysis_engine", "intelligent_context_selector", "test_position_management_inheritance", "aider_integration_service", "test_enhanced_relevance_matching", "..."] (total: 6)
- **Side Effects**: network_io, modifies_state, writes_log

### 7. select_intelligent_context (function)
- File: aider_integration_service.py


- Criticality: medium | Risk: medium
- **Calls**: ["_get_context_selector", "get", "lower", "select_optimal_context", "analyze_context_quality", "..."] (total: 7)
- **Used by**: ["aider_integration_service", "test_intelligent_context_selection"] (total: 2)
- **Side Effects**: network_io, modifies_state, writes_log

### 8. cmd_copy_context (function)
- File: aider-main\aider\commands.py


- Criticality: medium | Risk: medium
- **Calls**: ["format_chat_chunks", "get", "copy", "tool_output", "tool_error"] (total: 5)
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: network_io, modifies_state, writes_log

## KEY IMPLEMENTATIONS (8 functions)
Complete code available on request for any function.

### 1. process_direct_ir_context
```python
    def process_direct_ir_context(self, user_message):
        """
        NEW FLOW: Process user query directly to generate IR context package.
        This implements "Step 1" of the new required flow where:
        User query → system provides LLM-Friendly IR Context Package directly.

        Args:
            user_message: The user's original query

        Returns:
            True if IR context was generated and injected, False otherwise
        """
        try:
            # Check if CONTEXT_REQUEST is available
            if not CONTEXT_REQUEST_AVAILABLE:
                self.io.tool_warning("Direct IR Context functionality is not available. Falling back to traditional flow.")
                return False

            # Skip direct IR context for very short queries or commands
    # ... (implementation continues)
```

### 2. process_direct_ir_context
```python
            def process_direct_ir_context(self, user_message):
                print(f"🎯 process_direct_ir_context called with: '{user_message}'")
                
                # Call the original method
                result = super().process_direct_ir_context(user_message)
                
                if result:
                    print("✅ IR context generation successful")
                    
                    # Capture the injected context
                    if hasattr(self, 'cur_messages') and self.cur_messages:
                        for i, msg in enumerate(self.cur_messages):
                            if msg.get('role') == 'system' and 'Intelligent Context for Your Query' in msg.get('content', ''):
                                self.captured_context = msg['content']
    # ... (implementation continues)
```

### 3. select_optimal_context
```python
    def select_optimal_context(self, task_description: str, task_type: TaskType = TaskType.GENERAL_ANALYSIS,
                             focus_entities: Optional[List[str]] = None) -> ContextBundle:
        """
        Select the most relevant code context for a given task.

        Args:
            task_description: Natural language description of the task
            task_type: Type of development task (affects selection strategy)
            focus_entities: Optional list of specific entities to focus on

        Returns:
            ContextBundle containing the selected entities and metadata
        """
        print(f"🎯 Selecting optimal context for: {task_description}")
        print(f"   Task type: {task_type.value}")
        print(f"   Token budget: {self.max_tokens}")

    # ... (implementation continues)
```

### 4. select_intelligent_context
```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
    # ... (implementation continues)
```

### 5. cmd_copy_context
```python
    def cmd_copy_context(self, args=None):
        """Copy the current chat context as markdown, suitable to paste into a web UI"""

        chunks = self.coder.format_chat_chunks()

        markdown = ""

        # Only include specified chunks in order
        for messages in [chunks.repo, chunks.readonly_files, chunks.chat_files]:
            for msg in messages:
                # Only include user messages
                if msg["role"] != "user":
                    continue

                content = msg["content"]

                # Handle image/multipart content
                if isinstance(content, list):
                    for part in content:
                        if part.get("type") == "text":
                            markdown += part["text"] + "\n\n"
                else:
    # ... (implementation continues)
```

### 6. process_direct_ir_context
```python
            def process_direct_ir_context(self, user_message):
                print(f"🎯 process_direct_ir_context called with: '{user_message}'")
                
                # Check conditions
                if not hasattr(self, 'io'):
                    print("❌ No io attribute")
                    return False
                
                # Check if CONTEXT_REQUEST is available
                try:
                    from aider.context_request import ContextRequestHandler, IRContextRequest
                    print("✅ CONTEXT_REQUEST modules available in method")
                except ImportError as e:
                    print(f"❌ CONTEXT_REQUEST modules not available in method: {e}")
                    return False
                
                # Check query length
    # ... (implementation continues)
```

### 7. parse_context_request
```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
    # ... (implementation continues)
```

### 8. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context.

