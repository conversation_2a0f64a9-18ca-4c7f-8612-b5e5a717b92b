# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

### 1. test_determine_context_window_size (method)
- File: test_surgical_context_extractor.py
- Belongs to Class: `TestSurgicalContextExtractor`
- Inherits From: ['unittest.TestCase']
- Criticality: low | Risk: low

#### 🔁 Class Context
- Part of `TestSurgicalContextExtractor` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['_determine_context_window_size', 'assertGreaterEqual'] (total: 2)
- **Side Effects**: modifies_state

### 2. TestSurgicalContextExtractor (class)
- File: test_surgical_context_extractor.py
- Criticality: low | Risk: low

### 3. process_context_requests (method)
- File: test_repo_map_compatibility.py
- Belongs to Class: `MockCoder`
- Inherits From: No inheritance detected
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `MockCoder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['tool_error', 'detect_context_request', 'tool_output', '...'] (total: 13)
- **Used by**: ['test_llm_workflow_understanding', 'test_context_request_code_block_fix', 'test_context_request_fix', '...'] (total: 9)
- **Side Effects**: modifies_state, network_io, database_io

### 4. test_exchange_code_for_key_request_exception (method)
- File: aider-main\tests\basic\test_onboarding.py
- Belongs to Class: `TestOnboarding`
- Inherits From: ['unittest.TestCase']
- Criticality: low | Risk: low

#### 🔁 Class Context
- Part of `TestOnboarding` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['RequestException', 'DummyIO', 'MagicMock', '...'] (total: 7)
- **Side Effects**: network_io, modifies_state

### 5. test_request_timeout_from_extra_params (method)
- File: aider-main\tests\basic\test_models.py
- Belongs to Class: `TestModels`
- Inherits From: ['unittest.TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestModels` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['Model', 'send_completion', 'assert_called_with', '...'] (total: 4)
- **Side Effects**: network_io

### 6. test_request_timeout_default (method)
- File: aider-main\tests\basic\test_models.py
- Belongs to Class: `TestModels`
- Inherits From: ['unittest.TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestModels` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['Model', 'send_completion', 'assert_called_with', '...'] (total: 4)
- **Side Effects**: network_io

### 7. test_max_context_tokens (method)
- File: aider-main\tests\basic\test_models.py
- Belongs to Class: `TestModels`
- Inherits From: ['unittest.TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestModels` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['Model', 'assertEqual'] (total: 2)
- **Side Effects**: modifies_state

### 8. test_default_model_selection (method)
- File: aider-main\tests\basic\test_main.py
- Belongs to Class: `TestMain`
- Inherits From: ['TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestMain` class
- Inheritance chain: TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['GitTemporaryDirectory', 'main', 'DummyInput', '...'] (total: 9)
- **Side Effects**: modifies_container, network_io, modifies_state

## KEY IMPLEMENTATIONS (8 functions)

### 1. detect_context_request
```python
    def detect_context_request(self, llm_response):
        """
        Detect if the LLM response contains a context request.
        """
        # Implementation details...
        parse_context_request()
        join()
        # ... (implementation continues)
```

### 2. process_context_request
```python
    def process_context_request(self, context_request, original_user_query, repo_overview):
        """
        Process a context request and generate an augmented prompt.
        """
        # Implementation details...
        process_context_request()
        get()
        render_augmented_prompt()
        # ... (implementation continues)
```

### 3. get_context_request_summary
```python
    def get_context_request_summary(self, context_request):
        """
        Get a summary of the context request for logging purposes.
        """
        # Implementation details...
        join()
        # ... (implementation continues)
```

### 4. _get_context_selector
```python
    def _get_context_selector(self, project_path, max_tokens):
        """
        Get the Intelligent Context Selector, initializing it if necessary.
        """
        # Implementation details...
        generate_mid_level_ir()
        IntelligentContextSelector()
        # ... (implementation continues)
```

### 5. select_intelligent_context
```python
    def select_intelligent_context(self, project_path, task_description, task_type, focus_entities, max_tokens):
        """
        Select the most relevant code context for a given task using AI-powered analysis.
        """
        # Implementation details...
        _get_context_selector()
        get()
        lower()
        # ... (implementation continues)
```

### 6. get_intelligent_context
```python
    def get_intelligent_context(self, project_path, task_description, task_type, focus_entities, max_tokens):
        """
        Alias for select_intelligent_context for consistency with documentation.
        """
        # Implementation details...
        select_intelligent_context()
        # ... (implementation continues)
```

### 7. _get_context_extractor
```python
    def _get_context_extractor(self):
        """
        Get the surgical context extractor, initializing it if necessary.
        """
        # Implementation details...
        SurgicalContextExtractor()
        # ... (implementation continues)
```

### 8. get_contextual_dependencies
```python
    def get_contextual_dependencies(self, project_path, primary_file, max_snippets):
        """
        Get a comprehensive map of contextual dependencies for a file.
        """
        # Implementation details...
        _get_context_extractor()
        get_contextual_dependencies()
        append()
        # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes
5. **Analyze inheritance patterns** - understand OOP relationships and method overrides

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context with enhanced inheritance data and working source code implementations.
