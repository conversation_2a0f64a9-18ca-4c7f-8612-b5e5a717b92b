{% if page.highlight_image %}
<meta property="og:image" content="{{ site.url }}{{ page.highlight_image }}">
<meta property="twitter:image" content="{{ site.url }}{{ page.highlight_image }}">
{% else %}
<meta property="og:image" content="{{ site.url }}/assets/aider.jpg">
<meta property="twitter:image" content="{{ site.url }}/assets/aider-square.jpg">
{% endif %}

<!-- Custom site title styling -->
<style>
  @font-face {
    font-family: GlassTTYVT220;
    src: local("Glass TTY VT220"), local("Glass TTY VT220 Medium"), url(/assets/Glass_TTY_VT220.ttf) format("truetype");
  }
  
  .site-title {
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'GlassTTYVT220', monospace;
    color: #14b014; /* terminal green color */
    text-decoration: none;
    letter-spacing: 0.5px;
  }
  
  /* For SVG logo inside site-title */
  .site-title img {
    height: 1.8rem;
    vertical-align: middle;
  }
 
  /* Sidebar gradient styling to match hero section */
  .side-bar { 
    background: linear-gradient(135deg, #ffffff 0%, rgba(20, 176, 20, 0.01) 25%, rgba(20, 176, 20, 0.04) 40%, rgba(220, 230, 255, 0.4) 60%, rgba(205, 218, 255, 0.4) 80%, #F5F6FA 100%);
  }
</style>
<link rel="alternate" type="application/rss+xml" title="RSS Feed" href="{{ site.url }}/feed.xml">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="preload" href="https://fonts.googleapis.com/css?family=Open+Sans:400,700&display=swap" as="style" type="text/css" crossorigin>
<meta name="viewport" content="width=device-width, initial-scale=1">

<!-- Logo Progressive Enhancement for Jekyll pages -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const siteTitle = document.querySelector('.site-title');
    if (siteTitle) {
      const textContent = siteTitle.textContent; // Save the text for fallback
      
      // Create new image element
      const logoImg = new Image();
      logoImg.src = '/assets/logo.svg';
      logoImg.alt = 'Aider Logo';
      logoImg.style.height = '1.8rem';
      logoImg.style.verticalAlign = 'middle';
      
      // Only replace if image loads successfully
      logoImg.onload = function() {
        siteTitle.textContent = ''; // Clear text
        siteTitle.appendChild(logoImg);
      };
      
      // If image fails to load, do nothing (keep the text)
      logoImg.onerror = function() {
        console.log('SVG logo failed to load, keeping text fallback');
      };
    }
  });
</script>
<meta name="theme-color" content="#157878">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<link rel="icon" type="image/png" sizes="32x32" href="{{ '/assets/icons/favicon-32x32.png' | relative_url }}">
<link rel="icon" type="image/png" sizes="16x16" href="{{ '/assets/icons/favicon-16x16.png' | relative_url }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ '/assets/icons/apple-touch-icon.png' | relative_url }}">
<link rel="manifest" href="{{ '/assets/icons/site.webmanifest' | relative_url }}">
<link rel="mask-icon" href="{{ '/assets/icons/safari-pinned-tab.svg' | relative_url }}" color="#5bbad5">
<meta name="msapplication-TileColor" content="#da532c">
<meta name="theme-color" content="#ffffff">

{% if site.analytics.enabled %}
<!-- Cookie Consent -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/cookieconsent@3/build/cookieconsent.min.css" />
<script src="https://cdn.jsdelivr.net/npm/cookieconsent@3/build/cookieconsent.min.js" data-cfasync="false"></script>
<script>
window.addEventListener('load', function(){
  window.cookieconsent.initialise({
    palette: {
      popup: {
        background: "#333333",
        text: "#ffffff"
      },
      button: {
        background: "#ffffff",
        text: "#333333"
      }
    },
    type: "opt-in",
    position: "bottom-left",
    showLink: false,
    dismissOnScroll: true,
    cookie: {
      name: 'cookieconsent_status',
      path: '/',
      domain: 'aider.chat',
      expiryDays: 365
    },
    content: {
      message: "This website uses analytics cookies to help us understand how you use the site.",
      dismiss: "Decline",
      allow: "Accept",
      link: "Learn more",
      href: "https://aider.chat/docs/legal/privacy.html"
    },
    onInitialise: function(status) {
      var type = this.options.type;
      var didConsent = this.hasConsented();
      if (didConsent) {
        initPostHog();
      }
    },
    onStatusChange: function(status, chosenBefore) {
      var type = this.options.type;
      var didConsent = this.hasConsented();
      if (didConsent) {
        initPostHog();
      }
    }
  })
});

// PostHog initialization function
function initPostHog() {
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty createPersonProfile opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('{{ site.analytics.posthog_key }}', {
        api_host: '{{ site.analytics.posthog_host }}',
        person_profiles: 'identified_only'
    })
}
</script>
{% endif %}
