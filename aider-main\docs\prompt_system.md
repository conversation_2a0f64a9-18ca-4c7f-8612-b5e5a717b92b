# Aider Prompt System Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Prompt Hierarchy](#prompt-hierarchy)
3. [Key Prompt Components](#key-prompt-components)
4. [Prompt Processing Flow](#prompt-processing-flow)
5. [File Request System](#file-request-system)
6. [Designing Effective Prompts](#designing-effective-prompts)
7. [Best Practices](#best-practices)
8. [Examples](#examples)

## Introduction

Aider uses a sophisticated prompt system with multiple layers and inheritance to control how the AI model interacts with code. This document explains the structure of this system and provides guidance on how to design effective prompts within it.

## Prompt Hierarchy

The prompt system in Aider follows a class inheritance structure:

```
CoderPrompts (base_prompts.py)
├── Base class with common prompts and instructions
├── Contains file_access_reminder and file_request_instructions
│
├── Inherited by specialized prompt classes:
│   ├── WholeFilePrompts - For whole file editing
│   ├── EditBlockPrompts - For edit block format
│   ├── PatchPrompts - For patch format
│   ├── UdiffPrompts - For unified diff format
│   ├── AskPrompts - For informative mode
│   ├── ContextPrompts - For context analysis
│   └── etc.
```

Each specialized prompt class inherits from `CoderPrompts` and can override specific prompts or add new ones.

## Key Prompt Components

### 1. System Prompts

Each coder class has a `main_system` prompt that defines its primary behavior. This is the first instruction the AI model receives and sets the overall context for the interaction.

Example from `UdiffPrompts`:
```python
main_system = """Act as an expert software developer.
{final_reminders}
Always use best practices when coding.
Respect and use existing conventions, libraries, etc that are already present in the code base.

Take requests for changes to the supplied code.
If the request is ambiguous, ask questions.

Always reply to the user in {language}.

For each file that needs to be changed, write out the changes similar to a unified diff like `diff -U0` would produce.
"""
```

### 2. System Reminder

Additional instructions included at the end of system prompts. This can be used to reinforce important behaviors.

```python
system_reminder = """
NEVER RETURN CODE!
NEVER INVENT OR HALLUCINATE FILES!
ONLY MENTION FILES THAT ACTUALLY EXIST (KNOWN FROM USER INPUT)!
WHEN RESPONDING TO SIMPLE GREETINGS, JUST RESPOND WITH A GREETING!
"""
```

### 3. File Access Reminder

A critical component that's included in all system prompts to ensure the AI requests files before analyzing code.

```python
file_access_reminder = """
CRITICAL: You start with ZERO files loaded. You must explicitly request any file you need using {REQUEST_FILE: path}.
NEVER assume you have access to any file unless you've explicitly requested it and seen its contents.
NEVER provide information about file contents you haven't seen.
"""
```

### 4. File Request Instructions

Detailed instructions on how to request files, included in the repository content messages.

```python
file_request_instructions = """
You have access to a repository map that shows all files in the codebase and their structure.

CRITICAL WORKFLOW INSTRUCTIONS:
1. YOU DO NOT HAVE ANY FILES LOADED AT THE START OF A CONVERSATION. You must explicitly request them.
2. ALWAYS START by requesting relevant files BEFORE attempting to answer ANY code-related question.
...
"""
```

### 5. Repository Content Prefix

Instructions shown when repository information is provided.

```python
repo_content_prefix = """Here are summaries of some files present in my git repository.
These files are provided for your reference and are *read-only*.

CRITICAL WORKFLOW INSTRUCTIONS:
...
"""
```

### 6. Example Messages

Example conversations that demonstrate the expected interaction pattern.

```python
example_messages = [
    dict(
        role="user",
        content="Replace is_prime with a call to sympy.",
    ),
    dict(
        role="assistant",
        content="""Ok, I will:
        ...
        """
    ),
]
```

## Prompt Processing Flow

1. The `fmt_system_prompt` method in `base_coder.py` formats the system prompt:
   - Adds the file access reminder
   - Adds lazy/overeager prompts if configured
   - Adds language instructions
   - Formats platform information and shell command instructions

2. The `format_messages` method organizes messages into chunks:
   - System messages (including the formatted system prompt)
   - Example messages
   - Repository map messages
   - Read-only file messages
   - Chat file messages
   - Current conversation messages

3. These formatted messages are then sent to the AI model

## File Request System

The file request system allows the AI to explicitly request files it needs to analyze:

### Request Format

```
{REQUEST_FILE: path/to/file.py}
```

or for multiple files:

```
{REQUEST_FILES:
- path/to/file1.py
- path/to/file2.py
}
```

### Processing Flow

1. AI generates a response containing a file request
2. `process_file_requests` method detects the request using regex
3. `add_requested_file` method adds the file to the conversation
4. The AI's response is updated to remove the request syntax
5. The conversation continues with the requested file now available

## Designing Effective Prompts

When designing prompts for Aider, consider the following:

### 1. Understand the Inheritance Structure

- Identify which prompt class is most appropriate for your use case
- Understand which prompts are inherited and which are overridden
- Consider how your changes will interact with the existing prompt hierarchy

### 2. Focus on Critical Instructions

- Place the most important instructions at the beginning of prompts
- Use clear, direct language for critical instructions
- Use formatting (ALL CAPS, numbering) to emphasize key points

### 3. Consider the Context

- Remember that prompts are combined with file contents and conversation history
- Ensure your prompts work well with different types of code and user requests
- Test your prompts with a variety of inputs

### 4. Balance Detail and Brevity

- Provide enough detail for the AI to understand the task
- Avoid excessive verbosity that might dilute important instructions
- Use numbered lists for complex instructions

## Best Practices

### 1. Explicit File Requests

Always design prompts to encourage explicit file requests before analysis:

```
CRITICAL: You start with ZERO files loaded. You must explicitly request any file you need.
```

### 2. Clear Formatting Instructions

Be explicit about the expected output format:

```
For each file that needs to be changed, write out the changes similar to a unified diff like `diff -U0` would produce.
```

### 3. Error Prevention

Include instructions to prevent common errors:

```
NEVER assume you already have access to any file unless you've explicitly requested it and seen its contents.
NEVER provide information about file contents you haven't seen.
```

### 4. Example-Based Learning

Include clear examples of the expected interaction pattern:

```python
example_messages = [
    dict(role="user", content="..."),
    dict(role="assistant", content="..."),
]
```

### 5. Consistent Terminology

Use consistent terminology throughout your prompts:

- "Request files" instead of mixing "request", "ask for", "get", etc.
- "Repository map" instead of mixing "repo map", "file list", etc.

## Examples

### Example 1: Adding a New Instruction to All Prompts

To add a new instruction to all prompts, modify the `file_access_reminder` in `base_prompts.py`:

```python
file_access_reminder = """
CRITICAL: You start with ZERO files loaded. You must explicitly request any file you need using {REQUEST_FILE: path}.
NEVER assume you have access to any file unless you've explicitly requested it and seen its contents.
NEVER provide information about file contents you haven't seen.
NEW INSTRUCTION: Always explain your reasoning before requesting files.
"""
```

### Example 2: Creating a New Coder Prompt Class

To create a new coder prompt class with specialized behavior:

```python
from .base_prompts import CoderPrompts

class MySpecialPrompts(CoderPrompts):
    main_system = """Act as an expert software developer with focus on security.
    {final_reminders}
    Always check for security vulnerabilities in the code.
    ...
    """
    
    system_reminder = """
    ALWAYS check for:
    - SQL injection
    - XSS vulnerabilities
    - CSRF vulnerabilities
    """
    
    # Override or add other prompt components as needed
```

### Example 3: Modifying File Request Instructions

To modify how the AI requests files:

```python
file_request_instructions = """
You have access to a repository map that shows all files in the codebase and their structure.

When you need to see a file, request it using:

{REQUEST_FILE: exact_file_path}

IMPORTANT: Before requesting a file, explain why you need it and what you expect to find.
"""
```
