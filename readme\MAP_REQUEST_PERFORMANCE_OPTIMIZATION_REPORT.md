# MAP_REQUEST Performance Optimization Report

## Executive Summary

This report documents the performance bottleneck investigation and optimization of the MAP_REQUEST system in the Smart Map Request Handler. The optimizations target the significant performance gap between CONTEXT_REQUEST (milliseconds) and MAP_REQUEST (seconds) operations.

## Performance Bottlenecks Identified

### 1. **Repository Terms Generation** - MAJOR BOTTLENECK
- **Issue**: `_get_repository_terms()` scanned entire repository on every request
- **Impact**: Multiple seconds of processing time
- **Root Cause**: No caching, processed all files and tags repeatedly

### 2. **Keyword Expansion** - MAJOR BOTTLENECK  
- **Issue**: O(N×M) complexity where N=keywords, M=repository terms
- **Impact**: Nested loops with expensive ML operations (embeddings + cosine similarity)
- **Root Cause**: No batch processing, no result caching, no term limits

### 3. **Repository Map Generation** - MODERATE BOTTLENECK
- **Issue**: `get_ranked_tags()` processed ALL repository files
- **Impact**: Redundant file system operations and tag processing
- **Root Cause**: No incremental processing or caching

### 4. **File System Operations** - MINOR BOTTLENECK
- **Issue**: Multiple file system walks and redundant filtering
- **Impact**: I/O overhead on large repositories
- **Root Cause**: No file list caching

## Optimization Strategies Implemented

### 1. **Multi-Level Caching System**
```python
# Performance optimization caches
self._repository_files_cache = None      # File list cache
self._repository_terms_cache = None      # Terms cache (5min TTL)
self._ranked_tags_cache = None          # Tags cache
self._embedding_cache = {}              # ML embeddings cache
self._search_cache = {}                 # Search results cache
```

### 2. **Keyword Expansion Optimization**
- **Batch Embedding Generation**: Process multiple terms at once
- **Term Limiting**: Limit to 500 most relevant repository terms
- **Result Caching**: Cache expansion results by keyword set
- **Early Termination**: Limit to top 10 similar terms per keyword

### 3. **Repository Processing Optimization**
- **File List Caching**: Cache filtered file list indefinitely
- **Terms Caching**: Cache repository terms with 5-minute TTL
- **Tags Caching**: Cache ranked tags to avoid reprocessing

### 4. **Performance Monitoring**
- **Detailed Timing**: Track search, map generation, and total time
- **Cache Statistics**: Monitor cache hit rates and effectiveness
- **Performance Breakdown**: Show time spent in each phase

## Code Changes Summary

### Modified Files:
- `aider-main/aider/smart_map_request_handler.py` - Core optimization implementation

### Key Methods Optimized:
1. `__init__()` - Added cache initialization
2. `_expand_keywords()` - Added caching, batch processing, term limiting
3. `_get_repository_terms()` - Added caching with TTL
4. `_get_repository_files()` - Added file list caching
5. `_get_ranked_tags()` - Added tags caching
6. `handle_map_request()` - Added detailed performance monitoring

### New Methods Added:
- `clear_caches()` - Cache management
- `get_cache_stats()` - Performance monitoring

## Expected Performance Improvements

### First Request (Cold Start):
- **Before**: 3-10 seconds (full repository processing)
- **After**: 1-3 seconds (optimized processing with limits)
- **Improvement**: 50-70% reduction

### Subsequent Requests (Warm Cache):
- **Before**: 3-10 seconds (no caching)
- **After**: 100-500ms (cached data)
- **Improvement**: 90-95% reduction (10-50x speedup)

### Cache Effectiveness:
- **Repository Files**: Cached indefinitely (static data)
- **Repository Terms**: 5-minute TTL (balance freshness vs performance)
- **Embeddings**: Persistent cache (expensive ML operations)
- **Search Results**: Per-session cache (keyword combinations)

## Testing and Validation

### Performance Test Script:
- `test_map_request_performance.py` - Comprehensive performance testing
- Tests cold start, warm cache, and different keyword scenarios
- Provides detailed timing and cache effectiveness metrics

### Test Scenarios:
1. **Cold Start Test**: First request with empty caches
2. **Warm Cache Test**: Identical request with populated caches
3. **Partial Cache Test**: Different keywords with some cached data

## Monitoring and Maintenance

### Cache Statistics Available:
```python
{
    "repository_files_cached": bool,
    "repository_terms_cached": bool, 
    "ranked_tags_cached": bool,
    "embedding_cache_size": int,
    "search_cache_size": int,
    "cache_age_seconds": float
}
```

### Performance Metrics Tracked:
- Search time (keyword expansion + hierarchical search)
- Map generation time (focused map creation)
- Total request time (end-to-end)
- Cache hit/miss rates

## Recommendations

### Immediate Actions:
1. **Deploy Optimizations**: Implement the caching system
2. **Monitor Performance**: Track timing improvements in production
3. **Test Thoroughly**: Run performance tests on various repository sizes

### Future Enhancements:
1. **Persistent Caching**: Store embeddings to disk for cross-session persistence
2. **Incremental Updates**: Update caches when files change rather than full refresh
3. **Memory Management**: Implement cache size limits and LRU eviction
4. **Parallel Processing**: Use threading for independent operations

### Configuration Tuning:
- **Term Limit**: Adjust 500-term limit based on repository size
- **Cache TTL**: Tune 5-minute TTL based on development velocity
- **Similarity Threshold**: Optimize 0.5 threshold for relevance vs performance

## Conclusion

The implemented optimizations address the root causes of MAP_REQUEST performance issues through intelligent caching, batch processing, and term limiting. The expected 10-50x performance improvement for cached requests should make MAP_REQUEST operations comparable to CONTEXT_REQUEST in terms of response time.

The multi-level caching system ensures that expensive operations (file scanning, embedding generation, repository analysis) are performed only when necessary, while maintaining result accuracy and freshness through appropriate cache invalidation strategies.
