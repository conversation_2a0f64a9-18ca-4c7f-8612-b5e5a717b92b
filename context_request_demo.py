#!/usr/bin/env python

import os
import sys
from pathlib import Path

from aider_integration_service import AiderIntegrationService
from context_request_handler import Context<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContextRequest, SymbolRequest
from aider_template_renderer import <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from aider_context_request_integration import AiderContextRequestIntegration


def main():
    """
    Demo script for the context request functionality.
    """
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\n=== LLM Instructions ===")
    print(integration.get_llm_instructions())
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="SurgicalFileExtractor.extract_symbol_content",
                file_hint="surgical_file_extractor.py"
            ),
            SymbolRequest(
                type="class_definition",
                name="SurgicalFileExtractor",
                file_hint="surgical_file_extractor.py"
            )
        ],
        reason_for_request="To understand how the surgical file extractor works"
    )
    
    # Process the context request
    print("\n=== Processing Context Request ===")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt ===")
    print(augmented_prompt)
    
    # Test with a real LLM response
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: { 
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"},
    {"type": "class_definition", "name": "SurgicalFileExtractor", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    
    # Detect the context request
    print("\n=== Detecting Context Request ===")
    detected_request = integration.detect_context_request(llm_response)
    if detected_request:
        print(f"Detected request: {integration.get_context_request_summary(detected_request)}")
    else:
        print("No context request detected")


if __name__ == "__main__":
    main()
