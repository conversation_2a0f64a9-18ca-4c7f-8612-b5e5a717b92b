#!/usr/bin/env python3
"""
Test script to verify that the context request system handles partial failures gracefully.
This tests that when some symbols are found and others are not, the system provides what it can.
"""

import os
import sys
import json

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_partial_context_request():
    """Test that context requests handle partial failures gracefully."""
    print("🧪 Testing Partial Context Request Handling")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest

        handler = ContextRequestHandler(".")

        # Create a context request with one valid and one invalid symbol
        context_request = ContextRequest(
            original_user_query_context="Testing partial failure handling",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"  # This might exist
                ),
                SymbolRequest(
                    type="method_definition", 
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"  # This might not exist or not contain the symbol
                ),
                SymbolRequest(
                    type="method_definition",
                    name="nonexistent_function",
                    file_hint="nonexistent/file.py"  # This definitely doesn't exist
                )
            ],
            reason_for_request="Testing partial failure handling"
        )

        print("🔍 Testing context request with mixed valid/invalid symbols...")
        print(f"Symbols requested:")
        for symbol in context_request.symbols_of_interest:
            print(f"  - {symbol.name} in {symbol.file_hint}")

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"\n📊 Results:")
        print(f"  Extracted symbols: {len(result.get('extracted_symbols', []))}")
        print(f"  Not found symbols: {len(result.get('not_found_symbols', []))}")
        print(f"  Dependency snippets: {len(result.get('dependency_snippets', []))}")

        # Check that we got partial results
        extracted_symbols = result.get('extracted_symbols', [])
        not_found_symbols = result.get('not_found_symbols', [])

        success_criteria = [
            (len(extracted_symbols) > 0, "At least one symbol was extracted"),
            (len(not_found_symbols) > 0, "Missing symbols were tracked"),
            (len(extracted_symbols) + len(not_found_symbols) == len(context_request.symbols_of_interest), "All symbols accounted for"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            if condition:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")

        # Show details of what was found and what wasn't
        if extracted_symbols:
            print(f"\n✅ Successfully extracted symbols:")
            for symbol in extracted_symbols:
                symbol_name = symbol.get('symbol_name', '')
                file_path = symbol.get('file_path', '')
                content_length = len(symbol.get('content', ''))
                print(f"  - {symbol_name} from {file_path} ({content_length} chars)")

        if not_found_symbols:
            print(f"\n⚠️  Symbols not found (gracefully handled):")
            for symbol in not_found_symbols:
                symbol_name = symbol.get('symbol_name', '')
                reason = symbol.get('reason', '')
                print(f"  - {symbol_name}: {reason}")

        print(f"\n📊 Partial context request: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing partial context request: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_failure_handling():
    """Test that complete failures are handled gracefully."""
    print("\n🧪 Testing Complete Failure Handling")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest

        handler = ContextRequestHandler(".")

        # Create a context request with all invalid symbols
        context_request = ContextRequest(
            original_user_query_context="Testing complete failure handling",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="completely_nonexistent_function",
                    file_hint="nonexistent/file1.py"
                ),
                SymbolRequest(
                    type="method_definition", 
                    name="another_nonexistent_function",
                    file_hint="nonexistent/file2.py"
                )
            ],
            reason_for_request="Testing complete failure handling"
        )

        print("🔍 Testing context request with all invalid symbols...")

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"\n📊 Results:")
        print(f"  Extracted symbols: {len(result.get('extracted_symbols', []))}")
        print(f"  Not found symbols: {len(result.get('not_found_symbols', []))}")

        # Check that we got a graceful failure
        extracted_symbols = result.get('extracted_symbols', [])
        not_found_symbols = result.get('not_found_symbols', [])

        success_criteria = [
            (len(extracted_symbols) == 0, "No symbols were extracted (as expected)"),
            (len(not_found_symbols) == len(context_request.symbols_of_interest), "All symbols marked as not found"),
            (result is not None, "System didn't crash - returned a result"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            if condition:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")

        print(f"\n📊 Complete failure handling: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing complete failure handling: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering_with_partial_results():
    """Test that the template renderer handles partial results correctly."""
    print("\n🧪 Testing Template Rendering with Partial Results")
    print("=" * 60)

    try:
        from aider.context_request.aider_template_renderer import AiderTemplateRenderer

        renderer = AiderTemplateRenderer()

        # Create mock extracted context with partial results
        extracted_context = {
            "original_user_query_context": "Testing partial results",
            "reason_for_request": "Testing template rendering",
            "extracted_symbols": [
                {
                    "symbol_name": "found_function",
                    "file_path": "test/file.py",
                    "content": "def found_function():\n    return 'success'",
                    "essential_imports": "import os",
                    "containing_class": None
                }
            ],
            "dependency_snippets": [],
            "not_found_symbols": [
                {
                    "symbol_name": "missing_function",
                    "reason": "Could not find or extract the symbol content."
                }
            ]
        }

        print("🔍 Testing template rendering with partial results...")

        # Render the augmented prompt
        prompt = renderer.render_augmented_prompt(
            original_query="How do these functions work?",
            repo_overview="Test repository",
            extracted_context=extracted_context,
            conversation_history=[]
        )

        print(f"Generated prompt length: {len(prompt)} characters")

        # Check that the prompt contains both success and failure information
        success_criteria = [
            ("found_function" in prompt, "Successfully extracted symbol is included"),
            ("missing_function" in prompt, "Missing symbol is mentioned"),
            ("SYMBOLS NOT FOUND" in prompt, "Not found section is included"),
            (len(prompt) > 100, "Prompt is substantial"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            if condition:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")

        print(f"\n📊 Template rendering: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing template rendering: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all partial context request tests."""
    print("🚀 Testing Partial Context Request Handling")
    print("=" * 80)

    tests = [
        test_partial_context_request,
        test_complete_failure_handling,
        test_template_rendering_with_partial_results,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 Partial context request handling is working correctly!")
        print("\n📋 The system now:")
        print("  1. ✅ Provides partial results when some symbols are found")
        print("  2. ✅ Gracefully handles missing symbols without crashing")
        print("  3. ✅ Tracks which symbols were found and which weren't")
        print("  4. ✅ Renders templates that include both success and failure info")
        print("  5. ✅ Never leaves the LLM hanging with no response")
    else:
        print("⚠️  Some partial context request features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
