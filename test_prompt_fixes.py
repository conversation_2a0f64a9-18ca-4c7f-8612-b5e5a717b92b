#!/usr/bin/env python3
"""
Test script to verify that the prompt fixes are working correctly.
This tests that the LLM will be forced to execute MAP_REQUEST immediately
instead of thinking about it first.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_prompt_execution_enforcement():
    """Test that the prompt instructions enforce immediate execution."""
    print("🧪 Testing Prompt Execution Enforcement")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check for execution enforcement keywords
        execution_checks = [
            ("EXECUTE IMMEDIATELY", prompts.file_access_reminder),
            ("EXECUTE MAP_REQUEST IMMEDIATELY", prompts.repo_content_prefix),
            ("IMMEDIATE ACTION REQUIRED", prompts.repo_content_prefix),
            ("EXECUTION TRIGGERS", prompts.main_system),
            ("EXECUTE THE REQUEST IMMEDIATELY", prompts.main_system),
            ("VIOLATION WARNING", prompts.repo_content_prefix),
        ]

        passed = 0
        total = len(execution_checks)

        for check_name, prompt_content in execution_checks:
            if check_name in prompt_content:
                print(f"✅ {check_name} found in prompts")
                passed += 1
            else:
                print(f"❌ {check_name} NOT found in prompts")

        # Check that explanatory language is removed
        problematic_phrases = [
            "I'll explore the codebase",
            "Let me explore",
            "I need to explore",
            "I should explore",
        ]

        for phrase in problematic_phrases:
            if phrase in prompts.files_no_full_files_with_repo_map_reply:
                print(f"❌ Problematic phrase '{phrase}' still found in reply")
                passed -= 1

        # Check that the reply is direct
        if prompts.files_no_full_files_with_repo_map_reply.strip().startswith('{{MAP_REQUEST'):
            print("✅ files_no_full_files_with_repo_map_reply is direct (starts with MAP_REQUEST)")
            passed += 1
        else:
            print("❌ files_no_full_files_with_repo_map_reply is not direct")

        print(f"\n📊 Execution enforcement: {passed}/{total + 1} checks passed")
        return passed == (total + 1)

    except Exception as e:
        print(f"❌ Error testing prompt execution enforcement: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_anti_fabrication_strength():
    """Test that anti-fabrication rules are strong enough."""
    print("\n🧪 Testing Anti-Fabrication Strength")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check for strong anti-fabrication keywords
        anti_fab_checks = [
            ("NEVER fabricate", prompts.repo_content_prefix),
            ("NEVER provide function analysis without retrieving", prompts.repo_content_prefix),
            ("VIOLATION WARNING", prompts.repo_content_prefix),
            ("Code fabrication is strictly forbidden", prompts.main_system),
            ("CRITICAL VIOLATION", prompts.reality_check_prompt),
        ]

        passed = 0
        total = len(anti_fab_checks)

        for check_name, prompt_content in anti_fab_checks:
            if check_name in prompt_content:
                print(f"✅ {check_name} found in prompts")
                passed += 1
            else:
                print(f"❌ {check_name} NOT found in prompts")

        print(f"\n📊 Anti-fabrication strength: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing anti-fabrication strength: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_clarity():
    """Test that the workflow instructions are clear and direct."""
    print("\n🧪 Testing Workflow Clarity")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check for clear workflow instructions
        workflow_checks = [
            ("STEP 1 (REQUIRED)", prompts.file_access_reminder),
            ("STEP 1 - EXECUTE MAP_REQUEST IMMEDIATELY", prompts.repo_content_prefix),
            ("CANNOT execute CONTEXT_REQUEST or REQUEST_FILE without MAP_REQUEST", prompts.file_access_reminder),
            ("SEQUENTIAL REQUESTS ONLY", prompts.repo_content_prefix),
            ("NO SIMULTANEOUS REQUESTS", prompts.repo_content_prefix),
        ]

        passed = 0
        total = len(workflow_checks)

        for check_name, prompt_content in workflow_checks:
            if check_name in prompt_content:
                print(f"✅ {check_name} found in prompts")
                passed += 1
            else:
                print(f"❌ {check_name} NOT found in prompts")

        print(f"\n📊 Workflow clarity: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing workflow clarity: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all prompt fix tests."""
    print("🚀 Testing Prompt Fixes for LLM Instruction Following")
    print("=" * 80)

    tests = [
        test_prompt_execution_enforcement,
        test_anti_fabrication_strength,
        test_workflow_clarity,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 All prompt fixes are correctly implemented!")
        print("\n📋 The LLM should now:")
        print("  1. ✅ Execute MAP_REQUEST immediately without thinking")
        print("  2. ✅ Never fabricate code implementations")
        print("  3. ✅ Follow the sequential workflow strictly")
        print("  4. ✅ Use direct execution instead of explanatory language")
        print("  5. ✅ Override internal reasoning patterns that cause delays")
    else:
        print("⚠️  Some prompt fixes need attention. Please review the failed checks.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
