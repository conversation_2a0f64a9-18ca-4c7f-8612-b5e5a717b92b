#!/usr/bin/env python3
"""
Comprehensive Repository Map Analysis Tool

This script analyzes the repository map system to provide detailed metrics on:
- Size and scale analysis
- Token usage and limits
- Reliability and accuracy
- Performance impact
- Coverage and completeness
"""

import os
import sys
import time
import json
import psutil
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, asdict

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.repomap import RepoMap, find_src_files
from aider.models import Model
from aider.io import InputOutput


@dataclass
class RepoMapMetrics:
    """Data class to store comprehensive repository map metrics"""
    # Size and Scale
    total_size_bytes: int = 0
    total_size_kb: float = 0.0
    total_size_mb: float = 0.0
    num_files_mapped: int = 0
    num_symbols_tracked: int = 0
    memory_usage_mb: float = 0.0

    # Token Usage
    total_tokens: int = 0
    tokens_per_file: Dict[str, int] = None
    token_efficiency_ratio: float = 0.0
    context_window_usage_pct: float = 0.0

    # Reliability and Accuracy
    symbol_detection_accuracy_pct: float = 0.0
    missing_symbols: List[str] = None
    incorrectly_mapped_symbols: List[str] = None
    error_rate_pct: float = 0.0

    # Performance
    generation_time_seconds: float = 0.0
    cache_hit_rate_pct: float = 0.0
    processing_bottlenecks: List[str] = None

    # Coverage
    codebase_coverage_pct: float = 0.0
    file_types_included: List[str] = None
    file_types_excluded: List[str] = None
    dependencies_captured: int = 0

    def __post_init__(self):
        if self.tokens_per_file is None:
            self.tokens_per_file = {}
        if self.missing_symbols is None:
            self.missing_symbols = []
        if self.incorrectly_mapped_symbols is None:
            self.incorrectly_mapped_symbols = []
        if self.processing_bottlenecks is None:
            self.processing_bottlenecks = []
        if self.file_types_included is None:
            self.file_types_included = []
        if self.file_types_excluded is None:
            self.file_types_excluded = []


class RepoMapAnalyzer:
    """Comprehensive analyzer for repository map system"""

    def __init__(self, project_path: str = "aider-main", model_name: str = "gpt-3.5-turbo"):
        self.project_path = project_path
        self.model_name = model_name
        self.io = InputOutput()
        self.model = Model(model_name)
        self.metrics = RepoMapMetrics()

    def analyze_repository_map(self) -> RepoMapMetrics:
        """Run comprehensive analysis of the repository map system"""
        print("🔍 Starting comprehensive repository map analysis...")

        # Initialize repository map with high token limit for analysis
        repo_map = RepoMap(
            map_tokens=16384,  # High limit for comprehensive analysis
            root=self.project_path,
            main_model=self.model,
            io=self.io,
            verbose=True,
            refresh="always"
        )

        # Analyze size and scale
        self._analyze_size_and_scale(repo_map)

        # Analyze token usage
        self._analyze_token_usage(repo_map)

        # Analyze performance
        self._analyze_performance(repo_map)

        # Analyze coverage
        self._analyze_coverage(repo_map)

        # Analyze reliability
        self._analyze_reliability(repo_map)

        return self.metrics

    def _analyze_size_and_scale(self, repo_map: RepoMap):
        """Analyze size and scale metrics"""
        print("📏 Analyzing size and scale...")

        # Get all source files
        all_files = find_src_files(self.project_path)
        self.metrics.num_files_mapped = len(all_files)

        # Generate repository map and measure memory
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        start_time = time.time()
        repo_content = repo_map.get_repo_map([], all_files)
        end_time = time.time()

        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        self.metrics.memory_usage_mb = memory_after - memory_before
        self.metrics.generation_time_seconds = end_time - start_time

        if repo_content:
            self.metrics.total_size_bytes = len(repo_content.encode('utf-8'))
            self.metrics.total_size_kb = self.metrics.total_size_bytes / 1024
            self.metrics.total_size_mb = self.metrics.total_size_kb / 1024

            # Count symbols (rough estimate based on lines with definitions)
            lines = repo_content.split('\n')
            symbol_lines = [line for line in lines if any(marker in line for marker in ['def ', 'class ', '│def ', '│class '])]
            self.metrics.num_symbols_tracked = len(symbol_lines)

    def _analyze_token_usage(self, repo_map: RepoMap):
        """Analyze token usage and efficiency"""
        print("🎯 Analyzing token usage...")

        all_files = find_src_files(self.project_path)
        repo_content = repo_map.get_repo_map([], all_files)

        if repo_content:
            self.metrics.total_tokens = repo_map.token_count(repo_content)

            # Calculate token efficiency (tokens per byte)
            if self.metrics.total_size_bytes > 0:
                self.metrics.token_efficiency_ratio = self.metrics.total_tokens / self.metrics.total_size_bytes

            # Calculate context window usage
            max_context = getattr(self.model, 'max_input_tokens', 128000)  # Default to 128k
            if max_context:
                self.metrics.context_window_usage_pct = (self.metrics.total_tokens / max_context) * 100

    def _analyze_performance(self, repo_map: RepoMap):
        """Analyze performance metrics"""
        print("⚡ Analyzing performance...")

        # Test cache performance
        all_files = find_src_files(self.project_path)

        # First call (cache miss)
        start_time = time.time()
        repo_map.get_repo_map([], all_files, force_refresh=True)
        first_call_time = time.time() - start_time

        # Second call (cache hit)
        start_time = time.time()
        repo_map.get_repo_map([], all_files)
        second_call_time = time.time() - start_time

        # Calculate cache efficiency
        if first_call_time > 0:
            cache_speedup = first_call_time / max(second_call_time, 0.001)
            self.metrics.cache_hit_rate_pct = max(0, (1 - second_call_time / first_call_time) * 100)

        # Identify bottlenecks
        if self.metrics.generation_time_seconds > 5.0:
            self.metrics.processing_bottlenecks.append("Slow generation time")
        if self.metrics.memory_usage_mb > 100:
            self.metrics.processing_bottlenecks.append("High memory usage")

    def _analyze_coverage(self, repo_map: RepoMap):
        """Analyze coverage and completeness"""
        print("📊 Analyzing coverage...")

        # Get all files in project
        all_project_files = []
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                if not any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache']):
                    all_project_files.append(os.path.join(root, file))

        # Get source files that would be mapped
        mapped_files = find_src_files(self.project_path)

        # Calculate coverage
        if all_project_files:
            self.metrics.codebase_coverage_pct = (len(mapped_files) / len(all_project_files)) * 100

        # Analyze file types
        mapped_extensions = set()
        excluded_extensions = set()

        for file_path in all_project_files:
            ext = Path(file_path).suffix.lower()
            if file_path in mapped_files:
                mapped_extensions.add(ext)
            else:
                excluded_extensions.add(ext)

        self.metrics.file_types_included = sorted(list(mapped_extensions))
        self.metrics.file_types_excluded = sorted(list(excluded_extensions))

    def _analyze_reliability(self, repo_map: RepoMap):
        """Analyze reliability and accuracy"""
        print("🎯 Analyzing reliability...")

        # This is a simplified analysis - in a real scenario, you'd compare
        # against a ground truth or use more sophisticated symbol detection

        all_files = find_src_files(self.project_path)
        repo_content = repo_map.get_repo_map([], all_files)

        if repo_content:
            # Count actual symbols vs detected symbols (rough estimate)
            total_actual_symbols = 0
            for file_path in all_files[:10]:  # Sample first 10 files
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Count function and class definitions
                        lines = content.split('\n')
                        actual_symbols = len([line for line in lines if line.strip().startswith(('def ', 'class '))])
                        total_actual_symbols += actual_symbols
                except:
                    continue

            # Estimate accuracy based on symbol detection
            if total_actual_symbols > 0:
                detected_symbols = min(self.metrics.num_symbols_tracked, total_actual_symbols)
                self.metrics.symbol_detection_accuracy_pct = (detected_symbols / total_actual_symbols) * 100
            else:
                self.metrics.symbol_detection_accuracy_pct = 100.0

    def generate_report(self) -> str:
        """Generate a comprehensive analysis report"""
        report = f"""
# Repository Map Analysis Report

## 📏 Size and Scale Analysis
- **Total Size**: {self.metrics.total_size_mb:.2f} MB ({self.metrics.total_size_kb:.1f} KB, {self.metrics.total_size_bytes:,} bytes)
- **Files Mapped**: {self.metrics.num_files_mapped:,}
- **Symbols Tracked**: {self.metrics.num_symbols_tracked:,}
- **Memory Usage**: {self.metrics.memory_usage_mb:.2f} MB

## 🎯 Token Usage and Limits
- **Total Tokens**: {self.metrics.total_tokens:,}
- **Token Efficiency**: {self.metrics.token_efficiency_ratio:.4f} tokens/byte
- **Context Window Usage**: {self.metrics.context_window_usage_pct:.1f}%

## ⚡ Performance Impact
- **Generation Time**: {self.metrics.generation_time_seconds:.2f} seconds
- **Cache Hit Rate**: {self.metrics.cache_hit_rate_pct:.1f}%
- **Processing Bottlenecks**: {', '.join(self.metrics.processing_bottlenecks) if self.metrics.processing_bottlenecks else 'None detected'}

## 📊 Coverage and Completeness
- **Codebase Coverage**: {self.metrics.codebase_coverage_pct:.1f}%
- **File Types Included**: {', '.join(self.metrics.file_types_included)}
- **File Types Excluded**: {', '.join(self.metrics.file_types_excluded)}

## 🎯 Reliability and Accuracy
- **Symbol Detection Accuracy**: {self.metrics.symbol_detection_accuracy_pct:.1f}%
- **Error Rate**: {self.metrics.error_rate_pct:.1f}%

## 🔍 Critical Issues Identified
"""

        # Identify critical issues
        issues = []
        if self.metrics.context_window_usage_pct > 80:
            issues.append(f"⚠️  High context window usage ({self.metrics.context_window_usage_pct:.1f}%)")
        if self.metrics.generation_time_seconds > 10:
            issues.append(f"⚠️  Slow generation time ({self.metrics.generation_time_seconds:.1f}s)")
        if self.metrics.symbol_detection_accuracy_pct < 90:
            issues.append(f"⚠️  Low symbol detection accuracy ({self.metrics.symbol_detection_accuracy_pct:.1f}%)")
        if self.metrics.memory_usage_mb > 200:
            issues.append(f"⚠️  High memory usage ({self.metrics.memory_usage_mb:.1f} MB)")

        if issues:
            report += '\n'.join(issues)
        else:
            report += "✅ No critical issues detected"

        return report


def main():
    """Main analysis function"""
    analyzer = RepoMapAnalyzer()

    try:
        metrics = analyzer.analyze_repository_map()
        report = analyzer.generate_report()

        # Save detailed metrics to JSON
        with open('repomap_metrics.json', 'w') as f:
            json.dump(asdict(metrics), f, indent=2, default=str)

        # Save report to file
        with open('repomap_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("\n" + "="*80)
        print(report)
        print("="*80)
        print(f"\n📄 Detailed metrics saved to: repomap_metrics.json")
        print(f"📄 Analysis report saved to: repomap_analysis_report.md")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
