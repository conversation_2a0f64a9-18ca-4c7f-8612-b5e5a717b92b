#!/usr/bin/env python

import os
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import shutil
import sys

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

from aider_integration_service import AiderIntegrationService, AiderProjectManager


class TestAiderIntegrationService(unittest.TestCase):
    """
    Unit tests for the AiderIntegrationService class.
    """

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()

        # Create a mock project structure
        self.project_path = os.path.join(self.test_dir, "test_project")
        os.makedirs(self.project_path, exist_ok=True)

        # Create some test Python files
        self.create_test_files()

        # Initialize the service
        self.service = AiderIntegrationService()

        # Mock the dependency data
        self.mock_dependency_data()

    def tearDown(self):
        """Clean up after tests."""
        # Remove the temporary directory
        shutil.rmtree(self.test_dir)

    def create_test_files(self):
        """Create test Python files for the tests."""
        # Create a base class file
        base_class_path = os.path.join(self.project_path, "base.py")
        with open(base_class_path, "w") as f:
            f.write("""
class BaseClass:
    def __init__(self):
        self.value = 0

    def base_method(self):
        return self.value
""")

        # Create a derived class file
        derived_class_path = os.path.join(self.project_path, "derived.py")
        with open(derived_class_path, "w") as f:
            f.write("""
from base import BaseClass

class DerivedClass(BaseClass):
    def __init__(self):
        super().__init__()
        self.derived_value = 1

    def derived_method(self):
        return self.derived_value + self.base_method()
""")

        # Create a utility file
        util_path = os.path.join(self.project_path, "util.py")
        with open(util_path, "w") as f:
            f.write("""
def utility_function():
    return "Utility"

class UtilityClass:
    def util_method(self):
        return "Utility Method"
""")

        # Create a main file that imports the others
        main_path = os.path.join(self.project_path, "main.py")
        with open(main_path, "w") as f:
            f.write("""
from base import BaseClass
from derived import DerivedClass
from util import utility_function, UtilityClass

def main():
    base = BaseClass()
    derived = DerivedClass()
    util = UtilityClass()

    print(base.base_method())
    print(derived.derived_method())
    print(utility_function())
    print(util.util_method())

if __name__ == "__main__":
    main()
""")

    def mock_dependency_data(self):
        """Mock the dependency data for testing."""
        # Mock the dependency data in the project manager
        self.service.project_manager.dependency_data = {
            "central_files": [
                {"file": "base.py", "references": 2},
                {"file": "util.py", "references": 1},
                {"file": "derived.py", "references": 1}
            ],
            "dependencies": {
                "main.py": [
                    ("base.py", 3),
                    ("derived.py", 2),
                    ("util.py", 2)
                ],
                "derived.py": [
                    ("base.py", 2)
                ]
            },
            "class_dependencies": {}
        }

    @patch('aider_integration_service.RepoMap')
    def test_get_top_central_files(self, mock_repomap):
        """Test getting top central files."""
        # Test the method
        top_files = self.service.get_top_central_files(self.project_path, count=2)

        # Verify the results
        self.assertEqual(len(top_files), 2)
        self.assertEqual(top_files[0]["file"], "base.py")
        self.assertEqual(top_files[0]["references"], 2)
        self.assertEqual(top_files[1]["file"], "util.py")
        self.assertEqual(top_files[1]["references"], 1)

    def test_get_files_that_import(self):
        """Test getting files that import a target file."""
        # Mock the get_files_that_import method
        with patch.object(self.service.project_manager, 'get_files_that_import') as mock_method:
            # Set up the mock to return a list of files
            mock_method.return_value = ["main.py", "derived.py"]

            # Test the method
            target_file = os.path.join(self.project_path, "base.py")
            importing_files = self.service.get_files_that_import(self.project_path, target_file)

            # Verify the results
            self.assertEqual(len(importing_files), 2)
            self.assertIn("main.py", importing_files)
            self.assertIn("derived.py", importing_files)

            # Verify the mock was called with the correct arguments
            mock_method.assert_called_once()

    def test_get_files_imported_by(self):
        """Test getting files imported by a target file."""
        # Mock the get_files_imported_by method
        with patch.object(self.service.project_manager, 'get_files_imported_by') as mock_method:
            # Set up the mock to return a list of files
            mock_method.return_value = ["base.py"]

            # Test the method
            target_file = os.path.join(self.project_path, "derived.py")
            imported_files = self.service.get_files_imported_by(self.project_path, target_file)

            # Verify the results
            self.assertEqual(len(imported_files), 1)
            self.assertIn("base.py", imported_files)

            # Verify the mock was called with the correct arguments
            mock_method.assert_called_once()

    @patch('aider_integration_service.AiderProjectManager._extract_class_info_from_repomap')
    def test_get_base_classes_of(self, mock_extract):
        """Test getting base classes of a class."""
        # Mock the class info
        mock_extract.return_value = {
            "DerivedClass": {
                "file_path": "derived.py",
                "base_classes": ["BaseClass"],
                "derived_classes": [],
                "methods": ["derived_method"]
            },
            "BaseClass": {
                "file_path": "base.py",
                "base_classes": [],
                "derived_classes": ["DerivedClass"],
                "methods": ["base_method"]
            }
        }

        # Test the method
        base_classes = self.service.get_base_classes_of(self.project_path, "DerivedClass")

        # Verify the results
        self.assertEqual(len(base_classes), 1)
        self.assertEqual(base_classes[0]["class_name"], "BaseClass")

    @patch('aider_integration_service.AiderProjectManager._extract_class_info_from_repomap')
    def test_get_derived_classes_of(self, mock_extract):
        """Test getting derived classes of a class."""
        # Mock the class info
        mock_extract.return_value = {
            "DerivedClass": {
                "file_path": "derived.py",
                "base_classes": ["BaseClass"],
                "derived_classes": [],
                "methods": ["derived_method"]
            },
            "BaseClass": {
                "file_path": "base.py",
                "base_classes": [],
                "derived_classes": ["DerivedClass"],
                "methods": ["base_method"]
            }
        }

        # Test the method
        derived_classes = self.service.get_derived_classes_of(self.project_path, "BaseClass")

        # Verify the results
        self.assertEqual(len(derived_classes), 1)
        self.assertEqual(derived_classes[0]["class_name"], "DerivedClass")


if __name__ == "__main__":
    unittest.main()
