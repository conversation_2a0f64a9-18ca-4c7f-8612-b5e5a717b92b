#!/usr/bin/env python

import os
import re
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any

from aider_integration_service import AiderIntegrationService
from surgical_context_extractor import (
    SurgicalContextExtractor,
    CodeSnippet,
    UsageContext,
    DefinitionContext,
    ContextType,
    UsageType,
    DefinitionType
)
from surgical_file_extractor import (
    SurgicalFileExtractor,
    ExtractionRange,
    SymbolInfo
)


@dataclass
class EnhancedCodeContext:
    """Enhanced code context combining surgical context and file extraction."""
    # Primary symbol information
    symbol_name: str
    file_path: str
    symbol_type: str  # 'function', 'class', 'method'

    # Complete symbol implementation
    complete_implementation: Optional[str] = None
    extraction_range: Optional[ExtractionRange] = None

    # Essential imports and dependencies
    essential_imports: Optional[str] = None
    containing_class_signature: Optional[str] = None

    # Related code snippets
    usage_contexts: List[UsageContext] = field(default_factory=list)
    definition_contexts: List[DefinitionContext] = field(default_factory=list)

    # Metadata
    token_count: int = 0
    relevance_score: float = 1.0


class EnhancedSurgicalExtractor:
    """
    Enhanced surgical extractor combining context extraction and file extraction.

    This class provides methods to extract both targeted code snippets around
    dependency interaction points and complete function/method bodies using
    line number boundaries from repository mapping data.
    """

    def __init__(self, aider_service: AiderIntegrationService, cache_ttl: int = 3600):
        """
        Initialize the enhanced surgical extractor.

        Args:
            aider_service: The AiderIntegrationService instance
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.aider_service = aider_service
        self.context_extractor = SurgicalContextExtractor(aider_service, cache_ttl)
        self.file_extractor = SurgicalFileExtractor(aider_service, cache_ttl)
        self.cache_ttl = cache_ttl
        self.extraction_cache = {}
        self.cache_timestamps = {}

    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.extraction_cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.extraction_cache[cache_key]
        return None

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.extraction_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def _extract_essential_imports(self, project_path: str, file_path: str) -> str:
        """Extract essential imports from a file."""
        content = self.file_extractor._read_file_content(project_path, file_path)
        if not content:
            return ""

        lines = content.splitlines()
        import_lines = []

        # Simple regex pattern to match import statements
        import_pattern = re.compile(r'^(import|from)\s+.+')

        for line in lines:
            if import_pattern.match(line):
                import_lines.append(line)
            elif import_lines and line.strip() == "":
                # Keep one blank line after imports
                import_lines.append("")
            elif import_lines and not line.startswith((" ", "\t")):
                # Stop when we hit non-import, non-blank lines that aren't continuations
                break

        return "\n".join(import_lines)

    def _extract_containing_class(self, project_path: str, file_path: str, symbol_info: SymbolInfo) -> Optional[str]:
        """Extract the class definition if the symbol is a method."""
        if symbol_info.symbol_type != "method":
            return None

        # Get all symbols in the file
        symbols = self.file_extractor.get_symbols_in_file(project_path, file_path)
        if not symbols:
            return None

        # Find potential containing classes
        classes = [s for s in symbols if s.symbol_type == "class" and s.start_line < symbol_info.start_line]
        if not classes:
            return None

        # Find the closest class (the one with the highest start line that's still before our method)
        containing_class = max(classes, key=lambda c: c.start_line)

        # Extract the class signature
        content = self.file_extractor._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if containing_class.start_line > len(lines):
            return None

        # Get the class signature line
        class_line = lines[containing_class.start_line - 1]

        # Try to get a few lines of class docstring if available
        docstring_lines = []
        for i in range(containing_class.start_line, min(containing_class.start_line + 5, len(lines))):
            line = lines[i]
            if '"""' in line or "'''" in line:
                docstring_lines.append(line)
                # If the docstring is a single line, we're done
                if line.count('"""') == 2 or line.count("'''") == 2:
                    break
                # Otherwise, collect lines until we find the closing quote
                for j in range(i + 1, min(i + 10, len(lines))):
                    next_line = lines[j]
                    docstring_lines.append(next_line)
                    if '"""' in next_line or "'''" in next_line:
                        break
                break

        if docstring_lines:
            return class_line + "\n" + "\n".join(docstring_lines)
        else:
            return class_line

    def extract_enhanced_context(self, project_path: str, symbol_name: str, file_path: str) -> Optional[EnhancedCodeContext]:
        """
        Extract enhanced code context for a symbol, combining complete implementation and usage contexts.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to extract
            file_path: Path to the file containing the symbol

        Returns:
            EnhancedCodeContext object or None if extraction failed
        """
        cache_key = f"enhanced_context:{project_path}:{file_path}:{symbol_name}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get symbol information
        symbols = self.file_extractor.get_symbols_in_file(project_path, file_path)
        if not symbols:
            return None

        target_symbol = next((s for s in symbols if s.name == symbol_name), None)
        if not target_symbol:
            return None

        # Extract complete implementation
        extraction_range = self.file_extractor.extract_symbol_range(symbol_name, file_path, project_path)
        implementation = self.file_extractor.extract_symbol_content(symbol_name, file_path, project_path)

        # Extract essential imports
        essential_imports = self._extract_essential_imports(project_path, file_path)

        # Extract containing class signature if it's a method
        containing_class = self._extract_containing_class(project_path, file_path, target_symbol)

        # Extract usage contexts
        usage_contexts = self.context_extractor.extract_usage_contexts(project_path, symbol_name, file_path)

        # Create the enhanced context
        context = EnhancedCodeContext(
            symbol_name=symbol_name,
            file_path=file_path,
            symbol_type=target_symbol.symbol_type,
            complete_implementation=implementation,
            extraction_range=extraction_range,
            essential_imports=essential_imports,
            containing_class_signature=containing_class,
            usage_contexts=usage_contexts
        )

        # Cache the result
        self._update_cache(cache_key, context)

        return context

    def format_enhanced_context(self, context: EnhancedCodeContext) -> str:
        """Format the enhanced context for display or sending to an AI model."""
        if not context:
            return "No context available."

        output = "# 🎯 OPTIMIZED: Send only what's needed\n\n"
        output += f"### REQUESTED FILE CONTEXT (Surgical Extraction)\n\n"
        output += f"--- File: {context.file_path} (Targeted extraction for {context.symbol_name}) ---\n\n"

        # Add imports section if available
        if context.essential_imports:
            import_lines = context.essential_imports.splitlines()
            output += f"# File header & imports ({len(import_lines)} lines)\n"
            output += f"```python\n{context.essential_imports}\n```\n\n"

        # Add containing class if available
        if context.containing_class_signature:
            class_lines = context.containing_class_signature.splitlines()
            output += f"# Class/Module definition start ({len(class_lines)} lines)\n"
            output += f"```python\n{context.containing_class_signature}\n```\n\n"

        # Add the complete implementation
        if context.complete_implementation:
            impl_lines = context.complete_implementation.splitlines()
            output += f"# THE COMPLETE TARGET FUNCTION ({len(impl_lines)} lines)\n"
            output += f"```python\n{context.complete_implementation}\n```\n\n"

        # Add usage contexts if available
        if context.usage_contexts:
            output += f"### SURGICAL DEPENDENCY SNIPPETS\n"
            for i, usage in enumerate(context.usage_contexts[:3]):  # Limit to 3 usage examples
                snippet = usage.snippet
                output += f"#### Usage {i+1}: {usage.usage_type.value} in {snippet.file_path}\n"
                output += f"```python\n{snippet.content}\n```\n\n"

        return output
