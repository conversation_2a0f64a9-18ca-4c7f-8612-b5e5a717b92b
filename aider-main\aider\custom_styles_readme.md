# Aider Browser Interface Custom Styles

This document explains how to customize the appearance of the Aider browser interface.

## Overview

The Aider browser interface uses Streamlit, and its appearance can be customized by modifying the `custom_styles.css` file. This CSS file is loaded when the browser interface starts and is applied to the Streamlit components.

## How to Customize

1. Edit the `custom_styles.css` file in the `aider` directory
2. Save your changes
3. Restart Aider with the `--browser` flag to see your changes

## CSS Selectors

Here are the key CSS selectors you can use to customize different parts of the interface:

### Main Application
- `.stApp` - The main application container

### Sidebar
- `.css-1d391kg, .css-1lcbmhc` - Sidebar containers

### Chat Interface
- `.stChatMessage` - Chat message containers
- `.stChatMessageContent:has(div[data-testid="chatAvatarIcon-user"])` - User messages
- `.stChatMessageContent:has(div[data-testid="chatAvatarIcon-assistant"])` - Assistant messages

### Input Elements
- `.stTextInput > div > div > input` - Text input fields
- `.stButton > button` - Buttons

### Content Elements
- `pre` - Code blocks
- `h1, h2, h3` - Headings
- `a` - Links
- `.stAlert` - Alert/warning messages

## Example Customizations

### Change Background Color
```css
.stApp {
    background-color: #f0f5ff;  /* Light blue background */
}
```

### Change Sidebar Color
```css
.css-1d391kg, .css-1lcbmhc {
    background-color: #e6eeff;  /* Slightly darker blue for sidebar */
}
```

### Style Chat Messages
```css
.stChatMessageContent:has(div[data-testid="chatAvatarIcon-user"]) {
    background-color: #e3f2fd;  /* Light blue for user messages */
    border-radius: 8px;
}

.stChatMessageContent:has(div[data-testid="chatAvatarIcon-assistant"]) {
    background-color: #f1f8e9;  /* Light green for assistant messages */
    border-radius: 8px;
}
```

## Streamlit Documentation

For more information on Streamlit's components and styling, refer to the [Streamlit documentation](https://docs.streamlit.io/library/api-reference/theme).

## Troubleshooting

If your CSS changes don't appear to take effect:

1. Make sure you've saved the CSS file
2. Restart Aider completely
3. Clear your browser cache
4. Check the browser console for any CSS errors
