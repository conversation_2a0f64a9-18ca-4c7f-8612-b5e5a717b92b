#!/usr/bin/env python

import os
import sys
import time
from pathlib import Path

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from aider_context_request_integration import AiderContextRequestIntegration
    from context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider_template_renderer import AiderTemplateRenderer
    from aider_integration_service import AiderIntegrationService
except ImportError:
    print("Error: Could not import the required modules.")
    print("Make sure you have implemented the surgical extraction modules.")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""
    
    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []
    
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")
    
    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")
    
    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


class MockCoder:
    """Mock Coder class for testing."""
    
    def __init__(self):
        self.io = MockIO()
        self.root = os.getcwd()
        self.current_query_context_requests = 0
        self.context_request_integration = None
        
        # Initialize context request integration
        try:
            self.context_request_integration = AiderContextRequestIntegration(self.root)
        except Exception as e:
            self.io.tool_error(f"Failed to initialize context request integration: {e}")
    
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.
        
        Args:
            content: The LLM response content
            user_message: The original user message
            
        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        
        # Check if context request integration is available
        if not self.context_request_integration:
            self.io.tool_error("Context request integration not available")
            return content, None
        
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None
        
        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None
        
        # Increment the context request counter
        self.current_query_context_requests += 1
        
        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        
        # Get a sample repository overview
        repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
"""
        
        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )
        
        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)
        
        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        
        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt
    
    def send_message(self, user_message):
        """
        Simulate sending a message to the LLM and processing the response.
        
        Args:
            user_message: The user message
            
        Returns:
            The processed response
        """
        # Simulate an LLM response with a context request
        llm_response = f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {{"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"}}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}}}
"""
        
        # Process the context request
        cleaned_content, augmented_prompt = self.process_context_requests(llm_response, user_message)
        
        # Check if a context request was detected
        if augmented_prompt:
            self.io.tool_output("Context request detected and processed!")
            self.io.tool_output("Augmented prompt generated.")
            
            # Simulate sending the augmented prompt to the LLM
            self.io.tool_output("Sending augmented prompt to LLM...")
            
            # Simulate a delay for the LLM to process the augmented prompt
            time.sleep(1)
            
            # Simulate a new LLM response
            new_llm_response = """
The SurgicalFileExtractor is a specialized tool designed to extract specific code symbols (like functions, methods, or classes) from files in a project. Here's how it works:

1. The extract_symbol_content method is the main entry point for extracting a symbol's content from a file.
2. It first calls extract_symbol_range to determine the line range of the symbol in the file.
3. It then reads the file content and extracts the lines corresponding to the symbol.
4. The extracted content is returned as a string.

This surgical extraction approach allows for more targeted and efficient code analysis compared to loading entire files.
"""
            
            # Add the new response to the conversation history
            self.context_request_integration.update_conversation_history("assistant", new_llm_response)
            
            return new_llm_response
        else:
            self.io.tool_output("No context request detected.")
            return llm_response


def main():
    print("\n=== Testing Context Request Hang ===")
    
    # Create a mock coder
    coder = MockCoder()
    
    # Simulate a user message
    user_message = "How does the surgical file extractor work?"
    print(f"\nUser: {user_message}")
    
    # Process the message
    start_time = time.time()
    response = coder.send_message(user_message)
    end_time = time.time()
    
    # Print the response
    print(f"\nLLM: {response}")
    
    # Print the processing time
    print(f"\nProcessing time: {end_time - start_time:.2f} seconds")
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
