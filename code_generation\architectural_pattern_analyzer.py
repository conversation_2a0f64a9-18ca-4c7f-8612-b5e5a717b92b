"""
Architectural Pattern Analyzer

Analyzes existing codebase for architectural patterns, design patterns,
and coding conventions to guide intelligent code generation.
"""

import ast
import re
from typing import Dict, List, Any, Set, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
from pathlib import Path

from mid_level_ir.ir_context import IRContext


@dataclass
class ArchitecturalPattern:
    """Represents a detected architectural pattern."""
    name: str
    pattern_type: str  # 'design_pattern', 'architectural_style', 'coding_convention'
    confidence: float
    examples: List[str]
    characteristics: Dict[str, Any]
    usage_frequency: int


@dataclass
class CodingStyle:
    """Represents detected coding style conventions."""
    naming_conventions: Dict[str, str]  # 'class': 'PascalCase', 'function': 'snake_case'
    indentation: str  # '4_spaces', '2_spaces', 'tabs'
    line_length: int
    import_style: str  # 'absolute', 'relative', 'mixed'
    docstring_style: str  # 'google', 'numpy', 'sphinx', 'basic'
    type_hints_usage: float  # percentage of functions with type hints


@dataclass
class ArchitecturalAnalysis:
    """Complete architectural analysis results."""
    patterns: List[ArchitecturalPattern]
    coding_style: CodingStyle
    module_organization: Dict[str, Any]
    dependency_patterns: Dict[str, Any]
    quality_metrics: Dict[str, float]


class ArchitecturalPatternAnalyzer:
    """
    Analyzes codebase architecture to identify patterns and conventions.
    
    This analyzer detects:
    - Design patterns (Factory, Observer, Strategy, etc.)
    - Architectural styles (MVC, layered, microservices)
    - Coding conventions and style guidelines
    - Module organization patterns
    - Dependency injection patterns
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the architectural pattern analyzer.
        
        Args:
            config: Configuration dictionary for analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Pattern detection rules
        self.design_patterns = {
            'Factory': self._detect_factory_pattern,
            'Singleton': self._detect_singleton_pattern,
            'Observer': self._detect_observer_pattern,
            'Strategy': self._detect_strategy_pattern,
            'Decorator': self._detect_decorator_pattern,
            'Builder': self._detect_builder_pattern,
            'Adapter': self._detect_adapter_pattern,
            'Command': self._detect_command_pattern
        }
        
        self.architectural_styles = {
            'MVC': self._detect_mvc_pattern,
            'Layered': self._detect_layered_pattern,
            'Plugin': self._detect_plugin_pattern,
            'Pipeline': self._detect_pipeline_pattern
        }
    
    def analyze(self, ir_context: IRContext) -> ArchitecturalAnalysis:
        """
        Perform comprehensive architectural analysis.
        
        Args:
            ir_context: The IR context containing codebase analysis
            
        Returns:
            Complete architectural analysis results
        """
        if self.verbose:
            print(f"🏗️  Analyzing architectural patterns in {len(ir_context.modules)} modules")
        
        # Detect patterns
        patterns = self._detect_patterns(ir_context)
        
        # Analyze coding style
        coding_style = self._analyze_coding_style(ir_context)
        
        # Analyze module organization
        module_organization = self._analyze_module_organization(ir_context)
        
        # Analyze dependency patterns
        dependency_patterns = self._analyze_dependency_patterns(ir_context)
        
        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(ir_context)
        
        if self.verbose:
            print(f"   Detected {len(patterns)} architectural patterns")
            print(f"   Analyzed coding style and conventions")
        
        return ArchitecturalAnalysis(
            patterns=patterns,
            coding_style=coding_style,
            module_organization=module_organization,
            dependency_patterns=dependency_patterns,
            quality_metrics=quality_metrics
        )
    
    def _detect_patterns(self, ir_context: IRContext) -> List[ArchitecturalPattern]:
        """Detect all architectural and design patterns."""
        patterns = []
        
        # Detect design patterns
        for pattern_name, detector in self.design_patterns.items():
            pattern = detector(ir_context)
            if pattern:
                patterns.append(pattern)
        
        # Detect architectural styles
        for style_name, detector in self.architectural_styles.items():
            pattern = detector(ir_context)
            if pattern:
                patterns.append(pattern)
        
        return patterns
    
    def _analyze_coding_style(self, ir_context: IRContext) -> CodingStyle:
        """Analyze coding style conventions across the codebase."""
        naming_conventions = self._analyze_naming_conventions(ir_context)
        indentation = self._analyze_indentation(ir_context)
        line_length = self._analyze_line_length(ir_context)
        import_style = self._analyze_import_style(ir_context)
        docstring_style = self._analyze_docstring_style(ir_context)
        type_hints_usage = self._analyze_type_hints_usage(ir_context)
        
        return CodingStyle(
            naming_conventions=naming_conventions,
            indentation=indentation,
            line_length=line_length,
            import_style=import_style,
            docstring_style=docstring_style,
            type_hints_usage=type_hints_usage
        )
    
    def _analyze_naming_conventions(self, ir_context: IRContext) -> Dict[str, str]:
        """Analyze naming conventions for different code elements."""
        class_names = []
        function_names = []
        variable_names = []
        
        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    class_names.append(entity.name)
                elif entity.type in ('function', 'async_function'):
                    function_names.append(entity.name)
                elif entity.type == 'variable':
                    variable_names.append(entity.name)
        
        return {
            'class': self._detect_naming_style(class_names),
            'function': self._detect_naming_style(function_names),
            'variable': self._detect_naming_style(variable_names)
        }
    
    def _detect_naming_style(self, names: List[str]) -> str:
        """Detect the predominant naming style from a list of names."""
        if not names:
            return 'unknown'
        
        styles = {
            'snake_case': 0,
            'camelCase': 0,
            'PascalCase': 0,
            'UPPER_CASE': 0
        }
        
        for name in names:
            if '_' in name and name.islower():
                styles['snake_case'] += 1
            elif '_' in name and name.isupper():
                styles['UPPER_CASE'] += 1
            elif name[0].isupper() and any(c.isupper() for c in name[1:]):
                styles['PascalCase'] += 1
            elif name[0].islower() and any(c.isupper() for c in name[1:]):
                styles['camelCase'] += 1
        
        return max(styles, key=styles.get)

    def _analyze_indentation(self, ir_context: IRContext) -> str:
        """Analyze indentation style across the codebase."""
        indentation_counts = {'4_spaces': 0, '2_spaces': 0, 'tabs': 0}

        for module_info in ir_context.modules.values():
            if module_info.source_code:
                lines = module_info.source_code.split('\n')
                for line in lines:
                    if line.startswith('    '):  # 4 spaces
                        indentation_counts['4_spaces'] += 1
                    elif line.startswith('  ') and not line.startswith('    '):  # 2 spaces
                        indentation_counts['2_spaces'] += 1
                    elif line.startswith('\t'):  # tabs
                        indentation_counts['tabs'] += 1

        return max(indentation_counts, key=indentation_counts.get)

    def _analyze_line_length(self, ir_context: IRContext) -> int:
        """Analyze typical line length across the codebase."""
        line_lengths = []

        for module_info in ir_context.modules.values():
            if module_info.source_code:
                lines = module_info.source_code.split('\n')
                for line in lines:
                    if line.strip():  # Skip empty lines
                        line_lengths.append(len(line))

        if line_lengths:
            # Return 90th percentile as typical max line length
            line_lengths.sort()
            index = int(0.9 * len(line_lengths))
            return line_lengths[index]
        return 80  # Default

    def _analyze_import_style(self, ir_context: IRContext) -> str:
        """Analyze import style preferences."""
        absolute_imports = 0
        relative_imports = 0

        for module_info in ir_context.modules.values():
            for dependency in module_info.dependencies:
                if dependency.startswith('.'):
                    relative_imports += 1
                else:
                    absolute_imports += 1

        if relative_imports > absolute_imports:
            return 'relative'
        elif absolute_imports > relative_imports:
            return 'absolute'
        else:
            return 'mixed'

    def _analyze_docstring_style(self, ir_context: IRContext) -> str:
        """Analyze docstring style conventions."""
        docstring_styles = {'google': 0, 'numpy': 0, 'sphinx': 0, 'basic': 0}

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.doc:
                    style = self._detect_docstring_style(entity.doc)
                    docstring_styles[style] += 1

        return max(docstring_styles, key=docstring_styles.get)

    def _detect_docstring_style(self, docstring: str) -> str:
        """Detect the style of a specific docstring."""
        if 'Args:' in docstring and 'Returns:' in docstring:
            return 'google'
        elif 'Parameters' in docstring and '----------' in docstring:
            return 'numpy'
        elif ':param' in docstring and ':return' in docstring:
            return 'sphinx'
        else:
            return 'basic'

    def _analyze_type_hints_usage(self, ir_context: IRContext) -> float:
        """Calculate percentage of functions using type hints."""
        total_functions = 0
        functions_with_hints = 0

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'async_function'):
                    total_functions += 1
                    if entity.returns or (entity.params and any('type' in str(p) for p in entity.params)):
                        functions_with_hints += 1

        return functions_with_hints / total_functions if total_functions > 0 else 0.0

    def _analyze_module_organization(self, ir_context: IRContext) -> Dict[str, Any]:
        """Analyze how modules are organized in the codebase."""
        module_structure = defaultdict(list)

        for module_name, module_info in ir_context.modules.items():
            path_parts = Path(module_info.file).parts
            if len(path_parts) > 1:
                package = path_parts[-2]  # Parent directory
                module_structure[package].append(module_name)

        return {
            'package_structure': dict(module_structure),
            'total_packages': len(module_structure),
            'avg_modules_per_package': sum(len(modules) for modules in module_structure.values()) / len(module_structure) if module_structure else 0
        }

    def _analyze_dependency_patterns(self, ir_context: IRContext) -> Dict[str, Any]:
        """Analyze dependency patterns and relationships."""
        dependency_graph = {}
        circular_dependencies = []

        for module_name, module_info in ir_context.modules.items():
            dependency_graph[module_name] = module_info.dependencies

        # Detect circular dependencies (simplified)
        for module, deps in dependency_graph.items():
            for dep in deps:
                if dep in dependency_graph and module in dependency_graph[dep]:
                    circular_dependencies.append((module, dep))

        return {
            'total_dependencies': sum(len(deps) for deps in dependency_graph.values()),
            'circular_dependencies': circular_dependencies,
            'most_depended_on': self._find_most_depended_modules(dependency_graph)
        }

    def _find_most_depended_modules(self, dependency_graph: Dict[str, List[str]]) -> List[Tuple[str, int]]:
        """Find modules that are most depended upon."""
        dependency_counts = Counter()

        for deps in dependency_graph.values():
            for dep in deps:
                dependency_counts[dep] += 1

        return dependency_counts.most_common(5)

    def _calculate_quality_metrics(self, ir_context: IRContext) -> Dict[str, float]:
        """Calculate various code quality metrics."""
        total_entities = 0
        documented_entities = 0
        complex_entities = 0

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                total_entities += 1
                if entity.doc:
                    documented_entities += 1
                # Assume complexity > 5 is complex (this would come from metadata enricher)
                if hasattr(entity, 'complexity') and entity.complexity is not None and entity.complexity > 5:
                    complex_entities += 1

        return {
            'documentation_coverage': documented_entities / total_entities if total_entities > 0 else 0.0,
            'complexity_ratio': complex_entities / total_entities if total_entities > 0 else 0.0,
            'total_entities': total_entities
        }

    # Pattern Detection Methods

    def _detect_factory_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Factory pattern usage."""
        factory_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'class'):
                    name_lower = entity.name.lower()
                    if 'factory' in name_lower or 'create' in name_lower:
                        factory_indicators.append(f"{module_info.name}.{entity.name}")

        if len(factory_indicators) >= 2:
            return ArchitecturalPattern(
                name='Factory',
                pattern_type='design_pattern',
                confidence=min(0.9, len(factory_indicators) * 0.3),
                examples=factory_indicators[:3],
                characteristics={'creation_methods': len(factory_indicators)},
                usage_frequency=len(factory_indicators)
            )
        return None

    def _detect_singleton_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Singleton pattern usage."""
        singleton_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    # Look for singleton indicators in class methods
                    if any(method.name == 'getInstance' or method.name == 'get_instance'
                          for method in module_info.entities
                          if method.type == 'function' and entity.name in str(method)):
                        singleton_indicators.append(f"{module_info.name}.{entity.name}")

        if singleton_indicators:
            return ArchitecturalPattern(
                name='Singleton',
                pattern_type='design_pattern',
                confidence=0.8,
                examples=singleton_indicators,
                characteristics={'singleton_classes': len(singleton_indicators)},
                usage_frequency=len(singleton_indicators)
            )
        return None

    def _detect_observer_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Observer pattern usage."""
        observer_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'class'):
                    name_lower = entity.name.lower()
                    if any(keyword in name_lower for keyword in ['observer', 'listener', 'notify', 'subscribe']):
                        observer_indicators.append(f"{module_info.name}.{entity.name}")

        if len(observer_indicators) >= 2:
            return ArchitecturalPattern(
                name='Observer',
                pattern_type='design_pattern',
                confidence=min(0.85, len(observer_indicators) * 0.25),
                examples=observer_indicators[:3],
                characteristics={'observer_components': len(observer_indicators)},
                usage_frequency=len(observer_indicators)
            )
        return None

    def _detect_strategy_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Strategy pattern usage."""
        strategy_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    name_lower = entity.name.lower()
                    if 'strategy' in name_lower or 'algorithm' in name_lower:
                        strategy_indicators.append(f"{module_info.name}.{entity.name}")

        if len(strategy_indicators) >= 2:
            return ArchitecturalPattern(
                name='Strategy',
                pattern_type='design_pattern',
                confidence=min(0.9, len(strategy_indicators) * 0.4),
                examples=strategy_indicators[:3],
                characteristics={'strategy_classes': len(strategy_indicators)},
                usage_frequency=len(strategy_indicators)
            )
        return None

    def _detect_decorator_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Decorator pattern usage."""
        decorator_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'class'):
                    name_lower = entity.name.lower()
                    if 'decorator' in name_lower or 'wrapper' in name_lower:
                        decorator_indicators.append(f"{module_info.name}.{entity.name}")

        if decorator_indicators:
            return ArchitecturalPattern(
                name='Decorator',
                pattern_type='design_pattern',
                confidence=0.7,
                examples=decorator_indicators[:3],
                characteristics={'decorator_components': len(decorator_indicators)},
                usage_frequency=len(decorator_indicators)
            )
        return None

    def _detect_builder_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Builder pattern usage."""
        builder_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    name_lower = entity.name.lower()
                    if 'builder' in name_lower:
                        builder_indicators.append(f"{module_info.name}.{entity.name}")

        if builder_indicators:
            return ArchitecturalPattern(
                name='Builder',
                pattern_type='design_pattern',
                confidence=0.85,
                examples=builder_indicators,
                characteristics={'builder_classes': len(builder_indicators)},
                usage_frequency=len(builder_indicators)
            )
        return None

    def _detect_adapter_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Adapter pattern usage."""
        adapter_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    name_lower = entity.name.lower()
                    if 'adapter' in name_lower or 'wrapper' in name_lower:
                        adapter_indicators.append(f"{module_info.name}.{entity.name}")

        if adapter_indicators:
            return ArchitecturalPattern(
                name='Adapter',
                pattern_type='design_pattern',
                confidence=0.75,
                examples=adapter_indicators,
                characteristics={'adapter_classes': len(adapter_indicators)},
                usage_frequency=len(adapter_indicators)
            )
        return None

    def _detect_command_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Command pattern usage."""
        command_indicators = []

        for module_info in ir_context.modules.values():
            for entity in module_info.entities:
                if entity.type == 'class':
                    name_lower = entity.name.lower()
                    if 'command' in name_lower or 'action' in name_lower:
                        command_indicators.append(f"{module_info.name}.{entity.name}")

        if command_indicators:
            return ArchitecturalPattern(
                name='Command',
                pattern_type='design_pattern',
                confidence=0.8,
                examples=command_indicators,
                characteristics={'command_classes': len(command_indicators)},
                usage_frequency=len(command_indicators)
            )
        return None

    # Architectural Style Detection Methods

    def _detect_mvc_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect MVC (Model-View-Controller) architectural pattern."""
        mvc_components = {'model': [], 'view': [], 'controller': []}

        for module_info in ir_context.modules.values():
            module_name_lower = module_info.name.lower()
            file_path_lower = str(module_info.file).lower()

            if 'model' in module_name_lower or 'models' in file_path_lower:
                mvc_components['model'].append(module_info.name)
            elif 'view' in module_name_lower or 'views' in file_path_lower:
                mvc_components['view'].append(module_info.name)
            elif 'controller' in module_name_lower or 'controllers' in file_path_lower:
                mvc_components['controller'].append(module_info.name)

        # Check if we have at least one component of each type
        if all(mvc_components.values()):
            total_components = sum(len(components) for components in mvc_components.values())
            return ArchitecturalPattern(
                name='MVC',
                pattern_type='architectural_style',
                confidence=min(0.9, total_components * 0.2),
                examples=[f"Models: {mvc_components['model'][:2]}",
                         f"Views: {mvc_components['view'][:2]}",
                         f"Controllers: {mvc_components['controller'][:2]}"],
                characteristics=mvc_components,
                usage_frequency=total_components
            )
        return None

    def _detect_layered_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Layered architectural pattern."""
        layers = {'presentation': [], 'business': [], 'data': [], 'service': []}

        for module_info in ir_context.modules.values():
            file_path_lower = str(module_info.file).lower()
            module_name_lower = module_info.name.lower()

            if any(keyword in file_path_lower for keyword in ['ui', 'frontend', 'presentation', 'web']):
                layers['presentation'].append(module_info.name)
            elif any(keyword in file_path_lower for keyword in ['business', 'logic', 'core']):
                layers['business'].append(module_info.name)
            elif any(keyword in file_path_lower for keyword in ['data', 'database', 'repository', 'dao']):
                layers['data'].append(module_info.name)
            elif any(keyword in file_path_lower for keyword in ['service', 'api']):
                layers['service'].append(module_info.name)

        # Check if we have at least 2 different layers
        non_empty_layers = [layer for layer in layers.values() if layer]
        if len(non_empty_layers) >= 2:
            total_components = sum(len(components) for components in layers.values())
            return ArchitecturalPattern(
                name='Layered',
                pattern_type='architectural_style',
                confidence=min(0.8, len(non_empty_layers) * 0.25),
                examples=[f"{layer}: {components[:2]}" for layer, components in layers.items() if components],
                characteristics=layers,
                usage_frequency=total_components
            )
        return None

    def _detect_plugin_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Plugin architectural pattern."""
        plugin_indicators = []

        for module_info in ir_context.modules.values():
            file_path_lower = str(module_info.file).lower()
            module_name_lower = module_info.name.lower()

            if any(keyword in file_path_lower or keyword in module_name_lower
                  for keyword in ['plugin', 'extension', 'addon', 'hook']):
                plugin_indicators.append(module_info.name)

        if len(plugin_indicators) >= 2:
            return ArchitecturalPattern(
                name='Plugin',
                pattern_type='architectural_style',
                confidence=min(0.85, len(plugin_indicators) * 0.3),
                examples=plugin_indicators[:3],
                characteristics={'plugin_modules': len(plugin_indicators)},
                usage_frequency=len(plugin_indicators)
            )
        return None

    def _detect_pipeline_pattern(self, ir_context: IRContext) -> Optional[ArchitecturalPattern]:
        """Detect Pipeline architectural pattern."""
        pipeline_indicators = []

        for module_info in ir_context.modules.values():
            module_name_lower = module_info.name.lower()

            # Look for pipeline-related naming
            if any(keyword in module_name_lower
                  for keyword in ['pipeline', 'processor', 'filter', 'transformer', 'stage']):
                pipeline_indicators.append(module_info.name)

            # Look for entities with pipeline-like names
            for entity in module_info.entities:
                entity_name_lower = entity.name.lower()
                if any(keyword in entity_name_lower
                      for keyword in ['process', 'transform', 'filter', 'execute', 'run']):
                    pipeline_indicators.append(f"{module_info.name}.{entity.name}")

        if len(pipeline_indicators) >= 3:
            return ArchitecturalPattern(
                name='Pipeline',
                pattern_type='architectural_style',
                confidence=min(0.8, len(pipeline_indicators) * 0.2),
                examples=pipeline_indicators[:4],
                characteristics={'pipeline_components': len(pipeline_indicators)},
                usage_frequency=len(pipeline_indicators)
            )
        return None
