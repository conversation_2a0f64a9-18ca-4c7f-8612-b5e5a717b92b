#!/usr/bin/env python3
"""
Debug script to trace the new direct IR context flow.
This will show exactly what happens when a user query is processed.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_direct_ir_flow_tracing():
    """Test and trace the direct IR context flow step by step."""
    
    print("🔍 DEBUGGING DIRECT IR CONTEXT FLOW")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        print("✅ Imported required modules")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use external project path to simulate real usage
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
            print(f"⚠️  Using current directory instead: {external_project}")
        
        repo = GitRepo(io, [], external_project)
        
        print(f"📁 Using project path: {external_project}")
        
        # Create coder with informative mode (same as real usage)
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192,
            verbose=True,
            dry_run=True
        )
        
        print("✅ Created coder instance")
        
        # Check if the new flow is enabled
        enable_new_flow = getattr(coder, 'enable_direct_ir_context', True)
        print(f"🔧 Direct IR context enabled: {enable_new_flow}")
        
        # Check environment variables
        disable_env = os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes')
        enable_env = os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes')
        
        print(f"🌍 Environment - DISABLE: {disable_env}, ENABLE: {enable_env}")
        
        # Test the direct IR context method directly
        test_query = "How does position management work in the trading system?"
        print(f"\n📝 Testing query: '{test_query}'")
        
        # Check if CONTEXT_REQUEST is available
        try:
            from aider.context_request import ContextRequestHandler, IRContextRequest
            print("✅ CONTEXT_REQUEST modules available")
        except ImportError as e:
            print(f"❌ CONTEXT_REQUEST modules not available: {e}")
            return False
        
        # Test the process_direct_ir_context method
        print("\n🔄 Testing process_direct_ir_context method...")
        
        # Add some debugging to the method call
        class DebugCoder(coder.__class__):
            def process_direct_ir_context(self, user_message):
                print(f"🎯 process_direct_ir_context called with: '{user_message}'")
                
                # Check conditions
                if not hasattr(self, 'io'):
                    print("❌ No io attribute")
                    return False
                
                # Check if CONTEXT_REQUEST is available
                try:
                    from aider.context_request import ContextRequestHandler, IRContextRequest
                    print("✅ CONTEXT_REQUEST modules available in method")
                except ImportError as e:
                    print(f"❌ CONTEXT_REQUEST modules not available in method: {e}")
                    return False
                
                # Check query length
                if len(user_message.split()) < 3:
                    print(f"❌ Query too short: {len(user_message.split())} words")
                    return False
                
                if user_message.startswith('/'):
                    print("❌ Query starts with '/' (command)")
                    return False
                
                print("✅ All conditions passed, proceeding with IR context generation...")
                
                # Call the original method
                return super().process_direct_ir_context(user_message)
        
        # Replace the coder's class temporarily
        coder.__class__ = DebugCoder
        
        # Test the method
        result = coder.process_direct_ir_context(test_query)
        print(f"🎯 process_direct_ir_context result: {result}")
        
        # Test the run_one method to see if it calls our method
        print("\n🔄 Testing run_one method...")
        
        class DebugRunOneCoder(coder.__class__):
            def run_one(self, user_message, preproc):
                print(f"🎯 run_one called with: '{user_message}'")
                
                # Check the new flow logic
                enable_new_flow = getattr(self, 'enable_direct_ir_context', True)
                print(f"🔧 enable_direct_ir_context: {enable_new_flow}")
                
                # Check environment variables
                import os
                if os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
                    enable_new_flow = False
                    print("🌍 Disabled by AIDER_DISABLE_DIRECT_IR_CONTEXT")
                elif os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
                    enable_new_flow = True
                    print("🌍 Enabled by AIDER_ENABLE_DIRECT_IR_CONTEXT")
                
                print(f"🎯 Final enable_new_flow: {enable_new_flow}")
                
                if enable_new_flow:
                    print("🔄 Calling process_direct_ir_context...")
                    ir_context_result = self.process_direct_ir_context(user_message)
                    print(f"🎯 IR context result: {ir_context_result}")
                    if ir_context_result:
                        print("✅ IR context was generated and injected")
                        return  # Would normally continue with flow
                    else:
                        print("❌ IR context generation failed, would fall back to traditional flow")
                else:
                    print("❌ New flow disabled, would use traditional flow")
                
                # Don't actually continue with the full run_one to avoid LLM calls
                print("🛑 Stopping here to avoid LLM calls in debug mode")
                return
        
        # Replace the coder's class again
        coder.__class__ = DebugRunOneCoder
        
        # Test run_one
        coder.run_one(test_query, True)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_configuration():
    """Test different environment configurations."""
    
    print("\n🌍 TESTING ENVIRONMENT CONFIGURATIONS")
    print("=" * 60)
    
    # Test 1: Default (should be enabled)
    print("\n📝 Test 1: Default configuration")
    print(f"   AIDER_DISABLE_DIRECT_IR_CONTEXT: {os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', 'not set')}")
    print(f"   AIDER_ENABLE_DIRECT_IR_CONTEXT: {os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', 'not set')}")
    
    enable_new_flow = True  # Default
    if os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = False
    elif os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = True
    
    print(f"   Result: {'✅ ENABLED' if enable_new_flow else '❌ DISABLED'}")
    
    # Test 2: Explicitly disabled
    print("\n📝 Test 2: Explicitly disabled")
    os.environ['AIDER_DISABLE_DIRECT_IR_CONTEXT'] = 'true'
    
    enable_new_flow = True  # Default
    if os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = False
    elif os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = True
    
    print(f"   AIDER_DISABLE_DIRECT_IR_CONTEXT: {os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT')}")
    print(f"   Result: {'✅ ENABLED' if enable_new_flow else '❌ DISABLED'}")
    
    # Clean up
    if 'AIDER_DISABLE_DIRECT_IR_CONTEXT' in os.environ:
        del os.environ['AIDER_DISABLE_DIRECT_IR_CONTEXT']
    
    # Test 3: Explicitly enabled
    print("\n📝 Test 3: Explicitly enabled")
    os.environ['AIDER_ENABLE_DIRECT_IR_CONTEXT'] = 'true'
    
    enable_new_flow = True  # Default
    if os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = False
    elif os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes'):
        enable_new_flow = True
    
    print(f"   AIDER_ENABLE_DIRECT_IR_CONTEXT: {os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT')}")
    print(f"   Result: {'✅ ENABLED' if enable_new_flow else '❌ DISABLED'}")
    
    # Clean up
    if 'AIDER_ENABLE_DIRECT_IR_CONTEXT' in os.environ:
        del os.environ['AIDER_ENABLE_DIRECT_IR_CONTEXT']

def show_usage_instructions():
    """Show how to use the new flow."""
    
    print("\n📋 USAGE INSTRUCTIONS")
    print("=" * 60)
    
    print("🚀 To test the new direct IR context flow:")
    print()
    print("1. Enable verbose logging:")
    print("   Set-Location aider-main")
    print("   python -m aider.main --verbose --model ollama_chat/qwen3:1.7b --browser \"C:\\Users\\<USER>\\Documents\\____live_backtest_dashboard_____\"")
    print()
    print("2. Ask a query:")
    print("   > How does position management work?")
    print()
    print("3. Look for these log messages:")
    print("   🧠 Generating intelligent context for your query...")
    print("   📁 Using project path for IR context: [path]")
    print("   ✅ Generated IR context package ([size] chars)")
    print("   🎯 Using new direct IR context flow - skipping traditional MAP_REQUEST/CONTEXT_REQUEST")
    print()
    print("4. To disable new flow for comparison:")
    print("   $env:AIDER_DISABLE_DIRECT_IR_CONTEXT='true'")
    print("   python -m aider.main --verbose --model ollama_chat/qwen3:1.7b --browser \"C:\\Users\\<USER>\\Documents\\____live_backtest_dashboard_____\"")

if __name__ == "__main__":
    print("🔍 DIRECT IR CONTEXT FLOW DEBUGGING")
    print("Investigating why the system isn't catching user queries\n")
    
    success = test_direct_ir_flow_tracing()
    test_environment_configuration()
    show_usage_instructions()
    
    if success:
        print("\n✅ Debugging completed successfully!")
        print("The new direct IR context flow should be working.")
        print("If it's not working in real usage, check the verbose logs for the messages shown above.")
    else:
        print("\n❌ Debugging found issues.")
        print("Please check the error messages above and fix the implementation.")
