#!/usr/bin/env python3
"""
Quick Architecture Diagram Generator

A simple wrapper script to generate architecture diagrams from your IR JSON.
This script provides easy commands for common use cases.

Usage:
    python generate_architecture_diagrams.py [command]

Commands:
    all         - Generate all diagram types (default)
    business    - Generate business system architecture only
    overview    - Generate technical system overview only
    layers      - Generate component architecture only
    critical    - Generate critical path only
    deps        - Generate dependency graph only

Examples:
    python generate_architecture_diagrams.py
    python generate_architecture_diagrams.py business
    python generate_architecture_diagrams.py overview
    python generate_architecture_diagrams.py critical
"""

import sys
import subprocess
from pathlib import Path


def run_generator(diagram_type='all', ir_file='complete_mid_level_ir.json', output_dir='architecture_diagrams'):
    """Run the architecture diagram generator with specified parameters."""
    
    # Check if IR file exists
    if not Path(ir_file).exists():
        print(f"❌ Error: IR file '{ir_file}' not found!")
        print("   Make sure you have generated the IR first by running:")
        print("   python generate_full_ir.py")
        return False
    
    # Map friendly names to actual diagram types
    type_mapping = {
        'all': 'all',
        'business': 'business',
        'overview': 'overview',
        'layers': 'components',
        'critical': 'critical',
        'deps': 'dependencies'
    }
    
    actual_type = type_mapping.get(diagram_type, diagram_type)
    
    # Build command
    cmd = [
        'python', 
        'architecture_diagram_generator.py',
        ir_file,
        '--output-dir', output_dir,
        '--diagram-type', actual_type
    ]
    
    print(f"🎨 Generating {diagram_type} architecture diagram(s)...")
    print(f"   Command: {' '.join(cmd)}")
    print()
    
    # Run the generator
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running diagram generator:")
        print(e.stderr)
        return False
    except FileNotFoundError:
        print("❌ Error: architecture_diagram_generator.py not found!")
        print("   Make sure the generator script is in the current directory.")
        return False


def show_help():
    """Show help information."""
    print(__doc__)


def main():
    """Main function."""
    # Parse command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command in ['help', '-h', '--help']:
            show_help()
            return 0
        elif command in ['all', 'business', 'overview', 'layers', 'critical', 'deps']:
            success = run_generator(command)
            return 0 if success else 1
        else:
            print(f"❌ Unknown command: {command}")
            print("   Use 'help' to see available commands.")
            return 1
    else:
        # Default: generate all diagrams
        success = run_generator('all')
        return 0 if success else 1


if __name__ == "__main__":
    exit(main())
