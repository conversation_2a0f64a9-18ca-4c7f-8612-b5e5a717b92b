# Clear Aider Cache Script
# Run this after fixing the path resolution issues

Write-Host "Clearing Aider Caches..." -ForegroundColor Yellow

# Clear aider repository cache
$aiderCache = "aider-main\.aider.tags.cache.v3"
if (Test-Path $aiderCache) {
    Remove-Item -Recurse -Force $aiderCache
    Write-Host "Cleared aider repository cache: $aiderCache" -ForegroundColor Green
} else {
    Write-Host "No aider repository cache found" -ForegroundColor Gray
}

# Clear trading project cache
$tradingCache = "C:\Users\<USER>\Documents\____live_backtest_dashboard_____\.aider.tags.cache.v3"
if (Test-Path $tradingCache) {
    Remove-Item -Recurse -Force $tradingCache
    Write-Host "Cleared trading project cache: $tradingCache" -ForegroundColor Green
} else {
    Write-Host "No trading project cache found" -ForegroundColor Gray
}

# Clear any cache in current directory
$currentCache = ".aider.tags.cache.v3"
if (Test-Path $currentCache) {
    Remove-Item -Recurse -Force $currentCache
    Write-Host "Cleared current directory cache: $currentCache" -ForegroundColor Green
} else {
    Write-Host "No current directory cache found" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Cache clearing complete!" -ForegroundColor Green
Write-Host "Next time you run aider, it will regenerate clean caches with correct paths." -ForegroundColor Cyan
