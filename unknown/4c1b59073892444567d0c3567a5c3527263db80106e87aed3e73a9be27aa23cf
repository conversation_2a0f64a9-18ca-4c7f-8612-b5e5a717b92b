"""
Error Analyzer - Detects potential errors and exceptions in code.

This module analyzes code to identify potential exceptions that might be
raised and common error patterns in function implementations.
"""

import ast
from typing import Dict, List, Any, Set

from .ir_context import IRContext


class ErrorAnalyzer:
    """
    Analyzes code for potential errors and exceptions.
    
    This analyzer detects:
    - Explicit raise statements
    - Exception handling patterns
    - Common error-prone operations
    - Type-related errors
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the error analyzer with configuration.
        
        Args:
            config: Configuration dictionary for error analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Common error patterns and their associated exceptions
        self.error_patterns = {
            'IndexError': ['[', ']', 'index', 'pop'],
            'KeyError': ['{', '}', 'key', 'dict'],
            'AttributeError': ['.', 'getattr', 'hasattr'],
            'TypeError': ['int(', 'float(', 'str(', '+', '-', '*', '/'],
            'ValueError': ['int(', 'float(', 'split(', 'replace('],
            'FileNotFoundError': ['open(', 'read(', 'write('],
            'ImportError': ['import ', 'from '],
            'ZeroDivisionError': ['/', '//', '%', 'divmod'],
            'NameError': ['undefined', 'not defined'],
            'RuntimeError': ['runtime', 'execution']
        }
    
    def analyze(self, context: IRContext) -> IRContext:
        """
        Analyze potential errors for all entities in the context.
        
        Args:
            context: The IR context containing modules with entities
            
        Returns:
            Updated context with error information
        """
        if self.verbose:
            print(f"   Analyzing potential errors in {len(context.modules)} modules")
        
        total_analyzed = 0
        for module_info in context.modules.values():
            for entity in module_info.entities:
                if entity.type in ('function', 'async_function') and entity.ast_node:
                    errors = self._analyze_entity_errors(entity.ast_node, module_info.source_code)
                    entity.errors = errors
                    total_analyzed += 1
        
        if self.verbose:
            print(f"   Analyzed potential errors for {total_analyzed} entities")
        
        return context
    
    def _analyze_entity_errors(self, func_node: ast.FunctionDef, source_code: str) -> List[str]:
        """
        Analyze potential errors for a single function.
        
        Args:
            func_node: AST function definition node
            source_code: Source code for additional analysis
            
        Returns:
            List of potential exception types
        """
        errors = set()
        
        # Use AST visitor to detect explicit raises and error patterns
        visitor = ErrorVisitor()
        visitor.visit(func_node)
        
        errors.update(visitor.explicit_raises)
        errors.update(visitor.potential_errors)
        
        # Additional pattern-based analysis
        func_source = self._extract_function_source(func_node, source_code)
        if func_source:
            pattern_errors = self._analyze_error_patterns(func_source)
            errors.update(pattern_errors)
        
        return list(errors) if errors else ["RuntimeError"]
    
    def _extract_function_source(self, func_node: ast.FunctionDef, source_code: str) -> str:
        """Extract the source code for a specific function."""
        try:
            lines = source_code.split('\n')
            start_line = func_node.lineno - 1  # Convert to 0-based
            end_line = getattr(func_node, 'end_lineno', start_line + 10)
            
            if end_line and start_line < len(lines):
                return '\n'.join(lines[start_line:end_line])
        except:
            pass
        
        return ""
    
    def _analyze_error_patterns(self, func_source: str) -> Set[str]:
        """Analyze source code for error-prone patterns."""
        detected_errors = set()
        
        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if pattern in func_source:
                    detected_errors.add(error_type)
                    break  # One pattern match is enough for this error type
        
        return detected_errors


class ErrorVisitor(ast.NodeVisitor):
    """AST visitor to detect error patterns and explicit raises."""
    
    def __init__(self):
        self.explicit_raises = set()
        self.potential_errors = set()
    
    def visit_Raise(self, node: ast.Raise) -> None:
        """Visit raise statements to detect explicit exceptions."""
        if node.exc:
            exception_name = self._get_exception_name(node.exc)
            if exception_name:
                self.explicit_raises.add(exception_name)
        
        self.generic_visit(node)
    
    def visit_Subscript(self, node: ast.Subscript) -> None:
        """Visit subscript operations that might raise IndexError/KeyError."""
        self.potential_errors.add('IndexError')
        self.potential_errors.add('KeyError')
        self.generic_visit(node)
    
    def visit_Attribute(self, node: ast.Attribute) -> None:
        """Visit attribute access that might raise AttributeError."""
        self.potential_errors.add('AttributeError')
        self.generic_visit(node)
    
    def visit_Call(self, node: ast.Call) -> None:
        """Visit function calls that might raise various exceptions."""
        call_name = self._get_call_name(node.func)
        
        if call_name:
            # Check for type conversion functions
            if call_name in ('int', 'float'):
                self.potential_errors.add('ValueError')
                self.potential_errors.add('TypeError')
            elif call_name == 'open':
                self.potential_errors.add('FileNotFoundError')
                self.potential_errors.add('PermissionError')
            elif call_name in ('getattr', 'setattr', 'delattr'):
                self.potential_errors.add('AttributeError')
        
        self.generic_visit(node)
    
    def visit_BinOp(self, node: ast.BinOp) -> None:
        """Visit binary operations that might raise exceptions."""
        if isinstance(node.op, (ast.Div, ast.FloorDiv, ast.Mod)):
            self.potential_errors.add('ZeroDivisionError')
        
        # Type-related errors for arithmetic operations
        if isinstance(node.op, (ast.Add, ast.Sub, ast.Mult, ast.Div, ast.FloorDiv)):
            self.potential_errors.add('TypeError')
        
        self.generic_visit(node)
    
    def _get_exception_name(self, exc_node) -> str:
        """Extract exception name from a raise statement."""
        if isinstance(exc_node, ast.Name):
            return exc_node.id
        elif isinstance(exc_node, ast.Call) and isinstance(exc_node.func, ast.Name):
            return exc_node.func.id
        elif isinstance(exc_node, ast.Attribute):
            return exc_node.attr
        else:
            return ""
    
    def _get_call_name(self, func_node) -> str:
        """Extract function name from a call node."""
        if isinstance(func_node, ast.Name):
            return func_node.id
        elif isinstance(func_node, ast.Attribute):
            return func_node.attr
        else:
            return ""
