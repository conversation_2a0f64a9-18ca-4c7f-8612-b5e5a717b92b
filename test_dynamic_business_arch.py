#!/usr/bin/env python3
"""
Test script for dynamic business architecture generation
"""

import json
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

try:
    from architecture_diagram_generator import ArchitectureDiagramGenerator
    
    print("🔍 Loading IR data...")
    
    # Load IR data
    with open('complete_mid_level_ir.json', 'r', encoding='utf-8') as f:
        ir_data = json.load(f)
    
    print(f"   Loaded {len(ir_data.get('modules', []))} modules")
    
    # Generate dynamic business architecture
    print("\n🏢 Generating dynamic business architecture...")
    generator = ArchitectureDiagramGenerator(ir_data)
    diagram = generator.generate_business_system_architecture()
    
    # Save to file
    output_file = 'dynamic_business_architecture.mmd'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(diagram)
    
    print(f"✅ Generated dynamic business architecture diagram!")
    print(f"   Saved to: {output_file}")
    print(f"   Diagram length: {len(diagram)} characters")
    
    # Show first few lines
    lines = diagram.split('\n')
    print(f"\n📄 Preview (first 10 lines):")
    for i, line in enumerate(lines[:10], 1):
        print(f"   {i:2d}: {line}")
    
    if len(lines) > 10:
        print(f"   ... ({len(lines) - 10} more lines)")

except ImportError as e:
    print(f"❌ Import error: {e}")
except FileNotFoundError as e:
    print(f"❌ File not found: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
