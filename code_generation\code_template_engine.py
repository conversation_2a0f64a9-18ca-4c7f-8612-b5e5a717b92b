"""
Code Template Engine

Generates context-aware code templates based on architectural patterns,
coding styles, and existing codebase conventions.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from string import Template

from .architectural_pattern_analyzer import ArchitecturalAnalysis, CodingStyle


@dataclass
class CodeTemplate:
    """Represents a code template with metadata."""
    name: str
    template_type: str  # 'class', 'function', 'module', 'pattern'
    content: str
    variables: List[str]
    dependencies: List[str]
    pattern_compliance: Dict[str, float]
    style_compliance: float


@dataclass
class GenerationRequest:
    """Represents a code generation request."""
    request_type: str  # 'class', 'function', 'module', 'enhancement'
    target_name: str
    description: str
    context_entities: List[str]
    requirements: Dict[str, Any]
    target_module: Optional[str] = None


@dataclass
class GeneratedCode:
    """Represents generated code with metadata."""
    code: str
    template_used: str
    imports_needed: List[str]
    dependencies: List[str]
    quality_score: float
    pattern_compliance: Dict[str, float]
    integration_notes: List[str]


class CodeTemplateEngine:
    """
    Generates context-aware code templates and code based on architectural analysis.
    
    This engine:
    - Creates templates that match existing coding styles
    - Follows detected architectural patterns
    - Generates appropriate imports and dependencies
    - Ensures integration with existing code structures
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the code template engine.
        
        Args:
            config: Configuration dictionary for template generation options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Template library
        self.base_templates = {
            'class': self._get_class_templates(),
            'function': self._get_function_templates(),
            'module': self._get_module_templates(),
            'pattern': self._get_pattern_templates()
        }
        
        # Style formatters
        self.style_formatters = {
            'naming': self._apply_naming_conventions,
            'indentation': self._apply_indentation,
            'imports': self._format_imports,
            'docstrings': self._format_docstrings
        }
    
    def generate_code(self, request: GenerationRequest, 
                     architectural_analysis: ArchitecturalAnalysis) -> GeneratedCode:
        """
        Generate code based on the request and architectural analysis.
        
        Args:
            request: The code generation request
            architectural_analysis: Analysis of existing codebase architecture
            
        Returns:
            Generated code with metadata
        """
        if self.verbose:
            print(f"🔧 Generating {request.request_type}: {request.target_name}")
        
        # Select appropriate template
        template = self._select_template(request, architectural_analysis)
        
        # Apply architectural patterns
        template = self._apply_patterns(template, architectural_analysis.patterns)
        
        # Apply coding style
        template = self._apply_coding_style(template, architectural_analysis.coding_style)
        
        # Generate the actual code
        code = self._generate_from_template(template, request)
        
        # Calculate quality metrics
        quality_score = self._calculate_quality_score(code, architectural_analysis)
        
        # Determine required imports
        imports_needed = self._determine_imports(code, request, architectural_analysis)
        
        # Generate integration notes
        integration_notes = self._generate_integration_notes(request, architectural_analysis)
        
        if self.verbose:
            print(f"   Generated {len(code.split('\\n'))} lines with quality score: {quality_score:.2f}")
        
        return GeneratedCode(
            code=code,
            template_used=template.name,
            imports_needed=imports_needed,
            dependencies=template.dependencies,
            quality_score=quality_score,
            pattern_compliance=template.pattern_compliance,
            integration_notes=integration_notes
        )
    
    def _select_template(self, request: GenerationRequest, 
                        analysis: ArchitecturalAnalysis) -> CodeTemplate:
        """Select the most appropriate template for the request."""
        templates = self.base_templates.get(request.request_type, [])
        
        if not templates:
            # Fallback to basic template
            return self._create_basic_template(request.request_type)
        
        # Score templates based on pattern compliance
        best_template = None
        best_score = 0.0
        
        for template in templates:
            score = self._score_template_fit(template, request, analysis)
            if score > best_score:
                best_score = score
                best_template = template
        
        return best_template or templates[0]
    
    def _score_template_fit(self, template: CodeTemplate, request: GenerationRequest,
                           analysis: ArchitecturalAnalysis) -> float:
        """Score how well a template fits the request and existing architecture."""
        score = 0.0
        
        # Pattern compliance scoring
        for pattern in analysis.patterns:
            if pattern.name in template.pattern_compliance:
                score += template.pattern_compliance[pattern.name] * pattern.confidence
        
        # Style compliance scoring
        score += template.style_compliance * 0.3
        
        # Context relevance scoring
        if request.context_entities:
            # Bonus for templates that work well with existing entities
            score += 0.2
        
        return score
    
    def _apply_patterns(self, template: CodeTemplate, 
                       patterns: List) -> CodeTemplate:
        """Apply detected architectural patterns to the template."""
        modified_content = template.content
        
        for pattern in patterns:
            if pattern.name == 'Factory' and 'class' in template.template_type:
                # Add factory method pattern elements
                modified_content = self._add_factory_elements(modified_content)
            elif pattern.name == 'Observer' and 'class' in template.template_type:
                # Add observer pattern elements
                modified_content = self._add_observer_elements(modified_content)
            elif pattern.name == 'Strategy' and 'class' in template.template_type:
                # Add strategy pattern elements
                modified_content = self._add_strategy_elements(modified_content)
        
        return CodeTemplate(
            name=template.name,
            template_type=template.template_type,
            content=modified_content,
            variables=template.variables,
            dependencies=template.dependencies,
            pattern_compliance=template.pattern_compliance,
            style_compliance=template.style_compliance
        )
    
    def _apply_coding_style(self, template: CodeTemplate, 
                           coding_style: CodingStyle) -> CodeTemplate:
        """Apply detected coding style to the template."""
        content = template.content
        
        # Apply naming conventions
        content = self.style_formatters['naming'](content, coding_style.naming_conventions)
        
        # Apply indentation
        content = self.style_formatters['indentation'](content, coding_style.indentation)
        
        # Apply docstring style
        content = self.style_formatters['docstrings'](content, coding_style.docstring_style)
        
        return CodeTemplate(
            name=template.name,
            template_type=template.template_type,
            content=content,
            variables=template.variables,
            dependencies=template.dependencies,
            pattern_compliance=template.pattern_compliance,
            style_compliance=template.style_compliance
        )
    
    def _generate_from_template(self, template: CodeTemplate, 
                               request: GenerationRequest) -> str:
        """Generate actual code from template and request."""
        # Create template variables
        template_vars = {
            'class_name': self._format_name(request.target_name, 'class'),
            'function_name': self._format_name(request.target_name, 'function'),
            'module_name': self._format_name(request.target_name, 'module'),
            'description': request.description,
            'requirements': str(request.requirements)
        }
        
        # Add custom variables from request
        if 'variables' in request.requirements:
            template_vars.update(request.requirements['variables'])
        
        # Substitute template variables
        template_obj = Template(template.content)
        try:
            code = template_obj.substitute(template_vars)
        except KeyError as e:
            # Handle missing variables gracefully
            code = template_obj.safe_substitute(template_vars)
            if self.verbose:
                print(f"   Warning: Missing template variable {e}")
        
        return code
    
    def _format_name(self, name: str, name_type: str) -> str:
        """Format a name according to conventions."""
        # Remove special characters and normalize
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        
        if name_type == 'class':
            # PascalCase for classes
            return ''.join(word.capitalize() for word in clean_name.split('_'))
        elif name_type == 'function':
            # snake_case for functions
            return clean_name.lower()
        elif name_type == 'module':
            # snake_case for modules
            return clean_name.lower()
        
        return clean_name

    def _calculate_quality_score(self, code: str, analysis: ArchitecturalAnalysis) -> float:
        """Calculate quality score for generated code."""
        score = 0.5  # Base score

        # Check for proper structure
        if 'class ' in code or 'def ' in code:
            score += 0.2

        # Check for docstrings
        if '"""' in code or "'''" in code:
            score += 0.1

        # Check for type hints
        if '->' in code or ': ' in code:
            score += 0.1

        # Check line length compliance
        lines = code.split('\n')
        long_lines = sum(1 for line in lines if len(line) > analysis.coding_style.line_length)
        if long_lines == 0:
            score += 0.1

        return min(1.0, score)

    def _determine_imports(self, code: str, request: GenerationRequest,
                          analysis: ArchitecturalAnalysis) -> List[str]:
        """Determine required imports for the generated code."""
        imports = []

        # Standard library imports
        if 'typing' in code or 'List[' in code or 'Dict[' in code:
            imports.append('from typing import List, Dict, Any, Optional')

        if 'dataclass' in code:
            imports.append('from dataclasses import dataclass')

        if 'abc' in code or 'ABC' in code:
            imports.append('from abc import ABC, abstractmethod')

        # Context-based imports
        if request.context_entities:
            for entity in request.context_entities:
                if '.' in entity:
                    module, _ = entity.rsplit('.', 1)
                    imports.append(f'from {module} import *')

        return imports

    def _generate_integration_notes(self, request: GenerationRequest,
                                   analysis: ArchitecturalAnalysis) -> List[str]:
        """Generate notes for integrating the generated code."""
        notes = []

        notes.append(f"Generated {request.request_type} follows detected coding style")

        if analysis.patterns:
            pattern_names = [p.name for p in analysis.patterns]
            notes.append(f"Complies with detected patterns: {', '.join(pattern_names)}")

        if request.target_module:
            notes.append(f"Designed for integration with module: {request.target_module}")

        notes.append("Review imports and dependencies before integration")
        notes.append("Consider adding unit tests for the generated code")

        return notes

    # Template Creation Methods

    def _get_class_templates(self) -> List[CodeTemplate]:
        """Get available class templates."""
        return [
            CodeTemplate(
                name='basic_class',
                template_type='class',
                content='''class $class_name:
    """$description"""

    def __init__(self):
        """Initialize the $class_name."""
        pass

    def __str__(self) -> str:
        """Return string representation."""
        return f"$class_name()"
''',
                variables=['class_name', 'description'],
                dependencies=[],
                pattern_compliance={},
                style_compliance=0.8
            ),
            CodeTemplate(
                name='dataclass_template',
                template_type='class',
                content='''@dataclass
class $class_name:
    """$description"""

    def __post_init__(self):
        """Post-initialization processing."""
        pass
''',
                variables=['class_name', 'description'],
                dependencies=['dataclasses'],
                pattern_compliance={},
                style_compliance=0.9
            )
        ]

    def _get_function_templates(self) -> List[CodeTemplate]:
        """Get available function templates."""
        return [
            CodeTemplate(
                name='basic_function',
                template_type='function',
                content='''def $function_name() -> None:
    """$description"""
    pass
''',
                variables=['function_name', 'description'],
                dependencies=[],
                pattern_compliance={},
                style_compliance=0.8
            ),
            CodeTemplate(
                name='typed_function',
                template_type='function',
                content='''def $function_name(param: Any) -> Any:
    """
    $description

    Args:
        param: Input parameter

    Returns:
        Processed result
    """
    return param
''',
                variables=['function_name', 'description'],
                dependencies=['typing'],
                pattern_compliance={},
                style_compliance=0.9
            )
        ]

    def _get_module_templates(self) -> List[CodeTemplate]:
        """Get available module templates."""
        return [
            CodeTemplate(
                name='basic_module',
                template_type='module',
                content='''"""
$description
"""

__version__ = "1.0.0"
__author__ = "Generated Code"

# Module implementation here
''',
                variables=['description'],
                dependencies=[],
                pattern_compliance={},
                style_compliance=0.8
            )
        ]

    def _get_pattern_templates(self) -> List[CodeTemplate]:
        """Get pattern-specific templates."""
        return []

    def _create_basic_template(self, template_type: str) -> CodeTemplate:
        """Create a basic fallback template."""
        return CodeTemplate(
            name=f'basic_{template_type}',
            template_type=template_type,
            content=f'# Generated {template_type}: $target_name\n# $description\npass\n',
            variables=['target_name', 'description'],
            dependencies=[],
            pattern_compliance={},
            style_compliance=0.5
        )

    # Style Formatter Methods

    def _apply_naming_conventions(self, content: str, naming_conventions: Dict[str, str]) -> str:
        """Apply naming conventions to template content."""
        # This is a simplified implementation
        # In practice, this would be more sophisticated
        return content

    def _apply_indentation(self, content: str, indentation: str) -> str:
        """Apply indentation style to template content."""
        if indentation == '2_spaces':
            # Convert 4-space indentation to 2-space
            lines = content.split('\n')
            converted_lines = []
            for line in lines:
                if line.startswith('    '):
                    # Replace 4 spaces with 2 spaces
                    converted_lines.append('  ' + line[4:])
                else:
                    converted_lines.append(line)
            return '\n'.join(converted_lines)
        elif indentation == 'tabs':
            # Convert spaces to tabs
            lines = content.split('\n')
            converted_lines = []
            for line in lines:
                if line.startswith('    '):
                    # Replace 4 spaces with tab
                    converted_lines.append('\t' + line[4:])
                else:
                    converted_lines.append(line)
            return '\n'.join(converted_lines)

        return content  # Default 4_spaces

    def _format_imports(self, content: str, import_style: str) -> str:
        """Format import statements according to style."""
        # This would format imports based on detected style
        return content

    def _format_docstrings(self, content: str, docstring_style: str) -> str:
        """Format docstrings according to detected style."""
        if docstring_style == 'numpy':
            # Convert Google-style to NumPy-style
            content = content.replace('Args:', 'Parameters\n----------')
            content = content.replace('Returns:', 'Returns\n-------')
        elif docstring_style == 'sphinx':
            # Convert to Sphinx-style
            content = re.sub(r'Args:\s*\n\s*(\w+):', r':param \1:', content)
            content = content.replace('Returns:', ':return:')

        return content

    # Pattern Application Methods

    def _add_factory_elements(self, content: str) -> str:
        """Add factory pattern elements to class template."""
        if 'class ' in content and '__init__' in content:
            # Add a create method
            factory_method = '''
    @classmethod
    def create(cls, *args, **kwargs):
        """Factory method to create instances."""
        return cls(*args, **kwargs)
'''
            # Insert before the last line
            lines = content.split('\n')
            lines.insert(-1, factory_method)
            return '\n'.join(lines)
        return content

    def _add_observer_elements(self, content: str) -> str:
        """Add observer pattern elements to class template."""
        if 'class ' in content:
            observer_methods = '''
    def __init__(self):
        """Initialize with observer list."""
        self._observers = []
        super().__init__()

    def add_observer(self, observer):
        """Add an observer."""
        self._observers.append(observer)

    def remove_observer(self, observer):
        """Remove an observer."""
        if observer in self._observers:
            self._observers.remove(observer)

    def notify_observers(self, *args, **kwargs):
        """Notify all observers."""
        for observer in self._observers:
            observer.update(self, *args, **kwargs)
'''
            # Replace the basic __init__ with observer-aware version
            content = content.replace(
                'def __init__(self):\n        """Initialize the $class_name."""\n        pass',
                observer_methods.strip()
            )
        return content

    def _add_strategy_elements(self, content: str) -> str:
        """Add strategy pattern elements to class template."""
        if 'class ' in content:
            strategy_methods = '''
    def __init__(self, strategy=None):
        """Initialize with strategy."""
        self._strategy = strategy

    def set_strategy(self, strategy):
        """Set the strategy."""
        self._strategy = strategy

    def execute(self, *args, **kwargs):
        """Execute using the current strategy."""
        if self._strategy:
            return self._strategy.execute(*args, **kwargs)
        raise NotImplementedError("No strategy set")
'''
            # Replace the basic __init__ with strategy-aware version
            content = content.replace(
                'def __init__(self):\n        """Initialize the $class_name."""\n        pass',
                strategy_methods.strip()
            )
        return content
