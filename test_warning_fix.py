#!/usr/bin/env python3
"""
Test script to verify the warning fix for map-tokens > 16384
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.models import Model
from aider.io import InputOutput
from aider.coders.base_coder import Coder


def test_warning_threshold():
    """Test the updated warning threshold"""
    
    print("🧪 Testing warning threshold fix...")
    
    # Create model and IO
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Test the model's get_repo_map_tokens method
    model_tokens = model.get_repo_map_tokens()
    print(f"📊 Model's repo map tokens: {model_tokens}")
    
    # Calculate the new warning threshold (3x instead of 2x)
    warning_threshold = model_tokens * 3
    print(f"📊 Warning threshold (3x): {warning_threshold}")
    
    # Test with our optimized value
    our_tokens = 20202
    print(f"📊 Our token setting: {our_tokens}")
    
    if our_tokens <= warning_threshold:
        print(f"✅ SUCCESS: {our_tokens} <= {warning_threshold} - No warning should appear!")
    else:
        print(f"⚠️  WARNING: {our_tokens} > {warning_threshold} - Warning will still appear")
    
    # Test by creating a coder instance (this triggers the warning check)
    print("\n🔧 Testing with actual Coder instance...")
    
    try:
        # Create a minimal coder instance
        coder = Coder(
            main_model=model,
            io=io,
            map_tokens=20202,
            dry_run=True,
            verbose=True
        )
        
        # Get announcements (this is where the warning would appear)
        announcements = coder.get_announcements()
        
        print("📋 Announcements:")
        for announcement in announcements:
            print(f"   {announcement}")
            
        # Check if warning is present
        warning_found = any("Warning: map-tokens >" in announcement for announcement in announcements)
        
        if warning_found:
            print("❌ Warning still appears - threshold may need further adjustment")
        else:
            print("✅ No warning found - fix successful!")
            
    except Exception as e:
        print(f"❌ Error creating coder: {e}")
        import traceback
        traceback.print_exc()


def test_different_token_values():
    """Test various token values to understand the threshold"""
    
    print("\n🧪 Testing different token values...")
    
    model = Model("gpt-3.5-turbo")
    model_tokens = model.get_repo_map_tokens()
    
    test_values = [
        4096,    # Original default
        8192,    # 2x original
        16384,   # Old warning threshold
        20202,   # Our optimized value
        24576,   # 3x model tokens (new threshold)
        30000,   # Above new threshold
    ]
    
    print(f"Model tokens: {model_tokens}")
    print(f"2x threshold (old): {model_tokens * 2}")
    print(f"3x threshold (new): {model_tokens * 3}")
    print()
    
    for tokens in test_values:
        old_threshold = model_tokens * 2
        new_threshold = model_tokens * 3
        
        old_warning = "⚠️" if tokens > old_threshold else "✅"
        new_warning = "⚠️" if tokens > new_threshold else "✅"
        
        print(f"{tokens:>6} tokens: Old {old_warning} | New {new_warning}")


def main():
    """Main test function"""
    
    print("🔍 Testing Map-Tokens Warning Fix")
    print("=" * 50)
    
    # Test the warning threshold
    test_warning_threshold()
    
    # Test different values
    test_different_token_values()
    
    print("\n" + "=" * 50)
    print("✅ Warning fix testing complete!")


if __name__ == "__main__":
    main()
