#!/usr/bin/env python3
"""
Debug script to examine the complete repository map structure.
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_repository_map():
    """Debug the complete repository map structure."""
    print("🔍 Debugging Repository Map Structure")
    print("=" * 60)

    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.io import InputOutput

        # Create handler
        root_dir = "aider-main"
        io = InputOutput()
        repo_map = RepoMap(map_tokens=20000, root=root_dir, io=io)
        handler = SmartMapRequestHandler(repo_map, root_dir, io)

        print("✅ Handler created successfully")

        # Generate complete repository map
        print("\n🗺️ Generating complete repository map...")
        complete_map = handler._ensure_complete_repository_map()

        if complete_map:
            print(f"Complete map size: {len(complete_map)} characters")

            # Show first 50 lines to understand structure
            lines = complete_map.split('\n')
            print(f"Total lines: {len(lines)}")
            print("\n📋 First 50 lines of complete map:")
            print("-" * 60)
            for i, line in enumerate(lines[:50]):
                print(f"{i+1:3d}: {line}")

            print("\n📋 Lines containing 'repomap':")
            print("-" * 60)
            for i, line in enumerate(lines):
                if 'repomap' in line.lower():
                    print(f"{i+1:3d}: {line}")

            # Test the search results
            print("\n🔍 Testing search for repomap files...")
            request = {
                'keywords': ['repository', 'mapping', 'repomap'],
                'type': 'implementation',
                'scope': 'all',
                'max_results': 5
            }

            result = handler.process_map_request(request)

            # Extract the relevant files from the search
            print("\n📋 Search found these files:")
            print("-" * 60)
            # Parse the result to find the file list
            if "Search Results" in result:
                lines = result.split('\n')
                in_search_results = False
                for line in lines:
                    if "Search Results" in line:
                        in_search_results = True
                        continue
                    elif "## Repository Structure" in line:
                        break
                    elif in_search_results and line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
                        print(line.strip())

            # Test filtering logic manually
            print("\n🧪 Testing filtering logic...")
            test_files = [
                'aider-main\\aider\\repomap.py',
                'aider-main/aider/repomap.py',
                'aider\\repomap.py',
                'aider/repomap.py',
                'repomap.py'
            ]

            for test_file in test_files:
                matches = []
                for i, line in enumerate(lines[:100]):  # Check first 100 lines
                    if test_file in line or line.strip().endswith(test_file):
                        matches.append(f"Line {i+1}: {line}")

                print(f"\nTesting file path: '{test_file}'")
                if matches:
                    print("  Matches found:")
                    for match in matches[:3]:  # Show first 3 matches
                        print(f"    {match}")
                else:
                    print("  No matches found")

        else:
            print("❌ Complete map is empty or None")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_repository_map()
