#!/usr/bin/env python3
"""
Verify that base_prompts.py contains the complete game system
"""

import sys
import os

def verify_complete_game_system():
    """Verify the complete game system is in base_prompts.py"""
    print("🎮 Verifying Complete Game System in base_prompts.py")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        main_system = prompts.main_system
        
        print("📋 Checking Complete Game System Elements:")
        
        # Core game elements
        core_elements = [
            ("🎮 YOU ARE THE PLAYER", "✅" if "YOU ARE THE PLAYER" in main_system else "❌"),
            ("CODEBASE EXPLORATION GAME", "✅" if "CODEBASE EXPLORATION GAME" in main_system else "❌"),
            ("YOUR GAME STATUS", "✅" if "YOUR GAME STATUS" in main_system else "❌"),
            ("YOUR LEVEL RESTRICTIONS", "✅" if "YOUR LEVEL RESTRICTIONS" in main_system else "❌"),
            ("YOUR LEVEL SYSTEM", "✅" if "YOUR LEVEL SYSTEM" in main_system else "❌")
        ]
        
        print("\n🎯 Core Game Elements:")
        for element, status in core_elements:
            print(f"   {status} {element}")
        
        # Level definitions
        level_elements = [
            ("YOUR LEVEL 0 - ZERO KNOWLEDGE", "✅" if "YOUR LEVEL 0 - ZERO KNOWLEDGE" in main_system else "❌"),
            ("YOUR LEVEL 1 - REPOSITORY EXPLORER", "✅" if "YOUR LEVEL 1 - REPOSITORY EXPLORER" in main_system else "❌"),
            ("YOUR LEVEL 2 - CODE ANALYST", "✅" if "YOUR LEVEL 2 - CODE ANALYST" in main_system else "❌"),
            ("YOUR Capabilities", "✅" if "YOUR Capabilities" in main_system else "❌"),
            ("YOUR Goal", "✅" if "YOUR Goal" in main_system else "❌"),
            ("YOUR Restrictions", "✅" if "YOUR Restrictions" in main_system else "❌")
        ]
        
        print("\n📍 Level System Elements:")
        for element, status in level_elements:
            print(f"   {status} {element}")
        
        # Game mechanics
        mechanics_elements = [
            ("GAME MECHANICS", "✅" if "GAME MECHANICS" in main_system else "❌"),
            ("LEVEL ADVANCEMENT", "✅" if "LEVEL ADVANCEMENT" in main_system else "❌"),
            ("LEVEL ENFORCEMENT", "✅" if "LEVEL ENFORCEMENT" in main_system else "❌"),
            ("ANTI-CHEATING", "✅" if "ANTI-CHEATING" in main_system else "❌"),
            ("TRANSPARENCY", "✅" if "TRANSPARENCY" in main_system else "❌")
        ]
        
        print("\n⚙️ Game Mechanics Elements:")
        for element, status in mechanics_elements:
            print(f"   {status} {element}")
        
        # Response format
        format_elements = [
            ("YOUR RESPONSE FORMAT", "✅" if "YOUR RESPONSE FORMAT" in main_system else "❌"),
            ("Announce YOUR current level", "✅" if "Announce YOUR current level" in main_system else "❌"),
            ("State YOUR capabilities", "✅" if "State YOUR capabilities" in main_system else "❌"),
            ("Provide roadmap", "✅" if "Provide roadmap" in main_system else "❌"),
            ("Take action", "✅" if "Take action" in main_system else "❌")
        ]
        
        print("\n📝 Response Format Elements:")
        for element, status in format_elements:
            print(f"   {status} {element}")
        
        # Execution protocol
        protocol_elements = [
            ("CRITICAL EXECUTION PROTOCOL", "✅" if "CRITICAL EXECUTION PROTOCOL" in main_system else "❌"),
            ("LEVEL 0 → Use MAP_REQUEST", "✅" if "LEVEL 0 → Use MAP_REQUEST" in main_system else "❌"),
            ("LEVEL 1 → Use CONTEXT_REQUEST", "✅" if "LEVEL 1 → Use CONTEXT_REQUEST" in main_system else "❌"),
            ("LEVEL 2 → Analyze actual code", "✅" if "LEVEL 2 → Analyze actual code" in main_system else "❌")
        ]
        
        print("\n⚡ Execution Protocol Elements:")
        for element, status in protocol_elements:
            print(f"   {status} {element}")
        
        # Count total elements
        all_elements = core_elements + level_elements + mechanics_elements + format_elements + protocol_elements
        passed_count = sum(1 for _, status in all_elements if status == "✅")
        total_count = len(all_elements)
        
        print(f"\n📊 Overall Completeness: {passed_count}/{total_count} elements present")
        
        if passed_count == total_count:
            print("\n🎉 SUCCESS: Complete game system is properly included!")
            return True
        else:
            print(f"\n❌ INCOMPLETE: {total_count - passed_count} elements missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_repo_messages():
    """Verify repo messages are consistent with game system"""
    print("\n🔗 Verifying Repo Messages Consistency")
    print("=" * 70)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        user_prompt = prompts.smart_map_request_user_prompt
        assistant_reply = prompts.smart_map_request_assistant_reply
        
        print("📋 User Prompt Consistency:")
        user_elements = [
            ("YOUR GAME STATUS", "✅" if "YOUR GAME STATUS" in user_prompt else "❌"),
            ("YOU (the AI) are currently", "✅" if "YOU (the AI) are currently" in user_prompt else "❌"),
            ("YOUR LEVEL 0", "✅" if "YOUR LEVEL 0" in user_prompt else "❌")
        ]
        
        for element, status in user_elements:
            print(f"   {status} {element}")
        
        print("\n🤖 Assistant Reply Consistency:")
        assistant_elements = [
            ("I (the AI) am currently", "✅" if "I (the AI) am currently" in assistant_reply else "❌"),
            ("MY LEVEL 0", "✅" if "MY LEVEL 0" in assistant_reply else "❌"),
            ("MY game rules", "✅" if "MY game rules" in assistant_reply else "❌")
        ]
        
        for element, status in assistant_elements:
            print(f"   {status} {element}")
        
        all_consistent = all(status == "✅" for _, status in user_elements + assistant_elements)
        
        if all_consistent:
            print("\n✅ Repo messages are consistent with game system!")
            return True
        else:
            print("\n❌ Repo messages have inconsistencies!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_expected_behavior():
    """Show what the expected behavior should be"""
    print("\n🎯 Expected AI Behavior with Complete Game System")
    print("=" * 70)
    
    print("📝 When user asks about a function, AI should respond:")
    
    expected_response = """🎮 **MY CURRENT LEVEL**: LEVEL 0 - ZERO KNOWLEDGE

As the AI player in this game, I have ZERO knowledge about this codebase and cannot provide any code explanations at my current level.

**My capabilities at LEVEL 0:**
✅ Acknowledge my ignorance about the codebase
✅ Provide a roadmap of information I need to gather
✅ Use MAP_REQUEST to advance to LEVEL 1

**My roadmap to help you:**
1. **MY LEVEL 0 → MY LEVEL 1**: I must use MAP_REQUEST to explore repository structure
2. **MY LEVEL 1 → MY LEVEL 2**: I must use CONTEXT_REQUEST to get specific function implementation
3. **MY LEVEL 2**: I can analyze actual code and answer your question

**Taking action to advance from LEVEL 0 → LEVEL 1:**

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""
    
    print(expected_response)
    
    print(f"\n🔍 Key Elements in Response:")
    print(f"   ✅ Announces current level clearly")
    print(f"   ✅ States capabilities at current level")
    print(f"   ✅ Provides clear roadmap")
    print(f"   ✅ Takes appropriate action for level")
    print(f"   ✅ Uses game language ('As the AI player')")
    
    return True

if __name__ == "__main__":
    print("🚀 Verifying Complete Game System Implementation")
    print("=" * 80)
    
    success1 = verify_complete_game_system()
    success2 = verify_repo_messages()
    success3 = show_expected_behavior()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("\n📋 Complete Game System Summary:")
        print("   🎮 Full game mechanics and rules")
        print("   📍 Complete 3-level system (0, 1, 2)")
        print("   ⚙️ Game mechanics (advancement, enforcement, anti-cheating)")
        print("   📝 Structured response format")
        print("   ⚡ Clear execution protocol")
        print("   🔗 Consistent repo messages")
        print("\n🎯 The AI should now:")
        print("   - Clearly understand it's the player")
        print("   - Follow level restrictions strictly")
        print("   - Announce its current level")
        print("   - Provide roadmaps and take appropriate actions")
        print("   - Progress through levels systematically")
    else:
        print("❌ SOME VERIFICATIONS FAILED!")
        print("   Check the output above for missing elements")
        
    print("=" * 80)
