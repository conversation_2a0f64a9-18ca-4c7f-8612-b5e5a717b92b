# USER QUERY
Why is my context selection taking so long? I think there might be a performance issue.

# INTELLIGENT CONTEXT ANALYSIS PACKAGE
# Generated by IR_CONTEXT_REQUEST system

## TASK INFORMATION
- User Query: Why is my context selection taking so long? I think there might be a performance issue.
- Task Description: Why is my context selection taking so long? I think there might be a performance issue.
- Task Type: debugging

## CONTEXT SUMMARY
- Total Entities Selected: 16
- Total Tokens Used: 1999
- Critical Entities: 16
- High Priority Entities: 16
- Files Involved: 8
- Token Utilization: 100.0%

## SELECTION RATIONALE
Context Selection Rationale for debugging:

Selected 31 entities using 1999 tokens (100.0% of budget).

Priority Distribution:
  - critical: 31 entities

Criticality Distribution:
  - low: 2 entities
  - medium: 23 entities
  - high: 6 entities

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for debugging task requirements
- Maintained token budget constraints

## IR ANALYSIS DATA (16 entities)

### 1. parse_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 3.2520833333333328
- **Priority**: critical
- **Calls**: search, strip, group, rstrip, startswith, endswith, replace, loads, sub, get, append, SymbolRequest, ContextRequest
- **Used By**: test_context_request_missing_reason, test_json_parsing_fix, test_aider_context_request_integration, test_context_request_format_fix, test_directory_file_format, aider_context_request_integration
- **Side Effects**: network_io, writes_log
- **Potential Errors**: ValueError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 2. process_context_requests (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 3.238125
- **Priority**: critical
- **Calls**: tool_warning, getcwd, basename, exists, join, find_common_root, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, keys
- **Used By**: test_context_request_integration, test_context_request_fix, test_context_request_hang, test_full_aider_integration, test_repo_map_compatibility, test_aider_context_request, test_llm_workflow_understanding, test_aider_coder_path_fix, test_context_request_code_block_fix
- **Side Effects**: network_io, writes_log, modifies_state, database_io
- **Potential Errors**: KeyError, AttributeError, ImportError, TypeError, IndexError

### 3. process_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 3.1687499999999997
- **Priority**: critical
- **Calls**: join, _get_from_cache, _extract_symbol_content, _extract_essential_imports, SymbolInfo, _extract_containing_class, extract_usage_contexts, append, _update_cache
- **Used By**: test_complete_function_extraction, test_partial_context_request, test_surgical_integration, test_full_aider_integration, test_context_request_end_to_end, test_partial_success_fix, test_context_request, test_no_dependencies, test_surgical_extraction_integration, test_context_request_with_repo_map
- **Side Effects**: network_io, writes_log, database_io, modifies_state
- **Potential Errors**: KeyError, AttributeError, ImportError, TypeError, IndexError

### 4. process_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 3.15875
- **Priority**: critical
- **Calls**: process_context_request, get, render_augmented_prompt
- **Used By**: test_complete_function_extraction, test_partial_context_request, test_surgical_integration, test_full_aider_integration, test_context_request_end_to_end, test_partial_success_fix, test_context_request, test_no_dependencies, test_surgical_extraction_integration, test_context_request_with_repo_map
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 5. detect_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 3.13375
- **Priority**: critical
- **Calls**: parse_context_request, join
- **Used By**: context_request_demo, test_context_request_integration, test_context_request, test_aider_context_request, test_context_request_hang, test_repo_map_compatibility, test_full_aider_integration, base_coder, base_coder_old
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 6. process_context_requests (function)
- **Module**: base_coder_old
- **File**: aider-main\aider\coders\base_coder_old.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 3.103125
- **Priority**: critical
- **Calls**: tool_warning, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, join, keys, sub, process_context_request
- **Used By**: test_context_request_integration, test_context_request_fix, test_context_request_hang, test_full_aider_integration, test_repo_map_compatibility, test_aider_context_request, test_llm_workflow_understanding, test_aider_coder_path_fix, test_context_request_code_block_fix
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: TypeError, KeyError, AttributeError, ImportError

### 7. update_conversation_history (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 3.0956250000000005
- **Priority**: critical
- **Calls**: get, append
- **Used By**: test_conversation_history_fix, test_context_request_integration, test_context_request_fix, test_context_request_hang, test_full_aider_integration, test_aider_context_request_integration, test_repo_map_compatibility
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 8. select_optimal_context (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.98375
- **Priority**: critical
- **Calls**: _score_entities_for_task, _select_entities_within_budget, _enhance_with_dependency_context, _build_context_bundle, get_critical_entities
- **Used By**: aider_integration_service, intelligent_context_selector, iterative_analysis_engine
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, TypeError, IndexError

### 9. _extract_code_snippet (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.9789583333333334
- **Priority**: critical
- **Calls**: _read_file_content, splitlines, search, _find_complete_class_body, _find_complete_function_body, _find_surrounding_function, join, CodeSnippet
- **Used By**: test_surgical_context_extractor, simple_demo, surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: ZeroDivisionError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 10. _get_context_selector (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.9781250000000004
- **Priority**: critical
- **Calls**: generate_mid_level_ir, IntelligentContextSelector
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: writes_log, modifies_file, modifies_state
- **Potential Errors**: ValueError, TypeError, KeyError, AttributeError, ImportError

### 11. _read_file_content (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.9689583333333336
- **Priority**: critical
- **Calls**: join, read_text
- **Used By**: enhanced_surgical_extractor, surgical_file_extractor, surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: TypeError, KeyError, IndexError, AttributeError

### 12. analyze_context_quality (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.9587499999999998
- **Priority**: critical
- **Calls**: get
- **Used By**: aider_integration_service, intelligent_context_selector
- **Side Effects**: network_io, modifies_container, modifies_state
- **Potential Errors**: ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 13. select_intelligent_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.953125
- **Priority**: critical
- **Calls**: _get_context_selector, get, lower, select_optimal_context, analyze_context_quality, append, sort
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ValueError, KeyError, AttributeError, ImportError, TypeError, IndexError

### 14. cmd_copy_context (function)
- **Module**: commands
- **File**: aider-main\aider\commands.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.953125
- **Priority**: critical
- **Calls**: format_chat_chunks, get, copy, tool_output, tool_error
- **Used By**: base_coder, base_coder_old
- **Side Effects**: network_io, writes_log, modifies_state
- **Potential Errors**: ZeroDivisionError, KeyError, AttributeError, TypeError, IndexError

### 15. extract_dependency_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.9206250000000002
- **Priority**: critical
- **Calls**: _get_from_cache, get_files_imported_by, get_files_that_import, get_symbol_references_between_files, items, _find_symbol_line_numbers, _read_file_content, _determine_context_window_size, _extract_code_snippet, append, _update_cache
- **Used By**: surgical_context_extractor
- **Side Effects**: network_io, modifies_file, database_io, modifies_state
- **Potential Errors**: KeyError, AttributeError, ImportError, TypeError, IndexError

### 16. match (variable)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.462291666666667
- **Priority**: critical
- **Calls**: None
- **Used By**: benchmark, simple_enhanced_test, recording_audio, clean_metadata, versionbump, simple_extraction_test, surgical_context_extractor, help, enhanced_surgical_extractor, aider_integration_service
- **Side Effects**: none
- **Potential Errors**: RuntimeError

## SOURCE CODE IMPLEMENTATIONS (15 implementations)

### 1. parse_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 3.2520833333333328

```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError:
                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                request_data = json.loads(json_str)

            # Create the ContextRequest object
            symbols = []
            for symbol_data in request_data.get('symbols_of_interest', []):
                symbols.append(SymbolRequest(
                    type=symbol_data.get('type', 'unknown'),
                    name=symbol_data.get('name', ''),
                    file_hint=symbol_data.get('file_hint')
                ))

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=request_data.get('reason_for_request', '')
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

```

### 2. process_context_requests
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 3.238125

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration

                # Determine the correct project path for context requests
                # Priority: 1) Current working directory (where user runs aider)
                #          2) Git repository root (fallback)
                import os
                project_path = os.getcwd()

                # If current working directory is the aider repository itself,
                # and we have files in the chat, use the common root of those files
                if (os.path.basename(project_path) == 'aider' and
                    os.path.exists(os.path.join(project_path, 'aider-main')) and
                    (self.abs_fnames or self.abs_read_only_fnames)):
                    # Use the common root of the files in the chat
                    if self.abs_fnames:
                        project_path = utils.find_common_root(self.abs_fnames)
                    elif self.abs_read_only_fnames:
                        project_path = utils.find_common_root(self.abs_read_only_fnames)

                self.context_request_integration = AiderContextRequestIntegration(project_path, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        # DISABLED: Context request limit check
        # if self.current_query_context_requests >= 300:
        #     self.io.tool_error(f"Maximum number of context requests reached for this query. Current count: {self.current_query_context_requests}")
        #     return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # CRITICAL FIX: Extract the ACTUAL user query from user_message
            # The user_message might contain MAP_REQUEST content, we need the original query
            actual_user_query = self._extract_actual_user_query(user_message)

            # CRITICAL FIX: Do NOT pass repo_overview if it contains MAP_REQUEST content
            # For CONTEXT_REQUEST, we want ONLY the clean template response
            clean_repo_overview = ""
            if repo_overview and not self._contains_map_request_content(repo_overview):
                clean_repo_overview = repo_overview

            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=actual_user_query,
                repo_overview=clean_repo_overview
            )

            # Check if the context request completely failed (no symbols found at all)
            # Only trigger failure guidance if ALL symbols were not found, not just some
            if (augmented_prompt and
                "All requested symbols could not be found" in augmented_prompt or
                ("No extracted symbols were included" in augmented_prompt and "could not find" in augmented_prompt.lower())):

                # Extract requested symbols for guidance
                requested_symbols = []
                try:
                    # context_request is a ContextRequest object, not a dict
                    symbols_of_interest = context_request.symbols_of_interest if hasattr(context_request, 'symbols_of_interest') else []
                    for symbol in symbols_of_interest:
                        if hasattr(symbol, 'name'):
                            requested_symbols.append(symbol.name)
                        elif isinstance(symbol, dict) and 'name' in symbol:
                            requested_symbols.append(symbol['name'])
                        elif isinstance(symbol, str):
                            requested_symbols.append(symbol)
                except Exception:
                    requested_symbols = ["requested symbols"]

                # Generate smart guidance message
                reason = ""
                if hasattr(context_request, 'reason_for_request'):
                    reason = context_request.reason_for_request
                elif hasattr(context_request, 'original_user_query_context'):
                    reason = context_request.original_user_query_context
                guidance_message = self._generate_smart_guidance_message("context", requested_symbols, reason)

                # Replace the augmented prompt with guidance
                augmented_prompt = guidance_message

        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 3. process_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 3.1687499999999997

```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                continue

            # Extract essential imports
            essential_imports = None
            if hasattr(self.file_extractor, '_extract_essential_imports'):
                essential_imports = self.file_extractor._extract_essential_imports(self.project_path, file_path)

            # Extract containing class signature if it's a method
            containing_class = None
            if '.' in symbol.name and hasattr(self.file_extractor, '_extract_containing_class'):
                # Create a dummy SymbolInfo object
                from surgical_file_extractor import SymbolInfo
                symbol_info = SymbolInfo(
                    name=symbol_name,
                    start_line=0,  # This will be updated by the extractor
                    file_path=file_path,
                    symbol_type="method"
                )
                containing_class = self.file_extractor._extract_containing_class(self.project_path, file_path, symbol_info)

            # Extract usage contexts
            usage_contexts = self.context_extractor.extract_usage_contexts(self.project_path, symbol_name, file_path)

            # Add the extracted symbol to the result
            result["extracted_symbols"].append({
                "symbol_name": symbol.name,
                "file_path": file_path,
                "content": content,
                "essential_imports": essential_imports,
                "containing_class": containing_class
            })

            # Add dependency snippets
            for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                result["dependency_snippets"].append({
                    "file_path": usage.snippet.file_path,
                    "symbol_name": usage.snippet.symbol_name,
                    "content": usage.snippet.content,
                    "usage_type": usage.usage_type.value
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result
```

### 4. process_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 3.15875

```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

```

### 5. detect_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 3.13375

```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

```

### 6. process_context_requests
- **File**: aider-main\aider\coders\base_coder_old.py
- **Priority**: critical
- **Relevance Score**: 3.103125

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration
                self.context_request_integration = AiderContextRequestIntegration(self.root, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        if self.current_query_context_requests >= 300:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=user_message,
                repo_overview=repo_overview
            )
        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 7. update_conversation_history
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 3.0956250000000005

```python
    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

```

### 8. select_optimal_context
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.98375

```python
    def select_optimal_context(self, task_description: str, task_type: TaskType = TaskType.GENERAL_ANALYSIS,
                             focus_entities: Optional[List[str]] = None) -> ContextBundle:
        """
        Select the most relevant code context for a given task.

        Args:
            task_description: Natural language description of the task
            task_type: Type of development task (affects selection strategy)
            focus_entities: Optional list of specific entities to focus on

        Returns:
            ContextBundle containing the selected entities and metadata
        """
        print(f"🎯 Selecting optimal context for: {task_description}")
        print(f"   Task type: {task_type.value}")
        print(f"   Token budget: {self.max_tokens}")

        # Step 1: Score all entities for relevance
        scored_entities = self._score_entities_for_task(task_description, task_type, focus_entities)

        # Step 2: Select entities within token budget
        selected_entities = self._select_entities_within_budget(scored_entities, task_type)

        # Step 3: Enhance selection with dependency context
        enhanced_entities = self._enhance_with_dependency_context(selected_entities, task_type)

        # Step 4: Build the final context bundle
        context_bundle = self._build_context_bundle(
            task_description, task_type, enhanced_entities
        )

        print(f"✅ Context selection complete:")
        print(f"   Selected {len(context_bundle.entities)} entities")
        print(f"   Total tokens: {context_bundle.total_tokens}")
        print(f"   Critical entities: {len(context_bundle.get_critical_entities())}")

        return context_bundle

```

### 9. _extract_code_snippet
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.9789583333333334

```python
    def _extract_code_snippet(self, project_path: str, file_path: str,
                             line_num: int, context_window: int,
                             context_type: ContextType, symbol_name: str) -> Optional[CodeSnippet]:
        """
        Extract a code snippet from a file centered around a specific line.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to extract from
            line_num: The line number to center the snippet around (1-based)
            context_type: The type of context being extracted
            symbol_name: The name of the symbol being referenced

        Returns:
            A CodeSnippet object or None if extraction failed
        """
        content = self._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return None

        # Adjust line_num to 0-based for internal calculations
        line_idx = line_num - 1

        # For class definitions, extract the complete class body
        if context_type == ContextType.DEFINITION and re.search(r'^\s*class\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_class_body(lines, line_idx)
        # For function/method definitions, extract the complete function body
        elif context_type == ContextType.DEFINITION and re.search(r'^\s*def\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_function_body(lines, line_idx)
        else:
            # Use context window for other cases
            start_idx = max(0, line_idx - context_window)
            end_idx = min(len(lines) - 1, line_idx + context_window)

        # Extract the surrounding function/method name if possible
        surrounding_function = self._find_surrounding_function(lines, line_idx)

        # Extract the snippet content
        snippet_lines = lines[start_idx:end_idx + 1]
        snippet_content = '\n'.join(snippet_lines)

        return CodeSnippet(
            content=snippet_content,
            file_path=file_path,
            start_line=start_idx + 1,  # Convert back to 1-based
            end_line=end_idx + 1,      # Convert back to 1-based
            context_type=context_type,
            symbol_name=symbol_name,
            surrounding_function=surrounding_function
        )

```

### 10. _get_context_selector
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.9781250000000004

```python
    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

```

### 11. _read_file_content
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.9689583333333336

```python
    def _read_file_content(self, project_path: str, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        abs_path = os.path.join(project_path, file_path)
        return self.io.read_text(abs_path)

```

### 12. analyze_context_quality
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.9587499999999998

```python
    def analyze_context_quality(self, context_bundle: ContextBundle) -> Dict[str, Any]:
        """Analyze the quality and completeness of a context bundle."""
        entities = context_bundle.entities

        # Calculate coverage metrics
        total_entities = len(self.entity_map)
        selected_entities = len(entities)
        coverage_percentage = (selected_entities / total_entities) * 100

        # Analyze priority distribution
        priority_dist = {}
        for priority in ContextPriority:
            count = len([e for e in entities if e.priority == priority])
            priority_dist[priority.value] = count

        # Analyze criticality distribution
        criticality_dist = {}
        for criticality in ['low', 'medium', 'high']:
            count = len([e for e in entities if e.criticality == criticality])
            criticality_dist[criticality] = count

        # Calculate dependency completeness
        missing_deps = 0
        total_deps = 0
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in entities}

        for entity in entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"
            deps = self.dependency_graph.get(entity_name, set())
            total_deps += len(deps)
            missing_deps += len([d for d in deps if d not in selected_names])

        dependency_completeness = ((total_deps - missing_deps) / max(total_deps, 1)) * 100

        return {
            'coverage_percentage': coverage_percentage,
            'selected_entities': selected_entities,
            'total_entities': total_entities,
            'priority_distribution': priority_dist,
            'criticality_distribution': criticality_dist,
            'dependency_completeness': dependency_completeness,
            'token_utilization': (context_bundle.total_tokens / self.max_tokens) * 100,
            'average_relevance_score': sum(e.relevance_score for e in entities) / len(entities) if entities else 0
        }


```

### 13. select_intelligent_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.953125

```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

```

### 14. cmd_copy_context
- **File**: aider-main\aider\commands.py
- **Priority**: critical
- **Relevance Score**: 2.953125

```python
    def cmd_copy_context(self, args=None):
        """Copy the current chat context as markdown, suitable to paste into a web UI"""

        chunks = self.coder.format_chat_chunks()

        markdown = ""

        # Only include specified chunks in order
        for messages in [chunks.repo, chunks.readonly_files, chunks.chat_files]:
            for msg in messages:
                # Only include user messages
                if msg["role"] != "user":
                    continue

                content = msg["content"]

                # Handle image/multipart content
                if isinstance(content, list):
                    for part in content:
                        if part.get("type") == "text":
                            markdown += part["text"] + "\n\n"
                else:
                    markdown += content + "\n\n"

        args = args or ""
        markdown += f"""
Just tell me how to edit the files to make the changes.
Don't give me back entire files.
Just show me the edits I need to make.

{args}
"""

        try:
            pyperclip.copy(markdown)
            self.io.tool_output("Copied code context to clipboard.")
        except pyperclip.PyperclipException as e:
            self.io.tool_error(f"Failed to copy to clipboard: {str(e)}")
            self.io.tool_output(
                "You may need to install xclip or xsel on Linux, or pbcopy on macOS."
            )
        except Exception as e:
            self.io.tool_error(f"An unexpected error occurred while copying to clipboard: {str(e)}")


```

### 15. extract_dependency_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.9206250000000002

```python
    def extract_dependency_contexts(self, project_path: str, primary_file: str,
                                  context_window: int = 10) -> Dict[str, List[CodeSnippet]]:
        """
        Extract focused code snippets from all dependencies of a primary file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            context_window: Default number of lines to include above and below each reference

        Returns:
            Dictionary mapping file paths to lists of code snippets
        """
        cache_key = f"dependency_contexts:{project_path}:{primary_file}:{context_window}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # Get files that import the primary file
        importing_files = self.aider_service.get_files_that_import(project_path, primary_file)

        # Combine all related files
        related_files = list(set(imported_files + importing_files))

        # Initialize result dictionary
        result = {file_path: [] for file_path in related_files + [primary_file]}

        # Process each related file
        for file_path in related_files:
            # Get symbol references between the files
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, file_path)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in symbol_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is defined in the target file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.DEFINITION, symbol)

                        if snippet:
                            result[file_path].append(snippet)

            # Also check references from the related file to the primary file
            reverse_refs = self.aider_service.get_symbol_references_between_files(
                project_path, file_path, primary_file)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in reverse_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is used in the source file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.USAGE, symbol)

                        if snippet:
                            result[file_path].append(snippet)

        # Cache the result
        self._update_cache(cache_key, result)

        return result

```

## INSTRUCTIONS FOR LLM
You have been provided with intelligent context selection based on IR (Intermediate Representation) analysis and ICD (Intelligent Code Discovery).

### Context Quality
- This context was selected using task-specific algorithms
- Entities are prioritized by criticality and relevance
- Dependencies and risk factors have been analyzed
- Token budget has been optimized for maximum value

### Your Task
Please analyze the provided context and respond to the user's query: "Why is my context selection taking so long? I think there might be a performance issue."

Use the IR analysis data to understand:
1. **Entity Criticality**: Focus on high-criticality components
2. **Change Risk**: Consider risk factors when making recommendations
3. **Dependencies**: Understand how components interact
4. **Side Effects**: Be aware of potential impacts
5. **Error Patterns**: Identify potential issues

Provide a comprehensive, accurate response based on this intelligent context selection.
