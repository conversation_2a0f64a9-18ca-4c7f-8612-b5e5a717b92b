#!/usr/bin/env python3
"""
Debug script to check what project path the context request system is using
and why it can't find the trade_management/position_exit_manager.py file.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_context_request_paths():
    """Debug the path resolution in context request system."""
    print("🔍 Debugging Context Request Path Resolution")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
        
        # Get current working directory
        current_dir = os.getcwd()
        print(f"📁 Current working directory: {current_dir}")
        
        # Check what the context request handler thinks the project path is
        handler = ContextRequestHandler(current_dir)
        print(f"📁 Handler project path: {handler.project_path}")
        
        # Create a test symbol request
        symbol = SymbolRequest(
            type="method_definition",
            name="close_position_based_on_conditions",
            file_hint="trade_management/position_exit_manager.py"
        )
        
        print(f"\n🎯 Testing symbol request:")
        print(f"  Symbol name: {symbol.name}")
        print(f"  File hint: {symbol.file_hint}")
        
        # Test the file path construction
        expected_full_path = os.path.join(handler.project_path, symbol.file_hint)
        print(f"\n📂 Expected full path: {expected_full_path}")
        print(f"📂 File exists: {os.path.exists(expected_full_path)}")
        
        # Check if the directory exists
        expected_dir = os.path.dirname(expected_full_path)
        print(f"📂 Directory exists: {os.path.exists(expected_dir)}")
        
        # List what's actually in the project directory
        print(f"\n📋 Contents of project directory ({handler.project_path}):")
        try:
            items = os.listdir(handler.project_path)
            for item in sorted(items)[:20]:  # Show first 20 items
                item_path = os.path.join(handler.project_path, item)
                item_type = "📁" if os.path.isdir(item_path) else "📄"
                print(f"  {item_type} {item}")
            if len(items) > 20:
                print(f"  ... and {len(items) - 20} more items")
        except Exception as e:
            print(f"  ❌ Error listing directory: {e}")
        
        # Check if there's a trade_management directory anywhere
        print(f"\n🔍 Searching for 'trade_management' directory...")
        found_trade_dirs = []
        for root, dirs, files in os.walk(handler.project_path):
            if 'trade_management' in dirs:
                trade_dir = os.path.join(root, 'trade_management')
                found_trade_dirs.append(trade_dir)
                print(f"  📁 Found: {trade_dir}")
        
        if not found_trade_dirs:
            print(f"  ❌ No 'trade_management' directory found in {handler.project_path}")
        
        # Check if there are any Python files with 'position' in the name
        print(f"\n🔍 Searching for files with 'position' in the name...")
        found_position_files = []
        for root, dirs, files in os.walk(handler.project_path):
            for file in files:
                if 'position' in file.lower() and file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, handler.project_path)
                    found_position_files.append(rel_path)
                    print(f"  📄 Found: {rel_path}")
        
        if not found_position_files:
            print(f"  ❌ No files with 'position' in name found")
        
        # Test the actual _find_file_for_symbol method
        print(f"\n🧪 Testing _find_file_for_symbol method...")
        try:
            found_file = handler._find_file_for_symbol(symbol)
            print(f"  Result: {found_file}")
            if found_file:
                print(f"  ✅ File found successfully!")
            else:
                print(f"  ❌ File not found by handler")
        except Exception as e:
            print(f"  ❌ Error in _find_file_for_symbol: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging context request paths: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment_setup():
    """Check if we're in the right environment for the trading project."""
    print("\n🌍 Environment Setup Check")
    print("=" * 60)
    
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check if this looks like a trading project
    trading_indicators = [
        'trade_management',
        'services',
        'position_observer.py',
        'position_exit_manager.py',
        'trading',
        'backtest',
        'dashboard'
    ]
    
    found_indicators = []
    for root, dirs, files in os.walk(current_dir):
        for indicator in trading_indicators:
            if indicator in dirs or indicator in files:
                found_indicators.append(indicator)
                rel_path = os.path.relpath(root, current_dir)
                print(f"  📍 Found '{indicator}' in {rel_path}")
    
    if found_indicators:
        print(f"✅ Found {len(found_indicators)} trading project indicators")
    else:
        print(f"❌ No trading project indicators found")
        print(f"💡 This suggests we're in the wrong directory!")
        print(f"💡 The context request system needs to run in your trading project directory")
    
    return len(found_indicators) > 0

def main():
    """Run all debugging checks."""
    print("🚀 Context Request Path Debugging")
    print("=" * 80)
    
    # Check environment
    env_ok = check_environment_setup()
    
    # Debug paths
    path_debug_ok = debug_context_request_paths()
    
    print("\n" + "=" * 80)
    print("🎯 DIAGNOSIS:")
    
    if not env_ok:
        print("❌ WRONG DIRECTORY: You're running in the aider development directory")
        print("💡 SOLUTION: Navigate to your trading project directory before running aider")
        print("💡 The directory should contain 'trade_management' and 'services' folders")
    elif not path_debug_ok:
        print("❌ PATH RESOLUTION ISSUE: Context request system has path problems")
        print("💡 SOLUTION: Check the context request handler initialization")
    else:
        print("✅ Environment and paths look correct")
        print("💡 The issue might be elsewhere in the extraction process")
    
    return env_ok and path_debug_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
