---
parent: Screen recordings
nav_order: 1
layout: minimal
highlight_image: /assets/recordings.jpg
description: See how a new command-line option is added to automatically accept edits proposed by the architect model, with implementation. Aider also updates the project's HISTORY file.
---

# Add --auto-accept-architect feature

<script>
const recording_id = "auto-accept-architect";
const recording_url = "https://gist.githubusercontent.com/paul-gauthier/e7383fbc29c9bb343ee6fb7ee5d77e15/raw/c2194334085304bb1c6bb80814d791704d9719b6/707774.cast";
</script>

{% include recording.md %}

## Commentary

- 0:01 We're going to add a new feature to automatically accept edits proposed by the architect model.
- 0:11 First, let's add the new switch.
- 0:40 <PERSON><PERSON> figured out that it should be passed to the Coder class.
- 0:48 Now we need to implement the functionality.
- 1:00 Let's do some manual testing.
- 1:28 That worked. Let's make sure we can turn it off too.
- 1:42 That worked too. Let's have aider update the HISTORY file to document the new feature.
- 2:00 Let's quickly tidy up the changes to HISTORY.
- 2:05 All done!



