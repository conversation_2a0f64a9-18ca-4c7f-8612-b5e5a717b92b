#!/usr/bin/env python

import os
import sys
import time
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""
    
    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []
    
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")
    
    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")
    
    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


def create_test_function():
    """Create a test function file for extraction testing."""
    test_file_path = "test_function.py"
    
    test_function_content = """
def calculate_position_quantity(
        amount_to_risk: float,
        entry_price: float,
        stop_loss: float,
        contract_size: float,
        max_allowed: float,
        symbol: str
    ) -> float:
    \"\"\"
    Calculate the position quantity based on risk parameters.
    
    Args:
        amount_to_risk: Amount of money to risk
        entry_price: Entry price of the position
        stop_loss: Stop loss price
        contract_size: Contract size
        max_allowed: Maximum allowed position size
        symbol: Trading symbol
        
    Returns:
        Position quantity
    \"\"\"
    # Calculate the risk per pip
    price_difference = abs(entry_price - stop_loss)
    if price_difference == 0:
        return 0
        
    # Calculate the position size based on the risk
    position_size = amount_to_risk / price_difference
    
    # Adjust for contract size
    if contract_size > 0:
        position_size = position_size / contract_size
        
    # Ensure we don't exceed the maximum allowed position size
    position_size = min(position_size, max_allowed)
    
    # Round to 2 decimal places
    position_size = round(position_size, 2)
    
    return position_size
"""
    
    with open(test_file_path, "w") as f:
        f.write(test_function_content)
        
    return test_file_path


def main():
    print("\n=== Testing Complete Function Extraction ===")
    
    # Create a test function file
    test_file_path = create_test_function()
    print(f"Created test function file: {test_file_path}")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path)
    
    # Create a sample context request for the test function
    context_request = ContextRequest(
        original_user_query_context="User is asking about the calculate_position_quantity function",
        symbols_of_interest=[
            SymbolRequest(
                type="function_definition",
                name="calculate_position_quantity",
                file_hint="test_function.py"
            )
        ],
        reason_for_request="To analyze the implementation of the calculate_position_quantity function"
    )
    
    # Process the context request
    print("\nProcessing context request...")
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the calculate_position_quantity function work?",
        repo_overview=""
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt Content ===")
    print(augmented_prompt)
    
    # Extract the code context from the augmented prompt
    code_context_start = augmented_prompt.find("### REQUESTED SYMBOL DEFINITIONS")
    code_context_end = augmented_prompt.find("INSTRUCTIONS FOR THIS TURN")
    
    if code_context_start != -1 and code_context_end != -1:
        code_context = augmented_prompt[code_context_start:code_context_end].strip()
        print("\n=== Code Context ===")
        print(code_context)
        
        # Check if the calculate_position_quantity function is in the code context
        if "calculate_position_quantity" in code_context:
            print("\n✅ calculate_position_quantity function found in code context")
            
            # Check if the function implementation is complete
            if "return position_size" in code_context:
                print("✅ Complete function implementation found")
            else:
                print("❌ Function implementation is incomplete")
        else:
            print("\n❌ calculate_position_quantity function not found in code context")
    else:
        print("\n❌ No code context found in the augmented prompt")
    
    # Clean up
    os.remove(test_file_path)
    print(f"Removed test function file: {test_file_path}")
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
