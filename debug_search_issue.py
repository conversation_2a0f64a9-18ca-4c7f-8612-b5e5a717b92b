#!/usr/bin/env python3
"""
Debug the search issue - why exact function names aren't being found.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_search_issue():
    """Debug why exact function names aren't being found in the repository map."""
    print("🔍 Debugging Search Issue")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )
        
        print("✅ Handler created successfully")
        
        # Test 1: Check what repository files are available
        print("\n🧪 Test 1: Repository Files")
        print("-" * 60)
        
        all_files = handler._get_repository_files()
        print(f"Total repository files: {len(all_files)}")
        
        # Look for files that might contain the function
        target_function = "close_position_based_on_conditions"
        potential_files = []
        
        for file_path in all_files:
            if any(keyword in file_path.lower() for keyword in ['position', 'close', 'trading', 'executor']):
                potential_files.append(file_path)
        
        print(f"Files with position/close/trading keywords: {len(potential_files)}")
        for file_path in potential_files[:10]:  # Show first 10
            print(f"   - {file_path}")
        
        # Test 2: Check what tags are available
        print("\n🧪 Test 2: Repository Tags")
        print("-" * 60)
        
        ranked_tags = handler._get_ranked_tags(all_files)
        print(f"Total repository tags: {len(ranked_tags)}")
        
        # Look for function tags
        function_tags = []
        for tag in ranked_tags:
            if hasattr(tag, 'kind') and tag.kind in ['function', 'method']:
                function_tags.append(tag)
        
        print(f"Total function/method tags: {len(function_tags)}")
        
        # Look for the specific function
        target_found = False
        similar_functions = []
        
        for tag in function_tags:
            if hasattr(tag, 'name'):
                if target_function.lower() in tag.name.lower():
                    print(f"   🎯 FOUND SIMILAR: {tag.name} in {tag.rel_fname}")
                    similar_functions.append(tag)
                    if tag.name.lower() == target_function.lower():
                        target_found = True
                        print(f"   ✅ EXACT MATCH: {tag.name} in {tag.rel_fname}")
        
        if not target_found:
            print(f"   ❌ Function '{target_function}' NOT FOUND in repository tags")
        
        # Show some example function names
        print(f"\nExample function names (first 20):")
        for i, tag in enumerate(function_tags[:20]):
            if hasattr(tag, 'name'):
                print(f"   {i+1}. {tag.name} ({tag.rel_fname})")
        
        # Test 3: Test the search methods directly
        print("\n🧪 Test 3: Direct Search Method Testing")
        print("-" * 60)
        
        keywords = [target_function, "position", "close", "conditions"]
        
        # Test function name search
        print(f"Testing function name search with keywords: {keywords}")
        function_matches = handler._search_function_names(keywords, ranked_tags)
        print(f"Function matches found: {len(function_matches)}")
        
        for match in function_matches[:5]:
            print(f"   - {match['file']} (score: {match['relevance_score']:.1f}) - {match.get('symbol', 'N/A')}")
        
        # Test filename search
        print(f"\nTesting filename search with keywords: {keywords}")
        filename_matches = handler._search_filenames(keywords, all_files)
        print(f"Filename matches found: {len(filename_matches)}")
        
        for match in filename_matches[:5]:
            print(f"   - {match['file']} (score: {match['relevance_score']:.1f})")
        
        # Test content search
        print(f"\nTesting content search with keywords: {keywords}")
        content_matches = handler._search_content(keywords, ranked_tags)
        print(f"Content matches found: {len(content_matches)}")
        
        for match in content_matches[:5]:
            print(f"   - {match['file']} (score: {match['relevance_score']:.1f})")
        
        # Test 4: Check if the function exists in any actual files
        print("\n🧪 Test 4: Manual File Search")
        print("-" * 60)
        
        import os
        import glob
        
        # Search for the function in Python files
        python_files = glob.glob("aider-main/**/*.py", recursive=True)
        print(f"Searching {len(python_files)} Python files for '{target_function}'")
        
        files_with_function = []
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if target_function in content:
                        files_with_function.append(file_path)
                        print(f"   ✅ FOUND in: {file_path}")
                        
                        # Show the context
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if target_function in line:
                                print(f"      Line {i+1}: {line.strip()}")
                                
            except Exception as e:
                continue
        
        if not files_with_function:
            print(f"   ❌ Function '{target_function}' NOT FOUND in any Python files")
            print(f"   This explains why the search didn't find it!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in search debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_with_existing_function():
    """Test search with a function that definitely exists."""
    print("\n🔍 Testing Search with Existing Function")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )
        
        # Test with a function that should exist
        test_functions = [
            "get_repo_map",
            "handle_map_request", 
            "process_context_request",
            "__init__",
            "main"
        ]
        
        ranked_tags = handler._get_ranked_tags(handler._get_repository_files())
        
        for test_func in test_functions:
            print(f"\n🧪 Testing search for: '{test_func}'")
            
            # Search for this function
            function_matches = handler._search_function_names([test_func], ranked_tags)
            print(f"   Function matches: {len(function_matches)}")
            
            for match in function_matches[:3]:
                print(f"     - {match['file']} (score: {match['relevance_score']:.1f}) - {match.get('symbol', 'N/A')}")
            
            if function_matches:
                print(f"   ✅ Search working for '{test_func}'")
            else:
                print(f"   ❌ Search failed for '{test_func}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing existing functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run search debugging tests."""
    print("🚀 Search Issue Debugging")
    print("=" * 100)
    
    tests = [
        ("Search Issue Debug", debug_search_issue),
        ("Existing Function Test", test_search_with_existing_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 SEARCH DEBUGGING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
