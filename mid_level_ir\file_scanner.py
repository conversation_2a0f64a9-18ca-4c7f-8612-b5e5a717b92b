"""
File Scanner - Discovers and parses Python files into ASTs.

This module handles the initial phase of the pipeline: finding Python files,
reading their content, and parsing them into Abstract Syntax Trees (ASTs)
for further analysis.
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

from .ir_context import IRContext, ModuleInfo


class FileScanner:
    """
    Scans project directories for Python files and parses them into ASTs.
    
    This is the first stage of the analysis pipeline, responsible for:
    - Discovering Python files in the project
    - Reading file contents
    - Parsing files into ASTs
    - Creating initial ModuleInfo objects
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the file scanner with configuration.
        
        Args:
            config: Configuration dictionary with scanning options
        """
        self.config = config
        
        # Default exclusion patterns
        self.exclude_dirs = set(config.get('exclude_dirs', [
            '__pycache__', '.git', '.pytest_cache', 'node_modules',
            '.venv', 'venv', '.env', 'env', 'build', 'dist',
            '.tox', '.coverage', 'htmlcov'
        ]))
        
        self.exclude_files = set(config.get('exclude_files', [
            'setup.py', 'conftest.py'
        ]))
        
        self.include_patterns = config.get('include_patterns', ['*.py'])
        self.max_file_size = config.get('max_file_size_mb', 10) * 1024 * 1024  # Convert to bytes
        self.verbose = config.get('verbose', False)
    
    def scan(self, context: IRContext) -> IRContext:
        """
        Scan the project directory and populate the context with parsed modules.
        
        Args:
            context: The IR context to populate
            
        Returns:
            Updated context with discovered and parsed modules
        """
        if self.verbose:
            print(f"   Scanning directory: {context.project_path}")
        
        # Discover Python files
        python_files = self._discover_python_files(context.project_path)
        context.all_files = python_files
        
        if self.verbose:
            print(f"   Found {len(python_files)} Python files")
        
        # Parse each file and create ModuleInfo
        for i, file_path in enumerate(python_files, 1):
            try:
                if self.verbose and i % 10 == 0:
                    print(f"   Progress: {i}/{len(python_files)} files ({i/len(python_files)*100:.1f}%)")
                
                module_info = self._parse_file(file_path, context.project_path)
                if module_info:
                    context.add_module(module_info)
                    context.processed_files.add(file_path)
                    
            except Exception as e:
                if self.verbose:
                    print(f"   Warning: Failed to parse {file_path}: {e}")
                continue
        
        if self.verbose:
            print(f"   Successfully parsed {len(context.modules)} modules")
        
        return context
    
    def _discover_python_files(self, project_path: Path) -> List[Path]:
        """
        Discover all Python files in the project directory.
        
        Args:
            project_path: Root directory to scan
            
        Returns:
            List of Python file paths
        """
        python_files = []
        
        for root, dirs, files in os.walk(project_path):
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            root_path = Path(root)
            
            for file in files:
                if file.endswith('.py') and file not in self.exclude_files:
                    file_path = root_path / file
                    
                    # Check file size
                    try:
                        if file_path.stat().st_size > self.max_file_size:
                            if self.verbose:
                                print(f"   Skipping large file: {file_path}")
                            continue
                    except OSError:
                        continue
                    
                    python_files.append(file_path)
        
        return sorted(python_files)
    
    def _parse_file(self, file_path: Path, project_path: Path) -> Optional[ModuleInfo]:
        """
        Parse a single Python file into a ModuleInfo object.
        
        Args:
            file_path: Path to the Python file
            project_path: Root project path for relative path calculation
            
        Returns:
            ModuleInfo object or None if parsing failed
        """
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                source_code = f.read()
            
            # Parse into AST
            try:
                ast_tree = ast.parse(source_code, filename=str(file_path))
            except SyntaxError as e:
                if self.verbose:
                    print(f"   Syntax error in {file_path}: {e}")
                return None
            
            # Calculate relative path and module name
            rel_path = file_path.relative_to(project_path)
            module_name = self._get_module_name(rel_path)
            
            # Count lines of code (excluding comments and blank lines)
            loc = self._count_lines_of_code(source_code)
            
            # Create ModuleInfo
            module_info = ModuleInfo(
                name=module_name,
                file=str(rel_path),
                loc=loc,
                ast_tree=ast_tree,
                source_code=source_code
            )
            
            return module_info
            
        except Exception as e:
            if self.verbose:
                print(f"   Error parsing {file_path}: {e}")
            return None
    
    def _get_module_name(self, rel_path: Path) -> str:
        """
        Convert a file path to a module name.
        
        Args:
            rel_path: Relative path from project root
            
        Returns:
            Module name string
        """
        # Remove .py extension and convert path separators to dots
        module_name = str(rel_path.with_suffix(''))
        module_name = module_name.replace('/', '.').replace('\\', '.')
        
        # Remove __init__ from the end
        if module_name.endswith('.__init__'):
            module_name = module_name[:-9]
        
        # Use the last component as the module name for simplicity
        # This matches the existing behavior
        return module_name.split('.')[-1] if '.' in module_name else module_name
    
    def _count_lines_of_code(self, source_code: str) -> int:
        """
        Count lines of code excluding comments and blank lines.
        
        Args:
            source_code: The source code to analyze
            
        Returns:
            Number of lines of code
        """
        lines = source_code.split('\n')
        loc = 0
        
        for line in lines:
            stripped = line.strip()
            # Skip empty lines and comments
            if stripped and not stripped.startswith('#'):
                loc += 1
        
        return loc
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        Get basic information about a file without full parsing.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            return {
                'path': str(file_path),
                'size_bytes': stat.st_size,
                'modified_time': stat.st_mtime,
                'is_readable': os.access(file_path, os.R_OK)
            }
        except OSError:
            return {
                'path': str(file_path),
                'error': 'Cannot access file'
            }
