"""
IR Builder - Assembles the final JSON output from analyzed context.

This module takes the fully analyzed IRContext and builds the final
JSON structure that matches the Mid-Level IR specification.
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Any

from .ir_context import IRContext, ModuleInfo, EntityInfo, ParameterInfo, ReturnInfo


class IRBuilder:
    """
    Builds the final JSON IR structure from analyzed context.
    
    This is the final stage of the pipeline that assembles all analyzed
    information into the standardized Mid-Level IR JSON format.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the IR builder with configuration.
        
        Args:
            config: Configuration dictionary for output options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.include_metadata = config.get('include_metadata', True)
        self.include_ast_info = config.get('include_ast_info', False)
        self.pretty_print = config.get('pretty_print', True)
    
    def build(self, context: IRContext) -> Dict[str, Any]:
        """
        Build the final IR JSON structure from the context.
        
        Args:
            context: Fully analyzed IR context
            
        Returns:
            Dictionary containing the complete IR structure
        """
        if self.verbose:
            print(f"   Building IR for {len(context.modules)} modules")
        
        # Build modules list
        modules = []
        for module_info in context.modules.values():
            module_dict = self._build_module(module_info)
            modules.append(module_dict)
        
        # Sort modules by name for consistent output
        modules.sort(key=lambda m: m['name'])
        
        # Build metadata
        metadata = self._build_metadata(context)
        
        # Assemble final structure
        ir_data = {
            "modules": modules,
            "metadata": metadata
        }
        
        if self.verbose:
            print(f"   Built IR with {len(modules)} modules")
        
        return ir_data
    
    def _build_module(self, module_info: ModuleInfo) -> Dict[str, Any]:
        """
        Build the JSON representation of a module.
        
        Args:
            module_info: ModuleInfo object to convert
            
        Returns:
            Dictionary representing the module
        """
        # Build dependencies list
        dependencies = []
        for dep in module_info.dependencies:
            dep_dict = {
                "module": dep.module,
                "strength": dep.strength
            }
            # Add additional dependency info if available
            if dep.import_type != "import":
                dep_dict["import_type"] = dep.import_type
            if dep.alias:
                dep_dict["alias"] = dep.alias
            if dep.imported_names:
                dep_dict["imported_names"] = dep.imported_names
            
            dependencies.append(dep_dict)
        
        # Build entities list
        entities = []
        for entity in module_info.entities:
            entity_dict = self._build_entity(entity)
            entities.append(entity_dict)
        
        # Build module dictionary
        module_dict = {
            "name": module_info.name,
            "file": module_info.file,
            "loc": module_info.loc,
            "dependencies": dependencies,
            "entities": entities
        }
        
        # Add metadata if available and configured
        if self.include_metadata and module_info.metadata:
            module_dict["metadata"] = self._build_module_metadata(module_info.metadata)
        
        return module_dict
    
    def _build_entity(self, entity: EntityInfo) -> Dict[str, Any]:
        """
        Build the JSON representation of an entity.
        
        Args:
            entity: EntityInfo object to convert
            
        Returns:
            Dictionary representing the entity
        """
        # Build parameters
        if entity.params:
            # Enhanced parameter format with type information
            params = []
            for param in entity.params:
                if isinstance(param, ParameterInfo):
                    param_dict = {"name": param.name}
                    if param.type_hint:
                        param_dict["type"] = param.type_hint
                    if param.default_value:
                        param_dict["default"] = param.default_value
                    if param.is_optional:
                        param_dict["optional"] = True
                    params.append(param_dict)
                else:
                    # Fallback for simple string parameters
                    params.append({"name": str(param)})
        else:
            params = []
        
        # Build returns
        returns = None
        if entity.returns:
            if isinstance(entity.returns, ReturnInfo):
                returns = {"type": entity.returns.type_hint or "None"}
                if entity.returns.description:
                    returns["description"] = entity.returns.description
            else:
                # Fallback for simple string returns
                returns = {"type": str(entity.returns)}
        else:
            returns = {"type": "None"}
        
        # Build entity dictionary
        entity_dict = {
            "type": entity.type,
            "name": entity.name,
            "doc": entity.doc or f"{entity.type.title()} {entity.name}",
            "params": params,
            "returns": returns,
            "calls": entity.calls,
            "used_by": entity.used_by,
            "side_effects": entity.side_effects or ["none"],
            "errors": entity.errors or ["RuntimeError"],
            "criticality": entity.criticality,
            "change_risk": entity.change_risk,

            # Inheritance and OOP data
            "class_name": entity.class_name,
            "inherits_from": entity.inherits_from,
            "method_overrides": entity.method_overrides,
            "calls_super": entity.calls_super,
            "overridden_by": entity.overridden_by
        }
        
        # Add optional metadata
        if entity.decorators:
            entity_dict["decorators"] = entity.decorators
        
        if self.include_ast_info:
            if entity.line_start:
                entity_dict["line_start"] = entity.line_start
            if entity.line_end:
                entity_dict["line_end"] = entity.line_end
            if entity.complexity:
                entity_dict["complexity"] = entity.complexity
        
        return entity_dict
    
    def _build_module_metadata(self, metadata) -> Dict[str, Any]:
        """
        Build the JSON representation of module metadata.
        
        Args:
            metadata: ModuleMetadata object
            
        Returns:
            Dictionary representing the metadata
        """
        metadata_dict = {
            "doc_coverage": metadata.doc_coverage,
            "comment_density": metadata.comment_density,
            "avg_complexity": metadata.avg_complexity,
            "function_count": metadata.function_count,
            "class_count": metadata.class_count,
            "variable_count": metadata.variable_count
        }
        
        if metadata.most_critical_entity:
            metadata_dict["most_critical_entity"] = metadata.most_critical_entity
        
        if metadata.test_coverage is not None:
            metadata_dict["test_coverage"] = metadata.test_coverage
        
        return metadata_dict
    
    def _build_metadata(self, context: IRContext) -> Dict[str, Any]:
        """
        Build the global metadata for the IR.
        
        Args:
            context: IR context with analysis results
            
        Returns:
            Dictionary containing global metadata
        """
        stats = context.get_statistics()
        
        metadata = {
            "generated_at": time.time(),
            "project_path": str(context.project_path),
            "project_name": context.project_name,
            "generator_version": "2.0.0",
            "total_modules": stats["total_modules"],
            "total_entities": stats["total_entities"],
            "total_functions": stats["total_functions"],
            "total_classes": stats["total_classes"],
            "total_loc": stats["total_loc"],
            "total_dependencies": stats["total_dependencies"]
        }
        
        # Add analysis metadata if available
        if context.analysis_metadata:
            metadata.update(context.analysis_metadata)
        
        return metadata
    
    def save_to_file(self, ir_data: Dict[str, Any], output_path: str) -> None:
        """
        Save the IR data to a JSON file.
        
        Args:
            ir_data: The IR data to save
            output_path: Path to the output file
        """
        output_path = Path(output_path)
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            if self.pretty_print:
                json.dump(ir_data, f, indent=2, ensure_ascii=False)
            else:
                json.dump(ir_data, f, ensure_ascii=False)
        
        # Calculate file size
        file_size = output_path.stat().st_size / (1024 * 1024)  # MB
        
        if self.verbose:
            print(f"   Saved IR to {output_path} ({file_size:.2f} MB)")
    
    def validate_ir(self, ir_data: Dict[str, Any]) -> List[str]:
        """
        Validate the generated IR structure.
        
        Args:
            ir_data: The IR data to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required top-level fields
        if "modules" not in ir_data:
            errors.append("Missing 'modules' field")
        if "metadata" not in ir_data:
            errors.append("Missing 'metadata' field")
        
        # Validate modules
        if "modules" in ir_data:
            for i, module in enumerate(ir_data["modules"]):
                module_errors = self._validate_module(module, i)
                errors.extend(module_errors)
        
        return errors
    
    def _validate_module(self, module: Dict[str, Any], index: int) -> List[str]:
        """Validate a single module structure."""
        errors = []
        prefix = f"Module {index}"
        
        required_fields = ["name", "file", "loc", "dependencies", "entities"]
        for field in required_fields:
            if field not in module:
                errors.append(f"{prefix}: Missing '{field}' field")
        
        # Validate entities
        if "entities" in module:
            for j, entity in enumerate(module["entities"]):
                entity_errors = self._validate_entity(entity, index, j)
                errors.extend(entity_errors)
        
        return errors
    
    def _validate_entity(self, entity: Dict[str, Any], module_index: int, entity_index: int) -> List[str]:
        """Validate a single entity structure."""
        errors = []
        prefix = f"Module {module_index}, Entity {entity_index}"
        
        required_fields = ["type", "name", "doc", "params", "returns", "calls", "used_by", 
                          "side_effects", "errors", "criticality", "change_risk"]
        for field in required_fields:
            if field not in entity:
                errors.append(f"{prefix}: Missing '{field}' field")
        
        return errors
