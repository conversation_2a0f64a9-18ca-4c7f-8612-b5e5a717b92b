#!/usr/bin/env python3
"""
Test script for the Intelligent Code Discovery feature.

This script validates that the new find_relevant_code_for_feature() method
works correctly and produces the expected user-friendly output.
"""

import os
import sys
import json
from typing import Dict, Any

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

from aider_integration_service import AiderIntegrationService


def test_intelligent_code_discovery():
    """Test the main Intelligent Code Discovery feature."""
    print("🔍 Testing Intelligent Code Discovery Feature")
    print("=" * 60)
    
    # Initialize the service
    service = AiderIntegrationService()
    
    # Test with the current project
    project_path = "."
    
    # Test case 1: User authentication feature
    print("\n📋 Test Case 1: User Authentication Feature")
    print("-" * 40)
    
    try:
        results = service.find_relevant_code_for_feature(
            project_path=project_path,
            feature_description="Add user authentication with JWT tokens and session management",
            focus_areas=["user", "auth", "security", "token", "session"]
        )
        
        if 'error' in results:
            print(f"❌ Error: {results['error']}")
            return False
        
        # Display results in user-friendly format
        print_discovery_results(results)
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    
    # Test case 2: File processing feature
    print("\n📋 Test Case 2: File Processing Feature")
    print("-" * 40)
    
    try:
        results = service.find_relevant_code_for_feature(
            project_path=project_path,
            feature_description="Add file upload and processing with validation",
            focus_areas=["file", "upload", "process", "validate"]
        )
        
        if 'error' in results:
            print(f"❌ Error: {results['error']}")
            return False
        
        # Display condensed results
        print_condensed_results(results)
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    
    print("\n✅ Intelligent Code Discovery tests completed successfully!")
    return True


def print_discovery_results(results: Dict[str, Any]):
    """Print the discovery results in a user-friendly format."""
    
    # Analysis Summary
    summary = results.get('analysis_summary', {})
    print(f"\n📊 Analysis Summary:")
    print(f"   Total entities analyzed: {summary.get('total_entities_analyzed', 0)}")
    print(f"   Entities selected: {summary.get('entities_selected', 0)}")
    print(f"   Selection confidence: {summary.get('selection_confidence', 0.0):.2f}")
    print(f"   Task: {summary.get('task_description', 'N/A')}")
    
    # Critical Entities
    critical = results.get('critical_entities', [])
    print(f"\n🔴 Critical Entities ({len(critical)}):")
    for i, entity in enumerate(critical[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      File: {entity['file_path']}")
        print(f"      Risk: {entity['risk_explanation']}")
        print(f"      Used by: {entity['used_by_count']} components")
    
    # Safe Entities
    safe = results.get('safe_entities', [])
    print(f"\n🟢 Safe Integration Points ({len(safe)}):")
    for i, entity in enumerate(safe[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      File: {entity['file_path']}")
        print(f"      Why safe: {entity['why_safe']}")
        print(f"      Suggestion: {entity['integration_suggestion']}")
    
    # Related Entities
    related = results.get('related_entities', [])
    print(f"\n🟡 Related Entities ({len(related)}):")
    for i, entity in enumerate(related[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      Relationship: {entity['relationship_type']}")
        print(f"      Relevance: {entity['relevance_score']:.2f}")
    
    # Implementation Guidance
    guidance = results.get('implementation_guidance', '')
    print(f"\n📋 Implementation Guidance:")
    for line in guidance.split('\n'):
        if line.strip():
            print(f"   {line.strip()}")
    
    # Recommendations
    recommendations = results.get('recommendations', [])
    print(f"\n💡 Recommendations:")
    for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
        print(f"   {i}. {rec}")


def print_condensed_results(results: Dict[str, Any]):
    """Print condensed results for quick validation."""
    summary = results.get('analysis_summary', {})
    critical = results.get('critical_entities', [])
    safe = results.get('safe_entities', [])
    related = results.get('related_entities', [])
    
    print(f"📊 Quick Summary:")
    print(f"   Entities: {summary.get('entities_selected', 0)} selected")
    print(f"   Critical: {len(critical)}, Safe: {len(safe)}, Related: {len(related)}")
    print(f"   Confidence: {summary.get('selection_confidence', 0.0):.2f}")
    
    if critical:
        print(f"   Top critical: {critical[0]['entity_name']}")
    if safe:
        print(f"   Top safe: {safe[0]['entity_name']}")


def test_method_availability():
    """Test that all required methods are available."""
    print("\n🔧 Testing Method Availability")
    print("-" * 30)
    
    service = AiderIntegrationService()
    
    # Check main methods
    methods_to_check = [
        'find_relevant_code_for_feature',
        'get_intelligent_context',
        'select_intelligent_context',
        'analyze_with_multi_turn_reasoning'
    ]
    
    for method_name in methods_to_check:
        if hasattr(service, method_name):
            print(f"   ✅ {method_name}")
        else:
            print(f"   ❌ {method_name} - MISSING")
            return False
    
    return True


if __name__ == "__main__":
    print("🚀 Starting Intelligent Code Discovery Tests")
    print("=" * 60)
    
    # Test method availability first
    if not test_method_availability():
        print("\n❌ Method availability test failed!")
        sys.exit(1)
    
    # Test the main functionality
    if test_intelligent_code_discovery():
        print("\n🎉 All tests passed! Intelligent Code Discovery is working correctly.")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
