#!/usr/bin/env python3
"""
Comprehensive test to ensure the simplified system works after removing all specialized prompt files and coder classes.
"""

import os
import sys
import subprocess
import tempfile

def test_import_system():
    """Test that the simplified import system works."""
    print("🧪 Testing Import System")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        # Test basic imports
        print("Testing basic imports...")
        from aider.coders import Coder
        print("✅ Successfully imported Coder")

        from aider.coders.base_prompts import CoderPrompts
        print("✅ Successfully imported CoderPrompts")

        from aider.coders.base_coder import Coder as BaseCoder
        print("✅ Successfully imported BaseCoder")

        # Test that removed classes are no longer available
        removed_classes = [
            'AskCoder', 'ContextCoder', 'ArchitectCoder', 'EditBlockCoder',
            'PatchCoder', 'UdiffCoder', 'WholeFileCoder', 'HelpCoder'
        ]

        for class_name in removed_classes:
            try:
                exec(f"from aider.coders import {class_name}")
                print(f"❌ ERROR: {class_name} should have been removed but is still importable!")
                return False
            except ImportError:
                print(f"✅ Confirmed: {class_name} properly removed")

        # Test that Coder and BaseCoder are the same
        if Coder is BaseCoder:
            print("✅ Coder correctly references BaseCoder")
        else:
            print("❌ ERROR: Coder and BaseCoder are different classes")
            return False

        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coder_creation():
    """Test that we can create a Coder instance with the simplified system."""
    print("\n🧪 Testing Coder Creation")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        print("✅ All required modules imported successfully")

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Test with aider-main directory
        repo = GitRepo(io, [], "aider-main")

        print("✅ Components created successfully")

        # Create a Coder instance with the correct edit format
        coder = Coder.create(
            main_model=model,
            edit_format="informative",  # Use the only available format
            io=io,
            repo=repo,
            fnames=[],
            map_tokens=8192
        )

        print("✅ Coder instance created successfully")

        # Test that it has the correct prompts
        if hasattr(coder, 'gpt_prompts'):
            print("✅ Coder has gpt_prompts attribute")

            # Check that it's using CoderPrompts
            from aider.coders.base_prompts import CoderPrompts
            if isinstance(coder.gpt_prompts, CoderPrompts):
                print("✅ Coder is using CoderPrompts")
            else:
                print(f"❌ ERROR: Coder is using {type(coder.gpt_prompts)} instead of CoderPrompts")
                return False
        else:
            print("❌ ERROR: Coder missing gpt_prompts attribute")
            return False

        # Test that prompts contain our fixed instructions
        prompts = coder.gpt_prompts

        # Check for key instructions
        key_instructions = [
            "MAP_REQUEST",
            "CONTEXT_REQUEST",
            "REQUEST_FILE",
            "NEVER ask users to manually add files",
            "Smart Map Request System"
        ]

        prompt_content = str(prompts.repo_content_prefix) + str(prompts.file_access_reminder) + str(prompts.files_no_full_files_with_repo_map_reply)

        for instruction in key_instructions:
            if instruction in prompt_content:
                print(f"✅ Found instruction: '{instruction}'")
            else:
                print(f"❌ Missing instruction: '{instruction}'")
                return False

        # Check that old conflicting instructions are gone
        bad_instructions = [
            "ask them to add it to the chat",
            "please add that file to the chat",
            "add the relevant file(s) to the chat"
        ]

        for bad_instruction in bad_instructions:
            if bad_instruction in prompt_content:
                print(f"❌ ERROR: Found conflicting instruction: '{bad_instruction}'")
                return False
            else:
                print(f"✅ Confirmed removed: '{bad_instruction}'")

        return True

    except Exception as e:
        print(f"❌ Coder creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_aider_command_line():
    """Test that aider command line still works with simplified system."""
    print("\n🧪 Testing Aider Command Line")
    print("=" * 60)

    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")

        # Test basic help command
        cmd = [sys.executable, "-m", "aider.main", "--help"]

        print(f"Running command: {' '.join(cmd[:3])} --help")

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )

        print(f"Exit code: {result.returncode}")

        if result.returncode == 0:
            print("✅ Aider help command works")

            # Check that help output contains expected content
            if "usage:" in result.stdout.lower():
                print("✅ Help output contains usage information")
            else:
                print("❌ Help output missing usage information")
                return False

        else:
            print("❌ Aider help command failed")
            print(f"STDERR: {result.stderr}")
            return False

        # Test version command
        cmd = [sys.executable, "-m", "aider.main", "--version"]

        print(f"Running command: {' '.join(cmd[:3])} --version")

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )

        if result.returncode == 0:
            print("✅ Aider version command works")
            print(f"Version: {result.stdout.strip()}")
        else:
            print("❌ Aider version command failed")
            print(f"STDERR: {result.stderr}")
            return False

        return True

    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
        return False
    except Exception as e:
        print(f"❌ Command line test failed: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_smart_map_request_integration():
    """Test that Smart Map Request System still works."""
    print("\n🧪 Testing Smart Map Request Integration")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import SMART_MAP_REQUEST_AVAILABLE

        if SMART_MAP_REQUEST_AVAILABLE:
            print("✅ Smart Map Request System is available")

            from aider.smart_map_request_handler import SmartMapRequestHandler
            print("✅ SmartMapRequestHandler can be imported")

            return True
        else:
            print("❌ Smart Map Request System not available")
            return False

    except Exception as e:
        print(f"❌ Smart Map Request integration test failed: {e}")
        return False

def test_context_request_integration():
    """Test that Context Request System still works."""
    print("\n🧪 Testing Context Request Integration")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import CONTEXT_REQUEST_AVAILABLE

        if CONTEXT_REQUEST_AVAILABLE:
            print("✅ Context Request System is available")

            from aider.context_request import ContextRequestHandler
            print("✅ ContextRequestHandler can be imported")

            return True
        else:
            print("❌ Context Request System not available")
            return False

    except Exception as e:
        print(f"❌ Context Request integration test failed: {e}")
        return False

def main():
    """Run all tests for the simplified system."""
    print("🚀 Testing Simplified Aider System")
    print("=" * 100)

    tests = [
        ("Import System", test_import_system),
        ("Coder Creation", test_coder_creation),
        ("Command Line Interface", test_aider_command_line),
        ("Smart Map Request Integration", test_smart_map_request_integration),
        ("Context Request Integration", test_context_request_integration),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 100)
    print("📊 SIMPLIFIED SYSTEM TESTING SUMMARY")
    print("=" * 100)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 SIMPLIFIED SYSTEM WORKING PERFECTLY!")
        print("\n📋 System Status:")
        print("  ✅ All specialized prompt files removed")
        print("  ✅ All specialized coder classes removed")
        print("  ✅ Single source of truth: base_prompts.py")
        print("  ✅ No conflicting instructions")
        print("  ✅ Smart Map Request System integrated")
        print("  ✅ Context Request System integrated")
        print("  ✅ Command line interface working")
        print("\n🎯 The LLM will now:")
        print("  ✅ Use only CoderPrompts with fixed instructions")
        print("  ✅ NEVER ask users to manually add files")
        print("  ✅ Use MAP_REQUEST → CONTEXT_REQUEST workflow")
        print("  ✅ Follow anti-fabrication rules")
    else:
        print("❌ SOME TESTS FAILED!")
        print("   The simplified system may have issues")
        print("   Check the output above for details")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
