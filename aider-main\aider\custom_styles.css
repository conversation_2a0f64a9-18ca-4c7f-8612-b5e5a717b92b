/*
 * Custom styles for Aider browser interface
 * This file contains all UI customizations for the Streamlit-based browser interface
 */
/* Main application background */
.stApp, .st-emotion-cache-1ihwvbb, .stChatMessage {
    background-color: #252624 !important;
    font-family: 'Courier New', Courier, monospace !important;
}

.st-emotion-cache-hzygls {
    background-color: #252624 !important;
}
.st-emotion-cache-x1bvup, .st-ds, .st-emotion-cache-k2z1pe{
    background-color: #151514 !important;
}


/* Sidebar styling */
.css-1d391kg, .css-1lcbmhc {
    background-color: #151514 !important;
}


.stAlert {
    display: none;
}
.element-container > .stAlert {
    display: block;
}


/* Chat input styling */
.stTextInput > div > div > input {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 5px;
}

/* Button styling */
.stButton > button {
    background-color: #4c6ef5;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem 1rem;
}

/* Chat message containers */
.stChatMessage {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}

/* User messages */
.stChatMessageContent:has(div[data-testid="chatAvatarIcon-user"]) {
    background-color: #e3f2fd;
    border-radius: 8px;
}

/* Assistant messages */
.stChatMessageContent:has(div[data-testid="chatAvatarIcon-assistant"]) {
    background-color: #f1f8e9;
    border-radius: 8px;
}

/* Code blocks */
pre {
    background-color: #282a36;
    border-radius: 5px;
    padding: 1rem;
}

/* Headings */
h1, h2, h3 {
    color: #2c3e50;
}

/* Links */
a {
    color: #3949ab;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Warning messages */
.stAlert {
    background-color: rgba(255, 229, 153, 0.3);
    border-left: 4px solid #ffb74d;
}

/* Custom styling is now applied directly in the GUI code using inline styles */
