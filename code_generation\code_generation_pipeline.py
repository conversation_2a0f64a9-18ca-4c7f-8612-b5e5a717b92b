"""
Code Generation Pipeline

Main orchestrator for the Code Generation with Architectural Awareness system.
Coordinates architectural analysis, template selection, and code generation.
"""

import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from mid_level_ir.ir_context import IRContext
from .architectural_pattern_analyzer import Architectural<PERSON><PERSON>ern<PERSON><PERSON>y<PERSON>, ArchitecturalAnalysis
from .code_template_engine import CodeTemplateEngine, GenerationRequest, GeneratedCode


@dataclass
class CodeGenerationResult:
    """Complete result of code generation process."""
    generated_code: GeneratedCode
    architectural_analysis: ArchitecturalAnalysis
    generation_time: float
    recommendations: List[str]
    quality_assessment: Dict[str, Any]


class CodeGenerationPipeline:
    """
    Main pipeline for Code Generation with Architectural Awareness.
    
    This pipeline:
    1. Analyzes existing codebase architecture and patterns
    2. Selects appropriate templates based on context
    3. Generates code that follows established conventions
    4. Provides quality assessment and integration guidance
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the code generation pipeline.
        
        Args:
            config: Configuration dictionary for pipeline options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Initialize components
        self.pattern_analyzer = ArchitecturalPatternAnalyzer(
            config.get('pattern_analyzer', {})
        )
        self.template_engine = CodeTemplateEngine(
            config.get('template_engine', {})
        )
    
    def generate_code(self, ir_context: IRContext, 
                     generation_request: GenerationRequest) -> CodeGenerationResult:
        """
        Generate code with architectural awareness.
        
        Args:
            ir_context: The IR context containing codebase analysis
            generation_request: The code generation request
            
        Returns:
            Complete code generation result with metadata
        """
        start_time = time.time()
        
        if self.verbose:
            print(f"🚀 Starting Code Generation Pipeline")
            print(f"   Request: {generation_request.request_type} - {generation_request.target_name}")
            print("=" * 60)
        
        # Phase 1: Architectural Analysis
        if self.verbose:
            print("🏗️  Phase 1: Architectural Pattern Analysis")
        
        architectural_analysis = self.pattern_analyzer.analyze(ir_context)
        
        if self.verbose:
            print(f"   Detected {len(architectural_analysis.patterns)} patterns")
            print(f"   Analyzed coding style and conventions")
        
        # Phase 2: Code Generation
        if self.verbose:
            print("🔧 Phase 2: Context-Aware Code Generation")
        
        generated_code = self.template_engine.generate_code(
            generation_request, 
            architectural_analysis
        )
        
        if self.verbose:
            print(f"   Generated {len(generated_code.code.split())} lines")
            print(f"   Quality score: {generated_code.quality_score:.2f}")
        
        # Phase 3: Quality Assessment
        if self.verbose:
            print("📊 Phase 3: Quality Assessment")
        
        quality_assessment = self._assess_quality(
            generated_code, 
            architectural_analysis, 
            ir_context
        )
        
        # Phase 4: Generate Recommendations
        if self.verbose:
            print("💡 Phase 4: Integration Recommendations")
        
        recommendations = self._generate_recommendations(
            generated_code,
            architectural_analysis,
            generation_request,
            ir_context
        )
        
        generation_time = time.time() - start_time
        
        if self.verbose:
            print(f"\n✅ Code Generation Complete!")
            print(f"   Generation time: {generation_time:.2f} seconds")
            print(f"   Quality score: {quality_assessment['overall_score']:.2f}")
            print(f"   Recommendations: {len(recommendations)}")
        
        return CodeGenerationResult(
            generated_code=generated_code,
            architectural_analysis=architectural_analysis,
            generation_time=generation_time,
            recommendations=recommendations,
            quality_assessment=quality_assessment
        )
    
    def _assess_quality(self, generated_code: GeneratedCode,
                       architectural_analysis: ArchitecturalAnalysis,
                       ir_context: IRContext) -> Dict[str, Any]:
        """Assess the quality of generated code."""
        assessment = {
            'overall_score': generated_code.quality_score,
            'pattern_compliance': {},
            'style_compliance': 0.0,
            'integration_readiness': 0.0,
            'maintainability': 0.0
        }
        
        # Pattern compliance assessment
        for pattern in architectural_analysis.patterns:
            if pattern.name in generated_code.pattern_compliance:
                assessment['pattern_compliance'][pattern.name] = \
                    generated_code.pattern_compliance[pattern.name]
        
        # Style compliance assessment
        style_score = self._assess_style_compliance(
            generated_code.code, 
            architectural_analysis.coding_style
        )
        assessment['style_compliance'] = style_score
        
        # Integration readiness assessment
        integration_score = self._assess_integration_readiness(
            generated_code, 
            ir_context
        )
        assessment['integration_readiness'] = integration_score
        
        # Maintainability assessment
        maintainability_score = self._assess_maintainability(generated_code.code)
        assessment['maintainability'] = maintainability_score
        
        # Update overall score
        assessment['overall_score'] = (
            generated_code.quality_score * 0.4 +
            style_score * 0.2 +
            integration_score * 0.2 +
            maintainability_score * 0.2
        )
        
        return assessment
    
    def _assess_style_compliance(self, code: str, coding_style) -> float:
        """Assess how well the code follows the detected coding style."""
        score = 0.8  # Base score
        
        # Check naming conventions
        if 'class ' in code:
            # Check if class names follow convention
            import re
            class_names = re.findall(r'class (\w+)', code)
            for name in class_names:
                if coding_style.naming_conventions.get('class') == 'PascalCase':
                    if name[0].isupper():
                        score += 0.05
                elif coding_style.naming_conventions.get('class') == 'snake_case':
                    if '_' in name and name.islower():
                        score += 0.05
        
        # Check indentation
        lines = code.split('\n')
        indented_lines = [line for line in lines if line.startswith(' ') or line.startswith('\t')]
        if indented_lines:
            if coding_style.indentation == '4_spaces':
                correct_indent = sum(1 for line in indented_lines if line.startswith('    '))
                score += (correct_indent / len(indented_lines)) * 0.1
        
        return min(1.0, score)
    
    def _assess_integration_readiness(self, generated_code: GeneratedCode,
                                    ir_context: IRContext) -> float:
        """Assess how ready the code is for integration."""
        score = 0.7  # Base score
        
        # Check if imports are available
        if generated_code.imports_needed:
            score += 0.1
        
        # Check if dependencies are satisfied
        if generated_code.dependencies:
            # Check if dependencies exist in the codebase
            available_modules = set(ir_context.modules.keys())
            satisfied_deps = sum(1 for dep in generated_code.dependencies 
                               if dep in available_modules)
            if generated_code.dependencies:
                score += (satisfied_deps / len(generated_code.dependencies)) * 0.2
        
        return min(1.0, score)
    
    def _assess_maintainability(self, code: str) -> float:
        """Assess the maintainability of the generated code."""
        score = 0.6  # Base score
        
        # Check for docstrings
        if '"""' in code or "'''" in code:
            score += 0.2
        
        # Check for type hints
        if '->' in code or ': ' in code:
            score += 0.1
        
        # Check code length (shorter is often more maintainable)
        lines = [line for line in code.split('\n') if line.strip()]
        if len(lines) <= 20:
            score += 0.1
        elif len(lines) <= 50:
            score += 0.05
        
        return min(1.0, score)
    
    def _generate_recommendations(self, generated_code: GeneratedCode,
                                 architectural_analysis: ArchitecturalAnalysis,
                                 generation_request: GenerationRequest,
                                 ir_context: IRContext) -> List[str]:
        """Generate recommendations for code integration and improvement."""
        recommendations = []
        
        # Add basic integration notes
        recommendations.extend(generated_code.integration_notes)
        
        # Pattern-specific recommendations
        for pattern in architectural_analysis.patterns:
            if pattern.confidence > 0.7:
                recommendations.append(
                    f"Consider implementing {pattern.name} pattern consistently "
                    f"across related components"
                )
        
        # Style recommendations
        if architectural_analysis.coding_style.type_hints_usage > 0.8:
            recommendations.append(
                "Add comprehensive type hints to match codebase standards"
            )
        
        if architectural_analysis.coding_style.docstring_style != 'basic':
            recommendations.append(
                f"Use {architectural_analysis.coding_style.docstring_style} "
                f"docstring style for consistency"
            )
        
        # Quality recommendations
        if generated_code.quality_score < 0.8:
            recommendations.append(
                "Consider refactoring to improve code quality score"
            )
        
        # Integration recommendations
        if generation_request.target_module:
            target_module = generation_request.target_module
            if target_module in ir_context.modules:
                module_info = ir_context.modules[target_module]
                recommendations.append(
                    f"Integrate with existing {target_module} module "
                    f"({len(module_info.entities)} existing entities)"
                )
        
        return recommendations
