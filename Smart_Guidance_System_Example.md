# Smart Guidance System - Complete Example

This document demonstrates how the Smart Guidance System works when the LLM tries to use CONTEXT_REQUEST or REQUEST_FILE but the system cannot find the relevant files/context.

## 🎯 **The Problem We Solved**

**Before**: When LLM requested non-existent files or symbols, it got generic error messages and had no guidance on how to proceed.

**After**: The system provides intelligent guidance that leads the LLM back to the proper MAP_REQUEST workflow.

---

## 📋 **Example 1: File Request Fails**

**LLM Request**:
```json
{REQUEST_FILE: {"path": "auth/authentication_manager.py", "reason": "need to understand authentication logic"}}
```

**System Response** *(Smart Guidance)*:
```
❌ **File Request Failed**

I could not find the requested file(s): "auth/authentication_manager.py"

🔍 **Next Steps:**
1. **Check your repository map** - Do you have a current repository overview that shows the relevant files?
2. **If you don't have a relevant repository map**, use MAP_REQUEST to explore the codebase:
   ```json
   {MAP_REQUEST: {
     "keywords": ["relevant", "technical", "terms", "from", "your", "query"],
     "type": "implementation",
     "scope": "all",
     "max_results": 8
   }}
   ```
3. **If your current map is insufficient**, try MAP_REQUEST with different/broader keywords
4. **Only after getting relevant repository context**, retry your request with correct file paths

💡 **Suggested keywords for MAP_REQUEST**: authentication, understand, logic

**Remember**: You cannot use CONTEXT_REQUEST or REQUEST_FILE effectively without first understanding the repository structure via MAP_REQUEST.
```

---

## 📋 **Example 2: Context Request Fails**

**LLM Request**:
```json
{CONTEXT_REQUEST: {
  "original_user_query_context": "authentication flow",
  "symbols_of_interest": [
    {"type": "class_definition", "name": "AuthManager", "file_hint": "auth/manager.py"},
    {"type": "method_definition", "name": "UserModel.authenticate", "file_hint": "models/user.py"}
  ],
  "reason_for_request": "need to understand authentication implementation"
}}
```

**System Response** *(Smart Guidance)*:
```
❌ **Context Request Failed**

I could not find the requested symbols/context: AuthManager, UserModel.authenticate

🔍 **Next Steps:**
1. **Check your repository map** - Do you have a current repository overview that shows the relevant files?
2. **If you don't have a relevant repository map**, use MAP_REQUEST to explore the codebase:
   ```json
   {MAP_REQUEST: {
     "keywords": ["relevant", "technical", "terms", "from", "your", "query"],
     "type": "implementation",
     "scope": "all",
     "max_results": 8
   }}
   ```
3. **If your current map is insufficient**, try MAP_REQUEST with different/broader keywords
4. **Only after getting relevant repository context**, retry your request with correct file paths

💡 **Suggested keywords for MAP_REQUEST**: authentication, implementation, understand

**Remember**: You cannot use CONTEXT_REQUEST or REQUEST_FILE effectively without first understanding the repository structure via MAP_REQUEST.
```

---

## 📋 **Example 3: Complete Workflow with Guidance**

**User Query**: "How does user authentication work in this system?"

### **❌ Wrong Approach (Old Behavior)**
```
LLM: Let me look at the authentication code directly.

{CONTEXT_REQUEST: {"symbols_of_interest": [{"type": "class_definition", "name": "AuthManager"}]}}

System: ❌ Context Request Failed - I could not find AuthManager...
[Smart Guidance Message]

LLM: I need to explore the repository structure first.
```

### **✅ Correct Approach (New Behavior)**
```
LLM: I need to explore the repository structure to understand authentication.

{MAP_REQUEST: {"keywords": ["authentication", "auth", "login", "user"], "type": "implementation", "max_results": 8}}

System: [Provides focused repository map with authentication-related files]

LLM: Perfect! I can see the authentication system has these components:
- auth/authenticator.py (main authentication logic)
- models/user.py (user model with auth methods)
- middleware/auth_middleware.py (request authentication)

Now let me get the detailed implementation:

{CONTEXT_REQUEST: {
  "symbols_of_interest": [
    {"type": "class_definition", "name": "Authenticator", "file_hint": "auth/authenticator.py"},
    {"type": "method_definition", "name": "User.check_password", "file_hint": "models/user.py"}
  ]
}}

System: [Provides detailed code context]

LLM: [Provides comprehensive answer about authentication flow]
```

---

## 🎯 **Key Benefits**

### **1. Intelligent Error Recovery**
- No more generic "file not found" errors
- Clear guidance on what to do next
- Automatic keyword extraction from failed requests

### **2. Workflow Enforcement**
- Forces LLM to follow proper MAP_REQUEST → CONTEXT_REQUEST workflow
- Prevents blind guessing of file paths
- Ensures LLM has proper repository context

### **3. Educational Guidance**
- Teaches LLM the correct workflow
- Provides specific examples of how to proceed
- Suggests relevant keywords based on the failed request

### **4. Adaptive Learning**
- System learns from failed requests
- Extracts meaningful keywords from error context
- Provides increasingly better guidance

---

## 🔧 **Technical Implementation**

### **File Request Guidance**
```python
def _generate_smart_guidance_message(self, request_type, requested_items, reason=""):
    # Generates intelligent guidance based on:
    # - Type of request that failed (file vs context)
    # - What was requested
    # - Why it was requested (reason)
    # - Extracted keywords for MAP_REQUEST suggestions
```

### **Context Request Guidance**
```python
# In process_context_requests():
if augmented_prompt and "could not find" in augmented_prompt.lower():
    # Extract requested symbols
    # Generate smart guidance message
    # Replace error with guidance
```

### **Keyword Extraction**
```python
# Extract keywords from reason for MAP_REQUEST suggestions
words = re.findall(r'\b\w+\b', reason.lower())
keywords = [word for word in words if len(word) > 2 and word not in stopwords]
```

---

## 🎉 **Result**

The Smart Guidance System ensures that:

1. **LLM never gets stuck** with unhelpful error messages
2. **Always guided back** to the proper MAP_REQUEST workflow
3. **Learns the correct approach** through intelligent feedback
4. **Gets specific suggestions** for how to proceed
5. **Builds proper repository understanding** before diving into details

This creates a **self-correcting system** where the LLM learns to follow the optimal workflow and gets better guidance when it makes mistakes! 🚀
