#!/usr/bin/env python3
"""
Test the actual fix by clearing cache and forcing fresh generation
"""

import os
import sys
import shutil
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))


def clear_cache():
    """Clear the repository cache to force fresh generation"""
    
    cache_dir = Path("aider-main/.aider.tags.cache.v4")
    if cache_dir.exists():
        print(f"🗑️  Clearing cache directory: {cache_dir}")
        shutil.rmtree(cache_dir)
        print("✅ Cache cleared")
    else:
        print("ℹ️  No cache directory found")


def test_with_fresh_cache():
    """Test the repository map generation with fresh cache"""
    
    print("🧪 Testing with fresh cache and improved algorithm...")
    
    try:
        from aider.repomap import RepoMap, find_src_files
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create model and IO
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Create repo map with optimized settings and force refresh
        repo_map = RepoMap(
            map_tokens=20202,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=True,
            refresh="always"  # Force refresh
        )
        
        print(f"📊 Token Budget: {repo_map.max_map_tokens:,} tokens")
        
        # Get all files
        all_files = find_src_files("aider-main")
        print(f"📁 Total Files: {len(all_files):,}")
        
        # Get ranked tags with fresh generation
        print("\n🔄 Getting ranked tags with fresh generation...")
        ranked_tags = repo_map.get_ranked_tags(
            chat_fnames=[],
            other_fnames=all_files,
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        print(f"📊 Total Ranked Tags: {len(ranked_tags):,}")
        
        # Now test the improved binary search
        print("\n🔪 Testing Improved Binary Search Algorithm...")
        
        max_map_tokens = repo_map.max_map_tokens
        num_tags = len(ranked_tags)
        
        # This should now use 75% instead of ~28%
        initial_coverage = 0.75
        initial_middle = min(int(num_tags * initial_coverage), num_tags)
        
        print(f"📊 Improved Initial Middle (75% of tags): {initial_middle:,}")
        print(f"📊 Percentage of tags: {(initial_middle / num_tags) * 100:.1f}%")
        
        # Test the actual tree generation with improved starting point
        tree = repo_map.to_tree(ranked_tags[:initial_middle], set())
        tokens = repo_map.token_count(tree)
        
        print(f"📊 Tokens with 75% coverage: {tokens:,}")
        print(f"📊 Within budget: {'✅ Yes' if tokens <= max_map_tokens else '❌ No'}")
        
        # Compare with old algorithm
        old_middle = min(int(max_map_tokens // 25), num_tags)
        old_tree = repo_map.to_tree(ranked_tags[:old_middle], set())
        old_tokens = repo_map.token_count(old_tree)
        
        print(f"\n📊 Comparison:")
        print(f"   Old algorithm (28%): {old_middle:,} tags, {old_tokens:,} tokens")
        print(f"   New algorithm (75%): {initial_middle:,} tags, {tokens:,} tokens")
        print(f"   Improvement: {((initial_middle - old_middle) / old_middle) * 100:.1f}% more tags")
        
        # Test the full binary search with improved algorithm
        print(f"\n🎯 Testing Full Binary Search with Improved Algorithm...")
        
        # Simulate the binary search with improved starting point
        lower_bound = 0
        upper_bound = num_tags
        middle = initial_middle
        best_tree_tokens = 0
        best_middle = 0
        
        iterations = 0
        while lower_bound <= upper_bound and iterations < 10:
            tree = repo_map.to_tree(ranked_tags[:middle], set())
            num_tokens = repo_map.token_count(tree)
            
            print(f"   Iteration {iterations + 1}: middle={middle:,}, tokens={num_tokens:,}")
            
            pct_err = abs(num_tokens - max_map_tokens) / max_map_tokens
            ok_err = 0.25  # Improved error tolerance
            
            if (num_tokens <= max_map_tokens and num_tokens > best_tree_tokens) or pct_err < ok_err:
                best_tree_tokens = num_tokens
                best_middle = middle
                
                if pct_err < ok_err:
                    break
            
            if num_tokens < max_map_tokens:
                lower_bound = middle + 1
            else:
                upper_bound = middle - 1
            
            middle = int((lower_bound + upper_bound) // 2)
            iterations += 1
        
        print(f"\n🎯 Final Results with Improved Algorithm:")
        print(f"   Best slice point: {best_middle:,} tags")
        print(f"   Best tokens used: {best_tree_tokens:,} / {max_map_tokens:,}")
        print(f"   Percentage of tags included: {(best_middle / num_tags) * 100:.1f}%")
        print(f"   Tags excluded: {num_tags - best_middle:,}")
        
        # Calculate improvement
        old_coverage = (old_middle / num_tags) * 100
        new_coverage = (best_middle / num_tags) * 100
        improvement = new_coverage - old_coverage
        
        print(f"\n📈 Coverage Improvement:")
        print(f"   Old coverage: {old_coverage:.1f}%")
        print(f"   New coverage: {new_coverage:.1f}%")
        print(f"   Improvement: +{improvement:.1f} percentage points")
        
        if new_coverage > 60:
            print("✅ SUCCESS: Achieved >60% repository coverage!")
        elif new_coverage > 50:
            print("⚠️  PARTIAL: Achieved >50% repository coverage")
        else:
            print("❌ INSUFFICIENT: Still <50% repository coverage")
        
        return {
            'old_coverage': old_coverage,
            'new_coverage': new_coverage,
            'improvement': improvement,
            'best_middle': best_middle,
            'total_tags': num_tags
        }
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main test function"""
    
    print("🚀 Testing Repository Map Slicing Fix")
    print("=" * 60)
    
    # Clear cache first
    clear_cache()
    
    # Test with fresh cache
    results = test_with_fresh_cache()
    
    print("\n" + "=" * 60)
    if results and results['new_coverage'] > results['old_coverage']:
        print("✅ FIX WORKING!")
        print(f"Repository coverage improved from {results['old_coverage']:.1f}% to {results['new_coverage']:.1f}%")
    else:
        print("❌ FIX NOT WORKING")
        print("The algorithm is still using the old approach")
    print("=" * 60)


if __name__ == "__main__":
    main()
