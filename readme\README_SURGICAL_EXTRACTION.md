# Surgical File Extraction System

## This demo showcases the core functionality of the SurgicalContextExtractor
- `python simple_demo.py`

## using the surgical_file_extractor.py file and extract the extract_symbol_content method with its context:
- `python simple_enhanced_test.py surgical_file_extractor.py extract_symbol_content`

## Overview

The Surgical File Extraction system is a precision technique for extracting complete function or method bodies from source files using line number boundaries derived from existing repository mapping data. This approach minimizes context sent to AI models while ensuring complete, compilable code extraction.

## Components

The system consists of three main components:

1. **SurgicalFileExtractor**: Extracts complete function or method bodies from source files using line number boundaries.
2. **SurgicalContextExtractor**: Extracts focused code snippets around dependency interaction points.
3. **EnhancedSurgicalExtractor**: Combines both approaches for comprehensive code extraction.

## Installation

1. Ensure you have the required dependencies:
   ```
   pip install aider-chat
   ```

2. Copy the following files to your project:
   - `surgical_file_extractor.py`
   - `enhanced_surgical_extractor.py`
   - `surgical_extraction_demo.py`
   - `enhanced_extraction_demo.py`

## Usage

### Basic Surgical File Extraction

To extract a complete function or method body:

```python
from aider_integration_service import AiderIntegrationService
from surgical_file_extractor import SurgicalFileExtractor

# Initialize the AiderIntegrationService
aider_service = AiderIntegrationService()

# Initialize the SurgicalFileExtractor
extractor = SurgicalFileExtractor(aider_service)

# Extract a symbol's content
content = extractor.extract_symbol_content("function_name", "path/to/file.py", "project_path")
print(content)
```

### Enhanced Surgical Extraction

For comprehensive extraction including dependencies:

```python
from aider_integration_service import AiderIntegrationService
from enhanced_surgical_extractor import EnhancedSurgicalExtractor

# Initialize the AiderIntegrationService
aider_service = AiderIntegrationService()

# Initialize the EnhancedSurgicalExtractor
extractor = EnhancedSurgicalExtractor(aider_service)

# Extract enhanced context
context = extractor.extract_enhanced_context("project_path", "function_name", "path/to/file.py")

# Format the context for display or sending to an AI model
formatted_context = extractor.format_enhanced_context(context)
print(formatted_context)
```

### Command Line Demo

You can also use the provided demo scripts:

```bash
# Basic surgical file extraction
python surgical_extraction_demo.py <project_path> <file_path> <symbol_name>

# Enhanced surgical extraction
python enhanced_extraction_demo.py <project_path> <file_path> <symbol_name>
```

## Key Features

### Precision Extraction

- Extracts complete function/method bodies with perfect precision
- Uses repository mapping data to determine symbol boundaries
- Ensures extracted code is complete and compilable

### Optimized Context

- Minimizes token usage by sending only necessary code sections
- Includes essential imports and dependencies
- Preserves natural code structure and boundaries

### Comprehensive Analysis

- Extracts both symbol definitions and usage contexts
- Provides information about dependencies and inheritance
- Formats output for optimal AI model understanding

## Output Format

The enhanced extraction produces output in the following format:

```
# 🎯 OPTIMIZED: Send only what's needed

### REQUESTED FILE CONTEXT (Surgical Extraction)

--- File: {filename} (Targeted extraction for {target_symbol}) ---

# File header & imports ({n} lines)
{essential_imports_only}

# Class/Module definition start ({n} lines)  
{containing_class_signature}

# THE COMPLETE TARGET FUNCTION ({n} lines)
{extracted_function_body}

### SURGICAL DEPENDENCY SNIPPETS
{minimal_related_dependencies}
```

## Benefits

- **Reduced Token Usage**: Send only necessary code sections
- **Faster Processing**: Smaller context means quicker AI responses
- **Focused Analysis**: AI can concentrate on relevant code only
- **Complete Functions**: Never cut off mid-function
- **Proper Boundaries**: Respect natural code structure
- **Context Preservation**: Include necessary dependencies

## Error Handling

The system includes robust error handling for common edge cases:

- Symbol not found in repository map
- File access restrictions
- Parsing errors
- Repository map outdated relative to current file state

## Integration

This system is designed to integrate with Aider's existing repository mapping infrastructure, leveraging the existing code analysis capabilities while providing more precise extraction.

*-----------------------------------------------------------------------------------*
## **Flow (Focusing on the Augmented Prompt step):**
*-----------------------------------------------------------------------------------*

1.  **User Query:** "How does the `login_user` method work and create tokens?"
2.  **System -> LLM (Initial Prompt):** User Query + Initial Aider Repo Map Slice.
3.  **LLM Responds with `{CONTEXT_REQUEST}`:**
    ```json
    {
      CONTEXT_REQUEST: { 
        "original_user_query_context": "User is asking about the login_user method and its token generation.",
        "symbols_of_interest": [
          {"type": "method_definition", "name": "AuthService.login_user", "file_hint": "services/auth_service.py"},
          {"type": "method_definition", "name": "TokenService.generate_token", "file_hint": "services/token_service.py"}
        ],
        "reason_for_request": "To understand the full implementation of these specific methods to explain the login and token generation flow."
      }
    }
    ```
4.  **Your Backend (`AiderIntegrationService` + `SurgicalContextExtractor` + Dependency Logic):**
    *   Parses the request for `AuthService.login_user` and `TokenService.generate_token`.
    *   **Action A (Surgical Extraction of Primary Symbols):**
        *   Uses `SurgicalContextExtractor` to get the precise definition snippets for `login_user` (including its class header + imports from `auth_service.py`).
        *   Uses `SurgicalContextExtractor` to get the precise definition snippets for `generate_token` (including its class header + imports from `token_service.py`).
    *   **Action B (Proactive Surgical Dependency Snippet Extraction):**
        *   For `AuthService.login_user`, your `AiderIntegrationService` uses its dependency methods (`get_symbol_references_between_files`, etc., which are powered by `coder.repo_map`) to find out that `login_user` calls:
            *   `UserRepository.find_by_username` (in `user_repository.py`)
            *   `PasswordHasher.verify_password` (in `password_hasher.py`)
        *   Uses `SurgicalContextExtractor` to get surgical definition snippets for *these specific dependency methods* (`find_by_username` and `verify_password`) from their respective files.
        *   Similarly for `TokenService.generate_token` (e.g., if it uses a config value from `jwt_config.py`).
    *   Assembles all this context.

5.  **System -> LLM (Augmented Prompt - THIS IS THE RICH ONE):**

    ```plaintext
    Original User Query:
    "How does the `login_user` method work and create tokens?"

    ---
    Conversation History: 
    (if any)
    ---

    Code Context:

    ### REPOSITORY OVERVIEW (Aider-Generated Slice)
    (The initial, broader Aider map is still here for overall orientation)
    services/auth_service.py:
    │class AuthService:
    │    def login_user(self, username, password):
    services/token_service.py:
    │class TokenService:
    │    def generate_token(self, user_id):
    data/user_repository.py:
    │class UserRepository:
    │    def find_by_username(self, username):
    utils/password_hasher.py:
    │class PasswordHasher:
    │    def verify_password(self, plain_password, hashed_password):
    ---

    ### REQUESTED SYMBOL DEFINITIONS (Surgically Extracted)

    --- Definition from: services/auth_service.py ---
    # File header & imports
    from ..data.user_repository import UserRepository
    # ...
    class AuthService:
        # ...
        def login_user(self, username: str, password: str) -> str:
            # ... full method body of login_user ...
            user = self.user_repo.find_by_username(username)
            # ...
            token = self.token_service.generate_token(user.id, user.roles)
            # ...
    ---

    --- Definition from: services/token_service.py ---
    # File header & imports
    import jwt
    # ...
    class TokenService:
        # ...
        def generate_token(self, user_id: int, roles: list) -> str:
            # ... full method body of generate_token ...
    ---

    ### KEY DEPENDENCY SNIPPETS (Surgically Extracted for Clarity)

    --- Dependency used by `AuthService.login_user`: `UserRepository.find_by_username` (from `data/user_repository.py`) ---
    # File header & imports for user_repository.py
    # ...
    class UserRepository:
        def find_by_username(self, username: str) -> Optional[User]:
            # ... full method body of find_by_username ...
    ---

    --- Dependency used by `AuthService.login_user`: `PasswordHasher.verify_password` (from `utils/password_hasher.py`) ---
    # File header & imports for password_hasher.py
    # ...
    class PasswordHasher:
        def verify_password(self, plain_password: str, hashed_password: str) -> bool:
            # ... full method body of verify_password ...
    ---
    
    INSTRUCTIONS FOR THIS TURN:
    You requested definitions for `AuthService.login_user` and `TokenService.generate_token`. Their surgically extracted implementations are provided above, along with surgical snippets of their key direct dependencies (`UserRepository.find_by_username`, `PasswordHasher.verify_password`).
    Please use ALL this information to provide a detailed answer to the original user query: "How does the `login_user` method work and create tokens?"
    If you still require the full content of another specific file, or a definition for another specific symbol not adequately covered, you may request it using the JSON protocol.
    ```

**What happens next:**

*   **LLM has an incredibly rich, focused context:** It has the primary functions it asked for, *plus* the direct implementations of the functions those primary functions call.
*   **Reduced Need for Further Requests:** In many cases, this level of detail will be enough for the LLM to give a comprehensive answer without needing more files immediately. It can see the direct interactions.
*   **Can Still "Dig Deeper":** If, for example, `UserRepository.find_by_username` itself calls a complex ORM method or another service that the LLM feels is crucial *and* isn't clear from the snippet, it *can still* issue another `{CONTEXT_REQUEST}` or `{REQUEST_FILE}` for that deeper layer.

## In Summary

   If user asks about function X in file Y:
   ├── Extract: File header + imports (context)
   ├── Extract: Class definition line (structure)  
   ├── Extract: Complete function X (target)
   └── Skip: Everything else in file Y

   Then add surgical snippets for dependencies 
   (whether they're in file Y, Z, or anywhere else)