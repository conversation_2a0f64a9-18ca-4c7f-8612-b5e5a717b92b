Here are summaries of files in the repository:

analyze_data_flow.py:
⋮
│def parse_log_line(line):
⋮
│def extract_data_from_message(message):
⋮
│def analyze_log_file(log_file, output_format='text', output_file=None):
⋮

backtest\backtest_runner.py:
⋮
│class BacktestRunner:
│    """
│    Main class for running backtests.
│    Coordinates all components and manages the backtest execution.
⋮
│    async def run(self) -> bool:
⋮
│    def generate_report(self, output_dir: str = 'backtest_results') -> str:
⋮
│def parse_arguments():
⋮

check_backtest_data.py:
⋮
│def check_database(db_path):
⋮

check_database.py:
⋮
│db_path = os.path.join('database', 'indicator_data.db')
⋮
│conn = sqlite3.connect(db_path)
│cursor = conn.cursor()
│
⋮
│schema = cursor.fetchall()
⋮
│rows = cursor.fetchall()
⋮

check_database_again.py:
⋮
│db_path = os.path.join('database', 'indicator_data.db')
⋮
│conn = sqlite3.connect(db_path)
│cursor = conn.cursor()
│
⋮
│rows = cursor.fetchall()
⋮
│schema = cursor.fetchall()
⋮

check_date_range.py:
⋮
│def check_date_range(db_path, table_name, start_date, end_date):
⋮

check_datetime_format.py:
⋮
│def check_datetime_format(db_path, table_name):
⋮

check_db.py:
⋮
│engine = create_engine('sqlite:///database/live_trade_logs.db')
│
⋮
│inspector = inspect(engine)
│tables = inspector.get_table_names()
│
⋮

check_indicator_db.py:
⋮
│conn = sqlite3.connect('database/indicator_data.db')
│cursor = conn.cursor()
│
⋮
│tables = cursor.fetchall()
⋮
│rows = cursor.fetchall()
⋮

check_live_db.py:
⋮
│def check_live_database():
⋮

check_market_regime.py:
⋮
│cursor = conn.cursor()
│
⋮

check_scheduling.py:
⋮
│def check_scheduling():
⋮

check_web_dashboard.py:
⋮
│def get_dashboard_data():
⋮
│def simulate_web_data_processing():
⋮
│def check_database_directly():
⋮

config\backtest_config.py:
⋮
│class BacktestConfig:
│    # Transaction costs
│    DEFAULT_SLIPPAGE_PIPS = 2
⋮
│    @staticmethod
│    def get_config():
⋮

config\base_config.py:
⋮
│BASE_SYMBOLS_CONFIG = {
│    # 'EURUSD': {
│    #     'mt5_symbol': 'EURUSD',
│    #     'price_key': 'EURUSD',
│    #     'symbol_allowed': True,
│    #     'exchange': 'OANDA',
│    #     'prefix': 'eur',
│    #     'cache_intervals': [
│    #         Interval.in_1_hour
│    #     ],
⋮

config\environment.py:
│TELEGRAM_BOT_TOKEN = '**********************************************'
│TELEGRAM_CHAT_ID = '5185654136'
│
│TV_USERNAME = '<EMAIL>'
│TV_PASSWORD = '!#Uvv&Y9UM5t7F8'
│
⋮
│LIVE_DB_URI = 'sqlite:///database/live_data.db'
│BACKTEST_DB_URI = 'sqlite:///database/backtest_data.db'
│
⋮
│LIVE_TRADE_LOGS_DB_URI = 'sqlite:///database/live_trade_logs.db'
│BACKTEST_TRADE_LOGS_DB_URI = 'sqlite:///database/backtest_trade_logs.db'
│
⋮

config\trading_pairs_config.py:
⋮
│def configure_symbol_strategies():
⋮
│def init_config_store(config):
⋮

create_new_database.py:
⋮
│def create_new_database():
⋮

database\database_manager.py:
⋮
│class DatabaseManager:
│    """
│    Manages database connections for both live and backtest modes.
│    Ensures that backtest data doesn't contaminate live databases.
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def get_engine(self) -> Engine:
⋮

database\read_db_head.py:
⋮
│def print_head_of_table(table_name: str, db_path: str = "database/live_data.db"):
⋮

debug_data_access.py:
⋮
│def debug_data_access(db_path, table_name, target_date):
⋮

debug_historical_provider.py:
⋮
│def debug_historical_provider(db_path, symbol, interval, current_time):
⋮

find_continuous_data.py:
⋮
│def find_continuous_periods(db_path, table_name, min_period_hours=24):
⋮

fix_database_directly.py:
⋮
│def fix_database():
⋮

fix_database_with_pragma.py:
⋮
│def fix_database():
⋮

fix_market_direction.py:
⋮
│db_path = os.path.join('database', 'indicator_data.db')
⋮
│conn = sqlite3.connect(db_path)
│cursor = conn.cursor()
│
⋮
│rows = cursor.fetchall()
⋮
│schema = cursor.fetchall()
│
⋮

fix_market_direction_again.py:
⋮
│db_path = os.path.join('database', 'indicator_data.db')
⋮
│conn = sqlite3.connect(db_path)
│cursor = conn.cursor()
│
⋮
│schema = cursor.fetchall()
⋮
│rows = cursor.fetchall()
⋮
│rows = cursor.fetchall()
⋮
│market_direction = cursor.fetchone()[0]
⋮

fix_market_direction_field.py:
⋮
│def fix_market_direction():
⋮

fix_market_direction_field_again.py:
⋮
│def fix_market_direction():
⋮

get_date_range.py:
⋮
│def get_date_range(db_path, table_name):
⋮

main.py:
⋮
│async def initialize_components(is_backtest=False):
⋮
│def parse_arguments():
⋮

market_data\data_provider.py:
⋮
│class DataProvider:
│    """
│    Abstraction layer for data access that works in both live and backtest modes.
│    Delegates to the appropriate data source based on the current mode.
⋮
│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op
⋮

market_data\historical_data_provider.py:
⋮
│class HistoricalDataProvider:
│    """
│    Provides historical data for backtesting.
│    Loads data from source files/databases and prepares it for backtesting.
⋮
│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op
⋮

market_data\market_data_repository.py:
⋮
│class DatabaseManager:
│    """Handles robust data operations from old version"""
⋮
│    def get_and_save_data(self, symbol: str, interval, timestamp: datetime, max_retries: int = 20, 
⋮

market_data\tick.py:
⋮
│symbol_info = mt5.symbol_info(symbol)
⋮

market_data\timeframe_data.py:
⋮
│def analyze_volume(data: pd.DataFrame, lookback: int = 20) -> pd.DataFrame:
⋮
│def get_volume_signals(data: pd.DataFrame, thresh_strong: float = 75, thresh_weak: float = 25) -> p
│
⋮
│class CandleData:
│    def __init__(self, symbol: str, intervals: list, price_key: str, engine=None):
│        self.symbol = symbol
│        self.price_key = price_key
│        self.intervals = intervals
│        self.data_by_interval = {}
│
│        # Use the appropriate database based on mode
│        mode_manager = ModeManager()
│        if mode_manager.is_backtest_mode():
│            db_path = mode_manager.get_backtest_db_path()
⋮
│    def calculate_trend_and_indicators(self, data: pd.DataFrame) -> dict:
⋮

models\__init__.py:
⋮
│Base = declarative_base()
│engine = create_engine('sqlite:///database/manage_positions.db')
│Session = sessionmaker(bind=engine)
│
⋮

models\backtest_indicator_data.py:
⋮
│class BacktestIndicatorData(Base):
│    """
│    Model for storing indicator data during backtest iterations.
│    Stores values per symbol/interval for each candle date.
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt) -> 'BacktestIndicat
⋮
│def get_backtest_indicator_session():
⋮

models\live_indicator_data.py:
⋮
│class LiveIndicatorData(Base):
│    """
│    Model for storing indicator data during live trading.
│    Stores values per symbol/interval for each candle date.
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt, is_complete_candle:
│        """Create LiveIndicatorData instance from data dictionary"""
⋮
│        try:
│            print(f"DEBUG: from_dict called for {symbol} {interval} {candle_date}")
⋮
│            def get_value(key_prefix, default=0):
⋮
│def get_live_indicator_session():
⋮

models\position_repository.py:
⋮
│Session = sessionmaker(bind=engine)
│
│class Position(Base):
⋮
│def verify_position_records():
⋮

models\target_data.py:
⋮
│@dataclass
│class TargetData:
│    """Encapsulates all target-related data with dynamic attributes"""
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str) -> 'TargetData':
⋮
│    def to_dict(self) -> Dict:
⋮
│    def get_timeframe_data(self, tf: str) -> Dict:
⋮
│class TargetDataService:
│    """Manages target data caching and access"""
⋮
│    def get_target(self, symbol: str) -> Optional[TargetData]:
⋮

models\target_history.py:
⋮
│class TargetHistory(Base):
│    __tablename__ = 'target_history'
│    
⋮
│    @classmethod
│    def from_dict(cls, data: Dict, symbol: str, timeframe: str, timestamp: dt) -> 'TargetHistory':
⋮

models\trade_log.py:
⋮
│def get_trade_log_engine():
⋮
│class TradeLog(TradeLogBase):
│    """Model for logging trade information for each symbol"""
⋮
│    def to_dict(self) -> Dict:
⋮
│class TradeExecution(TradeLogBase):
│    """Model for tracking individual trade executions"""
⋮
│    def to_dict(self) -> Dict:
⋮
│def ensure_trade_log_tables_exist():
⋮
│def get_trade_log_session():
⋮

models\trigger_repository.py:
⋮
│class TriggerTimeframe(Base):
⋮
│class Trigger(Base):
⋮
│class TriggerRepository:
│    def __init__(self, session: SQLAlchemySession, trigger_strategy=None):  # Add trigger_strategy 
│        self.session = session
│        self.logger = logging.getLogger(__name__)
│        self.trigger_strategy = trigger_strategy  # Store the strategy
⋮
│    def get_latest_triggers(self, symbol: str, interval_name: str = None, limit: int = 2) -> List[T
⋮
│    def _convert_to_datetime(self, date_value):
⋮

monitor_data_flow.py:
⋮
│def parse_log_line(line):
⋮
│def get_color_for_message(message):
⋮
│def format_json_in_message(message):
⋮
│def monitor_log_file(log_file, follow=True, filter_symbol=None, filter_interval=None, max_lines=Non
⋮

reset_scheduling.py:
⋮
│def reset_scheduling_table():
⋮

run_backtest.py:
⋮
│def parse_arguments():
⋮

scripts\check_indicator_data.py:
⋮
│def check_indicator_data():
⋮

scripts\check_live_indicators_detailed.py:
⋮
│def check_live_indicators_detailed():
⋮

scripts\debug_live_indicator_service.py:
⋮
│class DebugLiveIndicatorService(LiveIndicatorService):
│    """Enhanced version of LiveIndicatorService with more detailed logging"""
│
⋮
│    async def store_indicator_data(self, symbol: str, interval: str, data: dict, is_complete_candle
⋮

scripts\debug_live_indicators.py:
⋮
│async def test_direct_db_write():
⋮

scripts\debug_try_store_indicator_data.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service"""
│    def get_candle_data(self, symbol, interval):
⋮
│class MockTechnicalAnalysisService:
⋮
│class MockTargetService:
⋮
│class MockMarketRegimeService:
│    """Mock market regime service"""
│    def get_market_regime(self, symbol):
⋮
│class DebugLiveIndicatorService(LiveIndicatorService):
⋮

scripts\initialize_components.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service that returns predefined data"""
⋮
│    async def get_candle_data(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service that returns predefined data"""
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service that returns predefined data"""
⋮
│    async def get_targets(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol, symbols_config=None):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service that returns predefined data"""
⋮
│    def get_market_regime(self, symbol):
⋮
│async def initialize_components():
⋮

scripts\insert_test_record.py:
⋮
│def insert_test_record():
⋮

scripts\monitor_events.py:
⋮
│async def event_callback(*args, **kwargs):
⋮
│async def monitor_events():
⋮

scripts\monitor_live_indicator_events.py:
⋮
│async def monitor_events():
⋮

scripts\print_indicator_tables.py:
⋮
│def get_table_info(db_path, table_name):
⋮
│def print_table_data(db_path, table_name, limit=10, where_clause=None):
⋮

scripts\recreate_backtest_indicator_table.py:
⋮
│def parse_arguments():
⋮

scripts\register_mock_components.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service"""
│    def get_candle_data(self, symbol, interval):
⋮
│class MockTechnicalAnalysisService:
⋮
│class MockTargetService:
⋮
│class MockMarketRegimeService:
│    """Mock market regime service"""
│    def get_market_regime(self, symbol):
⋮

scripts\test_database.py:
⋮
│def test_database_connection():
⋮

scripts\test_fixes.py:
⋮
│async def test_event_system():
⋮

scripts\test_live_indicators_with_mock_data.py:
⋮
│class MockCandleDataService:
│    """Mock candle data service that returns predefined data"""
⋮
│    async def get_candle_data(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│class MockTechnicalAnalysisService:
│    """Mock technical analysis service that returns predefined data"""
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol):
⋮
│class MockTargetService:
│    """Mock target service that returns predefined data"""
⋮
│    async def get_targets(self, symbol):
⋮
│    async def initialize_symbol(self, symbol):
⋮
│    async def process_symbol(self, symbol, symbols_config=None):
⋮
│class MockMarketRegimeService:
│    """Mock market regime service that returns predefined data"""
⋮
│    def get_market_regime(self, symbol):
⋮

scripts\view_backtest_indicators.py:
⋮
│def parse_arguments():
⋮

services\backtest_indicator_service.py:
⋮
│class BacktestIndicatorService:
│    """
│    Service for storing and retrieving indicator data during backtests.
⋮
│    def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any]) -> bool:
⋮
│    def get_indicator_data(self, symbol: str, interval: str,
│                          from_date: datetime, to_date: Optional[datetime] = None,
⋮

services\candle_data_service.py:
⋮
│class CandleDataService:
│    def __init__(self, price_manager, event_system=None):
│        self.price_manager = price_manager
│        self.historical_data = {}
⋮
│    async def initialize_symbol(self, symbol: str) -> bool:
⋮
│    async def get_candle_data(self, symbol: str, price_key: str = None) -> dict:
⋮

services\event_system.py:
⋮
│class DataEventSystem:
│    _instance = None
│    def __new__(cls):
⋮
│    def subscribe(self, event_or_symbol: str, callback: Callable) -> None:
⋮
│    def publish(self, event_name: str, *args, **kwargs) -> None:
⋮

services\live_indicator_service.py:
⋮
│class LiveIndicatorService:
│    """
│    Service for storing and retrieving indicator data during live trading.
⋮
│    async def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any], is_compl
│        """
│        Store indicator data for a symbol and interval
│
│        Args:
│            symbol: Trading symbol (e.g., 'GBPUSD')
│            interval: Timeframe interval (e.g., '1h')
│            data: Dictionary containing all indicator values
│            is_complete_candle: Whether this is a complete candle or a partial update
│
│        Returns:
⋮
│        try:
│            print(f"DEBUG: store_indicator_data called for {symbol} {interval}")
⋮
│            def get_value(key_prefix, default=0.0):
⋮
│    def get_indicator_data(self, symbol: str, interval: str,
│                          from_date: datetime, to_date: Optional[datetime] = None,
⋮
│    def get_storage_statistics(self) -> Dict[str, Any]:
⋮

services\market_regime_service.py:
⋮
│class MarketRegimeService:
│    def __init__(self, candle_data_service: CandleDataService, event_system: DataEventSystem):
│        self.candle_data_service = candle_data_service
│        self.event_system = event_system
│        self.market_regimes = {}
│        self.version = "1.0.0"
│
│        # Default parameters
│        self.volatility_window = 14
│        self.linear_reg_period = 20
│        self.ranging_sensitivity = 4
│
⋮
│    def get_market_regime(self, symbol: str, interval: str) -> Dict[str, Any]:
⋮
│    def analyze_market_regime(self, symbol: str, interval: str, config_symbol: str = None, current_
⋮

services\mock_technical_analysis_service.py:
⋮
│class MockTechnicalAnalysisService:
│    """
│    Mock version of TechnicalAnalysisService that returns predefined values
│    without doing any actual calculations.
⋮
│    async def initialize_symbols(self, symbols_config):
⋮
│    async def process_symbol(self, symbol, interval=None, current_time=None):
⋮
│    def _get_default_indicators(self):
⋮

services\target_service.py:
⋮
│class TargetService:
│    def __init__(self, session: Session):
│        self.trigger_repository = TriggerRepository(session)
│        self.trigger_strategy = TriggerStrategy(self.trigger_repository)
│        self.target_data_service = TargetDataService()
│        self.event_system = DataEventSystem()
│
│        # Cache for target calculations - no expiration, purely event-driven
│        self.target_cache = {}
│
│        class MinimalPriceManager:
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
⋮
│    async def initialize_symbols(self, symbols_config: Dict):
⋮
│    async def process_symbol(self, symbol: str, symbols_config: Dict, current_time: Optional[dateti
⋮
│    async def get_targets(self, symbol: str, force_recalculate: bool = False) -> Optional[Dict[str,
⋮

services\technical_analysis_service.py:
⋮
│class TechnicalAnalysisService:
│    """
│    Service for calculating technical indicators that combine data from
│    both candle data and target data sources.
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
⋮
│    async def initialize_symbols(self, symbols_config: Dict):
⋮
│    async def process_symbol(self, symbol: str, targets_data: Optional[Dict] = None, current_time: 
⋮

services\trade_logger.py:
⋮
│class TradeLogger:
│    """Service for logging trade information"""
│
⋮
│    def log_trade_exit(self,
│                       trade_id: str,
│                       exit_price: float,
│                       pnl: Optional[float] = None,
│                       pnl_percentage: Optional[float] = None,
│                       pnl_pips: Optional[float] = None,
⋮
│    def get_trade_by_id(self, trade_id: str) -> Optional[TradeLog]:
⋮
│    def calculate_pnl(self, trade_id: str, exit_price: float) -> Dict:
⋮
│    def get_performance_stats(self, symbol: Optional[str] = None,
│                             start_date: Optional[datetime] = None,
⋮

strategy\base.py:
⋮
│@dataclass
│class TradeDetails:
⋮

strategy\eur_strategy.py:
⋮
│def get_default_strategy_parameters() -> dict:
⋮
│class EURConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "EURUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
⋮
│    def print_data(self, data, title=""):
⋮

strategy\gbp_strategy.py:
⋮
│def get_default_strategy_parameters() -> dict:
⋮
│class GBPConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "GBPUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
⋮
│    def print_data(self, data, title=""):
⋮

strategy\price_target_calculator.py:
⋮
│@dataclass
│class TargetPrice:
⋮
│@dataclass
│class CombinedTarget:
⋮
│class TargetResult(NamedTuple):
⋮

strategy\trigger_strategy.py:
⋮
│class TriggerStrategy:
│    def __init__(self, trigger_repository: TriggerRepository):
│        self.trigger_repository = trigger_repository
│        self.trigger_repository.trigger_strategy = self  # Set self as the strategy
⋮
│    def _timeframe_obj_to_dict(self, tf_obj) -> Dict:
⋮
│    def initialize_symbols(self, symbols_config: Dict):
⋮

strategy\xau_strategy.py:
⋮
│class XAUConditionStrategy(BaseStrategyWithRetry):
│    def __init__(self):
│        super().__init__()
│        self.logger = logging.getLogger(__name__)
│        self.trading_symbol = "XAUUSD"
│        self.timeframes = ["1h"]
│        self.tf = "1h"
│        self.prefix = 'gold'
⋮
│    def print_data(self, data, title=""):
⋮

test_trade_logs.py:
⋮
│def test_trade_logs():
⋮

test_tv_connection.py:
⋮
│def test_tv_connection():
⋮

tests\test_event_system_coroutines.py:
⋮
│async def test_event_system():
⋮

update_scheduling.py:
⋮
│def update_scheduling():
⋮

utils\backtest_results copy.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮

utils\backtest_results.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮

utils\clock.py:
⋮
│class TradingClock:
│    """
│    Provides a consistent time interface for both live and backtest modes.
│    In live mode, returns the actual system time.
│    In backtest mode, returns a simulated time that can be advanced programmatically.
⋮
│    def now(self) -> datetime:
⋮

utils\components_registry.py:
⋮
│def register_component(name: str, component: Any) -> None:
⋮
│def get_component(name: str) -> Optional[Any]:
⋮

utils\config_manager.py:
⋮
│class ConfigManager:
│    """
│    A class to manage configuration settings for the trading system
⋮
│    def load_config(self) -> Dict[str, Any]:
⋮
│    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
⋮
│    def get(self, key: str, default: Any = None) -> Any:
⋮
│    def set(self, key: str, value: Any) -> bool:
⋮
│    def reset(self) -> bool:
⋮
│    def _deep_update(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
⋮
│config_manager = ConfigManager()

utils\config_utils.py:
⋮
│class ConfigStore:
│    """Singleton class to store and manage symbol configurations"""
⋮
│    def __new__(cls):
⋮
│    @classmethod
│    def set_config(cls, config: Dict) -> None:
⋮
│    @classmethod
│    def resolve_symbols(cls, symbol: str) -> Tuple[str, str, str]:
⋮
│def init_config_store(symbols_config: Dict) -> None:
⋮

utils\dashboard.py:
⋮
│class TradingDashboard:
│    """
│    A real-time dashboard for displaying critical trading information
⋮
│    def start(self):
⋮
│    def update_trades(self, trades):
⋮

utils\dashboard_manager.py:
⋮
│class DashboardManager:
│    """
│    Manager class to handle dashboard operations and data collection
⋮
│    def start(self, symbols=None, mode=None, debug=False):
⋮
│    def is_enabled(self) -> bool:
⋮

utils\data_flow_logger.py:
⋮
│class DataFlowLogger:
│    """
│    A specialized logger for tracking data flow through the system.
│    Provides clean, structured logging of market data updates, processing steps,
│    and indicator calculations with enhanced traceability.
⋮
│    def _format_value(self, value):
⋮
│    def _format_dict(self, data_dict):
⋮
│    def generate_correlation_id(self, symbol, interval):
⋮
│    def log_candle_data(self, symbol, interval, candle_data, source="Unknown", is_latest=False, ser
⋮
│    def log_event_published(self, event_name, symbol, interval, data=None):
⋮
│    def log_calculation_start(self, service, symbol, interval):
⋮
│    def log_error(self, symbol, interval, error_message, context=None):
⋮
│    def log_database_connection(self, db_path, connection_type, status, details=None):
⋮

utils\debug_utils.py:
⋮
│class DebugManager:
│    """
│    Manager class to handle debugging operations when dashboard is disabled
⋮
│    def start(self):
⋮
│    def is_enabled(self) -> bool:
⋮

utils\logger.py:
│def log(message):
⋮

utils\mode_manager.py:
⋮
│class ModeManager:
│    """
│    Controls whether the system runs in live or backtest mode.
│    Implemented as a singleton to ensure consistent mode across all components.
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def set_backtest_mode(self, start_date: datetime, end_date: datetime, db_path: Optional[str] = 
⋮
│    def set_live_mode(self):
⋮
│    def is_backtest_mode(self) -> bool:
⋮
│    def get_backtest_dates(self) -> Tuple[Optional[datetime], Optional[datetime]]:
⋮
│    def get_backtest_db_path(self) -> str:
⋮
│    def set_backtest_speed(self, speed_multiplier: float):
⋮
│    def get_backtest_speed(self) -> float:
⋮

utils\mt5_utils.py:
⋮
│class MT5Manager:
│    def __init__(self):
│        self._monitor_task = None
│        self._is_running = False
⋮
│    def initialize_mt5(self) -> bool:
⋮
│    def ensure_connection(self) -> bool:
⋮
│    async def stop_monitoring(self):
⋮
│    def get_account_balance(self):
⋮
│    def get_symbol_prices(self, symbols_config):
⋮
│def initialize_mt5():
⋮
│def get_account_balance():
⋮
│def get_symbol_prices():
⋮
│async def stop_monitoring():
⋮

utils\notification_service.py:
⋮
│def log(message):
⋮
│class TelegramManager:
│    def __init__(self, bot_token, chat_id):
│        self.bot_token = bot_token
│        self.chat_id = chat_id
│        self.bot = telegram.Bot(token=bot_token) 
│        self.message_sent_w = False
│        self.message_sent = False
│        self.message_sent_positions = False
⋮
│    async def send_message(self, app, message, max_retries=3):
⋮
│    async def notify_success(self, app, message):
⋮
│    async def notify_failure(self, app, message):
⋮

utils\performance_analyzer.py:
⋮
│class PerformanceAnalyzer:
│    """
│    Advanced performance metrics calculator for trading strategies
⋮
│    def calculate_metrics_from_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Union[str, f
⋮
│    def calculate_metrics_from_db(self, start_date: datetime, end_date: datetime) -> Dict[str, Unio
⋮

utils\progress.py:
⋮
│class ProgressIndicator:
⋮

utils\terminal_logger.py:
⋮
│class TerminalLogger:
│    """
│    Enhanced terminal logger with improved formatting and organization.
│
│    Features:
│    - Different log levels with color coding
│    - Structured output for complex data
│    - Section headers and separators
│    - Configurable verbosity
⋮
│    def _format_message(self, level: LogLevel, message: str) -> str:
⋮
│    def _format_data(self, data: Union[Dict[str, Any], List], indent: int = 0) -> str:
⋮
│    def log(self, level: LogLevel, message: str):
⋮
│    def error(self, message: str):
⋮
│    def warning(self, message: str):
⋮
│    def info(self, message: str):
⋮
│    def debug(self, message: str):
⋮
│def set_log_level(level: LogLevel):
⋮

utils\theme_manager.py:
⋮
│class ThemeManager:
│    """
│    A class to manage color themes for the terminal output
⋮
│    def get_color(self, category: str) -> Tuple[str, str]:
⋮
│    def get_reset(self) -> str:
⋮
│    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
⋮
│theme_manager = ThemeManager()

utils\trade_log_utils.py:
⋮
│def get_trade_logs(symbol: Optional[str] = None,
│                  days: Optional[int] = None,
⋮
│def print_trade_summary(symbol: Optional[str] = None,
⋮
│def plot_trade_performance(symbol: Optional[str] = None,
│                          days: Optional[int] = None,
⋮
│def print_trade_logs(symbol: Optional[str] = None,
│                    days: Optional[int] = None,
│                    status: Optional[str] = None,
⋮

utils\trades_dashboard.py:
⋮
│def format_number(num: float) -> str:
⋮

utils\uptime_tracker.py:
⋮
│class UptimeTracker:
│    """
│    Tracks system uptime since initialization
⋮
│    def __new__(cls):
⋮
│    def _initialize(self):
⋮
│    def start(self):
⋮

utils\web_dashboard_manager.py:
⋮
│class WebDashboardManager:
│    """
│    Manager class to handle web dashboard operations (FastAPI version)
⋮
│    def initialize(self, event_system):
⋮
│    def start(self, host=None, port=None, update_interval=None, **kwargs): # Removed unused args fo
⋮

web\server.py:
⋮
│def get_indicator_data_from_db():
⋮
│def update_clients():
⋮

web\server_fastapi.py:
⋮
│async def get_db_connection(db_path: str) -> aiosqlite.Connection:
⋮
│async def get_indicator_data_from_db_async(limit: int = 10) -> List[Dict[str, Any]]:
⋮
│async def get_trades_from_db_async(days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
⋮
│def serialize_value(value: Any) -> Any:
⋮
│async def get_dashboard_data() -> Dict[str, Any]:
⋮
│async def get_current_user(request: Request) -> Optional[str]:
⋮
│async def dashboard_update_task():
⋮
│async def start_server_async(host: str = SERVER_HOST, port: int = SERVER_PORT, update_interval: flo
⋮
│async def stop_server_async():
⋮

web\simple_server.py:
⋮
│def get_indicator_data_from_db():
⋮

web\static\js\dashboard.js:
⋮
│function startPolling() {
│    if (!pollingInterval) {
│        pollingInterval = setInterval(fetchDashboardData, 1000);
│        fetchDashboardData(); // Initial fetch
│    }
⋮
│function formatCurrency(value) {
│    return new Intl.NumberFormat('en-US', {
│        style: 'currency',
│        currency: 'USD'
│    }).format(value);
⋮
│function formatNumber(value, decimals = 5) {
│    return parseFloat(value).toFixed(decimals);
⋮
│function updateDashboard(data) {
│    console.log('updateDashboard called with data:', data);
│
│    // Check if data contains trades
│    if (data.trades) {
│        console.log('Data contains trades array with length:', data.trades.length);
│        if (data.trades.length > 0) {
│            console.log('First trade in data:', data.trades[0]);
│        }
│    } else {
⋮
