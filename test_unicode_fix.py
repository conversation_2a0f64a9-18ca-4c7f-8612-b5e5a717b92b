#!/usr/bin/env python3
"""
Test if the Unicode emoji fix resolved the Smart Map Request System crash.
"""

import os
import sys
import subprocess
import tempfile

def test_unicode_fix():
    """Test that Smart Map Request System doesn't crash on Unicode."""
    print("Testing Unicode Fix for Smart Map Request System")
    print("=" * 60)
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Test with a simple MAP_REQUEST that should trigger the system
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--message", "Show me files related to position calculation",
            "--no-stream",
            r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        ]
        
        print(f"Running command: {' '.join(cmd[:4])}...")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30  # Short timeout to catch crashes quickly
        )
        
        print(f"Exit code: {result.returncode}")
        
        # Check for Unicode errors
        output = result.stdout + result.stderr
        unicode_error = "UnicodeEncodeError" in output
        charmap_error = "charmap" in output
        emoji_error = "U+1F" in output
        
        print(f"Unicode error detected: {unicode_error}")
        print(f"Charmap error detected: {charmap_error}")
        print(f"Emoji error detected: {emoji_error}")
        
        # Check for Smart Map Request activity
        smart_map_activity = "Smart Map Request" in output or "MAP_REQUEST" in output
        
        print(f"Smart Map Request activity: {smart_map_activity}")
        
        if unicode_error or charmap_error or emoji_error:
            print("❌ UNICODE ISSUE STILL EXISTS!")
            print("Error output:")
            print(output[:1000])
            return False
        elif smart_map_activity:
            print("✅ Smart Map Request System working without Unicode errors!")
            return True
        else:
            print("⚠️  No Smart Map Request activity detected")
            print("Output preview:")
            print(output[:500])
            return False
        
    except subprocess.TimeoutExpired:
        print("⚠️  Command timed out (may still be working)")
        return True  # Timeout is better than crash
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_simple_map_request():
    """Test a simple MAP_REQUEST directly."""
    print("\nTesting Simple MAP_REQUEST")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        dashboard_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        
        if not os.path.exists(dashboard_path):
            print(f"❌ Dashboard path does not exist: {dashboard_path}")
            return False
        
        print(f"✅ Dashboard path exists")
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=dashboard_path,
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=dashboard_path,
            io=io
        )
        
        print("✅ Smart Map Request Handler created")
        
        # Test simple MAP_REQUEST
        map_request = {
            "keywords": ["position", "calculate"],
            "type": "implementation",
            "scope": "all",
            "max_results": 3
        }
        
        print(f"Testing MAP_REQUEST: {map_request}")
        
        result = handler.handle_map_request(map_request)
        
        print(f"✅ MAP_REQUEST completed successfully!")
        print(f"Result length: {len(result)} characters")
        
        if len(result) > 100:
            print(f"Result preview: {result[:200]}...")
            return True
        else:
            print(f"⚠️  Result seems too short: {result}")
            return False
        
    except UnicodeEncodeError as e:
        print(f"❌ Unicode error still exists: {e}")
        return False
    except Exception as e:
        print(f"❌ Error in MAP_REQUEST test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run Unicode fix tests."""
    print("🚀 Testing Unicode Fix for Smart Map Request System")
    print("=" * 80)
    
    tests = [
        ("Simple MAP_REQUEST Test", test_simple_map_request),
        ("Unicode Fix Test", test_unicode_fix),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 UNICODE FIX TESTING SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 UNICODE FIX SUCCESSFUL!")
        print("   Smart Map Request System no longer crashes on Unicode")
    else:
        print("❌ UNICODE ISSUES REMAIN!")
        print("   Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
