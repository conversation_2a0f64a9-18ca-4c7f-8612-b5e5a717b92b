# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

### 1. TestWholeFileCoder (class)
- File: aider-main\tests\basic\test_wholefile.py
- Criticality: low | Risk: low

### 2. TestUnifiedDiffCoder (class)
- File: aider-main\tests\basic\test_udiff.py
- Criticality: low | Risk: low

### 3. test_determine_context_window_size (method)
- File: test_surgical_context_extractor.py
- Belongs to Class: `TestSurgicalContextExtractor`
- Inherits From: ['unittest.TestCase']
- Criticality: low | Risk: low

#### 🔁 Class Context
- Part of `TestSurgicalContextExtractor` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['_determine_context_window_size', 'assertGreaterEqual'] (total: 2)
- **Side Effects**: modifies_state

### 4. TestSurgicalContextExtractor (class)
- File: test_surgical_context_extractor.py
- Criticality: low | Risk: low

### 5. process_context_requests (method)
- File: test_repo_map_compatibility.py
- Belongs to Class: `MockCoder`
- Inherits From: No inheritance detected
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `MockCoder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['tool_error', 'detect_context_request', 'tool_output', '...'] (total: 13)
- **Used by**: ['test_full_aider_integration', 'test_aider_coder_path_fix', 'test_context_request_integration', '...'] (total: 9)
- **Side Effects**: database_io, network_io, writes_log

### 6. test_exchange_code_for_key_request_exception (method)
- File: aider-main\tests\basic\test_onboarding.py
- Belongs to Class: `TestOnboarding`
- Inherits From: ['unittest.TestCase']
- Criticality: low | Risk: low

#### 🔁 Class Context
- Part of `TestOnboarding` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['RequestException', 'DummyIO', 'MagicMock', '...'] (total: 7)
- **Side Effects**: network_io, modifies_state

### 7. test_request_timeout_from_extra_params (method)
- File: aider-main\tests\basic\test_models.py
- Belongs to Class: `TestModels`
- Inherits From: ['unittest.TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestModels` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['Model', 'send_completion', 'assert_called_with', '...'] (total: 4)
- **Side Effects**: network_io

### 8. test_request_timeout_default (method)
- File: aider-main\tests\basic\test_models.py
- Belongs to Class: `TestModels`
- Inherits From: ['unittest.TestCase']
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `TestModels` class
- Inheritance chain: unittest.TestCase

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ['Model', 'send_completion', 'assert_called_with', '...'] (total: 4)
- **Side Effects**: network_io

## KEY IMPLEMENTATIONS (0 functions)

## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes
5. **Analyze inheritance patterns** - understand OOP relationships and method overrides

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context with enhanced inheritance data and real source code implementations.
