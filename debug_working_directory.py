#!/usr/bin/env python3
"""
Debug the working directory and file path issues.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_working_directory():
    """Debug working directory and file path issues."""
    print("🔍 Debugging Working Directory Issues")
    print("=" * 80)
    
    # Check current working directory
    current_dir = os.getcwd()
    print(f"Current working directory: {current_dir}")
    
    # Check if aider-main exists
    aider_main_path = os.path.join(current_dir, "aider-main")
    print(f"aider-main path: {aider_main_path}")
    print(f"aider-main exists: {os.path.exists(aider_main_path)}")
    
    if os.path.exists(aider_main_path):
        print(f"aider-main is directory: {os.path.isdir(aider_main_path)}")
        
        # List some files in aider-main
        try:
            files = os.listdir(aider_main_path)
            print(f"Files in aider-main (first 10): {files[:10]}")
        except Exception as e:
            print(f"Error listing aider-main: {e}")
    
    # Check specific files
    test_files = [
        "aider-main/.dockerignore",
        "aider-main/aider/repomap.py",
        "aider-main/aider/models.py",
        ".dockerignore",
        "aider/repomap.py",
        "aider/models.py"
    ]
    
    print(f"\nChecking specific file paths:")
    for file_path in test_files:
        full_path = os.path.join(current_dir, file_path)
        exists = os.path.exists(full_path)
        print(f"   {file_path}: {exists} ({full_path})")
    
    return True

def test_repomap_with_correct_paths():
    """Test RepoMap with correct file paths."""
    print("\n🔍 Testing RepoMap with Correct Paths")
    print("=" * 80)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Get current directory
        current_dir = os.getcwd()
        aider_main_path = os.path.join(current_dir, "aider-main")
        
        print(f"Creating RepoMap with root: {aider_main_path}")
        
        # Create RepoMap with absolute path
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=aider_main_path,  # Use absolute path
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap created with absolute path")
        
        # Test with a few specific files
        test_files = [
            "aider/repomap.py",
            "aider/models.py",
            "README.md"
        ]
        
        print(f"\nTesting get_ranked_tags with specific files:")
        
        # Check if files exist first
        existing_files = []
        for file_path in test_files:
            full_path = os.path.join(aider_main_path, file_path)
            if os.path.exists(full_path):
                existing_files.append(file_path)
                print(f"   ✅ {file_path} exists")
            else:
                print(f"   ❌ {file_path} missing")
        
        if existing_files:
            print(f"\nTesting get_ranked_tags with {len(existing_files)} existing files:")
            
            ranked_tags = repo_map.get_ranked_tags(
                chat_fnames=[],
                other_fnames=existing_files,
                mentioned_fnames=set(),
                mentioned_idents=set()
            )
            
            print(f"   Ranked tags returned: {len(ranked_tags)}")
            
            # Count different types of tags
            tag_types = {}
            for tag in ranked_tags:
                if hasattr(tag, 'kind'):
                    tag_types[tag.kind] = tag_types.get(tag.kind, 0) + 1
                else:
                    tag_type = type(tag).__name__
                    tag_types[tag_type] = tag_types.get(tag_type, 0) + 1
            
            print(f"   Tag types: {tag_types}")
            
            # Show some examples
            if ranked_tags:
                print("   Example tags:")
                for i, tag in enumerate(ranked_tags[:10]):
                    if hasattr(tag, 'name') and hasattr(tag, 'kind'):
                        print(f"     {i+1}. {tag.name} ({tag.kind}) in {tag.rel_fname}")
                    else:
                        print(f"     {i+1}. {tag} (type: {type(tag)})")
        else:
            print("   ❌ No existing files found to test with")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing RepoMap with correct paths: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_map_request_with_correct_paths():
    """Test Smart Map Request Handler with correct paths."""
    print("\n🔍 Testing Smart Map Request with Correct Paths")
    print("=" * 80)
    
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Get current directory
        current_dir = os.getcwd()
        aider_main_path = os.path.join(current_dir, "aider-main")
        
        print(f"Creating Smart Map Request Handler with root: {aider_main_path}")
        
        # Create components with absolute path
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=aider_main_path,  # Use absolute path
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=aider_main_path,  # Use absolute path
            io=io
        )
        
        print("✅ Smart Map Request Handler created with absolute path")
        
        # Test a simple search
        print(f"\nTesting search for existing functions:")
        
        test_request = {
            "keywords": ["get_repo_map", "RepoMap", "class"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }
        
        print(f"MAP_REQUEST: {test_request}")
        
        result = handler.handle_map_request(test_request)
        
        print(f"Result length: {len(result)} characters")
        print(f"Result preview: {result[:300]}...")
        
        if "get_repo_map" in result or "RepoMap" in result:
            print("✅ Search found relevant content!")
            return True
        else:
            print("❌ Search did not find expected content")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Smart Map Request with correct paths: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run working directory debugging tests."""
    print("🚀 Working Directory Debugging")
    print("=" * 100)
    
    tests = [
        ("Working Directory Debug", debug_working_directory),
        ("RepoMap with Correct Paths", test_repomap_with_correct_paths),
        ("Smart Map Request with Correct Paths", test_smart_map_request_with_correct_paths),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 WORKING DIRECTORY DEBUGGING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 WORKING DIRECTORY ISSUES RESOLVED!")
        print("   Smart Map Request System should now work correctly")
    else:
        print("⚠️  WORKING DIRECTORY ISSUES REMAIN!")
        print("   Need to fix file path resolution")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
