"""
Test script for the file not found suggestion feature.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import from aider-main directory
from aider_main.aider.io import InputOutput
from aider_main.aider.coders.base_coder import Coder
from aider_main.aider.models import Model
from aider_main.aider.file_request_validator import FileRequestValidator

class TestModel(Model):
    def __init__(self):
        self.name = "test-model"
        self.use_repo_map = True
        self.streaming = True
        self.edit_format = "whole"
        self.cache_control = False
        self.weak_model = None
        self.max_chat_history_tokens = 1000
        self.reasoning_tag = "reasoning"
        self.system_prompt_prefix = ""
        self.examples_as_sys_msg = False
        self.use_system_prompt = True
        self.reminder = "sys"
        self.lazy = False
        self.overeager = False
        self.info = {"max_input_tokens": 4000}

    def commit_message_models(self):
        return []

    def token_count(self, text):
        return len(text.split())

def create_test_files():
    """Create test files for the test."""
    # Create a test directory
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)

    # Create some test files
    (test_dir / "file1.py").write_text("# This is file1.py")
    (test_dir / "file2.py").write_text("# This is file2.py")
    (test_dir / "similar_file.py").write_text("# This is similar_file.py")
    (test_dir / "another_file.txt").write_text("This is another_file.txt")

    # Create a subdirectory
    sub_dir = test_dir / "subdir"
    sub_dir.mkdir(exist_ok=True)

    # Create files in the subdirectory
    (sub_dir / "subfile.py").write_text("# This is subfile.py")

    return test_dir

def test_file_not_found():
    """Test the file not found suggestion feature."""
    # Create test files
    test_dir = create_test_files()

    # Initialize IO
    io = InputOutput()

    # Create a model
    model = TestModel()

    # Create a coder
    coder = Coder(model, io, root=str(test_dir))

    # Create a file request validator
    coder.file_request_validator = FileRequestValidator(str(test_dir), io)

    # Test with a file that doesn't exist but has similar names
    success, message = coder.add_requested_file("simlar_file.py")

    print("\n=== Test 1: File not found but similar files exist ===")
    print(f"Success: {success}")
    print(f"Message: {message}")

    # Test with a file that doesn't exist and has no similar names
    success, message = coder.add_requested_file("nonexistent_file.py")

    print("\n=== Test 2: File not found and no similar files ===")
    print(f"Success: {success}")
    print(f"Message: {message}")

    # Test with a file that exists
    success, message = coder.add_requested_file("file1.py")

    print("\n=== Test 3: File exists ===")
    print(f"Success: {success}")
    print(f"Message: {message}")

    # Test with a file in a subdirectory
    success, message = coder.add_requested_file("subdir/subfile.py")

    print("\n=== Test 4: File in subdirectory ===")
    print(f"Success: {success}")
    print(f"Message: {message}")

    # Test process_file_requests with a file that doesn't exist
    content = """I need to see the file.

{REQUEST_FILE:
  "path": "nonexistent_file.py",
  "reason": "I need to understand the implementation"
}"""

    cleaned_content, file_message = coder.process_file_requests(content)

    print("\n=== Test 5: process_file_requests with nonexistent file ===")
    print(f"Cleaned content: {cleaned_content}")
    print(f"File message: {file_message}")

    # Test process_file_requests with a file that has similar names
    content = """I need to see the file.

{REQUEST_FILE:
  "path": "simlar_file.py",
  "reason": "I need to understand the implementation"
}"""

    cleaned_content, file_message = coder.process_file_requests(content)

    print("\n=== Test 6: process_file_requests with similar file ===")
    print(f"Cleaned content: {cleaned_content}")
    print(f"File message: {file_message}")

    # Clean up
    import shutil
    shutil.rmtree(test_dir)

if __name__ == "__main__":
    test_file_not_found()
