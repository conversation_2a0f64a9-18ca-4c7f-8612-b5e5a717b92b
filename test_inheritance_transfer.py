#!/usr/bin/env python3
"""
Test script to verify inheritance data transfer from IR to ContextEntity objects.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_inheritance_data_transfer():
    """Test the inheritance data transfer from IR to ContextEntity objects."""
    
    print("🔍 TESTING INHERITANCE DATA TRANSFER")
    print("=" * 60)
    
    try:
        # Generate IR data
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        ir_data = service.generate_mid_level_ir(external_project)
        
        if not ir_data or "modules" not in ir_data:
            print("❌ Failed to generate IR data")
            return False
        
        print(f"✅ Generated IR data with {len(ir_data['modules'])} modules")
        
        # Find entities with inheritance data in the raw IR
        entities_with_inheritance = []
        total_entities = 0
        
        for module in ir_data['modules']:
            for entity in module.get('entities', []):
                total_entities += 1
                
                inherits_from = entity.get('inherits_from', [])
                method_overrides = entity.get('method_overrides', [])
                calls_super = entity.get('calls_super', False)
                overridden_by = entity.get('overridden_by', [])
                class_name = entity.get('class_name')
                
                if inherits_from or method_overrides or calls_super or overridden_by or class_name:
                    entities_with_inheritance.append({
                        'module': module.get('name', 'unknown'),
                        'entity_name': entity.get('name', 'unknown'),
                        'entity_type': entity.get('type', 'unknown'),
                        'inherits_from': inherits_from,
                        'method_overrides': method_overrides,
                        'calls_super': calls_super,
                        'overridden_by': overridden_by,
                        'class_name': class_name
                    })
        
        print(f"📊 Raw IR Analysis:")
        print(f"   Total entities: {total_entities}")
        print(f"   Entities with inheritance data: {len(entities_with_inheritance)}")
        
        if entities_with_inheritance:
            print(f"\n🏗️ SAMPLE ENTITIES WITH INHERITANCE (first 5):")
            for i, entity in enumerate(entities_with_inheritance[:5], 1):
                print(f"   {i}. {entity['entity_name']} ({entity['entity_type']}) in {entity['module']}")
                if entity['inherits_from']:
                    print(f"      inherits_from: {entity['inherits_from']}")
                if entity['method_overrides']:
                    print(f"      method_overrides: {entity['method_overrides']}")
                if entity['calls_super']:
                    print(f"      calls_super: {entity['calls_super']}")
                if entity['overridden_by']:
                    print(f"      overridden_by: {entity['overridden_by']}")
                if entity['class_name']:
                    print(f"      class_name: {entity['class_name']}")
        
        # Now test the IntelligentContextSelector
        print(f"\n🔍 TESTING INTELLIGENT CONTEXT SELECTOR")
        print("=" * 60)
        
        from intelligent_context_selector import IntelligentContextSelector, TaskType
        
        selector = IntelligentContextSelector(ir_data, max_tokens=2000)
        
        print(f"✅ Created IntelligentContextSelector")
        print(f"   Total entities in entity_map: {len(selector.entity_map)}")
        
        # Check if inheritance data made it to the ContextEntity objects
        entities_with_inheritance_in_selector = []
        
        for entity_name, context_entity in selector.entity_map.items():
            if (context_entity.inherits_from or 
                context_entity.method_overrides or 
                context_entity.calls_super or 
                context_entity.overridden_by or
                context_entity.class_name):
                entities_with_inheritance_in_selector.append({
                    'entity_name': context_entity.entity_name,
                    'entity_type': context_entity.entity_type,
                    'module_name': context_entity.module_name,
                    'inherits_from': context_entity.inherits_from,
                    'method_overrides': context_entity.method_overrides,
                    'calls_super': context_entity.calls_super,
                    'overridden_by': context_entity.overridden_by,
                    'class_name': context_entity.class_name
                })
        
        print(f"📊 ContextEntity Analysis:")
        print(f"   Entities with inheritance data: {len(entities_with_inheritance_in_selector)}")
        
        if entities_with_inheritance_in_selector:
            print(f"\n🏗️ SAMPLE CONTEXT ENTITIES WITH INHERITANCE (first 5):")
            for i, entity in enumerate(entities_with_inheritance_in_selector[:5], 1):
                print(f"   {i}. {entity['entity_name']} ({entity['entity_type']}) in {entity['module_name']}")
                if entity['inherits_from']:
                    print(f"      inherits_from: {entity['inherits_from']}")
                if entity['method_overrides']:
                    print(f"      method_overrides: {entity['method_overrides']}")
                if entity['calls_super']:
                    print(f"      calls_super: {entity['calls_super']}")
                if entity['overridden_by']:
                    print(f"      overridden_by: {entity['overridden_by']}")
                if entity['class_name']:
                    print(f"      class_name: {entity['class_name']}")
        else:
            print("❌ No inheritance data found in ContextEntity objects!")
            
            # Debug: Check a specific entity that should have inheritance
            if entities_with_inheritance:
                sample_entity = entities_with_inheritance[0]
                entity_name = sample_entity['entity_name']
                module_name = sample_entity['module']
                full_name = f"{module_name}.{entity_name}"
                
                print(f"\n🔍 DEBUGGING SPECIFIC ENTITY: {full_name}")
                
                if full_name in selector.entity_map:
                    context_entity = selector.entity_map[full_name]
                    print(f"   Found in entity_map: ✅")
                    print(f"   inherits_from: {context_entity.inherits_from}")
                    print(f"   method_overrides: {context_entity.method_overrides}")
                    print(f"   calls_super: {context_entity.calls_super}")
                    print(f"   overridden_by: {context_entity.overridden_by}")
                    print(f"   class_name: {context_entity.class_name}")
                else:
                    print(f"   Not found in entity_map: ❌")
                    print(f"   Available entities: {list(selector.entity_map.keys())[:10]}...")
        
        # Summary
        print(f"\n📊 INHERITANCE DATA TRANSFER SUMMARY")
        print("=" * 60)
        print(f"Raw IR entities with inheritance: {len(entities_with_inheritance)}")
        print(f"ContextEntity objects with inheritance: {len(entities_with_inheritance_in_selector)}")
        
        if len(entities_with_inheritance) > 0 and len(entities_with_inheritance_in_selector) == 0:
            print("🚨 ISSUE: Inheritance data exists in IR but not in ContextEntity objects!")
            print("This indicates a problem in the IntelligentContextSelector._build_entity_map method.")
            return False
        elif len(entities_with_inheritance) == len(entities_with_inheritance_in_selector):
            print("✅ Inheritance data transfer is working correctly!")
            return True
        else:
            print("⚠️  Partial inheritance data transfer - some data may be lost.")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_inheritance_data_transfer()
    
    if success:
        print("\n✅ Inheritance data transfer test completed successfully!")
    else:
        print("\n❌ Inheritance data transfer test failed.")
        print("The issue is in the data flow from IR entities to ContextEntity objects.")
