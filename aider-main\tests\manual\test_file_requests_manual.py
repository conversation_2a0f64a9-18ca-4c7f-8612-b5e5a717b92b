"""
Manual test for the file request functionality.

This script creates a simple repository with a few files and then simulates
an AI response with file requests in both the old and new formats.

Usage:
    python tests/manual/test_file_requests_manual.py
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock

# Add the parent directory to the path so we can import aider
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from aider.coders.base_coder import Coder
from aider.io import InputOutput
from aider.models import Model


def create_test_repo():
    """Create a temporary repository with test files."""
    temp_dir = tempfile.mkdtemp()
    print(f"Created temporary directory: {temp_dir}")

    # Create some test files
    test_files = {
        "main.py": "def main():\n    print('Hello, world!')\n    calculator = Calculator()\n    result = calculator.add(1, 2)\n    print(result)",
        "calculator.py": "class Calculator:\n    def add(self, a, b):\n        return a + b\n    def subtract(self, a, b):\n        return a - b",
        "utils.py": "def format_result(result):\n    return f'Result: {result}'",
    }

    for filename, content in test_files.items():
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w') as f:
            f.write(content)

    return temp_dir, test_files


def test_old_format(coder):
    """Test the old file request format."""
    print("\n=== Testing Old Format ===")

    content = """I need to see the calculator file.

{REQUEST_FILE: calculator.py}"""

    cleaned_content, message = coder.process_file_requests(content)

    print(f"Cleaned content:\n{cleaned_content}")
    print(f"Message: {message}")


def test_new_format_single(coder):
    """Test the new file request format for a single file."""
    print("\n=== Testing New Format (Single File) ===")

    content = """I need to see the calculator file.

{REQUEST_FILE:
  "path": "calculator.py",
  "reason": "I need to see the implementation of the add method"
}"""

    # Debug the regex pattern
    import re
    single_file_pattern = r'\{REQUEST_FILE:\s*(.*?)\}'
    matches = re.findall(single_file_pattern, content, re.DOTALL)
    print(f"Regex matches: {matches}")

    if matches:
        req = matches[0]
        print(f"Request content: {req}")

        # Try to manually create a JSON object
        import json
        try:
            # Add curly braces to make it a valid JSON object
            json_str = '{' + req + '}'
            print(f"JSON string: {json_str}")

            req_json = json.loads(json_str)
            print(f"Parsed JSON: {req_json}")

            if isinstance(req_json, dict):
                file_path = req_json.get('path', '').strip()
                reason = req_json.get('reason', '')
                print(f"File path: {file_path}")
                print(f"Reason: {reason}")
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")

    # Process the request
    cleaned_content, message = coder.process_file_requests(content)

    print(f"Cleaned content:\n{cleaned_content}")
    print(f"Message: {message}")


def test_new_format_multiple(coder):
    """Test the new file request format for multiple files."""
    print("\n=== Testing New Format (Multiple Files) ===")

    content = """I need to see the calculator and utils files.

{REQUEST_FILES: [
  {
    "path": "calculator.py",
    "reason": "To understand the add and subtract methods"
  },
  {
    "path": "utils.py",
    "reason": "To see the format_result function"
  }
]}"""

    # Test the regex pattern for cleaning the content
    import re
    print("\nTesting regex pattern for cleaning the content:")

    # Try a more specific pattern for multiple files
    multi_file_pattern_block = r'\{REQUEST_FILES:.*?\]}'

    # Try to clean the content
    cleaned_content = content
    cleaned_content = re.sub(multi_file_pattern_block, '', cleaned_content, flags=re.DOTALL)

    print(f"Cleaned content using regex:\n{cleaned_content}")

    # Try a direct approach with regex to extract paths and reasons
    import re
    path_matches = re.findall(r'"path"\s*:\s*"([^"]+)"', content)
    reason_matches = re.findall(r'"reason"\s*:\s*"([^"]+)"', content)

    print(f"Path matches: {path_matches}")
    print(f"Reason matches: {reason_matches}")

    # Manually add the files
    for file_path in path_matches:
        print(f"Adding file: {file_path}")
        coder.add_requested_file(file_path)

    # Process the request
    cleaned_content, message = coder.process_file_requests(content)

    print(f"Cleaned content:\n{cleaned_content}")
    print(f"Message: {message}")


def main():
    """Run the manual tests."""
    # Create a test repository
    temp_dir, test_files = create_test_repo()

    try:
        # Initialize the IO and model
        io = InputOutput()

        # Create a simple model for testing
        class TestModel(Model):
            def __init__(self):
                self.name = "test-model"
                self.use_repo_map = True
                self.streaming = True
                self.edit_format = "whole"
                self.cache_control = False
                self.weak_model = None
                self.max_chat_history_tokens = 1000
                self.reasoning_tag = "reasoning"
                self.system_prompt_prefix = ""
                self.examples_as_sys_msg = False
                self.use_system_prompt = True
                self.reminder = "sys"
                self.lazy = False
                self.overeager = False
                self.info = {"max_input_tokens": 4000}

            def commit_message_models(self):
                return []

            def token_count(self, text):
                return len(text.split())

        model = TestModel()

        # Create a simple mock coder for testing
        coder = MagicMock(spec=Coder)
        coder.root = temp_dir
        coder.get_all_relative_files = lambda: list(test_files.keys())

        # Mock the add_requested_file method
        def mock_add_requested_file(file_path, is_additional=False, reason=""):
            message = f"Adding file: {file_path}"
            if is_additional:
                message += " (additional)"
            if reason:
                message += f" - Reason: {reason}"
            print(message)
            return True

        coder.add_requested_file = mock_add_requested_file

        # Mock the find_additional_files method
        def mock_find_additional_files(file_path, reason):
            print(f"Finding additional files for: {file_path} (reason: {reason})")
            if "add" in reason.lower() and file_path == "calculator.py":
                return ["main.py"]  # main.py uses the add method
            return []

        coder.find_additional_files = mock_find_additional_files

        # Use the real process_file_requests method
        from aider.coders.base_coder import Coder as RealCoder
        coder.process_file_requests = RealCoder.process_file_requests.__get__(coder, RealCoder)

        # Set the root directory
        coder.root = temp_dir

        # Add the informative_only attribute
        coder.informative_only = True

        # Run the tests
        test_old_format(coder)
        test_new_format_single(coder)
        test_new_format_multiple(coder)

    finally:
        # Clean up the temporary directory
        shutil.rmtree(temp_dir)
        print(f"\nRemoved temporary directory: {temp_dir}")


if __name__ == "__main__":
    main()
