#!/usr/bin/env python3
"""
Test to detect ANY hidden ways repository context might be reaching the LLM.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_all_possible_context_leaks():
    """Test ALL possible ways repository context could leak to the LLM."""
    print("🔍 Hidden Repository Context Detection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test 1: Check ALL prompt attributes for embedded repository content
        print("\n🧪 Test 1: Checking ALL prompt attributes")
        print("-" * 60)
        
        prompt_attrs = [
            'main_system',
            'system_reminder', 
            'file_access_reminder',
            'repo_content_prefix',
            'reality_check_prompt',
            'files_content_prefix',
            'files_no_full_files_with_repo_map',
            'files_no_full_files_with_repo_map_reply',
            'files_content_gpt_edits',
            'files_content_gpt_no_edits',
        ]
        
        hidden_context_found = False
        
        for attr_name in prompt_attrs:
            if hasattr(coder.gpt_prompts, attr_name):
                attr_value = getattr(coder.gpt_prompts, attr_name)
                if attr_value:
                    content_length = len(str(attr_value))
                    print(f"   {attr_name}: {content_length} characters")
                    
                    # Check for repository content indicators
                    content_str = str(attr_value)
                    repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ', 'from ']
                    repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in content_str)
                    
                    if content_length > 5000 or repo_indicator_count > 20:
                        print(f"     ❌ SUSPICIOUS CONTENT! ({repo_indicator_count} indicators)")
                        print(f"     Preview: {content_str[:200]}...")
                        hidden_context_found = True
                    else:
                        print(f"     ✅ Clean ({repo_indicator_count} indicators)")
        
        # Test 2: Check if repository map is embedded in system prompts
        print("\n🧪 Test 2: Checking system prompt construction")
        print("-" * 60)
        
        try:
            # Try to get the formatted system prompt
            system_prompt = coder.fmt_system_prompt()
            if system_prompt:
                system_length = len(system_prompt)
                print(f"   System prompt length: {system_length} characters")
                
                # Check for repository content
                repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ']
                repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in system_prompt)
                
                if system_length > 10000 or repo_indicator_count > 50:
                    print(f"   ❌ REPOSITORY CONTENT IN SYSTEM PROMPT! ({repo_indicator_count} indicators)")
                    print(f"   Preview: {system_prompt[:300]}...")
                    hidden_context_found = True
                else:
                    print(f"   ✅ System prompt clean ({repo_indicator_count} indicators)")
            else:
                print("   ✅ System prompt is None/empty")
                
        except Exception as e:
            print(f"   ⚠️  Error getting system prompt: {e}")
        
        # Test 3: Check repository map generation and caching
        print("\n🧪 Test 3: Checking repository map generation")
        print("-" * 60)
        
        # Check if repository map is being generated
        repo_map_result = coder.get_repo_map()
        if repo_map_result:
            print(f"   ❌ REPOSITORY MAP IS BEING GENERATED: {len(repo_map_result)} characters")
            print(f"   Preview: {repo_map_result[:200]}...")
            hidden_context_found = True
        else:
            print("   ✅ get_repo_map() returns None")
        
        # Check if repository map is cached somewhere
        if hasattr(coder, 'repo_map') and coder.repo_map:
            print("   🔍 Checking RepoMap instance...")
            
            # Check if there's cached content
            if hasattr(coder.repo_map, 'cache') and coder.repo_map.cache:
                print(f"   ⚠️  RepoMap has cache: {len(coder.repo_map.cache)} items")
                for key, value in list(coder.repo_map.cache.items())[:3]:  # Check first 3 items
                    if value and len(str(value)) > 1000:
                        print(f"     ❌ LARGE CACHED CONTENT: {len(str(value))} characters")
                        hidden_context_found = True
            else:
                print("   ✅ No RepoMap cache")
        
        # Test 4: Check if context is embedded in model or IO
        print("\n🧪 Test 4: Checking model and IO for embedded context")
        print("-" * 60)
        
        # Check if model has embedded context
        if hasattr(model, 'context') or hasattr(model, 'system_prompt'):
            print("   ⚠️  Model has context/system_prompt attributes")
        else:
            print("   ✅ Model has no embedded context")
        
        # Check if IO has embedded context
        if hasattr(io, 'context') or hasattr(io, 'repo_content'):
            print("   ⚠️  IO has context/repo_content attributes")
        else:
            print("   ✅ IO has no embedded context")
        
        return not hidden_context_found
        
    except Exception as e:
        print(f"❌ Error in hidden context detection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_construction_deep_dive():
    """Deep dive into how messages are constructed to find hidden repository content."""
    print("\n🔍 Message Construction Deep Dive")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Test all message construction methods
        message_methods = [
            ("get_system_message", lambda: coder.get_system_message()),
            ("get_repo_messages", lambda: coder.get_repo_messages()),
            ("get_readonly_files_messages", lambda: coder.get_readonly_files_messages()),
        ]
        
        hidden_content_found = False
        
        for method_name, method_call in message_methods:
            print(f"\n🧪 Testing {method_name}:")
            try:
                result = method_call()
                
                if result is None:
                    print(f"   ✅ {method_name} returns None")
                elif isinstance(result, list):
                    total_chars = 0
                    for i, msg in enumerate(result):
                        if isinstance(msg, dict) and 'content' in msg:
                            content = msg['content']
                            content_length = len(content)
                            total_chars += content_length
                            
                            print(f"   Message {i+1}: {content_length} characters")
                            
                            if content_length > 5000:
                                print(f"     ❌ LARGE CONTENT DETECTED!")
                                print(f"     Preview: {content[:200]}...")
                                hidden_content_found = True
                    
                    print(f"   Total characters: {total_chars}")
                    if total_chars > 10000:
                        print(f"   ❌ EXCESSIVE TOTAL CONTENT!")
                        hidden_content_found = True
                        
                elif isinstance(result, str):
                    content_length = len(result)
                    print(f"   String result: {content_length} characters")
                    
                    if content_length > 5000:
                        print(f"   ❌ LARGE STRING CONTENT!")
                        print(f"   Preview: {result[:200]}...")
                        hidden_content_found = True
                else:
                    print(f"   ✅ {method_name} returns {type(result)}")
                    
            except Exception as e:
                print(f"   ❌ {method_name} failed: {e}")
        
        return not hidden_content_found
        
    except Exception as e:
        print(f"❌ Error in message construction deep dive: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive hidden context detection tests."""
    print("🚀 Hidden Repository Context Detection")
    print("=" * 100)
    
    tests = [
        ("All Possible Context Leaks", test_all_possible_context_leaks),
        ("Message Construction Deep Dive", test_message_construction_deep_dive),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 HIDDEN REPOSITORY CONTEXT DETECTION SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 NO HIDDEN REPOSITORY CONTEXT DETECTED!")
        print("\n📋 Confirmed:")
        print("  ✅ No repository content in prompts")
        print("  ✅ No repository content in system prompts")
        print("  ✅ No repository content in message construction")
        print("  ✅ No cached repository content")
        print("\n🎯 The LLM fabrication was due to hallucination, not hidden context")
    else:
        print("⚠️  HIDDEN REPOSITORY CONTEXT DETECTED!")
        print("   Repository content is being embedded somewhere in the system")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
