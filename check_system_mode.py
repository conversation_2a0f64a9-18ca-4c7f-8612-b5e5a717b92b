#!/usr/bin/env python3
"""
Test script to verify which mode the system is running in and whether repo_content_prefix is being sent to the LLM.
"""

import os
import sys

def check_smart_map_request_availability():
    """Check if SMART_MAP_REQUEST_AVAILABLE is True or False."""
    print("🔍 Checking SMART_MAP_REQUEST_AVAILABLE...")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_coder import SMART_MAP_REQUEST_AVAILABLE
        
        print(f"SMART_MAP_REQUEST_AVAILABLE: {SMART_MAP_REQUEST_AVAILABLE}")
        
        if SMART_MAP_REQUEST_AVAILABLE:
            print("✅ Smart Map Request System is ACTIVE")
            print("   → repo_content_prefix is NOT sent to LLM")
            print("   → LLM gets smart_map_request_user_prompt and smart_map_request_assistant_reply")
            return True
        else:
            print("❌ Smart Map Request System is NOT available")
            print("   → repo_content_prefix IS sent to LLM with repository map")
            print("   → LLM gets legacy repository map behavior")
            return False
        
    except Exception as e:
        print(f"❌ Error checking SMART_MAP_REQUEST_AVAILABLE: {e}")
        return None

def check_repo_messages():
    """Check what messages are actually sent to the LLM."""
    print("\n🔍 Checking actual repo messages sent to LLM...")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            edit_format="informative",  # Fix: specify valid edit format
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )
        
        # Get the repo messages
        repo_messages = coder.get_repo_messages()
        
        print(f"Number of repo messages: {len(repo_messages)}")
        
        for i, msg in enumerate(repo_messages):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            print(f"\n--- Message {i+1} ({role}) ---")
            print(f"Length: {len(content)} characters")
            
            # Check for repo_content_prefix content
            if "🎮 Repository Map:" in content:
                print("✅ Found repo_content_prefix content!")
                print("   → This means repo_content_prefix IS being sent to LLM")
            elif "🎮 **GAME REMINDER**" in content:
                print("✅ Found smart_map_request content!")
                print("   → This means Smart Map Request system is active")
            
            # Show preview
            preview = content[:200].replace('\n', '\\n')
            print(f"Preview: {preview}...")
        
        return repo_messages
        
    except Exception as e:
        print(f"❌ Error checking repo messages: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_repo_content_prefix():
    """Check the current repo_content_prefix content."""
    print("\n🔍 Checking repo_content_prefix content...")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        repo_content_prefix = prompts.repo_content_prefix
        
        print(f"repo_content_prefix length: {len(repo_content_prefix)} characters")
        print(f"repo_content_prefix content:")
        print("-" * 40)
        print(repo_content_prefix)
        print("-" * 40)
        
        return repo_content_prefix
        
    except Exception as e:
        print(f"❌ Error checking repo_content_prefix: {e}")
        return None

def main():
    """Main test function."""
    print("🧪 SYSTEM MODE VERIFICATION TEST")
    print("=" * 60)
    
    # Test 1: Check SMART_MAP_REQUEST_AVAILABLE
    smart_map_available = check_smart_map_request_availability()
    
    # Test 2: Check actual repo messages
    repo_messages = check_repo_messages()
    
    # Test 3: Check repo_content_prefix content
    repo_content_prefix = check_repo_content_prefix()
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 60)
    
    if smart_map_available is True:
        print("🎯 CONCLUSION: Smart Map Request System is ACTIVE")
        print("   → repo_content_prefix is NOT sent to LLM by default")
        print("   → LLM starts with game prompts and uses MAP_REQUEST to explore")
    elif smart_map_available is False:
        print("🎯 CONCLUSION: Legacy Mode is ACTIVE")
        print("   → repo_content_prefix IS sent to LLM with repository map")
        print("   → LLM gets traditional repository overview")
    else:
        print("❓ CONCLUSION: Unable to determine system mode")
    
    if repo_messages:
        print(f"   → {len(repo_messages)} messages are sent to LLM initially")

if __name__ == "__main__":
    main()
