"""
Mid-Level Intermediate Representation (IR) Generator

A modular pipeline for analyzing Python codebases and generating comprehensive
intermediate representations with detailed metadata, dependencies, and risk analysis.

This package provides:
- Modular analysis pipeline
- Rich entity extraction
- Dependency mapping
- Risk assessment
- Extensible architecture
"""

from .main import MidLevelIRPipeline
from .ir_context import IRContext, ModuleInfo, EntityInfo
from .ir_builder import IRBuilder

__version__ = "2.0.0"
__author__ = "Aider Integration Team"

__all__ = [
    "MidLevelIRPipeline",
    "IRContext", 
    "ModuleInfo",
    "EntityInfo",
    "IRBuilder"
]
