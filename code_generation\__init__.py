"""
Code Generation with Architectural Awareness

A comprehensive system for generating code that respects existing architecture patterns,
coding styles, and dependency relationships. Built on top of the Mid-Level IR pipeline
and Intelligent Context Selection Engine.

This package provides:
- Architectural pattern analysis and recognition
- Context-aware code template generation
- Dependency integration management
- Quality assurance and validation
- Incremental code enhancement capabilities
"""

from .architectural_pattern_analyzer import ArchitecturalPatternAnalyzer
from .code_template_engine import CodeTemplateEngine
from .dependency_integration_manager import DependencyIntegrationManager
from .quality_assurance_engine import QualityAssuranceEngine
from .incremental_code_builder import IncrementalCodeBuilder
from .code_generation_pipeline import CodeGenerationPipeline

__version__ = "1.0.0"
__author__ = "Aider Integration Team"

__all__ = [
    "ArchitecturalPatternAnalyzer",
    "CodeTemplateEngine", 
    "DependencyIntegrationManager",
    "QualityAssuranceEngine",
    "IncrementalCodeBuilder",
    "CodeGenerationPipeline"
]
