#!/usr/bin/env python3
"""
Test script to verify the Smart Map Request System implementation.
"""

import os
import sys
import json
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_smart_map_request_handler():
    """Test the SmartMapRequestHandler directly."""
    print("🧪 Testing Smart Map Request Handler")
    print("=" * 60)

    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )

        # Create handler
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )

        print("✅ SmartMapRequestHandler created successfully")

        # Test map request
        test_request = {
            "keywords": ["repository", "mapping", "repomap"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        print(f"\n🔍 Testing map request: {test_request}")

        focused_map = handler.handle_map_request(test_request)

        print(f"\n📋 Focused Map Result:")
        print("-" * 40)
        print(focused_map[:500] + "..." if len(focused_map) > 500 else focused_map)
        print("-" * 40)

        return True

    except Exception as e:
        print(f"❌ Error testing SmartMapRequestHandler: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_search_fallback():
    """Test the updated smart_search_fallback function."""
    print("\n🧪 Testing Smart Search Fallback")
    print("=" * 60)

    try:
        from aider.search_repo import smart_search_fallback
        from aider.io import InputOutput

        io = InputOutput()

        # Test search
        query = "repository mapping repomap"
        print(f"🔍 Testing search query: '{query}'")

        results = smart_search_fallback("aider-main", query, io)

        print(f"\n📋 Search Results:")
        print("-" * 40)

        if results:
            for i, result in enumerate(results[:5], 1):
                print(f"{i}. {result['file']} (score: {result['relevance_score']:.2f})")
        else:
            print("No results found")

        print("-" * 40)

        return True

    except Exception as e:
        print(f"❌ Error testing smart_search_fallback: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_map_request_integration():
    """Test MAP_REQUEST integration with base_coder."""
    print("\n🧪 Testing MAP_REQUEST Integration")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create a GitRepo instance
        repo = GitRepo(io, "aider-main", "aider-main")

        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo  # Pass GitRepo object
        )

        print("✅ Coder instance created successfully")

        # Test that LLM starts with zero repository map
        repo_content = coder.get_repo_map()
        if repo_content is None:
            print("✅ LLM starts with zero repository map (Smart Map Request System active)")
        else:
            print("❌ LLM still gets default repository map")
            print(f"Repository content preview: {repo_content[:200]}...")

        # Test MAP_REQUEST processing
        test_content = '''I need to understand the repository structure.

{MAP_REQUEST: {"keywords": ["repository", "mapping"], "type": "implementation", "max_results": 5}}'''

        user_message = "How does aider handle repository mapping?"

        print(f"\n🔍 Testing MAP_REQUEST processing...")

        cleaned_content, map_prompt = coder.process_map_requests(test_content, user_message)

        if map_prompt:
            print("✅ MAP_REQUEST detected and processed")
            print(f"📝 Cleaned content: {cleaned_content[:100]}...")
            print(f"📋 Map prompt preview: {map_prompt[:200]}...")
        else:
            print("❌ MAP_REQUEST not detected or processed")

        # Test repo messages
        repo_messages = coder.get_repo_messages()
        print(f"\n📋 Repo messages: {len(repo_messages)} messages")
        for i, msg in enumerate(repo_messages):
            print(f"  {i+1}. {msg['role']}: {msg['content'][:100]}...")

        return map_prompt is not None and repo_content is None

    except Exception as e:
        print(f"❌ Error testing MAP_REQUEST integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_instructions():
    """Test that the prompt instructions include MAP_REQUEST."""
    print("\n🧪 Testing Prompt Instructions")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check file_access_reminder
        if hasattr(prompts, 'file_access_reminder'):
            if "MAP_REQUEST" in prompts.file_access_reminder:
                print("✅ MAP_REQUEST found in file_access_reminder")
            else:
                print("❌ MAP_REQUEST not found in file_access_reminder")

        # Check repo_content_prefix
        if hasattr(prompts, 'repo_content_prefix'):
            if "MAP_REQUEST" in prompts.repo_content_prefix:
                print("✅ MAP_REQUEST found in repo_content_prefix")
            else:
                print("❌ MAP_REQUEST not found in repo_content_prefix")

        return True

    except Exception as e:
        print(f"❌ Error testing prompt instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Smart Map Request System Implementation Test")
    print("=" * 80)

    tests = [
        ("Smart Map Request Handler", test_smart_map_request_handler),
        ("Smart Search Fallback", test_smart_search_fallback),
        ("MAP_REQUEST Integration", test_map_request_integration),
        ("Prompt Instructions", test_prompt_instructions),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Smart Map Request System is implemented correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
