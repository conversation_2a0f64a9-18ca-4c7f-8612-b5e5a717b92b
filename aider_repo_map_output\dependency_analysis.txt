# Dependency Analysis

## Top Central Files
These files are referenced by many other files and are central to the codebase:

- aider\io.py: referenced by 84 files
- tests\scrape\test_playwright_disable.py: referenced by 79 files
- aider\coders\search_replace.py: referenced by 68 files
- aider\models.py: referenced by 68 files
- aider\commands.py: referenced by 63 files
- aider\linter.py: referenced by 61 files
- aider\waiting.py: referenced by 61 files
- aider\watch.py: referenced by 60 files
- aider\history.py: referenced by 60 files
- aider\copypaste.py: referenced by 59 files
- tests\manual\test_file_requests_manual.py: referenced by 59 files
- aider\scrape.py: referenced by 59 files
- benchmark\over_time.py: referenced by 58 files
- benchmark\refactor_tools.py: referenced by 57 files
- tests\fixtures\sample-code-base\sample.py: referenced by 57 files
- aider\coders\editblock_coder.py: referenced by 55 files
- scripts\blame.py: referenced by 55 files
- benchmark\rungrid.py: referenced by 55 files
- benchmark\benchmark.py: referenced by 54 files
- aider\diffs.py: referenced by 54 files

## Strong Dependencies
These are files with strong dependencies (3+ references) to other files:

### aider\analytics.py strongly depends on:
- aider\io.py (3 references)

### aider\coders\architect_coder.py strongly depends on:
- aider\coders\base_coder.py (4 references)

### aider\coders\base_coder.py strongly depends on:
- aider\io.py (19 references)
- tests\scrape\test_playwright_disable.py (14 references)
- aider\repo.py (10 references)
- aider\utils.py (10 references)
- aider\commands.py (9 references)
- aider\models.py (8 references)
- aider\file_request_validator.py (6 references)
- aider\linter.py (6 references)
- tests\basic\test_onboarding.py (6 references)
- aider\history.py (5 references)
- aider\repomap.py (5 references)
- aider\gui.py (5 references)
- aider\coders\chat_chunks.py (4 references)
- aider\exceptions.py (4 references)
- aider\waiting.py (4 references)
- aider\coders\wholefile_func_coder.py (3 references)
- aider\copypaste.py (3 references)
- aider\reasoning_tags.py (3 references)
- tests\manual\test_file_requests_manual.py (3 references)
- aider\analytics.py (3 references)
- aider\coders\single_wholefile_func_coder.py (3 references)
- aider\watch.py (3 references)

### aider\coders\base_prompts.py strongly depends on:
- aider\coders\shell.py (3 references)

### aider\coders\context_coder.py strongly depends on:
- aider\coders\base_coder.py (5 references)
- tests\scrape\test_playwright_disable.py (3 references)

### aider\coders\editblock_coder.py strongly depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)

### aider\coders\editblock_func_coder.py strongly depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\coders\base_coder.py (3 references)

### aider\coders\editor_diff_fenced_prompts.py strongly depends on:
- aider\coders\shell.py (3 references)

### aider\coders\editor_editblock_prompts.py strongly depends on:
- aider\coders\shell.py (3 references)

### aider\coders\patch_coder.py strongly depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (4 references)

### aider\coders\search_replace.py strongly depends on:
- aider\io.py (3 references)

### aider\coders\single_wholefile_func_coder.py strongly depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)

### aider\coders\udiff_coder.py strongly depends on:
- aider\coders\search_replace.py (3 references)

### aider\coders\wholefile_coder.py strongly depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)

### aider\coders\wholefile_func_coder.py strongly depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)

### aider\commands.py strongly depends on:
- aider\coders\base_coder.py (15 references)
- tests\scrape\test_playwright_disable.py (14 references)
- aider\repo.py (12 references)
- aider\models.py (11 references)
- aider\io.py (11 references)
- tests\basic\test_onboarding.py (5 references)
- aider\linter.py (4 references)
- aider\scrape.py (4 references)
- aider\repomap.py (3 references)
- aider\help.py (3 references)
- aider\gui.py (3 references)

### aider\copypaste.py strongly depends on:
- aider\io.py (3 references)
- aider\waiting.py (3 references)
- aider\watch.py (3 references)

### aider\file_request_validator.py strongly depends on:
- aider\search_repo.py (3 references)

### aider\gui.py strongly depends on:
- tests\scrape\test_playwright_disable.py (7 references)
- aider\coders\base_coder.py (6 references)
- aider\io.py (6 references)
- tests\basic\test_onboarding.py (3 references)
- aider\scrape.py (3 references)

### aider\history.py strongly depends on:
- aider\models.py (4 references)

### aider\io.py strongly depends on:
- tests\scrape\test_playwright_disable.py (6 references)
- aider\commands.py (5 references)
- tests\basic\test_onboarding.py (4 references)
- aider\gui.py (3 references)
- aider\watch.py (3 references)

### aider\linter.py strongly depends on:
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (3 references)

### aider\main.py strongly depends on:
- aider\io.py (11 references)
- aider\models.py (10 references)
- tests\scrape\test_playwright_disable.py (7 references)
- aider\commands.py (7 references)
- tests\basic\test_onboarding.py (6 references)
- aider\coders\base_coder.py (6 references)
- aider\analytics.py (6 references)
- aider\gui.py (4 references)
- aider\onboarding.py (4 references)
- aider\copypaste.py (3 references)
- aider\utils.py (3 references)
- aider\versioncheck.py (3 references)
- aider\watch.py (3 references)

### aider\models.py strongly depends on:
- aider\io.py (5 references)
- aider\openrouter.py (5 references)
- tests\scrape\test_playwright_disable.py (4 references)
- aider\exceptions.py (3 references)
- aider\gui.py (3 references)

### aider\onboarding.py strongly depends on:
- tests\basic\test_onboarding.py (7 references)
- aider\io.py (7 references)
- tests\scrape\test_playwright_disable.py (6 references)
- aider\gui.py (3 references)

### aider\openrouter.py strongly depends on:
- aider\io.py (3 references)

### aider\repo.py strongly depends on:
- tests\scrape\test_playwright_disable.py (5 references)
- aider\io.py (4 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)

### aider\repomap.py strongly depends on:
- aider\io.py (6 references)
- tests\scrape\test_playwright_disable.py (6 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)
- aider\waiting.py (3 references)

### aider\scrape.py strongly depends on:
- tests\scrape\test_playwright_disable.py (5 references)
- aider\io.py (4 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)

### aider\search_repo.py strongly depends on:
- aider\gui.py (3 references)

### aider\utils.py strongly depends on:
- aider\io.py (6 references)
- tests\scrape\test_playwright_disable.py (6 references)
- aider\waiting.py (6 references)
- aider\gui.py (5 references)
- tests\basic\test_onboarding.py (4 references)

### aider\versioncheck.py strongly depends on:
- tests\basic\test_onboarding.py (3 references)
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\gui.py (3 references)

### aider\waiting.py strongly depends on:
- aider\copypaste.py (3 references)
- aider\watch.py (3 references)

### aider\watch.py strongly depends on:
- aider\io.py (5 references)
- tests\scrape\test_playwright_disable.py (5 references)
- aider\copypaste.py (3 references)
- aider\waiting.py (3 references)

### benchmark\benchmark.py strongly depends on:
- aider\io.py (6 references)
- aider\models.py (5 references)
- aider\coders\base_coder.py (4 references)

### tests\basic\test_analytics.py strongly depends on:
- aider\analytics.py (7 references)

### tests\basic\test_coder.py strongly depends on:
- aider\coders\base_coder.py (16 references)
- aider\io.py (3 references)
- aider\repo.py (3 references)

### tests\basic\test_commands.py strongly depends on:
- aider\commands.py (23 references)
- aider\io.py (5 references)
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\utils.py (4 references)
- aider\repo.py (3 references)

### tests\basic\test_editblock.py strongly depends on:
- aider\coders\editblock_coder.py (5 references)

### tests\basic\test_editor.py strongly depends on:
- aider\editor.py (5 references)

### tests\basic\test_exceptions.py strongly depends on:
- aider\exceptions.py (3 references)

### tests\basic\test_file_requests.py strongly depends on:
- aider\coders\base_coder.py (5 references)

### tests\basic\test_history.py strongly depends on:
- aider\history.py (5 references)

### tests\basic\test_io.py strongly depends on:
- aider\io.py (14 references)

### tests\basic\test_linter.py strongly depends on:
- aider\linter.py (5 references)

### tests\basic\test_main.py strongly depends on:
- aider\main.py (4 references)
- aider\utils.py (4 references)
- aider\io.py (4 references)
- aider\copypaste.py (3 references)
- aider\waiting.py (3 references)
- aider\coders\base_coder.py (3 references)
- aider\watch.py (3 references)

### tests\basic\test_model_info_manager.py strongly depends on:
- aider\models.py (4 references)

### tests\basic\test_models.py strongly depends on:
- aider\models.py (12 references)

### tests\basic\test_onboarding.py strongly depends on:
- aider\onboarding.py (9 references)

### tests\basic\test_reasoning.py strongly depends on:
- aider\coders\base_coder.py (4 references)
- aider\models.py (3 references)

### tests\basic\test_repo.py strongly depends on:
- aider\repo.py (7 references)

### tests\basic\test_sendchat.py strongly depends on:
- aider\models.py (3 references)

### tests\basic\test_voice.py strongly depends on:
- aider\voice.py (5 references)

### tests\basic\test_watch.py strongly depends on:
- aider\watch.py (5 references)

### tests\basic\test_wholefile.py strongly depends on:
- aider\coders\base_coder.py (6 references)
- aider\io.py (3 references)
- aider\coders\wholefile_coder.py (3 references)

### tests\help\test_help.py strongly depends on:
- aider\commands.py (3 references)
- aider\help.py (3 references)

### tests\scrape\test_scrape.py strongly depends on:
- aider\scrape.py (6 references)
- aider\commands.py (3 references)


## Class Dependencies
These are dependencies specifically for files likely containing classes:

### aider\coders\__init__.py depends on:
- aider\coders\editor_diff_fenced_coder.py (1 references)
- aider\coders\udiff_simple.py (1 references)
- aider\coders\patch_coder.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- aider\coders\wholefile_coder.py (1 references)
- aider\coders\architect_coder.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\coders\editor_editblock_coder.py (1 references)
- aider\coders\editor_whole_coder.py (1 references)
- aider\llm.py (1 references)

### aider\coders\architect_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\coders\architect_prompts.py (1 references)
- scripts\blame.py (1 references)
- aider\commands.py (1 references)
- tests\basic\test_onboarding.py (1 references)
- aider\mdstream.py (1 references)

### aider\coders\architect_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\ask_coder.py depends on:
- aider\coders\ask_prompts.py (1 references)

### aider\coders\ask_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\base_coder.py depends on:
- aider\io.py (19 references)
- tests\scrape\test_playwright_disable.py (14 references)
- aider\repo.py (10 references)
- aider\utils.py (10 references)
- aider\commands.py (9 references)
- aider\models.py (8 references)
- aider\file_request_validator.py (6 references)
- aider\linter.py (6 references)
- tests\basic\test_onboarding.py (6 references)
- aider\history.py (5 references)

### aider\coders\base_prompts.py depends on:
- aider\coders\shell.py (3 references)

### aider\coders\context_coder.py depends on:
- aider\coders\base_coder.py (5 references)
- tests\scrape\test_playwright_disable.py (3 references)
- tests\basic\test_watch.py (2 references)
- aider\io.py (2 references)
- aider\repomap.py (2 references)
- aider\linter.py (2 references)
- aider\mdstream.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\search_replace.py (1 references)

### aider\coders\context_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\editblock_coder.py depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)
- aider\coders\udiff_coder.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\linter.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\coders\patch_coder.py (1 references)
- scripts\recording_audio.py (1 references)

### aider\coders\editblock_fenced_coder.py depends on:
- aider\coders\editblock_fenced_prompts.py (1 references)

### aider\coders\editblock_func_coder.py depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\coders\base_coder.py (3 references)
- aider\coders\search_replace.py (2 references)
- aider\gui.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\editblock_func_prompts.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)

### aider\coders\editblock_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\editor_diff_fenced_coder.py depends on:
- aider\coders\editor_diff_fenced_prompts.py (1 references)

### aider\coders\editor_diff_fenced_prompts.py depends on:
- aider\coders\shell.py (3 references)
- aider\coders\editblock_fenced_prompts.py (1 references)

### aider\coders\editor_editblock_coder.py depends on:
- aider\coders\editor_editblock_prompts.py (1 references)

### aider\coders\editor_editblock_prompts.py depends on:
- aider\coders\shell.py (3 references)
- aider\coders\editblock_prompts.py (1 references)

### aider\coders\editor_whole_coder.py depends on:
- aider\coders\editor_whole_prompts.py (1 references)

### aider\coders\editor_whole_prompts.py depends on:
- aider\coders\wholefile_prompts.py (1 references)

### aider\coders\help_coder.py depends on:
- aider\coders\help_prompts.py (1 references)

### aider\coders\help_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\patch_coder.py depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (4 references)
- tests\basic\test_onboarding.py (2 references)
- aider\gui.py (2 references)
- aider\coders\patch_prompts.py (1 references)
- aider\repo.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\base_coder.py (1 references)

### aider\coders\search_replace.py depends on:
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)

### aider\coders\single_wholefile_func_coder.py depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)
- aider\repo.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\single_wholefile_func_prompts.py (1 references)
- aider\linter.py (1 references)

### aider\coders\single_wholefile_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\coders\udiff_coder.py depends on:
- aider\coders\search_replace.py (3 references)
- aider\io.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\repo.py (1 references)
- aider\coders\base_coder.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\coders\udiff_prompts.py (1 references)

### aider\coders\udiff_simple.py depends on:
- aider\coders\udiff_simple_prompts.py (1 references)

### aider\coders\udiff_simple_prompts.py depends on:
- aider\coders\udiff_prompts.py (1 references)

### aider\coders\wholefile_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\io.py (2 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- aider\coders\patch_coder.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- aider\coders\wholefile_prompts.py (1 references)
- aider\repo.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)

### aider\coders\wholefile_func_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\repo.py (2 references)
- aider\coders\single_wholefile_func_coder.py (2 references)
- aider\io.py (2 references)
- aider\coders\search_replace.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\wholefile_func_prompts.py (1 references)
- aider\linter.py (1 references)

### aider\coders\wholefile_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

### aider\prompts.py depends on:
- aider\history.py (1 references)

### tests\basic\test_coder.py depends on:
- aider\coders\base_coder.py (16 references)
- aider\io.py (3 references)
- aider\repo.py (3 references)
- aider\coders\context_coder.py (2 references)
- aider\waiting.py (2 references)
- aider\utils.py (2 references)
- aider\watch.py (2 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)

