#!/usr/bin/env python

import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'aider-main')))

# Import the required modules
try:
    from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider.context_request.aider_template_renderer import AiderTemplateRenderer
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)

def test_no_dependencies():
    """Test the case where a method has no dependencies."""
    print("\n=== Testing No Dependencies Case ===")

    # Create a test file with a simple method that has no dependencies
    test_file = "test_simple.py"
    with open(test_file, 'w') as f:
        f.write("""
def simple_function(a: int, b: int) -> int:
    \"\"\"
    A simple function that adds two numbers.

    Args:
        a: First number
        b: Second number

    Returns:
        Sum of a and b
    \"\"\"
    return a + b
""")

    print(f"Created test file: {test_file}")

    try:
        # Create a context request handler
        handler = ContextRequestHandler(project_path=".")

        # Create a context request
        context_request = ContextRequest(
            original_user_query_context="User is asking about the simple_function",
            symbols_of_interest=[
                SymbolRequest(
                    type="function_definition",
                    name="simple_function",
                    file_hint="test_simple.py"
                )
            ],
            reason_for_request="To analyze the implementation of the simple_function"
        )

        # Process the context request
        print("Processing context request...")
        result = handler.process_context_request(context_request)

        # Render the augmented prompt
        renderer = AiderTemplateRenderer()
        prompt = renderer.render_augmented_prompt(
            original_query="How does the simple_function work?",
            repo_overview="",
            extracted_context=result
        )

        print("\n=== Augmented Prompt Content ===")
        print(prompt)

        # Check if the "No dependencies" message appears in the prompt
        if "No direct dependencies were found" in prompt or "No functional dependencies were found" in prompt:
            print("✅ 'No dependencies' message found in prompt")
        else:
            print("❌ 'No dependencies' message not found in prompt")

        # Check if the function was found
        if "simple_function" in prompt:
            print("✅ simple_function found in code context")
        else:
            print("❌ simple_function not found in code context")

    except Exception as e:
        print(f"❌ Error in test: {e}")
    finally:
        # Clean up the test file
        try:
            os.remove(test_file)
            print(f"Removed test file: {test_file}")
        except:
            pass

    print("\n=== Test completed! ===")

if __name__ == "__main__":
    test_no_dependencies()
