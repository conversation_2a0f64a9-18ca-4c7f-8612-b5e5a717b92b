"""
Dependency Integration Manager

Manages dependency analysis and integration for generated code.
Ensures new code properly integrates with existing dependencies.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from mid_level_ir.ir_context import IRContext


@dataclass
class DependencyAnalysis:
    """Analysis of dependencies for code integration."""
    required_imports: List[str]
    available_modules: List[str]
    missing_dependencies: List[str]
    circular_dependencies: List[Tuple[str, str]]
    integration_complexity: float


class DependencyIntegrationManager:
    """
    Manages dependency integration for generated code.
    
    This manager:
    - Analyzes existing dependency patterns
    - Validates dependency requirements
    - Suggests optimal integration strategies
    - Detects potential conflicts
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the dependency integration manager."""
        self.config = config
        self.verbose = config.get('verbose', False)
    
    def analyze_dependencies(self, ir_context: IRContext, 
                           generated_code: str,
                           target_module: Optional[str] = None) -> DependencyAnalysis:
        """
        Analyze dependencies for generated code integration.
        
        Args:
            ir_context: The IR context containing codebase analysis
            generated_code: The generated code to analyze
            target_module: Optional target module for integration
            
        Returns:
            Dependency analysis results
        """
        if self.verbose:
            print("📦 Analyzing dependencies for integration")
        
        # Extract required imports from generated code
        required_imports = self._extract_imports(generated_code)
        
        # Get available modules from IR context
        available_modules = list(ir_context.modules.keys())
        
        # Identify missing dependencies
        missing_dependencies = self._find_missing_dependencies(
            required_imports, available_modules
        )
        
        # Check for circular dependencies
        circular_dependencies = self._detect_circular_dependencies(
            ir_context, target_module, required_imports
        )
        
        # Calculate integration complexity
        integration_complexity = self._calculate_integration_complexity(
            required_imports, missing_dependencies, circular_dependencies
        )
        
        return DependencyAnalysis(
            required_imports=required_imports,
            available_modules=available_modules,
            missing_dependencies=missing_dependencies,
            circular_dependencies=circular_dependencies,
            integration_complexity=integration_complexity
        )
    
    def _extract_imports(self, code: str) -> List[str]:
        """Extract import statements from generated code."""
        imports = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                imports.append(line)
        
        return imports
    
    def _find_missing_dependencies(self, required_imports: List[str],
                                  available_modules: List[str]) -> List[str]:
        """Find dependencies that are not available in the codebase."""
        missing = []
        
        for import_stmt in required_imports:
            if import_stmt.startswith('from '):
                # Extract module name from "from module import ..."
                parts = import_stmt.split()
                if len(parts) >= 2:
                    module = parts[1]
                    if module not in available_modules and not module.startswith('.'):
                        missing.append(module)
            elif import_stmt.startswith('import '):
                # Extract module name from "import module"
                parts = import_stmt.split()
                if len(parts) >= 2:
                    module = parts[1].split('.')[0]  # Get root module
                    if module not in available_modules:
                        missing.append(module)
        
        return list(set(missing))  # Remove duplicates
    
    def _detect_circular_dependencies(self, ir_context: IRContext,
                                    target_module: Optional[str],
                                    required_imports: List[str]) -> List[Tuple[str, str]]:
        """Detect potential circular dependencies."""
        circular = []
        
        if not target_module:
            return circular
        
        # Check if any required imports would create circular dependencies
        for import_stmt in required_imports:
            imported_module = self._extract_module_name(import_stmt)
            if imported_module and imported_module in ir_context.modules:
                # Check if the imported module depends on the target module
                imported_module_info = ir_context.modules[imported_module]
                if target_module in imported_module_info.dependencies:
                    circular.append((target_module, imported_module))
        
        return circular
    
    def _extract_module_name(self, import_stmt: str) -> Optional[str]:
        """Extract module name from import statement."""
        if import_stmt.startswith('from '):
            parts = import_stmt.split()
            if len(parts) >= 2:
                return parts[1]
        elif import_stmt.startswith('import '):
            parts = import_stmt.split()
            if len(parts) >= 2:
                return parts[1].split('.')[0]
        return None
    
    def _calculate_integration_complexity(self, required_imports: List[str],
                                        missing_dependencies: List[str],
                                        circular_dependencies: List[Tuple[str, str]]) -> float:
        """Calculate the complexity of integrating the generated code."""
        complexity = 0.0
        
        # Base complexity from number of imports
        complexity += len(required_imports) * 0.1
        
        # Additional complexity from missing dependencies
        complexity += len(missing_dependencies) * 0.3
        
        # High complexity from circular dependencies
        complexity += len(circular_dependencies) * 0.5
        
        return min(1.0, complexity)
    
    def suggest_integration_strategy(self, analysis: DependencyAnalysis) -> List[str]:
        """Suggest strategies for integrating the code based on dependency analysis."""
        strategies = []
        
        if analysis.missing_dependencies:
            strategies.append(
                f"Install missing dependencies: {', '.join(analysis.missing_dependencies)}"
            )
        
        if analysis.circular_dependencies:
            strategies.append(
                "Refactor to eliminate circular dependencies or use dependency injection"
            )
        
        if analysis.integration_complexity > 0.7:
            strategies.append(
                "Consider breaking down the generated code into smaller, more focused modules"
            )
        elif analysis.integration_complexity < 0.3:
            strategies.append(
                "Integration should be straightforward with minimal dependencies"
            )
        
        return strategies
