#!/usr/bin/env python3
"""
Direct test to verify that the enhanced relevance matching fixes are working.
This test bypasses complex imports and directly tests the core functionality.
"""

import os
import sys
import json

def test_focus_entity_extraction():
    """Test the enhanced focus entity extraction directly."""
    print("🧪 Testing Enhanced Focus Entity Extraction")
    print("=" * 60)
    
    # Add the aider-main directory to path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
    
    try:
        # Import the enhanced method
        from aider.coders.base_coder import BaseCoder
        
        # Create a mock coder instance
        class TestCoder(BaseCoder):
            def __init__(self):
                pass
        
        coder = TestCoder()
        
        test_queries = [
            "Why is my context_selection() function taking so long?",
            "How does the IntelligentContextSelector.select_optimal_context() method work?",
            "Fix the bug in parse_request() function",
            "Debug the _calculate_relevance_score method"
        ]
        
        for query in test_queries:
            entities = coder._extract_focus_entities_from_query(query)
            print(f"Query: '{query}'")
            print(f"Focus entities: {entities}")
            
            # Check if function names are properly extracted
            if "context_selection" in query and "context_selection" in entities:
                print("  ✅ PASS: Function name extracted correctly")
            elif "select_optimal_context" in query and "select_optimal_context" in entities:
                print("  ✅ PASS: Method name extracted correctly")
            elif "parse_request" in query and "parse_request" in entities:
                print("  ✅ PASS: Function name extracted correctly")
            elif "_calculate_relevance_score" in query and "_calculate_relevance_score" in entities:
                print("  ✅ PASS: Method name extracted correctly")
            else:
                print("  ⚠️  PARTIAL: Some entities extracted but may need improvement")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_context_selector():
    """Test the enhanced IntelligentContextSelector directly."""
    print("🎯 Testing Enhanced IntelligentContextSelector")
    print("=" * 60)
    
    try:
        # Import the enhanced selector
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main', 'aider', 'context_request'))
        from intelligent_context_selector import IntelligentContextSelector, TaskType, ContextEntity
        
        # Create mock IR data with test entities
        mock_ir_data = {
            "modules": [
                {
                    "module_name": "test_module",
                    "functions": [
                        {
                            "name": "context_selection",
                            "line_start": 1,
                            "line_end": 10,
                            "complexity": "medium",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": [],
                            "criticality": "high",
                            "change_risk": "medium"
                        },
                        {
                            "name": "select_optimal_context", 
                            "line_start": 11,
                            "line_end": 20,
                            "complexity": "high",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": [],
                            "criticality": "high",
                            "change_risk": "medium"
                        },
                        {
                            "name": "other_function",
                            "line_start": 21,
                            "line_end": 30,
                            "complexity": "low",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": [],
                            "criticality": "low",
                            "change_risk": "low"
                        }
                    ],
                    "classes": []
                }
            ]
        }
        
        # Create context selector
        selector = IntelligentContextSelector(mock_ir_data, max_tokens=2000)
        
        # Test exact match scenarios
        test_cases = [
            {
                "query": "Why is my context_selection function taking so long?",
                "focus_entities": ["context_selection"],
                "expected_top_match": "context_selection"
            },
            {
                "query": "How does select_optimal_context work?",
                "focus_entities": ["select_optimal_context"],
                "expected_top_match": "select_optimal_context"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test Case {i}: {test_case['query']}")
            print(f"Focus entities: {test_case['focus_entities']}")
            
            # Select context
            context_bundle = selector.select_optimal_context(
                task_description=test_case['query'],
                task_type=TaskType.DEBUGGING,
                focus_entities=test_case['focus_entities']
            )
            
            # Check if expected function is in top results
            if context_bundle.entities:
                top_entity = context_bundle.entities[0]
                print(f"  Top match: {top_entity.entity_name} (score: {top_entity.relevance_score:.2f})")
                
                if top_entity.entity_name == test_case['expected_top_match']:
                    print(f"  ✅ PASS: Exact match found as top result")
                else:
                    print(f"  ❌ FAIL: Expected '{test_case['expected_top_match']}' but got '{top_entity.entity_name}'")
                    
                # Show all entities for debugging
                print(f"  All entities:")
                for j, entity in enumerate(context_bundle.entities):
                    print(f"    {j+1}. {entity.entity_name} (score: {entity.relevance_score:.2f})")
            else:
                print(f"  ❌ FAIL: No entities selected")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_with_real_ir():
    """Test with real IR data generation."""
    print("🔄 Testing End-to-End with Real IR Data")
    print("=" * 60)
    
    try:
        # Import required modules
        sys.path.insert(0, os.path.dirname(__file__))
        from aider_integration_service import AiderIntegrationService
        
        # Generate real IR data for current project
        service = AiderIntegrationService()
        ir_data = service.generate_mid_level_ir(".")
        
        print(f"Generated IR data with {len(ir_data.get('modules', []))} modules")
        
        if len(ir_data.get('modules', [])) == 0:
            print("❌ No modules found in IR data - cannot test relevance matching")
            return False
        
        # Import the enhanced selector
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main', 'aider', 'context_request'))
        from intelligent_context_selector import IntelligentContextSelector, TaskType
        
        # Create context selector with real data
        selector = IntelligentContextSelector(ir_data, max_tokens=2000)
        
        print(f"Created selector with {len(selector.entity_map)} entities")
        
        # Test with a query that should find real functions
        test_query = "How does the IntelligentContextSelector work?"
        focus_entities = ["intelligentcontextselector", "select_optimal_context"]
        
        print(f"Testing query: '{test_query}'")
        print(f"Focus entities: {focus_entities}")
        
        context_bundle = selector.select_optimal_context(
            task_description=test_query,
            task_type=TaskType.DEBUGGING,
            focus_entities=focus_entities
        )
        
        if context_bundle.entities:
            print(f"✅ Found {len(context_bundle.entities)} relevant entities:")
            for i, entity in enumerate(context_bundle.entities[:5]):
                print(f"  {i+1}. {entity.entity_name} (score: {entity.relevance_score:.2f}, priority: {entity.priority.value})")
            
            # Check if we found IntelligentContextSelector related entities
            found_relevant = any("context" in entity.entity_name.lower() or "selector" in entity.entity_name.lower() 
                               for entity in context_bundle.entities[:3])
            
            if found_relevant:
                print("✅ PASS: Found relevant entities for the query")
            else:
                print("⚠️  PARTIAL: Entities found but relevance could be improved")
        else:
            print("❌ FAIL: No entities found")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all verification tests."""
    print("🚀 Enhanced Relevance Matching Fix Verification")
    print("=" * 80)
    
    tests = [
        ("Focus Entity Extraction", test_focus_entity_extraction),
        ("IntelligentContextSelector", test_intelligent_context_selector),
        ("End-to-End with Real IR", test_end_to_end_with_real_ir)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Test Results Summary:")
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced relevance matching fixes are working.")
    else:
        print("⚠️  Some tests failed. The fixes may not be fully integrated.")
        print("\nPossible issues:")
        print("- Import path problems")
        print("- IR data generation issues")
        print("- Module integration problems")

if __name__ == "__main__":
    main()
