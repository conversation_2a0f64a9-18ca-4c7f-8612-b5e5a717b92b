graph TB
    %% Component Architecture - Layered View

    subgraph presentation[Presentation Layer]
        gui["gui<br/>(102)"]
        aider_template_renderer["aider_templa...<br/>(43)"]
        ir_builder["ir_builder<br/>(42)"]
        test_ui_formatting["test_ui_form...<br/>(29)"]
        test_clean_dynamic_template["test_clean_d...<br/>(25)"]
        test_repository_overview_removal["test_reposit...<br/>(24)"]
        call_graph_builder["call_graph_b...<br/>(22)"]
    end

    subgraph service[Service Layer]
        aider_integration_service["aider_integr...<br/>(337)"]
        smart_map_request_handler["smart_map_re...<br/>(164)"]
        context_request_handler["context_requ...<br/>(49)"]
        test_aider_integration_service["test_aider_i...<br/>(21)"]
    end

    subgraph core[Core Layer]
        base_coder["base_coder<br/>(673)"]
        base_coder_old["base_coder_old<br/>(607)"]
        commands["commands<br/>(344)"]
        repomap["repomap<br/>(247)"]
        surgical_context_extractor["surgical_con...<br/>(232)"]
        io["io<br/>(210)"]
        test_main["test_main<br/>(206)"]
        benchmark["benchmark<br/>(195)"]
    end

    subgraph data[Data Layer]
        models["models<br/>(195)"]
        test_models["test_models<br/>(102)"]
        entity_extractor["entity_extra...<br/>(48)"]
        clean_metadata["clean_metadata<br/>(44)"]
        metadata_enricher["metadata_enr...<br/>(22)"]
        my_models["my_models<br/>(18)"]
        test_model_info_manager["test_model_i...<br/>(9)"]
    end

    subgraph utility[Utility Layer]
        utils["utils<br/>(64)"]
        refactor_tools["refactor_tools<br/>(38)"]
    end

    %% Layer Dependencies
    presentation --> core
    service --> core
    core --> presentation
    core --> service
    core --> data
    core --> utility
    data --> core
    utility --> core
    test --> core
    test --> service
    script --> core
    script --> service