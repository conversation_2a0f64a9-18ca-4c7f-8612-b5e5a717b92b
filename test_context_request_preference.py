#!/usr/bin/env python

import os
import sys
import argparse
from pathlib import Path
import re

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from aider_context_request_integration import AiderContextRequestIntegration
    from context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider_template_renderer import Aider<PERSON><PERSON>plateR<PERSON><PERSON>
    from aider_integration_service import AiderIntegrationService
except ImportError:
    print("Error: Could not import the required modules.")
    print("Make sure you have implemented the surgical extraction modules.")
    sys.exit(1)


class MockLLM:
    """Mock LLM class for testing."""
    
    def __init__(self, response_type="context_request"):
        self.response_type = response_type
    
    def generate_response(self, prompt):
        """Generate a response based on the prompt."""
        if "function" in prompt.lower() or "method" in prompt.lower():
            if self.response_type == "context_request":
                return self._generate_context_request_response(prompt)
            else:
                return self._generate_request_file_response(prompt)
        else:
            return "I don't see any specific functions or methods to analyze in your query."
    
    def _generate_context_request_response(self, prompt):
        """Generate a response with a CONTEXT_REQUEST."""
        function_match = re.search(r'function\s+(\w+)', prompt.lower())
        if function_match:
            function_name = function_match.group(1)
        else:
            function_name = "example_function"
        
        return f"""
I need to analyze the implementation of the {function_name} function to answer your question.

{{CONTEXT_REQUEST: {{
  "original_user_query_context": "User is asking about the {function_name} function",
  "symbols_of_interest": [
    {{"type": "function_definition", "name": "{function_name}", "file_hint": "example.py"}}
  ],
  "reason_for_request": "To understand the implementation of the {function_name} function"
}}}}
"""
    
    def _generate_request_file_response(self, prompt):
        """Generate a response with a REQUEST_FILE."""
        return """
I need to see the implementation of this function to answer your question.

{REQUEST_FILE: 
  "path": "example.py",
  "reason": "To analyze the implementation of the function"
}
"""


def parse_args():
    parser = argparse.ArgumentParser(description="Test the preference for CONTEXT_REQUEST over REQUEST_FILE")
    parser.add_argument(
        "--response-type",
        type=str,
        choices=["context_request", "request_file"],
        default="context_request",
        help="Type of response to generate (default: context_request)",
    )
    return parser.parse_args()


def test_llm_preference(response_type):
    """Test the LLM's preference for CONTEXT_REQUEST over REQUEST_FILE."""
    print("\n=== Testing LLM Preference for CONTEXT_REQUEST ===")
    
    # Create a mock LLM
    llm = MockLLM(response_type)
    
    # Test cases
    test_cases = [
        "How does the calculate_total function work?",
        "Can you explain the implementation of the process_data method?",
        "What does the validate_input function do?",
        "I need to understand how the parse_json method works.",
    ]
    
    # Test each case
    for i, test_case in enumerate(test_cases):
        print(f"\nTest case {i+1}: {test_case}")
        response = llm.generate_response(test_case)
        print(f"Response: {response}")
        
        # Check if the response contains a CONTEXT_REQUEST or REQUEST_FILE
        if "{CONTEXT_REQUEST:" in response:
            print("✅ LLM used CONTEXT_REQUEST")
        elif "{REQUEST_FILE:" in response:
            print("❌ LLM used REQUEST_FILE")
        else:
            print("❓ LLM did not use either protocol")


def main():
    args = parse_args()
    
    # Test the LLM's preference
    test_llm_preference(args.response_type)
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
