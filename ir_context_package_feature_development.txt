# USER QUERY
I want to add a new code analysis feature. How should I integrate it with the existing system?

# INTELLIGENT CONTEXT ANALYSIS PACKAGE
# Generated by IR_CONTEXT_REQUEST system

## TASK INFORMATION
- User Query: I want to add a new code analysis feature. How should I integrate it with the existing system?
- Task Description: I want to add a new code analysis feature. How should I integrate it with the existing system?
- Task Type: feature_development

## CONTEXT SUMMARY
- Total Entities Selected: 67
- Total Tokens Used: 6000
- Critical Entities: 67
- High Priority Entities: 67
- Files Involved: 9
- Token Utilization: 100.0%

## SELECTION RATIONALE
Context Selection Rationale for feature_development:

Selected 93 entities using 6000 tokens (100.0% of budget).

Priority Distribution:
  - critical: 93 entities

Criticality Distribution:
  - low: 3 entities
  - medium: 86 entities
  - high: 4 entities

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for feature_development task requirements
- Maintained token budget constraints

## IR ANALYSIS DATA (67 entities)

### 1. find_relevant_code_for_feature (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.8123529411764707
- **Priority**: critical
- **Calls**: get_intelligent_context, analyze_with_multi_turn_reasoning, _format_results_by_mode, get
- **Used By**: demo_actual_output_examples, demo_output_modes, test_intelligent_code_discovery, test_output_modes, demo_intelligent_code_discovery
- **Side Effects**: network_io, modifies_file, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 2. get_symbol_references_between_files (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.7600000000000002
- **Priority**: critical
- **Calls**: get_symbols_defined_in_file, _get_repo_map, _normalize_path, relpath, get_tags, _get_module_path, append, abspath, exists, open, read, replace, basename, dirname, split
- **Used By**: aider_integration_service, surgical_context_extractor
- **Side Effects**: writes_log, modifies_file, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, ImportError, KeyError

### 3. process_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.7000000000000006
- **Priority**: critical
- **Calls**: process_context_request, get, render_augmented_prompt
- **Used By**: aider_context_request_integration, test_context_request_with_repo_map, test_augmented_prompt_content, base_coder_old, test_surgical_integration, test_error_handling_fix, test_class_method_extraction, test_context_request_hang, test_complete_function_extraction, test_context_request_integration
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 4. detect_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6800000000000006
- **Priority**: critical
- **Calls**: parse_context_request, join
- **Used By**: test_aider_context_request, test_context_request_integration, base_coder, test_repo_map_compatibility, test_context_request_hang, test_full_aider_integration, base_coder_old, context_request_demo, test_context_request
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 5. update_conversation_history (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6600000000000006
- **Priority**: critical
- **Calls**: get, append
- **Used By**: test_context_request_integration, test_repo_map_compatibility, test_context_request_hang, test_aider_context_request_integration, test_full_aider_integration, test_conversation_history_fix, test_context_request_fix
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 6. analyze_with_multi_turn_reasoning (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.572352941176471
- **Priority**: critical
- **Calls**: _get_iterative_engine, analyze_incrementally
- **Used By**: aider_integration_service, test_iaa_protocol, test_context_bundle_builder
- **Side Effects**: modifies_state, network_io, modifies_container, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 7. get_files_that_import (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5666666666666673
- **Priority**: critical
- **Calls**: get_files_that_import
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError, ImportError

### 8. _get_repo_map (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5600000000000005
- **Priority**: critical
- **Calls**: _normalize_path, Model, InputOutput, RepoMap
- **Used By**: aider_integration_service, surgical_file_extractor
- **Side Effects**: modifies_state, writes_log, modifies_container, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 9. _get_context_selector (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5600000000000005
- **Priority**: critical
- **Calls**: generate_mid_level_ir, IntelligentContextSelector
- **Used By**: aider_integration_service, test_intelligent_context_selection
- **Side Effects**: modifies_file, modifies_state, writes_log
- **Potential Errors**: KeyError, AttributeError, ValueError, TypeError, ImportError

### 10. AiderContextRequestIntegration (class)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5533333333333337
- **Priority**: critical
- **Calls**: None
- **Used By**: test_context_request_with_repo_map, base_coder, test_augmented_prompt_content, base_coder_old, test_surgical_integration, test_class_method_extraction, test_context_request_hang, test_complete_function_extraction, test_context_request_integration, test_surgical_extraction_integration
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 11. AiderIntegrationService (class)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5533333333333337
- **Priority**: critical
- **Calls**: None
- **Used By**: test_modular_pipeline, aider_context_request_integration, test_mid_level_ir, demo_output_modes, test_context_request_root_fix, test_output_modes, surgical_extraction_demo, context_request_handler, demo_actual_output_examples, test_iaa_protocol
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 12. _get_ir_generator (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5466666666666673
- **Priority**: critical
- **Calls**: MidLevelIRPipeline, MidLevelIRGenerator
- **Used By**: aider_integration_service, test_integration, generate_full_ir, test_mid_level_ir
- **Side Effects**: modifies_state, writes_log
- **Potential Errors**: IndexError, KeyError, AttributeError, ValueError, TypeError, ImportError

### 13. calculate_entity_confidence (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5452941176470594
- **Priority**: critical
- **Calls**: get
- **Used By**: iterative_analysis_engine, test_iaa_protocol
- **Side Effects**: modifies_state, modifies_container, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, KeyError

### 14. select_intelligent_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5400000000000005
- **Priority**: critical
- **Calls**: _get_context_selector, get, lower, select_optimal_context, analyze_context_quality, append, sort
- **Used By**: aider_integration_service, test_intelligent_context_selection
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 15. get_context_request_summary (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.5333333333333337
- **Priority**: critical
- **Calls**: join
- **Used By**: test_context_request_integration, base_coder, test_repo_map_compatibility, test_context_request_hang, test_context_request_availability, test_aider_context_request_integration, test_full_aider_integration, base_coder_old, context_request_demo, test_context_request
- **Side Effects**: none
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 16. get_files_imported_by (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get_files_imported_by
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 17. get_symbols_defined_in_file (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get_symbols_defined_in_file
- **Used By**: aider_integration_service, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 18. generate_mid_level_ir (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: _get_ir_generator, generate_ir, generate_mid_level_ir
- **Used By**: aider_integration_service, test_modular_pipeline, test_integration, intelligent_context_selector, generate_full_ir
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: AttributeError, TypeError

### 19. _generate_recommendations (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5266666666666673
- **Priority**: critical
- **Calls**: get, strip, append, extend
- **Used By**: aider_integration_service, code_generation_pipeline
- **Side Effects**: modifies_global, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 20. _update_entity_usage_simple (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: get, append
- **Used By**: aider_integration_service
- **Side Effects**: writes_log, modifies_container, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 21. _detect_architectural_layers (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: lower, get, append, items
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 22. generate_system_overview_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, items, _sanitize_name, get, defaultdict, _get_module_group, _is_internal_module, add, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, ImportError, KeyError

### 23. generate_dependency_graph_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, get, items, _sanitize_name, next, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: modifies_state, network_io, modifies_container, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 24. generate_component_architecture_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, _sanitize_name, title, get, defaultdict, items, _is_internal_module, _get_module_layer, add, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, ImportError, KeyError

### 25. generate_critical_path_diagram (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: append, _sanitize_name, get, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 26. _analyze_business_components (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: lower, get, append, _classify_ui_type, _classify_service_type, _get_module_criticality, _classify_integration_type, _classify_data_type, add
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 27. generate_business_system_architecture (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5200000000000005
- **Priority**: critical
- **Calls**: _analyze_business_components, append, split, get, _sanitize_name, _get_ui_icon, _get_service_icon, _get_integration_icon, _get_data_icon, join
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, ImportError, KeyError

### 28. get_base_classes_of (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5066666666666673
- **Priority**: critical
- **Calls**: get_base_classes_of
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 29. get_derived_classes_of (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5066666666666673
- **Priority**: critical
- **Calls**: get_derived_classes_of
- **Used By**: aider_integration_service, test_aider_integration_service, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 30. main (function)
- **Module**: token_budget_analysis
- **File**: token_budget_analysis.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5052941176470593
- **Priority**: critical
- **Calls**: TokenBudgetAnalyzer, analyze_token_budget, generate_optimization_report, open, dump, asdict, write, print_exc
- **Used By**: test_main, test_browser, test_deprecated, test_ssl_verification
- **Side Effects**: writes_log, modifies_file, network_io
- **Potential Errors**: AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, ImportError, KeyError

### 31. update_entity_analysis (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5043137254901966
- **Priority**: critical
- **Calls**: get_entity_memory, isoformat, now, append, add, discard
- **Used By**: iterative_analysis_engine, test_iaa_protocol
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 32. _simulate_analysis (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5043137254901966
- **Priority**: critical
- **Calls**: calculate_entity_confidence, append, join, values, items
- **Used By**: iterative_analysis_engine
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, ImportError, KeyError

### 33. _load_dependency_data (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5000000000000004
- **Priority**: critical
- **Calls**: exists, open, read, split, strip, startswith, append
- **Used By**: aider_integration_service
- **Side Effects**: modifies_state, writes_log, modifies_file, modifies_container
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, ImportError, KeyError

### 34. _extract_class_info_from_repomap (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5000000000000004
- **Priority**: critical
- **Calls**: _get_repo_map, walk, endswith, append, join, relpath, abspath, open, read, get_tags, startswith, finditer, group, strip, match
- **Used By**: aider_integration_service
- **Side Effects**: network_io, modifies_state, writes_log, modifies_file, modifies_container
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, ImportError, KeyError

### 35. _find_file_defining_symbol (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5000000000000004
- **Priority**: critical
- **Calls**: _is_cache_valid, _get_repo_map, walk, endswith, append, join, relpath, get_tags, _update_cache_timestamp
- **Used By**: aider_integration_service
- **Side Effects**: modifies_state, database_io, modifies_container, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 36. analyze_file_filtering_impact (function)
- **Module**: corrected_token_analysis
- **File**: corrected_token_analysis.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.491960784313726
- **Priority**: critical
- **Calls**: Model, InputOutput, find_src_files, lower, Path, append, RepoMap, get_repo_map, token_count
- **Used By**: corrected_token_analysis
- **Side Effects**: network_io, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 37. get_top_central_files (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: get_top_central_files
- **Used By**: aider_integration_service, test_aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 38. _analyze_side_effects (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: escape, search, group, append
- **Used By**: aider_integration_service
- **Side Effects**: modifies_global, modifies_state
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, FileNotFoundError, ImportError, KeyError

### 39. _calculate_change_risk (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: get, lower
- **Used By**: aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 40. _calculate_simple_dependencies (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: join, open, read, split, strip, startswith, append, lower, replace
- **Used By**: aider_integration_service
- **Side Effects**: modifies_file, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, FileNotFoundError, PermissionError, ImportError, KeyError

### 41. get_contextual_dependencies (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: _get_context_extractor, get_contextual_dependencies, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 42. get_focused_inheritance_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4866666666666672
- **Priority**: critical
- **Calls**: _get_context_extractor, get_focused_inheritance_context, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 43. provide_edit_analysis (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4790196078431377
- **Priority**: critical
- **Calls**: tool_output, analyze_content_changes, analyze_diff_changes
- **Used By**: None
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, ImportError, KeyError

### 44. provide_edit_analysis (function)
- **Module**: base_coder_old
- **File**: aider-main\aider\coders\base_coder_old.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4790196078431377
- **Priority**: critical
- **Calls**: tool_output, analyze_content_changes, analyze_diff_changes
- **Used By**: None
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, ImportError, KeyError

### 45. main (function)
- **Module**: repomap_analysis
- **File**: repomap_analysis.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.471960784313726
- **Priority**: critical
- **Calls**: RepoMapAnalyzer, analyze_repository_map, generate_report, open, dump, asdict, write, print_exc
- **Used By**: test_main, test_browser, test_deprecated, test_ssl_verification
- **Side Effects**: modifies_file, writes_log
- **Potential Errors**: AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, ImportError, KeyError

### 46. _calculate_criticality (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4666666666666672
- **Priority**: critical
- **Calls**: get, lower
- **Used By**: aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: AttributeError, IndexError, TypeError

### 47. main (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4666666666666672
- **Priority**: critical
- **Calls**: ArgumentParser, add_argument, parse_args, load_ir_data, ArchitectureDiagramGenerator, generate_all_diagrams, method, Path, mkdir, open, write
- **Used By**: test_main, test_browser, test_deprecated, test_ssl_verification
- **Side Effects**: modifies_file, writes_log
- **Potential Errors**: IndexError, ValueError, TypeError, ZeroDivisionError, FileNotFoundError, KeyError, RuntimeError, AttributeError, PermissionError, ImportError

### 48. get_entity_memory (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.451960784313726
- **Priority**: critical
- **Calls**: EntityAnalysisMemory
- **Used By**: iterative_analysis_engine, test_iaa_protocol
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 49. analyze_actual_token_usage (function)
- **Module**: corrected_token_analysis
- **File**: corrected_token_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: Model, InputOutput, get_repo_map_tokens, RepoMap, find_src_files, get_repo_map, token_count, open, dump
- **Used By**: corrected_token_analysis
- **Side Effects**: network_io, modifies_file, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, FileNotFoundError, PermissionError, ImportError, KeyError

### 50. _perform_iteration (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: _get_enhanced_context, _simulate_analysis, _update_memory_with_results, AnalysisResult, isoformat, now, add_analysis_result
- **Used By**: iterative_analysis_engine
- **Side Effects**: network_io, database_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 51. _get_enhanced_context (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: _enhance_focus_with_memory, _get_context_bundle_builder, build, _save_enhanced_bundle_data, _convert_enhanced_bundle_to_context_bundle, get, lower, select_optimal_context
- **Used By**: iterative_analysis_engine
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 52. _save_enhanced_bundle_data (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: isoformat, now, append, add_enhanced_bundle_data
- **Used By**: iterative_analysis_engine
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 53. _compile_final_results (function)
- **Module**: iterative_analysis_engine
- **File**: iterative_analysis_engine.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: update, get_entity_memory, values, extend, get_analysis_summary, get_confidence_summary, get_enhanced_bundle_history
- **Used By**: iterative_analysis_engine
- **Side Effects**: modifies_state, network_io, modifies_container, database_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 54. _analyze_size_and_scale (function)
- **Module**: repomap_analysis
- **File**: repomap_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: find_src_files, Process, memory_info, time, get_repo_map, encode, split
- **Used By**: repomap_analysis
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError

### 55. _analyze_token_usage (function)
- **Module**: repomap_analysis
- **File**: repomap_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: find_src_files, get_repo_map, token_count
- **Used By**: repomap_analysis
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError

### 56. _analyze_performance (function)
- **Module**: repomap_analysis
- **File**: repomap_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: find_src_files, time, get_repo_map, append
- **Used By**: repomap_analysis
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError

### 57. _analyze_reliability (function)
- **Module**: repomap_analysis
- **File**: repomap_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: find_src_files, get_repo_map, open, read, split, startswith, strip
- **Used By**: repomap_analysis
- **Side Effects**: network_io, modifies_file, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, FileNotFoundError, PermissionError, KeyError

### 58. _analyze_current_config (function)
- **Module**: token_budget_analysis
- **File**: token_budget_analysis.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4452941176470593
- **Priority**: critical
- **Calls**: find_src_files, get_repo_map, token_count
- **Used By**: token_budget_analysis
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError

### 59. _analyze_module (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: relpath, _get_module_name, _count_lines_of_code, get_symbols_defined_in_file, _analyze_entities
- **Used By**: aider_integration_service, test_mid_level_ir
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 60. _analyze_entities (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: open, read, get, _analyze_function, append, _analyze_class
- **Used By**: aider_integration_service
- **Side Effects**: writes_log, modifies_file, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, FileNotFoundError, PermissionError, KeyError

### 61. _get_iterative_engine (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: _get_context_selector, IterativeAnalysisEngine
- **Used By**: aider_integration_service
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: KeyError, AttributeError, ValueError, TypeError, ImportError

### 62. _analyze_modules (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: defaultdict, get, split, replace, append
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 63. _build_dependency_graph (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: defaultdict, get, _is_internal_module, append, values
- **Used By**: context_bundle_builder, intelligent_context_selector, architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 64. _identify_critical_modules (function)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.4400000000000004
- **Priority**: critical
- **Calls**: get, append
- **Used By**: architecture_diagram_generator
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, KeyError

### 65. match (variable)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.3933333333333335
- **Priority**: critical
- **Calls**: None
- **Used By**: enhanced_surgical_extractor, aider_integration_service, recording_audio, clean_metadata, versionbump, simple_enhanced_test, benchmark, help, simple_extraction_test, surgical_context_extractor
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 66. group (variable)
- **Module**: architecture_diagram_generator
- **File**: architecture_diagram_generator.py
- **Criticality**: low
- **Change Risk**: low
- **Relevance Score**: 2.3533333333333335
- **Priority**: critical
- **Calls**: None
- **Used By**: models, aider_integration_service, context_request_handler, simple_analysis, test_map_request_pattern, update-history, test_ui_formatting, recording_audio, test_context_request_preference, base_coder
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 67. config (variable)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: low
- **Change Risk**: low
- **Relevance Score**: 2.1933333333333334
- **Priority**: critical
- **Calls**: None
- **Used By**: test_main, repo
- **Side Effects**: none
- **Potential Errors**: RuntimeError

## SOURCE CODE IMPLEMENTATIONS (64 implementations)

### 1. find_relevant_code_for_feature
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.8123529411764707

```python
    def find_relevant_code_for_feature(self, project_path: str,
                                     feature_description: str,
                                     focus_areas: list = None,
                                     output_mode: str = "concise") -> dict:
        """
        Find relevant code for a new feature with user-friendly output.

        This is the main entry point for the Intelligent Code Discovery feature.
        It combines intelligent context selection and multi-turn reasoning to provide
        organized, actionable results for feature development.

        Args:
            project_path: Path to the project
            feature_description: Natural language description of the feature
            focus_areas: Key areas/keywords to focus on (optional)
            output_mode: Output verbosity mode:
                - "concise": Essential info only (default)
                - "detailed": Full analysis results
                - "llm_friendly": Optimized for LLM consumption
                - "summary": High-level overview only

        Returns:
            Dictionary with organized, actionable results. Content varies by output_mode:
            - critical_entities: High-risk entities that need careful handling
            - related_entities: Relevant entities that may need modification
            - safe_entities: Safe integration points to start with
            - implementation_guidance: Step-by-step guidance
            - dependency_map: Relationship mapping (detailed/llm_friendly only)
            - recommendations: Actionable recommendations
        """
        try:
            print(f"🔍 Starting Intelligent Code Discovery")
            print(f"   Feature: {feature_description}")
            print(f"   Focus areas: {focus_areas}")

            # Use existing intelligent context selection
            context_result = self.get_intelligent_context(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas
            )

            # Check if context selection was successful
            if 'error' in context_result:
                return {
                    'error': f"Context selection failed: {context_result['error']}",
                    'fallback': 'Use traditional code exploration methods'
                }

            # Use existing multi-turn reasoning for deeper analysis
            analysis_results = self.analyze_with_multi_turn_reasoning(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas,
                max_iterations=3
            )

            # Check if analysis was successful
            if 'error' in analysis_results:
                print(f"⚠️ Multi-turn analysis failed, using single-turn results")
                analysis_results = {'global_insights': [], 'overall_confidence': 0.5}

            # Format into user-friendly structure based on output mode
            result = self._format_results_by_mode(
                context_result, analysis_results, feature_description,
                focus_areas, output_mode
            )

            print(f"✅ Code Discovery Complete")
            # Handle different output modes for logging
            if output_mode == "summary":
                print(f"   Critical: {result.get('critical_count', 0)}")
                print(f"   Safe: {result.get('safe_count', 0)}")
                print(f"   Related: {result.get('related_count', 0)}")
            else:
                print(f"   Critical entities: {len(result.get('critical_entities', []))}")
                print(f"   Safe entities: {len(result.get('safe_entities', []))}")
                print(f"   Related entities: {len(result.get('related_entities', []))}")

            return result

        except Exception as e:
            print(f"⚠️ Error in intelligent code discovery: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional code exploration methods'
            }

```

### 2. get_symbol_references_between_files
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.7600000000000002

```python
    def get_symbol_references_between_files(self, project_path: str, source_file: str, target_file: str, model_name: str = "gpt-3.5-turbo") -> Dict[str, List[str]]:
        """
        Get detailed information about symbols referenced between two files.

        Args:
            project_path: Path to the project root
            source_file: Path to the source file that references symbols
            target_file: Path to the target file that defines symbols
            model_name: Name of the model to use

        Returns:
            Dictionary with symbol types as keys and lists of referenced symbol names as values
        """
        # Get symbols defined in the target file
        target_symbols = self.get_symbols_defined_in_file(project_path, target_file, model_name)

        # Get all symbols in the source file
        repo_map = self.project_manager._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize file paths
            source_file = self.project_manager._normalize_path(source_file)
            target_file = self.project_manager._normalize_path(target_file)

            source_rel_path = os.path.relpath(source_file, project_path)
            target_rel_path = os.path.relpath(target_file, project_path)

            # Get tags for the source file
            source_tags = list(repo_map.get_tags(source_file, source_rel_path))

            # Find references to symbols defined in the target file
            referenced_symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            # Check for direct imports
            for tag in source_tags:
                if tag.kind == 'import':
                    # Check if this import references the target file
                    target_module = self.project_manager._get_module_path(target_rel_path)
                    if target_module in tag.name:
                        referenced_symbols['imports'].append(tag.name)

            # Check for references to symbols defined in the target file
            for tag in source_tags:
                if tag.kind == 'ref':
                    # Check if this reference is to a symbol defined in the target file
                    if tag.name in target_symbols['functions']:
                        if tag.name not in referenced_symbols['functions']:
                            referenced_symbols['functions'].append(tag.name)
                    elif tag.name in target_symbols['classes']:
                        if tag.name not in referenced_symbols['classes']:
                            referenced_symbols['classes'].append(tag.name)
                    elif tag.name in target_symbols['variables']:
                        if tag.name not in referenced_symbols['variables']:
                            referenced_symbols['variables'].append(tag.name)

            # If we have access to the source file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(source_file)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Check for imports from the target file
                    target_basename = os.path.basename(target_file).replace('.py', '')
                    target_dirname = os.path.basename(os.path.dirname(target_file))

                    import_patterns = [
                        rf'from\s+{target_dirname}\.{target_basename}\s+import\s+(.+)',
                        rf'from\s+{target_basename}\s+import\s+(.+)',
                        rf'import\s+{target_dirname}\.{target_basename}',
                        rf'import\s+{target_basename}'
                    ]

                    for pattern in import_patterns:
                        for line in file_content.split('\n'):
                            match = re.search(pattern, line.strip())
                            if match and match.group(1):
                                imports = [imp.strip() for imp in match.group(1).split(',')]
                                for imp in imports:
                                    if imp in target_symbols['functions'] and imp not in referenced_symbols['functions']:
                                        referenced_symbols['functions'].append(imp)
                                    elif imp in target_symbols['classes'] and imp not in referenced_symbols['classes']:
                                        referenced_symbols['classes'].append(imp)
                                    elif imp in target_symbols['variables'] and imp not in referenced_symbols['variables']:
                                        referenced_symbols['variables'].append(imp)
            except Exception as e:
                print(f"Error extracting additional references from file: {e}")

            return referenced_symbols
        except Exception as e:
            print(f"Error getting symbol references between files: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}


if __name__ == "__main__":
    # Example usage
    service = AiderIntegrationService()
    project_path = "aider-main"

    print("\n=== DEPENDENCY ANALYSIS DEMO ===")
    print("\n1. Basic File Dependencies")

    # Get top central files
    top_files = service.get_top_central_files(project_path, count=10)
    print("\nTop Central Files:")
    for file in top_files:
        print(f"- {file['file']}: referenced by {file['references']} files")

    # Get files that import a specific file
    target_file = "aider-main/aider/repomap.py"
    importing_files = service.get_files_that_import(project_path, target_file)
    print(f"\nFiles that import {target_file}:")
    for file in importing_files:
        print(f"- {file}")

    # Get files imported by a specific file
    imported_files = service.get_files_imported_by(project_path, target_file)
    print(f"\nFiles imported by {target_file}:")
    for file in imported_files:
        print(f"- {file}")

    print("\n2. Class Inheritance Analysis")

    # Get base classes of a specific class
    class_name = "RepoMap"
    base_classes = service.get_base_classes_of(project_path, class_name)
    print(f"\nBase classes of {class_name}:")
    if base_classes:
        for base_class in base_classes:
            print(f"- {base_class['class_name']} in {base_class['file_path']} ({base_class['module_path']})")
    else:
        print("- No base classes found")

    # Get derived classes of a specific class
    class_name = "Coder"
    derived_classes = service.get_derived_classes_of(project_path, class_name)
    print(f"\nDerived classes of {class_name}:")
    for derived_class in derived_classes:
        print(f"- {derived_class['class_name']} in {derived_class['file_path']} ({derived_class['module_path']})")

    print("\n3. Symbol Analysis")

    # Get symbols defined in a file
    file_path = "aider-main/aider/repomap.py"
    symbols = service.get_symbols_defined_in_file(project_path, file_path)
    print(f"\nSymbols defined in {file_path}:")
    for symbol_type, symbol_list in symbols.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list[:5]:  # Show only first 5 symbols of each type
                print(f"    - {symbol}")
            if len(symbol_list) > 5:
                print(f"    - ... and {len(symbol_list) - 5} more")

    # Find file defining a symbol
    symbol_name = "RepoMap"
    defining_file = service.find_file_defining_symbol(project_path, symbol_name)
    print(f"\nFile defining symbol '{symbol_name}':")
    print(f"- {defining_file if defining_file else 'Not found'}")

    print("\n4. Detailed Symbol References (New Feature)")

    # Get symbol references between files
    source_file = "aider-main/aider/coders/base_coder.py"
    target_file = "aider-main/aider/repomap.py"

    print(f"\nSymbols from {target_file} referenced in {source_file}:")
    symbol_refs = service.get_symbol_references_between_files(project_path, source_file, target_file)

    for symbol_type, symbol_list in symbol_refs.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list:
                print(f"    - {symbol}")

    print("\n5. Surgical Context Extraction (New Feature)")

    # Get contextual dependencies for a file
    primary_file = "aider-main/aider/coders/base_coder.py"
    print(f"\nContextual dependencies for {primary_file}:")

    try:
        context_map = service.get_contextual_dependencies(project_path, primary_file, max_snippets=6)

        print(f"\nPrimary file: {context_map['primary_file']}")
        print(f"Related files: {', '.join(context_map['related_files'][:3]) if context_map['related_files'] else 'None'}...")

        # Show usage contexts
        print("\nUsage contexts:")
        for i, usage_ctx in enumerate(context_map['usage_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {usage_ctx['symbol_name']} ({usage_ctx['usage_type']})")
            print(f"     File: {usage_ctx['file_path']}, Lines: {usage_ctx['start_line']}-{usage_ctx['end_line']}")
            print(f"     Surrounding function: {usage_ctx['surrounding_function'] or 'N/A'}")

            # Show a snippet of the content (first 3 lines)
            content_lines = usage_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show definition contexts
        print("\nDefinition contexts:")
        for i, def_ctx in enumerate(context_map['definition_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {def_ctx['symbol_name']} ({def_ctx['definition_type']})")
            print(f"     File: {def_ctx['file_path']}, Lines: {def_ctx['start_line']}-{def_ctx['end_line']}")
            if def_ctx['signature']:
                print(f"     Signature: {def_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = def_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating contextual dependencies: {e}")

    # Get focused inheritance context
    class_name = "Coder"
    file_path = "aider-main/aider/coders/base_coder.py"
    print(f"\nFocused inheritance context for {class_name}:")

    try:
        inheritance_ctx = service.get_focused_inheritance_context(project_path, class_name, file_path)

        # Show base classes
        print("\nBase classes:")
        for i, base_ctx in enumerate(inheritance_ctx['base_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {base_ctx['symbol_name']}")
            print(f"     File: {base_ctx['file_path']}, Lines: {base_ctx['start_line']}-{base_ctx['end_line']}")
            if base_ctx['signature']:
                print(f"     Signature: {base_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = base_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show derived classes
        print("\nDerived classes:")
        for i, derived_ctx in enumerate(inheritance_ctx['derived_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {derived_ctx['symbol_name']}")
            print(f"     File: {derived_ctx['file_path']}, Lines: {derived_ctx['start_line']}-{derived_ctx['end_line']}")

            # Show a snippet of the content (first 3 lines)
            content_lines = derived_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating inheritance context: {e}")

    print("\nSurgical Context Extraction successfully implemented!")

    print("\n6. Intelligent Context Selection (Phase 2 Feature)")

    # Test the new intelligent context selection
    print("\nTesting Intelligent Context Selection...")

    try:
        # Test different task scenarios
        test_scenarios = [
            {
                'task': 'Fix a bug in the file processing logic',
                'type': 'debugging',
                'focus': ['file', 'process']
            },
            {
                'task': 'Add a new feature for dependency analysis',
                'type': 'feature_development',
                'focus': ['dependency', 'analysis']
            }
        ]

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📋 Scenario {i}: {scenario['task']}")

            context_result = service.select_intelligent_context(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['type'],
                focus_entities=scenario['focus'],
                max_tokens=2000  # Smaller budget for demo
            )

            if 'error' not in context_result:
                print(f"✅ Context selected successfully!")
                print(f"   Selected entities: {context_result['total_entities']}")
                print(f"   Token utilization: {context_result['quality_metrics']['token_utilization']:.1f}%")
                print(f"   Average relevance: {context_result['quality_metrics']['average_relevance_score']:.2f}")

                # Show top 3 entities
                top_entities = context_result['entities'][:3]
                print(f"   Top entities:")
                for j, entity in enumerate(top_entities, 1):
                    print(f"     {j}. {entity['module_name']}.{entity['entity_name']} "
                          f"(score: {entity['relevance_score']:.2f})")
            else:
                print(f"⚠️ Context selection failed: {context_result['error']}")

        print("\n✅ Intelligent Context Selection successfully demonstrated!")

    except Exception as e:
        print(f"⚠️ Intelligent Context Selection test failed: {e}")

    print("\n7. Mid-Level IR Generation (Foundation Feature)")

    # Generate Mid-Level IR for a subset of files (to avoid overwhelming output)
    print("\nGenerating Mid-Level IR for the project...")

    try:
        # Generate the complete IR
        ir_data = service.generate_mid_level_ir(project_path)

        print(f"\n✅ Mid-Level IR generated successfully!")
        print(f"   Total modules analyzed: {ir_data['metadata']['total_modules']}")
        print(f"   Generated at: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ir_data['metadata']['generated_at']))}")

        # Show a sample of the IR structure
        print("\n📋 Sample IR Structure (first 2 modules):")

        sample_modules = ir_data['modules'][:2]  # Show first 2 modules

        for i, module in enumerate(sample_modules, 1):
            print(f"\n  Module {i}: {module['name']}")
            print(f"    File: {module['file']}")
            print(f"    Lines of Code: {module['loc']}")
            print(f"    Dependencies: {len(module['dependencies'])}")
            print(f"    Entities: {len(module['entities'])}")

            # Show dependencies
            if module['dependencies']:
                print("    Dependencies:")
                for dep in module['dependencies'][:3]:  # Show first 3 dependencies
                    print(f"      - {dep['module']} (strength: {dep['strength']})")
                if len(module['dependencies']) > 3:
                    print(f"      - ... and {len(module['dependencies']) - 3} more")

            # Show entities
            if module['entities']:
                print("    Entities:")
                for entity in module['entities'][:2]:  # Show first 2 entities
                    print(f"      - {entity['type']}: {entity['name']}")
                    print(f"        Doc: {entity['doc'][:50]}...")
                    print(f"        Params: {entity['params']}")
                    print(f"        Returns: {entity['returns']}")
                    print(f"        Calls: {entity['calls'][:3]}")  # Show first 3 calls
                    print(f"        Side effects: {entity['side_effects']}")
                    print(f"        Criticality: {entity['criticality']}")
                    print(f"        Change risk: {entity['change_risk']}")
                if len(module['entities']) > 2:
                    print(f"      - ... and {len(module['entities']) - 2} more entities")

        # Save the IR to a file for inspection
        output_file = "mid_level_ir_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Complete Mid-Level IR saved to: {output_file}")
        print("   You can inspect the full structure in this file.")

        # Show some statistics
        total_entities = sum(len(module['entities']) for module in ir_data['modules'])
        total_dependencies = sum(len(module['dependencies']) for module in ir_data['modules'])

        print(f"\n📊 IR Statistics:")
        print(f"   Total modules: {len(ir_data['modules'])}")
        print(f"   Total entities: {total_entities}")
        print(f"   Total dependencies: {total_dependencies}")

        # Show criticality distribution
        criticality_counts = {'high': 0, 'medium': 0, 'low': 0}
        for module in ir_data['modules']:
            for entity in module['entities']:
                criticality = entity.get('criticality', 'medium')
                criticality_counts[criticality] = criticality_counts.get(criticality, 0) + 1

        print(f"   Criticality distribution:")
        print(f"     High: {criticality_counts['high']}")
        print(f"     Medium: {criticality_counts['medium']}")
        print(f"     Low: {criticality_counts['low']}")

    except Exception as e:
        print(f"❌ Error generating Mid-Level IR: {e}")
        import traceback
        traceback.print_exc()

    print("\n7. Cache Management")
    print("\nClearing cache...")
    service.clear_cache()
    print("Cache cleared successfully.")

    print("\nSetting cache TTL to 1 hour...")
    service.set_cache_ttl(3600)
    print("Cache TTL set successfully.")
```

### 3. process_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.7000000000000006

```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

```

### 4. detect_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.6800000000000006

```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

```

### 5. update_conversation_history
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.6600000000000006

```python
    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

```

### 6. analyze_with_multi_turn_reasoning
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.572352941176471

```python
    def analyze_with_multi_turn_reasoning(self, project_path: str, task_description: str,
                                        task_type: str = "general_analysis",
                                        focus_entities: list = None, max_iterations: int = 5,
                                        max_tokens: int = 8000):
        """
        Perform multi-turn reasoning analysis using the IAA Protocol.

        This method implements the Iterative Analysis Accumulation protocol that builds
        understanding across multiple iterations, using intelligent context selection
        and analysis memory to progressively improve understanding.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the analysis task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_iterations: Maximum number of analysis iterations
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing comprehensive multi-turn analysis results
        """
        try:
            print(f"🧠 Starting Multi-Turn Reasoning Analysis")
            print(f"   Task: {task_description}")
            print(f"   Type: {task_type}")
            print(f"   Max iterations: {max_iterations}")

            # Get the iterative analysis engine
            engine = self._get_iterative_engine(project_path, max_tokens)

            if engine is None:
                return {
                    'error': 'Iterative Analysis Engine not available',
                    'fallback': 'Use single-turn intelligent context selection'
                }

            # Update max iterations if specified
            if max_iterations != engine.max_iterations:
                engine.max_iterations = max_iterations

            # Perform iterative analysis
            analysis_results = engine.analyze_incrementally(
                task=task_description,
                task_type=task_type,
                focus_entities=focus_entities
            )

            # Add metadata about the analysis
            analysis_results['analysis_metadata'] = {
                'project_path': project_path,
                'max_tokens': max_tokens,
                'engine_version': 'IAA Protocol v1.0',
                'analysis_type': 'multi_turn_reasoning'
            }

            print(f"✅ Multi-Turn Analysis Complete")
            print(f"   Iterations: {analysis_results['total_iterations']}")
            print(f"   Confidence: {analysis_results['overall_confidence']:.2f}")
            print(f"   Entities: {len(analysis_results['entity_summaries'])}")

            return analysis_results

        except Exception as e:
            print(f"⚠️ Error in multi-turn reasoning analysis: {e}")
            return {
                'error': str(e),
                'fallback': 'Use single-turn intelligent context selection'
            }

```

### 7. get_files_that_import
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5666666666666673

```python
    def get_files_that_import(self, project_path: str, model_name: str, target_file_path: str) -> List[str]:
        """
        Get a list of files that import (reference) the target file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            target_file_path: Path to the target file

        Returns:
            A list of file paths that import the target file
        """
        # Try to use RepoMap for more accurate results
        repo_map = self._get_repo_map(project_path, model_name)
        if repo_map:
            try:
                # Normalize the target file path
                target_file_path = self._normalize_path(target_file_path)
                rel_path = os.path.relpath(target_file_path, project_path)

                # Get all Python files in the project
                python_files = []
                for root, _, files in os.walk(project_path):
                    for file in files:
                        if file.endswith('.py'):
                            python_files.append(os.path.join(root, file))

                # Find files that import the target file
                importing_files = []

                # Get tags for the target file to find defined symbols
                target_tags = list(repo_map.get_tags(target_file_path, rel_path))
                defined_symbols = [tag.name for tag in target_tags if tag.kind == 'def' or tag.kind == 'class']

                # Check each file for references to the defined symbols
                for file_path in python_files:
                    if file_path == target_file_path:
                        continue

                    file_rel_path = os.path.relpath(file_path, project_path)
                    file_tags = list(repo_map.get_tags(file_path, file_rel_path))

                    # Check if any of the defined symbols are referenced in this file
                    for tag in file_tags:
                        if tag.kind == 'ref' and tag.name in defined_symbols:
                            importing_files.append(file_rel_path)
                            break

                return importing_files
            except Exception as e:
                print(f"Error using RepoMap for get_files_that_import: {e}")
                # Fall back to static analysis

        # Fall back to static analysis if RepoMap is not available or fails
        if not self.dependency_data:
            return []

        # Get the dependencies from the dependency data
        dependencies = self.dependency_data["dependencies"]

        # Find files that import the target file
        importing_files = []
        target_file_rel = os.path.basename(target_file_path)

        for file, deps in dependencies.items():
            for dep_file, _ in deps:
                if dep_file == target_file_rel or dep_file.endswith('/' + target_file_rel):
                    importing_files.append(file)

        return importing_files

```

### 8. _get_repo_map
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5600000000000005

```python
    def _get_repo_map(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Optional[Any]:
        """
        Get or create a RepoMap instance for the given project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use

        Returns:
            RepoMap instance or None if RepoMap is not available
        """
        if not REPOMAP_AVAILABLE:
            return None

        # Normalize the project path
        project_path = self._normalize_path(project_path)

        # Check if we already have a RepoMap for this project
        if project_path in self.repo_maps:
            return self.repo_maps[project_path]

        try:
            # Create a simple model instance for token counting
            model = Model(model_name)

            # Create an InputOutput instance
            io = InputOutput()

            # Initialize the RepoMap with a higher token limit for better coverage
            repo_map = RepoMap(
                map_tokens=8192,  # Increased token limit for better coverage
                root=project_path,
                main_model=model,
                io=io,
                repo_content_prefix="# Repository Map\n\nThis map shows the structure and dependencies of the codebase:\n\n",
                verbose=False
            )

            self.repo_maps[project_path] = repo_map
            return repo_map
        except Exception as e:
            print(f"Error creating RepoMap: {e}")
            return None

```

### 9. _get_context_selector
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5600000000000005

```python
    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

```

### 10. AiderContextRequestIntegration
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.5533333333333337

```python
class AiderContextRequestIntegration:
    """
    Integrates the context request handler with the Aider system.
    """

```

### 11. AiderIntegrationService
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5533333333333337

```python
class AiderIntegrationService:
    """
    Service for integrating with Aider and exposing its functionality.

    This service provides methods to analyze and understand the dependencies
    between files and classes in a codebase. It uses Aider's RepoMap for
    dynamic analysis when available, and falls back to static analysis
    when necessary.

    It also provides surgical context extraction capabilities to extract
    focused code snippets around dependency interaction points.

    Enhanced with Multi-Turn Reasoning Loop (IAA Protocol) for complex
    iterative analysis workflows.
    """

```

### 12. _get_ir_generator
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5466666666666673

```python
    def _get_ir_generator(self):
        """Get the Mid-Level IR generator, initializing it if necessary."""
        if self.ir_generator is None:
            # Try to use the new modular pipeline first
            try:
                from mid_level_ir import MidLevelIRPipeline

                # Configure the modular pipeline
                config = {
                    'verbose': False,  # Keep quiet for integration
                    'file_scanner': {
                        'exclude_dirs': ['__pycache__', '.git', '.pytest_cache', 'node_modules'],
                        'max_file_size_mb': 10
                    },
                    'entity_extractor': {
                        'extract_variables': True,
                        'extract_constants': True,
                        'min_function_lines': 1
                    },
                    'call_graph_builder': {
                        'max_calls_per_entity': 15,
                        'include_builtin_calls': False
                    },
                    'dependency_analyzer': {
                        'include_stdlib': False,
                        'include_external': True
                    },
                    'ir_builder': {
                        'include_metadata': True,
                        'include_ast_info': False,  # Keep output size manageable
                        'pretty_print': True
                    }
                }

                self.ir_generator = MidLevelIRPipeline(config)
                print("✅ Using enhanced modular Mid-Level IR pipeline")

            except ImportError:
                # Fall back to the original implementation
                self.ir_generator = MidLevelIRGenerator(self.project_manager)
                print("⚠️  Using legacy Mid-Level IR generator (modular pipeline not available)")

        return self.ir_generator

```

### 13. calculate_entity_confidence
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.5452941176470594

```python
    def calculate_entity_confidence(self, entity_id: str, analysis_data: Dict[str, Any]) -> float:
        """
        Calculate confidence score for an entity based on multiple factors.
        
        Args:
            entity_id: The entity being analyzed
            analysis_data: Data about the entity and its analysis
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        factors = {}
        
        # Factor 1: Documentation completeness
        doc_coverage = analysis_data.get('doc_coverage', 0.0)
        factors['documentation'] = min(doc_coverage, 1.0)
        
        # Factor 2: Code complexity (inverse relationship)
        complexity = analysis_data.get('complexity', 5)
        factors['complexity'] = max(0.0, 1.0 - (complexity - 5) / 10.0)
        
        # Factor 3: Dependency clarity
        dependency_count = len(analysis_data.get('dependencies', []))
        factors['dependencies'] = 1.0 if dependency_count < 5 else max(0.3, 1.0 - dependency_count / 20.0)
        
        # Factor 4: Analysis depth
        insights_count = len(analysis_data.get('insights', []))
        factors['analysis_depth'] = min(insights_count / 3.0, 1.0)
        
        # Factor 5: Issue resolution
        open_issues = len(analysis_data.get('open_issues', []))
        factors['issue_resolution'] = max(0.0, 1.0 - open_issues / 5.0)
        
        # Weighted average
        weights = {
            'documentation': 0.2,
            'complexity': 0.25,
            'dependencies': 0.2,
            'analysis_depth': 0.2,
            'issue_resolution': 0.15
        }
        
        confidence = sum(factors[factor] * weights[factor] for factor in factors)

        # Ensure confidence is within valid range [0.0, 1.0]
        confidence = max(0.0, min(1.0, confidence))

        # Store factors for debugging
        self.confidence_factors[entity_id] = factors
        self.entity_confidences[entity_id] = confidence

        return confidence
    
```

### 14. select_intelligent_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5400000000000005

```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

```

### 15. get_context_request_summary
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.5333333333333337

```python
    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request for logging purposes.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"
```

### 16. get_files_imported_by
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def get_files_imported_by(self, project_path: str, target_file_path: str, model_name: str = "gpt-3.5-turbo") -> List[str]:
        """
        Get a list of files that are imported by the target file.

        Args:
            project_path: Path to the project root
            target_file_path: Path to the target file
            model_name: Name of the model to use

        Returns:
            A list of file paths that are imported by the target file
        """
        return self.project_manager.get_files_imported_by(project_path, model_name, target_file_path)

```

### 17. get_symbols_defined_in_file
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def get_symbols_defined_in_file(self, project_path: str, model_name: str, file_path: str) -> Dict[str, List[str]]:
        """
        Get all symbols (functions, classes, variables) defined in a file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            file_path: Path to the file to analyze

        Returns:
            Dictionary with symbol types as keys and lists of symbol names as values
        """
        # Check if we have this information in the cache
        cache_key = (project_path, file_path, 'symbols')
        if cache_key in self.symbol_cache and self._is_cache_valid(cache_key):
            return self.symbol_cache[cache_key]

        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize the file path
            file_path = self._normalize_path(file_path)
            rel_path = os.path.relpath(file_path, project_path)

            # Get tags for the file
            tags = list(repo_map.get_tags(file_path, rel_path))

            # Categorize symbols by type
            symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            for tag in tags:
                if tag.kind == 'def':
                    if tag.name.startswith('class '):
                        class_name = tag.name.split(' ')[1]
                        if class_name not in symbols['classes']:
                            symbols['classes'].append(class_name)
                    elif tag.name.startswith('def '):
                        func_name = tag.name.split(' ')[1]
                        if func_name not in symbols['functions']:
                            symbols['functions'].append(func_name)
                    else:
                        if tag.name not in symbols['functions']:
                            symbols['functions'].append(tag.name)
                elif tag.kind == 'class':
                    if tag.name not in symbols['classes']:
                        symbols['classes'].append(tag.name)
                elif tag.kind == 'variable':
                    if tag.name not in symbols['variables']:
                        symbols['variables'].append(tag.name)
                elif tag.kind == 'import':
                    if tag.name not in symbols['imports']:
                        symbols['imports'].append(tag.name)

            # If we have access to the file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(file_path)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Extract imports
                    import_pattern = r'^(?:from\s+(\S+)\s+import\s+(.+)|import\s+(.+))$'
                    for line in file_content.split('\n'):
                        match = re.match(import_pattern, line.strip())
                        if match:
                            if match.group(1) and match.group(2):  # from X import Y
                                module = match.group(1)
                                imports = [imp.strip() for imp in match.group(2).split(',')]
                                for imp in imports:
                                    import_str = f"{module}.{imp}"
                                    if import_str not in symbols['imports']:
                                        symbols['imports'].append(import_str)
                            elif match.group(3):  # import X
                                imports = [imp.strip() for imp in match.group(3).split(',')]
                                for imp in imports:
                                    if imp not in symbols['imports']:
                                        symbols['imports'].append(imp)
            except Exception as e:
                print(f"Error extracting additional symbols from file: {e}")

            # Update the cache
            self.symbol_cache[cache_key] = symbols
            self._update_cache_timestamp(cache_key)

            return symbols
        except Exception as e:
            print(f"Error getting symbols from file: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}

```

### 18. generate_mid_level_ir
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def generate_mid_level_ir(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Dict:
        """
        Generate Mid-Level Intermediate Representation for the project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use for analysis

        Returns:
            Dictionary containing the Mid-Level IR structure
        """
        generator = self._get_ir_generator()

        # Check if we're using the new modular pipeline
        if hasattr(generator, 'generate_ir'):
            # New modular pipeline
            return generator.generate_ir(project_path)
        else:
            # Legacy generator
            return generator.generate_mid_level_ir(project_path, model_name)

```

### 19. _generate_recommendations
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5266666666666673

```python
    def _generate_recommendations(self, analysis_results: dict, context_result: dict) -> list:
        """Generate actionable recommendations."""
        recommendations = []

        # Extract global insights from multi-turn analysis
        global_insights = analysis_results.get('global_insights', [])
        for insight in global_insights[:3]:  # Top 3 insights
            if isinstance(insight, str) and len(insight.strip()) > 0:
                recommendations.append(insight.strip())

        # Add confidence-based recommendations
        overall_confidence = analysis_results.get('overall_confidence', 0.0)
        if overall_confidence < 0.7:
            recommendations.append("Consider running additional analysis iterations for better confidence")

        # Add entity-specific recommendations
        total_entities = context_result.get('total_entities', 0)
        if total_entities > 30:
            recommendations.append("Large number of entities selected - consider breaking feature into smaller parts")
        elif total_entities < 5:
            recommendations.append("Few entities selected - verify feature scope is comprehensive")

        # Add quality-based recommendations
        quality_metrics = context_result.get('quality_metrics', {})
        token_utilization = quality_metrics.get('token_utilization', 0)
        if token_utilization < 50:
            recommendations.append("Low token utilization - consider expanding analysis scope")
        elif token_utilization > 95:
            recommendations.append("High token utilization - consider reducing scope or increasing token budget")

        # Add general best practices
        recommendations.extend([
            "Start with safe integration points to minimize risk",
            "Test critical entities thoroughly before modification",
            "Document changes to maintain code clarity"
        ])

        return recommendations[:10]  # Limit to top 10 recommendations

```

### 20. _update_entity_usage_simple
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def _update_entity_usage_simple(self, module: Dict, all_modules: List[Dict]) -> None:
        """Update entity usage with a simpler approach."""
        try:
            # For each entity in this module, find which other modules might use it
            for entity in module.get("entities", []):
                used_by = []

                # Check other modules for potential usage
                for other_module in all_modules:
                    if other_module["name"] == module["name"]:
                        continue

                    # Simple heuristic: if entity name appears in other module's entities' calls
                    for other_entity in other_module.get("entities", []):
                        if entity["name"] in other_entity.get("calls", []):
                            if other_module["name"] not in used_by:
                                used_by.append(other_module["name"])

                entity["used_by"] = used_by[:5]  # Limit to first 5

        except Exception as e:
            print(f"   Error updating simple entity usage for {module['name']}: {e}")


```

### 21. _detect_architectural_layers
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def _detect_architectural_layers(self):
        """Detect architectural layers based on naming patterns and dependencies."""
        print("🏗️  Detecting architectural layers...")
        
        layers = {
            'presentation': [],
            'service': [],
            'core': [],
            'data': [],
            'utility': [],
            'test': [],
            'script': []
        }
        
        for module in self.modules:
            module_name = module['name'].lower()
            file_path = module.get('file', '').lower()
            
            # Classify based on naming patterns
            if any(keyword in module_name for keyword in ['ui', 'view', 'template', 'render', 'display']):
                layers['presentation'].append(module)
            elif any(keyword in module_name for keyword in ['service', 'api', 'endpoint', 'handler']):
                layers['service'].append(module)
            elif any(keyword in module_name for keyword in ['core', 'main', 'engine', 'pipeline']):
                layers['core'].append(module)
            elif any(keyword in module_name for keyword in ['data', 'model', 'entity', 'storage', 'db']):
                layers['data'].append(module)
            elif any(keyword in module_name for keyword in ['util', 'helper', 'tool', 'common']):
                layers['utility'].append(module)
            elif any(keyword in file_path for keyword in ['test', 'spec']):
                layers['test'].append(module)
            elif any(keyword in file_path for keyword in ['script', 'example', 'demo']):
                layers['script'].append(module)
            else:
                # Default to core if unclear
                layers['core'].append(module)
        
        # Remove empty layers
        self.architectural_layers = {k: v for k, v in layers.items() if v}
        print(f"   Detected {len(self.architectural_layers)} architectural layers")
    
```

### 22. generate_system_overview_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_system_overview_diagram(self) -> str:
        """Generate a high-level system overview diagram."""
        print("📊 Generating system overview diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% System Overview - Top Level Architecture")
        mermaid.append("")

        # Add module groups as subgraphs
        for group_name, modules in self.module_groups.items():
            if len(modules) < 2:  # Skip single-module groups for clarity
                continue

            sanitized_group = self._sanitize_name(group_name)
            mermaid.append(f"    subgraph {sanitized_group}[{group_name}]")

            # Add top modules from this group (limit for readability)
            top_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:5]

            for module in top_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Color code by entity count
                if entity_count > 50:
                    style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # Red for large
                elif entity_count > 20:
                    style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Yellow for medium
                else:
                    style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Blue for small

                mermaid.append(f"        {sanitized_name}[\"{module_name}<br/>({entity_count} entities)\"]")
                mermaid.append(f"        style {sanitized_name} {style}")

            mermaid.append("    end")
            mermaid.append("")

        # Add key dependencies between groups
        group_connections = defaultdict(set)
        for module in self.modules:
            module_group = self._get_module_group(module['name'])
            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if self._is_internal_module(dep_module):
                    dep_group = self._get_module_group(dep_module)
                    if module_group != dep_group and dep_group:
                        group_connections[module_group].add(dep_group)

        # Add group-level connections
        mermaid.append("    %% Group Dependencies")
        for source_group, target_groups in group_connections.items():
            for target_group in target_groups:
                if source_group in self.module_groups and target_group in self.module_groups:
                    source_sanitized = self._sanitize_name(source_group)
                    target_sanitized = self._sanitize_name(target_group)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

```

### 23. generate_dependency_graph_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_dependency_graph_diagram(self, max_modules: int = 30) -> str:
        """Generate a detailed dependency graph diagram."""
        print("🔗 Generating dependency graph diagram...")

        mermaid = ["graph LR"]
        mermaid.append("    %% Module Dependency Graph")
        mermaid.append("")

        # Get most connected modules
        module_scores = {}
        for module in self.modules:
            module_name = module['name']
            outgoing = len(module.get('dependencies', []))
            incoming = sum(1 for m in self.modules
                          for dep in m.get('dependencies', [])
                          if dep.get('module') == module_name)
            module_scores[module_name] = outgoing + incoming

        # Select top modules by connectivity
        top_modules = sorted(module_scores.items(), key=lambda x: x[1], reverse=True)[:max_modules]
        selected_modules = {name for name, _ in top_modules}

        # Add nodes with styling
        for module_name, score in top_modules:
            sanitized_name = self._sanitize_name(module_name)

            # Find the actual module data
            module_data = next((m for m in self.modules if m['name'] == module_name), None)
            if not module_data:
                continue

            entity_count = len(module_data.get('entities', []))

            # Style based on connectivity and size
            if score > 10:
                style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # High connectivity
            elif score > 5:
                style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Medium connectivity
            else:
                style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Low connectivity

            # Truncate long names
            display_name = module_name if len(module_name) <= 20 else module_name[:17] + "..."
            mermaid.append(f"    {sanitized_name}[\"{display_name}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies
        mermaid.append("    %% Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in selected_modules:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in selected_modules:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Style arrows by dependency strength
                    if strength == 'strong':
                        arrow_style = "stroke:#d63031,stroke-width:3px"
                    elif strength == 'medium':
                        arrow_style = "stroke:#ff9ff3,stroke-width:2px"
                    else:
                        arrow_style = "stroke:#74b9ff,stroke-width:1px"

                    mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    # Note: Mermaid doesn't support per-edge styling in this syntax

        return "\n".join(mermaid)

```

### 24. generate_component_architecture_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_component_architecture_diagram(self) -> str:
        """Generate a component architecture diagram showing layers."""
        print("🏗️  Generating component architecture diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% Component Architecture - Layered View")
        mermaid.append("")

        # Define layer order (top to bottom)
        layer_order = ['presentation', 'service', 'core', 'data', 'utility']

        # Add layers as subgraphs
        for layer_name in layer_order:
            if layer_name not in self.architectural_layers:
                continue

            modules = self.architectural_layers[layer_name]
            if not modules:
                continue

            sanitized_layer = self._sanitize_name(layer_name)
            layer_display = layer_name.title() + " Layer"
            mermaid.append(f"    subgraph {sanitized_layer}[{layer_display}]")

            # Add key modules from this layer (limit for readability)
            key_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:8]

            for module in key_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Truncate long names
                display_name = module_name if len(module_name) <= 15 else module_name[:12] + "..."

                mermaid.append(f"        {sanitized_name}[\"{display_name}<br/>({entity_count})\"]")

            mermaid.append("    end")
            mermaid.append("")

        # Add layer dependencies
        mermaid.append("    %% Layer Dependencies")
        layer_deps = defaultdict(set)

        for layer_name, modules in self.architectural_layers.items():
            for module in modules:
                for dep in module.get('dependencies', []):
                    dep_module = dep.get('module', '')
                    if self._is_internal_module(dep_module):
                        dep_layer = self._get_module_layer(dep_module)
                        if dep_layer and dep_layer != layer_name:
                            layer_deps[layer_name].add(dep_layer)

        # Add layer connections
        for source_layer, target_layers in layer_deps.items():
            for target_layer in target_layers:
                if source_layer in self.architectural_layers and target_layer in self.architectural_layers:
                    source_sanitized = self._sanitize_name(source_layer)
                    target_sanitized = self._sanitize_name(target_layer)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

```

### 25. generate_critical_path_diagram
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_critical_path_diagram(self) -> str:
        """Generate a diagram showing critical modules and their relationships."""
        print("⚠️  Generating critical path diagram...")

        mermaid = ["graph TD"]
        mermaid.append("    %% Critical Path - High Impact Components")
        mermaid.append("")

        # Take top critical modules
        top_critical = self.critical_modules[:15]
        critical_names = {module['name'] for module in top_critical}

        # Add critical modules with styling
        for module_info in top_critical:
            module_name = module_info['name']
            sanitized_name = self._sanitize_name(module_name)
            score = module_info['score']
            high_crit = module_info['high_crit_entities']
            incoming = module_info['incoming_deps']

            # Style based on criticality score
            if score > 1.0:
                style = "fill:#ff4757,stroke:#ff3742,color:#fff"  # Critical
            elif score > 0.7:
                style = "fill:#ff6348,stroke:#ff4757,color:#fff"  # High
            elif score > 0.5:
                style = "fill:#ffa502,stroke:#ff9ff3,color:#000"  # Medium
            else:
                style = "fill:#fffa65,stroke:#ffdd59,color:#000"  # Low

            # Truncate long names
            display_name = module_name if len(module_name) <= 18 else module_name[:15] + "..."

            mermaid.append(f"    {sanitized_name}[\"{display_name}<br/>Score: {score:.2f}<br/>Critical: {high_crit}<br/>Used by: {incoming}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies between critical modules
        mermaid.append("    %% Critical Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in critical_names:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in critical_names:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Add dependency with strength indicator
                    if strength == 'strong':
                        mermaid.append(f"    {sanitized_source} ==> {sanitized_target}")
                    elif strength == 'medium':
                        mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    else:
                        mermaid.append(f"    {sanitized_source} -.-> {sanitized_target}")

        return "\n".join(mermaid)

```

### 26. _analyze_business_components
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def _analyze_business_components(self):
        """Analyze IR data to identify business components dynamically."""
        print("🔍 Analyzing business components from IR data...")

        business_components = {
            'user_interfaces': [],
            'core_services': [],
            'integration_services': [],
            'data_services': [],
            'external_systems': set(),
            'business_workflows': []
        }

        # Analyze modules to identify business patterns
        for module in self.modules:
            module_name = module['name'].lower()
            entities = module.get('entities', [])
            dependencies = module.get('dependencies', [])

            # Identify user interfaces
            if any(keyword in module_name for keyword in ['gui', 'ui', 'interface', 'cli', 'web', 'browser', 'streamlit']):
                business_components['user_interfaces'].append({
                    'name': module['name'],
                    'type': self._classify_ui_type(module_name),
                    'entities': len(entities)
                })

            # Identify core business services
            elif any(keyword in module_name for keyword in ['coder', 'editor', 'chat', 'context', 'repomap', 'main']):
                business_components['core_services'].append({
                    'name': module['name'],
                    'type': self._classify_service_type(module_name),
                    'entities': len(entities),
                    'criticality': self._get_module_criticality(module['name'])
                })

            # Identify integration services
            elif any(keyword in module_name for keyword in ['integration', 'service', 'handler', 'api', 'client']):
                business_components['integration_services'].append({
                    'name': module['name'],
                    'type': self._classify_integration_type(module_name),
                    'entities': len(entities)
                })

            # Identify data services
            elif any(keyword in module_name for keyword in ['model', 'data', 'storage', 'db', 'cache']):
                business_components['data_services'].append({
                    'name': module['name'],
                    'type': self._classify_data_type(module_name),
                    'entities': len(entities)
                })

            # Identify external systems from dependencies
            for dep in dependencies:
                dep_name = dep.get('module', '').lower()
                if any(ext in dep_name for ext in ['git', 'api', 'http', 'openai', 'anthropic', 'llm']):
                    business_components['external_systems'].add(dep_name)

        return business_components

```

### 27. generate_business_system_architecture
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.5200000000000005

```python
    def generate_business_system_architecture(self) -> str:
        """Generate a dynamic business-level system architecture diagram."""
        print("🏢 Generating dynamic business system architecture diagram...")

        # Analyze business components from IR data
        business_components = self._analyze_business_components()

        mermaid = ["graph TB"]
        mermaid.append("    %% Dynamic Business System Architecture")
        mermaid.append("")

        # Add external actors (generic)
        mermaid.append("    %% External Actors & Systems")
        mermaid.append("    Users[👥 Users]")
        mermaid.append("    style Users fill:#e1f5fe,stroke:#0277bd,color:#000")
        mermaid.append("    ExternalSystems[🌐 External Systems]")
        mermaid.append("    style ExternalSystems fill:#fff3e0,stroke:#f57c00,color:#000")
        mermaid.append("")

        # System boundary
        project_name = self.metadata.get('project_path', 'System').split('\\')[-1] or 'System'
        mermaid.append(f"    subgraph SystemBoundary[🏢 {project_name}]")
        mermaid.append("        direction TB")
        mermaid.append("")

        # User Interfaces
        if business_components['user_interfaces']:
            mermaid.append("        subgraph UserInterfaces[User Interfaces]")
            for ui in business_components['user_interfaces'][:5]:  # Limit for readability
                ui_name = self._sanitize_name(ui['name'])
                ui_type = ui['type']
                entity_count = ui['entities']
                icon = self._get_ui_icon(ui_type)

                mermaid.append(f"            {ui_name}[{icon} {ui_type}<br/>({entity_count} components)]")
                mermaid.append(f"            style {ui_name} fill:#e8f5e8,stroke:#2e7d32,color:#000")
            mermaid.append("        end")
            mermaid.append("")

        # Core Services
        if business_components['core_services']:
            mermaid.append("        subgraph CoreServices[Core Business Services]")
            # Sort by criticality and entity count
            sorted_services = sorted(business_components['core_services'],
                                   key=lambda x: (x['criticality'] == 'high', x['entities']),
                                   reverse=True)

            for service in sorted_services[:6]:  # Limit for readability
                service_name = self._sanitize_name(service['name'])
                service_type = service['type']
                entity_count = service['entities']
                criticality = service['criticality']
                icon = self._get_service_icon(service_type)

                mermaid.append(f"            {service_name}[{icon} {service_type}<br/>({entity_count} components)]")

                # Color by criticality
                if criticality == 'high':
                    mermaid.append(f"            style {service_name} fill:#ffebee,stroke:#d32f2f,color:#000")
                elif criticality == 'medium':
                    mermaid.append(f"            style {service_name} fill:#fff8e1,stroke:#f9a825,color:#000")
                else:
                    mermaid.append(f"            style {service_name} fill:#f3e5f5,stroke:#7b1fa2,color:#000")

            mermaid.append("        end")
            mermaid.append("")

        # Integration Services
        if business_components['integration_services']:
            mermaid.append("        subgraph IntegrationServices[Integration Services]")
            for integration in business_components['integration_services'][:5]:
                int_name = self._sanitize_name(integration['name'])
                int_type = integration['type']
                entity_count = integration['entities']
                icon = self._get_integration_icon(int_type)

                mermaid.append(f"            {int_name}[{icon} {int_type}<br/>({entity_count} components)]")
                mermaid.append(f"            style {int_name} fill:#fce4ec,stroke:#c2185b,color:#000")
            mermaid.append("        end")
            mermaid.append("")

        # Data Services
        if business_components['data_services']:
            mermaid.append("        subgraph DataServices[Data Services]")
            for data_service in business_components['data_services'][:4]:
                data_name = self._sanitize_name(data_service['name'])
                data_type = data_service['type']
                entity_count = data_service['entities']
                icon = self._get_data_icon(data_type)

                mermaid.append(f"            {data_name}[{icon} {data_type}<br/>({entity_count} components)]")
                mermaid.append(f"            style {data_name} fill:#e0f2f1,stroke:#00695c,color:#000")
            mermaid.append("        end")
            mermaid.append("")

        mermaid.append("    end")
        mermaid.append("")

        # Add basic workflows
        mermaid.append("    %% Primary Workflows")
        mermaid.append("    Users --> UserInterfaces")
        if business_components['user_interfaces'] and business_components['core_services']:
            mermaid.append("    UserInterfaces --> CoreServices")
        if business_components['core_services'] and business_components['integration_services']:
            mermaid.append("    CoreServices --> IntegrationServices")
        if business_components['integration_services']:
            mermaid.append("    IntegrationServices <--> ExternalSystems")
        if business_components['core_services'] and business_components['data_services']:
            mermaid.append("    CoreServices --> DataServices")

        return "\n".join(mermaid)

```

### 28. get_base_classes_of
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5066666666666673

```python
    def get_base_classes_of(self, project_path: str, class_name: str, file_path: str = None, model_name: str = "gpt-3.5-turbo") -> List[Dict]:
        """
        Get the base classes of the specified class.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)
            model_name: Name of the model to use

        Returns:
            A list of dictionaries with base class information
        """
        return self.project_manager.get_base_classes_of(project_path, model_name, class_name, file_path)

```

### 29. get_derived_classes_of
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5066666666666673

```python
    def get_derived_classes_of(self, project_path: str, model_name: str, class_name: str, file_path: str = None) -> List[Dict]:
        """
        Get the derived classes of the specified class.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)

        Returns:
            A list of dictionaries with derived class information
        """
        # Check if we have this information in the cache
        cache_key = (project_path, class_name, 'derived_classes')
        if cache_key in self.class_inheritance_cache and self._is_cache_valid(cache_key):
            return self.class_inheritance_cache[cache_key]

        # Try to use dynamic analysis with RepoMap
        if REPOMAP_AVAILABLE:
            try:
                # Get class inheritance information from RepoMap
                class_info = self._extract_class_info_from_repomap(project_path, model_name)

                if class_name in class_info:
                    derived_classes = []
                    for derived_class_name in class_info[class_name]["derived_classes"]:
                        if derived_class_name in class_info:
                            derived_classes.append({
                                "class_name": derived_class_name,
                                "file_path": os.path.join(project_path, class_info[derived_class_name]["file_path"]),
                                "module_path": self._get_module_path(class_info[derived_class_name]["file_path"])
                            })

                    # Update the cache
                    self.class_inheritance_cache[cache_key] = derived_classes
                    self._update_cache_timestamp(cache_key)

                    return derived_classes
            except Exception as e:
                print(f"Error using RepoMap for class inheritance analysis: {e}")
                # Fall back to static mapping

        # Fall back to static mapping if RepoMap is not available or fails
        # Example class inheritance for Aider's codebase
        class_inheritance = {
            "Coder": {
                "derived_classes": ["EditBlockCoder", "WholeFileCoder", "UDiffCoder", "PatchCoder", "ContextCoder", "ArchitectCoder"],
                "file_path": "aider/coders/base_coder.py"
            },
            "EditBlockCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/editblock_coder.py"
            },
            "WholeFileCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/wholefile_coder.py"
            },
            "UDiffCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/udiff_coder.py"
            },
            "PatchCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/patch_coder.py"
            },
            "ContextCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/context_coder.py"
            },
            "ArchitectCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/architect_coder.py"
            },
            "RepoMap": {
                "derived_classes": [],
                "file_path": "aider/repomap.py"
            }
        }

        if class_name not in class_inheritance:
            return []

        derived_classes = []
        for derived_class_name in class_inheritance[class_name]["derived_classes"]:
            if derived_class_name in class_inheritance:
                derived_classes.append({
                    "class_name": derived_class_name,
                    "file_path": os.path.join(project_path, class_inheritance[derived_class_name]["file_path"]),
                    "module_path": self._get_module_path(class_inheritance[derived_class_name]["file_path"])
                })

        # Update the cache
        self.class_inheritance_cache[cache_key] = derived_classes
        self._update_cache_timestamp(cache_key)

        return derived_classes

```

### 30. main
- **File**: token_budget_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.5052941176470593

```python
def main():
    """Main analysis function"""
    analyzer = TokenBudgetAnalyzer()
    
    try:
        analysis = analyzer.analyze_token_budget()
        report = analyzer.generate_optimization_report(analysis)
        
        # Save detailed analysis to JSON
        with open('token_budget_analysis.json', 'w') as f:
            json.dump(asdict(analysis), f, indent=2, default=str)
        
        # Save report to file
        with open('token_budget_optimization_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("\n" + "="*80)
        print(report)
        print("="*80)
        print(f"\n📄 Detailed analysis saved to: token_budget_analysis.json")
        print(f"📄 Optimization report saved to: token_budget_optimization_report.md")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
```

### 31. update_entity_analysis
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.5043137254901966

```python
    def update_entity_analysis(self, entity_id: str, confidence: float,
                             insights: List[str], issues: List[str]):
        """Update analysis results for an entity."""
        memory = self.get_entity_memory(entity_id)
        memory.last_confidence = confidence
        memory.iterations += 1
        memory.last_analysis_time = datetime.now(timezone.utc).isoformat()

        # Add only new insights to avoid duplicates
        for insight in insights:
            if insight not in memory.insights:
                memory.insights.append(insight)

        # Add only new issues to avoid duplicates
        for issue in issues:
            if issue not in memory.open_issues:
                memory.open_issues.append(issue)
        
        # Update confidence tracking
        if confidence < 0.6:
            self.low_confidence_entities.add(entity_id)
            self.high_confidence_entities.discard(entity_id)
        elif confidence > 0.8:
            self.high_confidence_entities.add(entity_id)
            self.low_confidence_entities.discard(entity_id)
    
```

### 32. _simulate_analysis
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.5043137254901966

```python
    def _simulate_analysis(self, context_bundle, iteration: int) -> Dict[str, Any]:
        """
        Simulate analysis results for the context bundle.

        In a real implementation, this would send the context to an LLM
        and parse the structured response.
        """
        entities = context_bundle.entities
        confidence_scores = {}
        insights = {}
        open_issues = {}

        # Simulate analysis for each entity
        for entity in entities:
            # Create a unique entity ID from module and entity name
            entity_id = f"{entity.module_name}.{entity.entity_name}"

            # Calculate confidence based on entity properties
            analysis_data = {
                'doc_coverage': 0.8 if hasattr(entity, 'docstring') and entity.docstring else 0.2,
                'complexity': len(entity.calls) + len(entity.used_by),
                'dependencies': entity.calls,
                'insights': [f"Analysis insight for {entity_id}"],
                'open_issues': [] if iteration > 2 else [f"Need more analysis of {entity_id}"]
            }

            confidence = self.confidence_tracker.calculate_entity_confidence(entity_id, analysis_data)
            confidence_scores[entity_id] = confidence

            # Generate insights based on entity type and properties (vary by iteration)
            entity_insights = []
            if entity.entity_type == "function":
                if iteration == 1:
                    entity_insights.append(f"Function {entity.entity_name} has {len(entity.calls)} dependencies")
                    if entity.side_effects:
                        entity_insights.append(f"Has side effects: {', '.join(entity.side_effects)}")
                elif iteration == 2:
                    entity_insights.append(f"Function {entity.entity_name} complexity analysis: {len(entity.calls + entity.used_by)} total connections")
                    if entity.errors:
                        entity_insights.append(f"Potential issues detected: {', '.join(entity.errors)}")
                elif iteration >= 3:
                    entity_insights.append(f"Function {entity.entity_name} architectural role: {'core' if len(entity.used_by) > 3 else 'utility'}")
                    if entity.criticality == "high":
                        entity_insights.append(f"High criticality function requiring careful maintenance")
            elif entity.entity_type == "class":
                if iteration == 1:
                    entity_insights.append(f"Class {entity.entity_name} with {len(entity.calls)} methods")
                elif iteration == 2:
                    entity_insights.append(f"Class {entity.entity_name} inheritance and composition analysis")
                elif iteration >= 3:
                    entity_insights.append(f"Class {entity.entity_name} design pattern analysis complete")

            insights[entity_id] = entity_insights

            # Generate open issues for low confidence entities
            if confidence < 0.6:
                open_issues[entity_id] = [f"Low confidence analysis for {entity_id}", "Needs deeper investigation"]
            else:
                open_issues[entity_id] = []

        # Calculate overall confidence
        if confidence_scores:
            overall_confidence = sum(confidence_scores.values()) / len(confidence_scores)
        else:
            overall_confidence = 0.5

        # Determine next focus areas
        next_focus_areas = [
            entity_id for entity_id, confidence in confidence_scores.items()
            if confidence < 0.7
        ][:5]  # Top 5 entities needing more analysis

        # Determine completion status
        if overall_confidence > 0.85 and len(next_focus_areas) == 0:
            completion_status = "complete"
        elif overall_confidence < 0.3:
            completion_status = "needs_clarification"
        else:
            completion_status = "continue"

        return {
            "confidence_scores": confidence_scores,
            "insights": insights,
            "open_issues": open_issues,
            "overall_confidence": overall_confidence,
            "next_focus_areas": next_focus_areas,
            "completion_status": completion_status
        }

```

### 33. _load_dependency_data
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5000000000000004

```python
    def _load_dependency_data(self):
        """
        Load dependency data from the analysis file.
        """
        try:
            if not os.path.exists(DEPENDENCY_ANALYSIS_FILE):
                print(f"Dependency analysis file not found: {DEPENDENCY_ANALYSIS_FILE}")
                return

            # Parse the dependency analysis file
            with open(DEPENDENCY_ANALYSIS_FILE, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract central files
            central_files = []
            central_files_section = content.split("## Top Central Files")[1].split("##")[0]
            for line in central_files_section.strip().split('\n'):
                if line.startswith('- '):
                    parts = line[2:].split(': referenced by ')
                    if len(parts) == 2:
                        file_path = parts[0]
                        ref_count = int(parts[1].split(' ')[0])
                        central_files.append({"file": file_path, "references": ref_count})

            # Extract strong dependencies
            dependencies = {}
            strong_deps_section = content.split("## Strong Dependencies")[1].split("##")[0]
            current_file = None

            for line in strong_deps_section.strip().split('\n'):
                if line.startswith('### '):
                    current_file = line[4:].split(' strongly depends on:')[0]
                    dependencies[current_file] = []
                elif line.startswith('- ') and current_file:
                    parts = line[2:].split(' (')
                    if len(parts) == 2:
                        dep_file = parts[0]
                        ref_count = int(parts[1].split(' references')[0])
                        dependencies[current_file].append((dep_file, ref_count))

            # Extract class dependencies
            class_dependencies = {}
            class_deps_section = content.split("## Class Dependencies")[1]
            current_class = None

            for line in class_deps_section.strip().split('\n'):
                if line.startswith('### '):
                    current_class = line[4:].split(' depends on:')[0]
                    class_dependencies[current_class] = []
                elif line.startswith('- ') and current_class:
                    parts = line[2:].split(' (')
                    if len(parts) == 2:
                        dep_class = parts[0]
                        ref_count = int(parts[1].split(' references')[0])
                        class_dependencies[current_class].append((dep_class, ref_count))

            self.dependency_data = {
                "central_files": central_files,
                "dependencies": dependencies,
                "class_dependencies": class_dependencies
            }
        except Exception as e:
            print(f"Error loading dependency data: {e}")
            self.dependency_data = None

```

### 34. _extract_class_info_from_repomap
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5000000000000004

```python
    def _extract_class_info_from_repomap(self, project_path: str, model_name: str) -> Dict[str, Dict]:
        """
        Extract class inheritance information from RepoMap.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use

        Returns:
            Dictionary mapping class names to their inheritance information
        """
        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return {}

        class_info = {}

        # Get all Python files in the project
        python_files = []
        for root, _, files in os.walk(project_path):
            # Skip __pycache__ directories and other non-source directories
            if "__pycache__" in root or ".git" in root:
                continue

            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        # Extract class definitions and inheritance from each file
        for file_path in python_files:
            try:
                rel_path = os.path.relpath(file_path, project_path)
                abs_path = os.path.abspath(file_path)

                # Read the file content to analyze class definitions more accurately
                with open(abs_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # Get tags for the file
                tags = list(repo_map.get_tags(file_path, rel_path))

                # Find class definitions
                class_defs = []
                for tag in tags:
                    if tag.kind == 'def' and tag.name.startswith('class '):
                        class_defs.append(tag)
                    elif hasattr(tag, 'kind') and tag.kind == 'class':
                        class_defs.append(tag)

                # If no class definitions found through tags, try regex pattern matching
                if not class_defs:
                    class_pattern = r'class\s+(\w+)\s*(?:\((.*?)\))?:'
                    for match in re.finditer(class_pattern, file_content):
                        class_name = match.group(1)
                        base_classes_str = match.group(2) or ""

                        # Initialize class info
                        if class_name not in class_info:
                            class_info[class_name] = {
                                "file_path": rel_path,
                                "base_classes": [],
                                "derived_classes": [],
                                "methods": [],
                                "attributes": []
                            }

                        # Extract base classes
                        if base_classes_str:
                            # Handle complex inheritance with commas inside parentheses
                            # For example: class A(B, C(D, E), F):
                            base_classes = []
                            current = ""
                            paren_level = 0

                            for char in base_classes_str:
                                if char == '(' and paren_level == 0:
                                    paren_level += 1
                                    current += char
                                elif char == ')' and paren_level > 0:
                                    paren_level -= 1
                                    current += char
                                elif char == ',' and paren_level == 0:
                                    base_classes.append(current.strip())
                                    current = ""
                                else:
                                    current += char

                            if current:
                                base_classes.append(current.strip())

                            # Clean up base class names (remove generic parameters, etc.)
                            cleaned_bases = []
                            for base in base_classes:
                                # Extract just the class name without generic parameters
                                base_name = re.match(r'([a-zA-Z0-9_]+)', base)
                                if base_name:
                                    cleaned_bases.append(base_name.group(1))

                            class_info[class_name]["base_classes"] = cleaned_bases
                else:
                    # Process class definitions found through tags
                    for tag in class_defs:
                        class_name = tag.name
                        if hasattr(tag, 'name') and tag.name.startswith('class '):
                            class_name = tag.name.split(' ')[1]

                        # Initialize class info
                        if class_name not in class_info:
                            class_info[class_name] = {
                                "file_path": rel_path,
                                "base_classes": [],
                                "derived_classes": [],
                                "methods": [],
                                "attributes": []
                            }

                        # Look for inheritance information in the tag scope or file content
                        class_pattern = r'class\s+' + re.escape(class_name) + r'\s*\((.*?)\):'
                        match = re.search(class_pattern, file_content)
                        if match:
                            base_classes_str = match.group(1)
                            base_classes = [bc.strip().split('[')[0].split('(')[0] for bc in base_classes_str.split(',')]
                            class_info[class_name]["base_classes"] = base_classes

                # Extract methods for each class
                method_pattern = r'def\s+(\w+)\s*\('
                class_blocks = re.finditer(r'class\s+(\w+)[^\{]*?:\s*(?:\n\s+[^\n]+)*', file_content, re.MULTILINE)

                for class_block_match in class_blocks:
                    class_name = class_block_match.group(1)
                    if class_name in class_info:
                        class_block = class_block_match.group(0)
                        # Find methods in the class block
                        for method_match in re.finditer(method_pattern, class_block):
                            method_name = method_match.group(1)
                            if method_name not in ['__init__', '__str__', '__repr__']:  # Skip common magic methods
                                if method_name not in class_info[class_name]["methods"]:
                                    class_info[class_name]["methods"].append(method_name)
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                continue

        # Build derived classes relationships
        for class_name, info in class_info.items():
            for base_class in info["base_classes"]:
                if base_class in class_info:
                    if class_name not in class_info[base_class]["derived_classes"]:
                        class_info[base_class]["derived_classes"].append(class_name)

        return class_info

```

### 35. _find_file_defining_symbol
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5000000000000004

```python
    def _find_file_defining_symbol(self, project_path: str, model_name: str, symbol_name: str) -> Optional[str]:
        """
        Find the file that defines a specific symbol.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            symbol_name: Name of the symbol to find

        Returns:
            Relative path to the file defining the symbol, or None if not found
        """
        # Check if we have this information in the cache
        cache_key = (project_path, symbol_name)
        if cache_key in self.symbol_cache and self._is_cache_valid(cache_key):
            return self.symbol_cache[cache_key]

        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return None

        # Get all Python files in the project
        python_files = []
        for root, _, files in os.walk(project_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        # Look for the symbol definition in each file
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, project_path)

            # Get tags for the file
            tags = list(repo_map.get_tags(file_path, rel_path))

            # Check if the symbol is defined in this file
            for tag in tags:
                if (tag.kind == 'def' or tag.kind == 'class') and tag.name == symbol_name:
                    # Update the cache
                    self.symbol_cache[cache_key] = rel_path
                    self._update_cache_timestamp(cache_key)
                    return rel_path

        return None

```

### 36. analyze_file_filtering_impact
- **File**: corrected_token_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.491960784313726

```python
def analyze_file_filtering_impact():
    """Analyze the impact of file filtering on token usage"""
    
    print("\n🔍 Analyzing file filtering impact...")
    
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Get all files
    all_files = find_src_files("aider-main")
    
    # Categorize files
    source_files = []
    other_files = []
    
    source_extensions = {
        '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.rb', '.go', 
        '.rs', '.php', '.swift', '.kt', '.scala', '.dart', '.ex', '.elm'
    }
    
    exclude_extensions = {
        '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.mp3', '.mp4', 
        '.ttf', '.woff', '.woff2', '.eot', '.pyc', '.pyo', '.class', '.o', 
        '.so', '.dll', '.exe', '.db', '.db-shm', '.db-wal', '.cache'
    }
    
    for file_path in all_files:
        ext = Path(file_path).suffix.lower()
        if ext in exclude_extensions:
            other_files.append(file_path)
        elif ext in source_extensions:
            source_files.append(file_path)
        else:
            # Config, docs, etc. - keep these
            source_files.append(file_path)
    
    print(f"📁 Source + essential files: {len(source_files)}")
    print(f"📁 Excludable files: {len(other_files)}")
    
    # Test with filtered files
    repo_map = RepoMap(
        root="aider-main",
        main_model=model,
        io=io,
        verbose=False,
        refresh="always"
    )
    
    print("🔄 Generating repo map with FILTERED files...")
    filtered_content = repo_map.get_repo_map([], source_files)
    
    if filtered_content:
        filtered_tokens = int(repo_map.token_count(filtered_content))
        print(f"📊 Filtered token usage: {filtered_tokens:,} tokens")
        print(f"📊 Default token limit: {repo_map.max_map_tokens:,} tokens")
        
        if filtered_tokens <= repo_map.max_map_tokens:
            print(f"✅ WITHIN DEFAULT LIMIT after filtering!")
            savings = len(all_files) - len(source_files)
            print(f"💾 Files saved: {savings} ({(savings/len(all_files)*100):.1f}%)")
        else:
            print(f"⚠️  Still over limit even after filtering")
        
        return {
            "filtered_tokens": filtered_tokens,
            "filtered_files": len(source_files),
            "excluded_files": len(other_files),
            "within_limit": filtered_tokens <= repo_map.max_map_tokens
        }
    
    return None


```

### 37. get_top_central_files
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def get_top_central_files(self, project_path: str, count: int = 10, model_name: str = "gpt-3.5-turbo") -> List[Dict]:
        """
        Get the top central files in the project based on reference count.

        Args:
            project_path: Path to the project root
            count: Number of top files to return
            model_name: Name of the model to use

        Returns:
            A list of dictionaries with file paths and reference counts
        """
        return self.project_manager.get_top_central_files(project_path, model_name, count)

```

### 38. _analyze_side_effects
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def _analyze_side_effects(self, file_content: str, func_name: str) -> List[str]:
        """Analyze potential side effects of a function."""
        side_effects = []

        try:
            # Find the function body
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\([^)]*\).*?:(.*?)(?=\ndef|\nclass|\Z)'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return side_effects

            func_body = match.group(1)

            # Check for common side effects
            if 'print(' in func_body or 'logging.' in func_body:
                side_effects.append('writes_log')
            if 'open(' in func_body or 'write(' in func_body:
                side_effects.append('modifies_file')
            if 'self.' in func_body and '=' in func_body:
                side_effects.append('modifies_state')
            if 'global ' in func_body:
                side_effects.append('modifies_global')
            if 'import ' in func_body:
                side_effects.append('dynamic_import')

        except Exception:
            pass

        return side_effects if side_effects else ['none']

```

### 39. _calculate_change_risk
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def _calculate_change_risk(self, symbol_name: str, dependencies: List[str], project_path: str, model_name: str) -> str:
        """Calculate the change risk level of a symbol."""
        try:
            # Risk factors:
            # 1. Number of dependencies
            # 2. Complexity of dependencies
            # 3. Usage across the codebase

            risk_score = 0

            # Factor 1: Number of dependencies
            dep_count = len(dependencies)
            if dep_count > 10:
                risk_score += 3
            elif dep_count > 5:
                risk_score += 2
            elif dep_count > 2:
                risk_score += 1

            # Factor 2: Check if symbol is in a central file
            if self.project_manager.dependency_data:
                central_files = self.project_manager.dependency_data.get("central_files", [])
                for file_info in central_files[:10]:  # Top 10 central files
                    if symbol_name.lower() in file_info.get("file", "").lower():
                        risk_score += 2
                        break

            # Factor 3: Check for complex dependencies
            complex_deps = ['database', 'network', 'file', 'thread', 'async']
            for dep in dependencies:
                if any(complex_word in dep.lower() for complex_word in complex_deps):
                    risk_score += 1
                    break

            # Determine risk level
            if risk_score >= 5:
                return "high"
            elif risk_score >= 3:
                return "medium"
            else:
                return "low"

        except Exception:
            return "medium"  # Default to medium if analysis fails

```

### 40. _calculate_simple_dependencies
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def _calculate_simple_dependencies(self, module: Dict, all_modules: List[Dict], project_path: str) -> List[Dict]:
        """Calculate dependencies using a simpler approach based on imports in the file."""
        dependencies = []

        try:
            file_path = os.path.join(project_path, module["file"])

            # Read the file and look for import statements
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find import statements
            import_lines = []
            for line in content.split('\n'):
                stripped = line.strip()
                if stripped.startswith('import ') or stripped.startswith('from '):
                    import_lines.append(stripped)

            # Check which other modules might be imported
            for other_module in all_modules:
                if other_module["name"] == module["name"]:
                    continue

                # Check if this module is referenced in imports
                module_referenced = False
                for import_line in import_lines:
                    # Simple heuristic: check if module name appears in import
                    if other_module["name"].lower() in import_line.lower():
                        module_referenced = True
                        break

                    # Check if file name (without extension) appears
                    file_base = other_module["file"].replace('.py', '').replace('/', '.').replace('\\', '.')
                    if file_base.lower() in import_line.lower():
                        module_referenced = True
                        break

                if module_referenced:
                    # Simple strength calculation based on number of import lines
                    strength = "weak"
                    if len(import_lines) > 10:
                        strength = "strong"
                    elif len(import_lines) > 5:
                        strength = "medium"

                    dependencies.append({
                        "module": other_module["name"],
                        "strength": strength
                    })

            return dependencies[:10]  # Limit to first 10 dependencies

        except Exception as e:
            print(f"   Error calculating simple dependencies for {module['name']}: {e}")
            return []

```

### 41. get_contextual_dependencies
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> Dict:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A dictionary with contextual dependency information
        """
        extractor = self._get_context_extractor()
        context_map = extractor.get_contextual_dependencies(project_path, primary_file, max_snippets)

        # Convert to a serializable dictionary
        result = {
            'primary_file': context_map.primary_file,
            'related_files': context_map.related_files,
            'usage_contexts': [],
            'definition_contexts': []
        }

        # Add usage contexts
        for usage_ctx in context_map.usage_contexts:
            result['usage_contexts'].append({
                'file_path': usage_ctx.snippet.file_path,
                'start_line': usage_ctx.snippet.start_line,
                'end_line': usage_ctx.snippet.end_line,
                'content': usage_ctx.snippet.content,
                'symbol_name': usage_ctx.snippet.symbol_name,
                'usage_type': usage_ctx.usage_type.value,
                'surrounding_function': usage_ctx.snippet.surrounding_function
            })

        # Add definition contexts
        for def_ctx in context_map.definition_contexts:
            result['definition_contexts'].append({
                'file_path': def_ctx.snippet.file_path,
                'start_line': def_ctx.snippet.start_line,
                'end_line': def_ctx.snippet.end_line,
                'content': def_ctx.snippet.content,
                'symbol_name': def_ctx.snippet.symbol_name,
                'definition_type': def_ctx.definition_type.value,
                'signature': def_ctx.signature,
                'docstring': def_ctx.docstring
            })

        return result

```

### 42. get_focused_inheritance_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4866666666666672

```python
    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> Dict:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            A dictionary with inheritance context information
        """
        extractor = self._get_context_extractor()
        inheritance_map = extractor.get_focused_inheritance_context(project_path, class_name, file_path)

        # Convert to a serializable dictionary
        result = {
            'class_name': inheritance_map.class_name,
            'file_path': inheritance_map.file_path,
            'base_classes': [],
            'derived_classes': [],
            'implementation_contexts': []
        }

        # Add base classes
        for base_ctx in inheritance_map.base_classes:
            result['base_classes'].append({
                'file_path': base_ctx.snippet.file_path,
                'start_line': base_ctx.snippet.start_line,
                'end_line': base_ctx.snippet.end_line,
                'content': base_ctx.snippet.content,
                'symbol_name': base_ctx.snippet.symbol_name,
                'signature': base_ctx.signature,
                'docstring': base_ctx.docstring
            })

        # Add derived classes
        for derived_ctx in inheritance_map.derived_classes:
            result['derived_classes'].append({
                'file_path': derived_ctx.snippet.file_path,
                'start_line': derived_ctx.snippet.start_line,
                'end_line': derived_ctx.snippet.end_line,
                'content': derived_ctx.snippet.content,
                'symbol_name': derived_ctx.snippet.symbol_name,
                'signature': derived_ctx.signature,
                'docstring': derived_ctx.docstring
            })

        # Add implementation contexts
        for impl_ctx in inheritance_map.implementation_contexts:
            result['implementation_contexts'].append({
                'file_path': impl_ctx.file_path,
                'start_line': impl_ctx.start_line,
                'end_line': impl_ctx.end_line,
                'content': impl_ctx.content,
                'symbol_name': impl_ctx.symbol_name,
                'surrounding_function': impl_ctx.surrounding_function
            })

        return result

```

### 43. provide_edit_analysis
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.4790196078431377

```python
    def provide_edit_analysis(self, edits):
        """
        Instead of applying edits, analyze them and provide informative feedback.
        This is the core method that transforms Aider from an editing tool to an informative one.
        """
        if not edits:
            self.io.tool_output("No changes were suggested for analysis.")
            return

        self.io.tool_output("\n📊 Code Analysis Report 📊\n", bold=True)

        for edit in edits:
            path = edit[0]
            self.io.tool_output(f"📁 File: {path}", bold=True)

            # Different coders have different edit formats, so we need to handle them differently
            if len(edit) > 2:  # For wholefile_coder format
                _, source, content = edit
                self.io.tool_output(f"Source: {source}")
                self.analyze_content_changes(path, content)
            elif len(edit) == 2:  # For editblock_coder format
                if isinstance(edit[1], str):  # Simple content replacement
                    content = edit[1]
                    self.analyze_content_changes(path, content)
                else:  # Original/updated blocks
                    original, updated = edit[1]
                    self.analyze_diff_changes(path, original, updated)

        self.io.tool_output("\n💡 Recommendations", bold=True)
        self.io.tool_output("The analysis above shows potential improvements that could be made to your code.")
        self.io.tool_output("Consider implementing these changes manually if they align with your goals.")
        self.io.tool_output("Remember: This is an informative tool and does not modify your files.")

```

### 44. provide_edit_analysis
- **File**: aider-main\aider\coders\base_coder_old.py
- **Priority**: critical
- **Relevance Score**: 2.4790196078431377

```python
    def provide_edit_analysis(self, edits):
        """
        Instead of applying edits, analyze them and provide informative feedback.
        This is the core method that transforms Aider from an editing tool to an informative one.
        """
        if not edits:
            self.io.tool_output("No changes were suggested for analysis.")
            return

        self.io.tool_output("\n📊 Code Analysis Report 📊\n", bold=True)

        for edit in edits:
            path = edit[0]
            self.io.tool_output(f"📁 File: {path}", bold=True)

            # Different coders have different edit formats, so we need to handle them differently
            if len(edit) > 2:  # For wholefile_coder format
                _, source, content = edit
                self.io.tool_output(f"Source: {source}")
                self.analyze_content_changes(path, content)
            elif len(edit) == 2:  # For editblock_coder format
                if isinstance(edit[1], str):  # Simple content replacement
                    content = edit[1]
                    self.analyze_content_changes(path, content)
                else:  # Original/updated blocks
                    original, updated = edit[1]
                    self.analyze_diff_changes(path, original, updated)

        self.io.tool_output("\n💡 Recommendations", bold=True)
        self.io.tool_output("The analysis above shows potential improvements that could be made to your code.")
        self.io.tool_output("Consider implementing these changes manually if they align with your goals.")
        self.io.tool_output("Remember: This is an informative tool and does not modify your files.")

```

### 45. main
- **File**: repomap_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.471960784313726

```python
def main():
    """Main analysis function"""
    analyzer = RepoMapAnalyzer()

    try:
        metrics = analyzer.analyze_repository_map()
        report = analyzer.generate_report()

        # Save detailed metrics to JSON
        with open('repomap_metrics.json', 'w') as f:
            json.dump(asdict(metrics), f, indent=2, default=str)

        # Save report to file
        with open('repomap_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("\n" + "="*80)
        print(report)
        print("="*80)
        print(f"\n📄 Detailed metrics saved to: repomap_metrics.json")
        print(f"📄 Analysis report saved to: repomap_analysis_report.md")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
```

### 46. _calculate_criticality
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4666666666666672

```python
    def _calculate_criticality(self, symbol_name: str, project_path: str, model_name: str) -> str:
        """Calculate the criticality level of a symbol."""
        try:
            # Get files that use this symbol
            used_by_count = 0

            # Check if this symbol is used across multiple files
            if self.project_manager.dependency_data:
                central_files = self.project_manager.dependency_data.get("central_files", [])
                for file_info in central_files:
                    if symbol_name.lower() in file_info.get("file", "").lower():
                        used_by_count = file_info.get("references", 0)
                        break

            # Determine criticality based on usage
            if used_by_count > 50:
                return "high"
            elif used_by_count > 20:
                return "medium"
            else:
                return "low"

        except Exception:
            return "medium"  # Default to medium if analysis fails

```

### 47. main
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.4666666666666672

```python
def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Generate system architecture diagrams from Mid-Level IR JSON data"
    )
    parser.add_argument(
        'ir_file',
        nargs='?',
        default='complete_mid_level_ir.json',
        help='Path to the IR JSON file (default: complete_mid_level_ir.json)'
    )
    parser.add_argument(
        '--output-dir',
        default='diagrams',
        help='Output directory for generated diagrams (default: diagrams)'
    )
    parser.add_argument(
        '--diagram-type',
        choices=['all', 'business', 'overview', 'dependencies', 'components', 'critical'],
        default='all',
        help='Type of diagram to generate (default: all)'
    )

    args = parser.parse_args()

    print("🎨 System Architecture Diagram Generator")
    print("=" * 50)

    # Load IR data
    print(f"📂 Loading IR data from: {args.ir_file}")
    ir_data = load_ir_data(args.ir_file)
    if not ir_data:
        return 1

    # Initialize generator
    generator = ArchitectureDiagramGenerator(ir_data)

    # Generate diagrams based on type
    if args.diagram_type == 'all':
        diagrams = generator.generate_all_diagrams(args.output_dir)
        print(f"\n✅ Generated {len(diagrams)} architecture diagrams in '{args.output_dir}' directory")
    else:
        # Generate specific diagram type
        diagram_methods = {
            'business': generator.generate_business_system_architecture,
            'overview': generator.generate_system_overview_diagram,
            'dependencies': generator.generate_dependency_graph_diagram,
            'components': generator.generate_component_architecture_diagram,
            'critical': generator.generate_critical_path_diagram
        }

        method = diagram_methods[args.diagram_type]
        diagram_content = method()

        # Save single diagram
        output_path = Path(args.output_dir)
        output_path.mkdir(exist_ok=True)
        file_path = output_path / f"{args.diagram_type}.mmd"

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(diagram_content)

        print(f"\n✅ Generated {args.diagram_type} diagram: {file_path}")

    print("\n🔧 To view the diagrams:")
    print("   1. Copy .mmd content to https://mermaid.live/")
    print("   2. Use VS Code with Mermaid Preview extension")
    print("   3. Use mermaid-cli: mmdc -i diagram.mmd -o diagram.png")

    return 0


if __name__ == "__main__":
    exit(main())
```

### 48. get_entity_memory
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.451960784313726

```python
    def get_entity_memory(self, entity_id: str) -> EntityAnalysisMemory:
        """Get or create memory for a specific entity."""
        if entity_id not in self.entity_memories:
            self.entity_memories[entity_id] = EntityAnalysisMemory(entity_id=entity_id)
        return self.entity_memories[entity_id]
    
```

### 49. analyze_actual_token_usage
- **File**: corrected_token_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
def analyze_actual_token_usage():
    """Analyze token usage with ACTUAL aider defaults"""
    
    print("🔍 Analyzing ACTUAL token configuration...")
    
    # Create model and IO
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Get the ACTUAL default token limit from the model
    actual_default_tokens = model.get_repo_map_tokens()
    print(f"📊 Model's default repo map tokens: {actual_default_tokens}")
    
    # Create RepoMap with DEFAULT settings (no custom token limit)
    repo_map_default = RepoMap(
        # Don't specify map_tokens - let it use the default
        root="aider-main",
        main_model=model,
        io=io,
        verbose=True,
        refresh="always"
    )
    
    print(f"📊 RepoMap default tokens: {repo_map_default.max_map_tokens}")
    print(f"📊 No-files multiplier: {repo_map_default.map_mul_no_files}")
    
    # Get all source files
    all_files = find_src_files("aider-main")
    print(f"📁 Total files found: {len(all_files)}")
    
    # Generate repo map with default settings
    print("🔄 Generating repo map with DEFAULT settings...")
    repo_content = repo_map_default.get_repo_map([], all_files)
    
    if repo_content:
        actual_tokens = int(repo_map_default.token_count(repo_content))
        print(f"📊 Actual token usage: {actual_tokens:,} tokens")
        print(f"📊 Default token limit: {repo_map_default.max_map_tokens:,} tokens")
        
        # Calculate if we're over the limit
        if actual_tokens > repo_map_default.max_map_tokens:
            over_limit = actual_tokens - repo_map_default.max_map_tokens
            over_pct = (over_limit / repo_map_default.max_map_tokens) * 100
            print(f"⚠️  OVER LIMIT by {over_limit:,} tokens ({over_pct:.1f}%)")
        else:
            under_limit = repo_map_default.max_map_tokens - actual_tokens
            under_pct = (under_limit / repo_map_default.max_map_tokens) * 100
            print(f"✅ Under limit by {under_limit:,} tokens ({under_pct:.1f}%)")
        
        # Calculate expanded limit (when no files in chat)
        max_context = getattr(model, 'max_input_tokens', 128000)
        padding = 4096
        expanded_limit = min(
            int(repo_map_default.max_map_tokens * repo_map_default.map_mul_no_files),
            max_context - padding
        )
        print(f"📊 Expanded limit (no files): {expanded_limit:,} tokens")
        
        if actual_tokens > expanded_limit:
            print(f"⚠️  Even EXPANDED limit exceeded!")
        else:
            print(f"✅ Within expanded limit")
        
        # Save results
        results = {
            "model_default_tokens": actual_default_tokens,
            "repomap_default_tokens": repo_map_default.max_map_tokens,
            "actual_usage_tokens": actual_tokens,
            "no_files_multiplier": repo_map_default.map_mul_no_files,
            "expanded_limit": expanded_limit,
            "max_context_window": max_context,
            "files_processed": len(all_files),
            "over_default_limit": actual_tokens > repo_map_default.max_map_tokens,
            "over_expanded_limit": actual_tokens > expanded_limit,
            "default_limit_excess": max(0, actual_tokens - repo_map_default.max_map_tokens),
            "expanded_limit_excess": max(0, actual_tokens - expanded_limit)
        }
        
        with open('actual_token_analysis.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Results saved to: actual_token_analysis.json")
        
        return results
    else:
        print("❌ Failed to generate repo map")
        return None


```

### 50. _perform_iteration
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _perform_iteration(self, task: str, task_type: str, focus_entities: Optional[List[str]],
                          iteration: int) -> AnalysisResult:
        """Perform a single analysis iteration."""
        print(f"   🎯 Selecting context for iteration {iteration}")

        # Get enhanced context selection with memory integration
        context_bundle = self._get_enhanced_context(task, task_type, focus_entities)

        print(f"   📊 Selected {len(context_bundle.entities)} entities")
        print(f"   🎯 Token usage: {context_bundle.total_tokens}")

        # Simulate analysis (in real implementation, this would call LLM)
        analysis_results = self._simulate_analysis(context_bundle, iteration)

        # Update memory with results
        self._update_memory_with_results(analysis_results)

        # Create iteration result
        result = AnalysisResult(
            task=task,
            iteration=iteration,
            timestamp=datetime.now(timezone.utc).isoformat(),
            entities_analyzed=[f"{entity.module_name}.{entity.entity_name}" for entity in context_bundle.entities],
            confidence_scores=analysis_results["confidence_scores"],
            insights=analysis_results["insights"],
            open_issues=analysis_results["open_issues"],
            overall_confidence=analysis_results["overall_confidence"],
            next_focus_areas=analysis_results["next_focus_areas"],
            completion_status=analysis_results["completion_status"]
        )

        # Add to memory
        self.analysis_memory.add_analysis_result(result)

        return result

```

### 51. _get_enhanced_context
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _get_enhanced_context(self, task: str, task_type: str, focus_entities: Optional[List[str]]):
        """Get context selection enhanced with memory and confidence tracking."""
        # Enhance focus entities with memory-driven suggestions
        enhanced_focus = self._enhance_focus_with_memory(focus_entities)

        # Try to use ContextBundleBuilder for enhanced context selection
        bundle_builder = self._get_context_bundle_builder()

        if bundle_builder is not None:
            print(f"   🏗️ Using ContextBundleBuilder for enhanced context selection")

            # Use the enhanced context bundle builder
            enhanced_bundle = bundle_builder.build(
                task=task,
                task_type=task_type,
                focus_entities=enhanced_focus
            )

            # SAVE THE ENHANCED BUNDLE DATA BEFORE CONVERSION
            self._save_enhanced_bundle_data(enhanced_bundle)

            # Convert enhanced bundle to compatible format
            context_bundle = self._convert_enhanced_bundle_to_context_bundle(enhanced_bundle)

        else:
            print(f"   🎯 Using standard IntelligentContextSelector")

            # Fall back to standard context selector
            from intelligent_context_selector import TaskType

            # Map string task type to enum
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Get context bundle from selector
            context_bundle = self.context_selector.select_optimal_context(
                task_description=task,
                task_type=task_enum,
                focus_entities=enhanced_focus
            )

        return context_bundle

```

### 52. _save_enhanced_bundle_data
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _save_enhanced_bundle_data(self, enhanced_bundle):
        """Save the enhanced bundle data to analysis memory for future reference."""
        try:
            # Extract detailed data from the enhanced bundle
            enhanced_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "iteration": self.current_iteration,
                "task": enhanced_bundle.task,
                "task_type": enhanced_bundle.task_type,
                "selected_entities_count": len(enhanced_bundle.context_bundle),
                "token_estimate": enhanced_bundle.token_estimate,
                "selection_rationale": enhanced_bundle.selection_rationale,
                "score_distribution": enhanced_bundle.score_distribution,
                "confidence_analysis": getattr(enhanced_bundle, 'confidence_analysis', {}),
                "memory_insights": getattr(enhanced_bundle, 'memory_insights', {}),
                "entity_scores": []
            }

            # Save detailed entity scoring information
            for entity in enhanced_bundle.context_bundle:
                if hasattr(entity, 'score_breakdown'):
                    entity_score_data = {
                        "entity_id": f"{entity.module_name}.{entity.entity_name}",
                        "total_score": entity.total_score,
                        "score_breakdown": {
                            "criticality": entity.score_breakdown.criticality,
                            "change_risk": entity.score_breakdown.change_risk,
                            "task_relevance": entity.score_breakdown.task_relevance,
                            "confidence_gap": entity.score_breakdown.confidence_gap,
                            "dependency_proximity": entity.score_breakdown.dependency_proximity,
                            "complexity": entity.score_breakdown.complexity,
                            "doc_gap": entity.score_breakdown.doc_gap,
                            "historical_relevance": entity.score_breakdown.historical_relevance
                        }
                    }
                    enhanced_data["entity_scores"].append(entity_score_data)

            # Store in analysis memory
            self.analysis_memory.add_enhanced_bundle_data(enhanced_data)

            print(f"   💾 Saved enhanced bundle data with {len(enhanced_data['entity_scores'])} entity scores")

        except Exception as e:
            print(f"   ⚠️ Warning: Could not save enhanced bundle data: {e}")

```

### 53. _compile_final_results
- **File**: iterative_analysis_engine.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _compile_final_results(self, all_results: List[AnalysisResult],
                              overall_confidence: float) -> Dict[str, Any]:
        """Compile final analysis results from all iterations."""
        # Aggregate all entities analyzed
        all_entities = set()
        for result in all_results:
            all_entities.update(result.entities_analyzed)

        # Create entity summaries
        entity_summaries = {}
        for entity_id in all_entities:
            memory = self.analysis_memory.get_entity_memory(entity_id)
            entity_summaries[entity_id] = {
                "final_confidence": memory.last_confidence,
                "total_iterations": memory.iterations,
                "insights": memory.insights,
                "open_issues": memory.open_issues,
                "suggested_next_steps": memory.suggested_next_steps
            }

        # Aggregate insights across all iterations
        all_insights = []
        for result in all_results:
            for entity_insights in result.insights.values():
                all_insights.extend(entity_insights)

        return {
            "task": self.analysis_memory.task_context["task"],
            "task_type": self.analysis_memory.task_context["task_type"],
            "total_iterations": len(all_results),
            "overall_confidence": overall_confidence,
            "entity_summaries": entity_summaries,
            "global_insights": list(set(all_insights)),  # Remove duplicates
            "analysis_summary": self.analysis_memory.get_analysis_summary(),
            "confidence_summary": self.confidence_tracker.get_confidence_summary(),
            "enhanced_bundle_history": self.analysis_memory.get_enhanced_bundle_history(),  # Include saved ContextBundleBuilder data
            "iteration_history": [
                {
                    "iteration": result.iteration,
                    "timestamp": result.timestamp,
                    "entities_count": len(result.entities_analyzed),
                    "confidence": result.overall_confidence,
                    "status": result.completion_status
                }
                for result in all_results
            ]
        }
```

### 54. _analyze_size_and_scale
- **File**: repomap_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _analyze_size_and_scale(self, repo_map: RepoMap):
        """Analyze size and scale metrics"""
        print("📏 Analyzing size and scale...")

        # Get all source files
        all_files = find_src_files(self.project_path)
        self.metrics.num_files_mapped = len(all_files)

        # Generate repository map and measure memory
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        start_time = time.time()
        repo_content = repo_map.get_repo_map([], all_files)
        end_time = time.time()

        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        self.metrics.memory_usage_mb = memory_after - memory_before
        self.metrics.generation_time_seconds = end_time - start_time

        if repo_content:
            self.metrics.total_size_bytes = len(repo_content.encode('utf-8'))
            self.metrics.total_size_kb = self.metrics.total_size_bytes / 1024
            self.metrics.total_size_mb = self.metrics.total_size_kb / 1024

            # Count symbols (rough estimate based on lines with definitions)
            lines = repo_content.split('\n')
            symbol_lines = [line for line in lines if any(marker in line for marker in ['def ', 'class ', '│def ', '│class '])]
            self.metrics.num_symbols_tracked = len(symbol_lines)

```

### 55. _analyze_token_usage
- **File**: repomap_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _analyze_token_usage(self, repo_map: RepoMap):
        """Analyze token usage and efficiency"""
        print("🎯 Analyzing token usage...")

        all_files = find_src_files(self.project_path)
        repo_content = repo_map.get_repo_map([], all_files)

        if repo_content:
            self.metrics.total_tokens = repo_map.token_count(repo_content)

            # Calculate token efficiency (tokens per byte)
            if self.metrics.total_size_bytes > 0:
                self.metrics.token_efficiency_ratio = self.metrics.total_tokens / self.metrics.total_size_bytes

            # Calculate context window usage
            max_context = getattr(self.model, 'max_input_tokens', 128000)  # Default to 128k
            if max_context:
                self.metrics.context_window_usage_pct = (self.metrics.total_tokens / max_context) * 100

```

### 56. _analyze_performance
- **File**: repomap_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _analyze_performance(self, repo_map: RepoMap):
        """Analyze performance metrics"""
        print("⚡ Analyzing performance...")

        # Test cache performance
        all_files = find_src_files(self.project_path)

        # First call (cache miss)
        start_time = time.time()
        repo_map.get_repo_map([], all_files, force_refresh=True)
        first_call_time = time.time() - start_time

        # Second call (cache hit)
        start_time = time.time()
        repo_map.get_repo_map([], all_files)
        second_call_time = time.time() - start_time

        # Calculate cache efficiency
        if first_call_time > 0:
            cache_speedup = first_call_time / max(second_call_time, 0.001)
            self.metrics.cache_hit_rate_pct = max(0, (1 - second_call_time / first_call_time) * 100)

        # Identify bottlenecks
        if self.metrics.generation_time_seconds > 5.0:
            self.metrics.processing_bottlenecks.append("Slow generation time")
        if self.metrics.memory_usage_mb > 100:
            self.metrics.processing_bottlenecks.append("High memory usage")

```

### 57. _analyze_reliability
- **File**: repomap_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _analyze_reliability(self, repo_map: RepoMap):
        """Analyze reliability and accuracy"""
        print("🎯 Analyzing reliability...")

        # This is a simplified analysis - in a real scenario, you'd compare
        # against a ground truth or use more sophisticated symbol detection

        all_files = find_src_files(self.project_path)
        repo_content = repo_map.get_repo_map([], all_files)

        if repo_content:
            # Count actual symbols vs detected symbols (rough estimate)
            total_actual_symbols = 0
            for file_path in all_files[:10]:  # Sample first 10 files
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Count function and class definitions
                        lines = content.split('\n')
                        actual_symbols = len([line for line in lines if line.strip().startswith(('def ', 'class '))])
                        total_actual_symbols += actual_symbols
                except:
                    continue

            # Estimate accuracy based on symbol detection
            if total_actual_symbols > 0:
                detected_symbols = min(self.metrics.num_symbols_tracked, total_actual_symbols)
                self.metrics.symbol_detection_accuracy_pct = (detected_symbols / total_actual_symbols) * 100
            else:
                self.metrics.symbol_detection_accuracy_pct = 100.0

```

### 58. _analyze_current_config
- **File**: token_budget_analysis.py
- **Priority**: critical
- **Relevance Score**: 2.4452941176470593

```python
    def _analyze_current_config(self, repo_map: RepoMap, analysis: TokenBudgetAnalysis):
        """Analyze current token configuration"""
        print("📊 Analyzing current token configuration...")
        
        # Get current settings
        analysis.current_map_tokens = repo_map.max_map_tokens
        analysis.current_max_context = getattr(self.model, 'max_input_tokens', 128000)
        analysis.no_files_multiplier = repo_map.map_mul_no_files
        
        # Calculate expanded limit (when no files in chat)
        if analysis.current_max_context:
            padding = 4096
            target = min(
                int(analysis.current_map_tokens * analysis.no_files_multiplier),
                analysis.current_max_context - padding
            )
            analysis.expanded_token_limit = target
            analysis.dynamic_allocation_active = target > analysis.current_map_tokens
        
        # Get actual usage
        all_files = find_src_files(self.project_path)
        repo_content = repo_map.get_repo_map([], all_files)
        if repo_content:
            analysis.current_usage_tokens = int(repo_map.token_count(repo_content))
            analysis.current_usage_pct = (analysis.current_usage_tokens / analysis.current_max_context) * 100
    
```

### 59. _analyze_module
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _analyze_module(self, project_path: str, file_path: str, model_name: str) -> Optional[Dict]:
        """
        Analyze a single file as a module.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze
            model_name: Name of the model to use

        Returns:
            Dictionary containing module information or None if analysis fails
        """
        try:
            rel_path = os.path.relpath(file_path, project_path)
            module_name = self._get_module_name(rel_path)

            # Get basic file info
            loc = self._count_lines_of_code(file_path)

            # Get symbols defined in the file
            symbols = self.project_manager.get_symbols_defined_in_file(project_path, model_name, file_path)

            # Analyze entities (functions, classes)
            entities = self._analyze_entities(project_path, file_path, symbols, model_name)

            module_info = {
                "name": module_name,
                "file": rel_path,
                "loc": loc,
                "dependencies": [],  # Will be filled later
                "entities": entities
            }

            return module_info

        except Exception as e:
            print(f"Error analyzing module {file_path}: {e}")
            return None

```

### 60. _analyze_entities
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _analyze_entities(self, project_path: str, file_path: str, symbols: Dict[str, List[str]], model_name: str) -> List[Dict]:
        """
        Analyze entities (functions, classes) in a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze
            symbols: Dictionary of symbols defined in the file
            model_name: Name of the model to use

        Returns:
            List of entity dictionaries
        """
        entities = []

        try:
            # Read the file content for detailed analysis
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # Analyze functions
            for func_name in symbols.get('functions', []):
                entity = self._analyze_function(file_content, func_name, project_path, file_path, model_name)
                if entity:
                    entities.append(entity)

            # Analyze classes
            for class_name in symbols.get('classes', []):
                entity = self._analyze_class(file_content, class_name, project_path, file_path, model_name)
                if entity:
                    entities.append(entity)

        except Exception as e:
            print(f"Error analyzing entities in {file_path}: {e}")

        return entities

```

### 61. _get_iterative_engine
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _get_iterative_engine(self, project_path: str, max_tokens: int = 8000):
        """Get the Iterative Analysis Engine, initializing it if necessary."""
        if self.iterative_engine is None:
            try:
                from iterative_analysis_engine import IterativeAnalysisEngine

                # Get the context selector first
                context_selector = self._get_context_selector(project_path, max_tokens)

                if context_selector is None:
                    print("⚠️ Cannot initialize Iterative Analysis Engine without context selector")
                    return None

                # Create the iterative engine
                self.iterative_engine = IterativeAnalysisEngine(
                    context_selector=context_selector,
                    max_iterations=5
                )
                print("✅ Iterative Analysis Engine initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IterativeAnalysisEngine: {e}")
                self.iterative_engine = None
            except Exception as e:
                print(f"⚠️ Error initializing iterative engine: {e}")
                self.iterative_engine = None

        return self.iterative_engine

```

### 62. _analyze_modules
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _analyze_modules(self):
        """Analyze modules to identify patterns and groupings."""
        print("🔍 Analyzing module structure...")
        
        # Group modules by directory/namespace
        groups = defaultdict(list)
        
        for module in self.modules:
            module_name = module['name']
            file_path = module.get('file', '')
            
            # Extract directory structure
            if '\\' in file_path or '/' in file_path:
                # Get the directory path
                path_parts = file_path.replace('\\', '/').split('/')
                if len(path_parts) > 1:
                    # Use the first directory as the group
                    group = path_parts[0] if path_parts[0] != 'aider-main' else path_parts[1] if len(path_parts) > 1 else 'root'
                else:
                    group = 'root'
            else:
                group = 'root'
            
            groups[group].append(module)
        
        self.module_groups = dict(groups)
        print(f"   Found {len(self.module_groups)} module groups")
    
```

### 63. _build_dependency_graph
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _build_dependency_graph(self):
        """Build a comprehensive dependency graph."""
        print("🔗 Building dependency graph...")
        
        graph = defaultdict(list)
        
        for module in self.modules:
            module_name = module['name']
            dependencies = module.get('dependencies', [])
            
            for dep in dependencies:
                dep_module = dep.get('module', '')
                strength = dep.get('strength', 'weak')
                
                # Only include internal dependencies (not external libraries)
                if self._is_internal_module(dep_module):
                    graph[module_name].append({
                        'target': dep_module,
                        'strength': strength
                    })
        
        self.dependency_graph = dict(graph)
        total_deps = sum(len(deps) for deps in self.dependency_graph.values())
        print(f"   Built graph with {total_deps} internal dependencies")
    
```

### 64. _identify_critical_modules
- **File**: architecture_diagram_generator.py
- **Priority**: critical
- **Relevance Score**: 2.4400000000000004

```python
    def _identify_critical_modules(self):
        """Identify modules with high criticality or many dependencies."""
        print("⚠️  Identifying critical modules...")
        
        critical = []
        
        for module in self.modules:
            module_name = module['name']
            entities = module.get('entities', [])
            
            # Count high-criticality entities
            high_crit_count = sum(1 for e in entities if e.get('criticality') == 'high')
            total_entities = len(entities)
            
            # Count incoming dependencies
            incoming_deps = sum(1 for m in self.modules 
                              for dep in m.get('dependencies', [])
                              if dep.get('module') == module_name)
            
            # Calculate criticality score
            crit_score = (high_crit_count / max(total_entities, 1)) * 0.5 + (incoming_deps * 0.1)
            
            if crit_score > 0.3 or high_crit_count > 5 or incoming_deps > 3:
                critical.append({
                    'name': module_name,
                    'score': crit_score,
                    'high_crit_entities': high_crit_count,
                    'incoming_deps': incoming_deps,
                    'total_entities': total_entities
                })
        
        # Sort by criticality score
        self.critical_modules = sorted(critical, key=lambda x: x['score'], reverse=True)
        print(f"   Identified {len(self.critical_modules)} critical modules")
    
```

## INSTRUCTIONS FOR LLM
You have been provided with intelligent context selection based on IR (Intermediate Representation) analysis and ICD (Intelligent Code Discovery).

### Context Quality
- This context was selected using task-specific algorithms
- Entities are prioritized by criticality and relevance
- Dependencies and risk factors have been analyzed
- Token budget has been optimized for maximum value

### Your Task
Please analyze the provided context and respond to the user's query: "I want to add a new code analysis feature. How should I integrate it with the existing system?"

Use the IR analysis data to understand:
1. **Entity Criticality**: Focus on high-criticality components
2. **Change Risk**: Consider risk factors when making recommendations
3. **Dependencies**: Understand how components interact
4. **Side Effects**: Be aware of potential impacts
5. **Error Patterns**: Identify potential issues

Provide a comprehensive, accurate response based on this intelligent context selection.
