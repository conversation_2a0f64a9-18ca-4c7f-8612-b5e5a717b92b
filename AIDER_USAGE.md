# Using Aider with Ollama Qwen3 Model

This guide explains how to use <PERSON><PERSON> with the Ollama Qwen3 model while preventing the AI from being overly eager to edit files.

## Setup

1. Make sure you have Ollama installed and running
2. Pull the Qwen3 model:
   ```
   ollama pull qwen3:4b
   ```
   Or for a smaller model:
   ```
   ollama pull qwen3:1.7b
   ```
3. Start Ollama with a larger context window:
   ```
   OLLAMA_CONTEXT_LENGTH=16384 ollama serve
   ```

## Configuration Files

The following configuration files have been created:

### .aider.model.settings.yml

This file configures specific settings for the Qwen3 model:
- **Starts in ask mode by default (disables edit mode)**
- Sets a larger context window
- Uses repository mapping for better code understanding

### .aider.conf.yml

This file sets global Aider settings:
- **Starts in ask mode by default (disables edit mode)**
- Disables automatic acceptance of architect changes

## Running Aider

To start Aider with the Qwen3 model:

```
aider --model ollama_chat/qwen3:4b --browser
```

Or for the smaller model:

```
aider --model ollama_chat/qwen3:1.7b --browser
```

## Chat Modes in Aider

Aider has several chat modes:
- **ask mode**: Answer questions about code without making changes (default with our configuration)
- **code mode**: Make changes to your code
- **architect mode**: Design code changes with one model, implement with another
- **help mode**: Get help about using Aider

### Switching Between Modes

You can switch modes during a session:
- To temporarily switch to code mode for one message: `/code your request here`
- To permanently switch to code mode: `/chat-mode code`
- To switch back to ask mode: `/chat-mode ask` or just `/ask`

## Tips for Working with Aider

1. Be specific in your requests
2. With our configuration, Aider starts in ask mode by default, so the AI won't try to edit files
3. When you want the AI to make code changes, use `/code` or `/chat-mode code`
4. Use `/clear` to clear the conversation history if the AI gets stuck in a pattern
5. Use `/drop` to remove files from the chat if you don't want them to be edited

## Troubleshooting

### Command-line Errors

If you encounter command-line errors like `unrecognized arguments`:
1. Make sure you're using the correct command format: `aider --model ollama_chat/qwen3:4b --browser`
2. Don't add extra parameters like `--overeager=False` or `--temperature=0.2` as they're already set in the configuration files
3. If you need to override settings, use the correct parameter names (e.g., `--chat-mode ask` instead of `--edit-format ask`)

### AI Behavior Issues

If the AI is still too eager to edit files:
1. Try using architect mode: `/chat-mode architect`
2. Try a different edit format: `aider --edit-format diff --model ollama_chat/qwen3:4b`
3. Try a different temperature: `aider --temperature 0 --model ollama_chat/qwen3:4b`
