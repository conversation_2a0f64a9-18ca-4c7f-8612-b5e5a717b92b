#!/usr/bin/env python3

"""
Simple test to check if the KeyError is fixed.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

def test_keyerror_fix():
    print("🔍 Testing KeyError fix...")
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        prompts = CoderPrompts()
        
        # Test that we can access the main_system prompt without KeyError
        main_system = prompts.main_system
        print(f"✅ main_system prompt length: {len(main_system)} characters")
        
        # Test formatting with basic variables
        test_format = main_system.format(
            final_reminders="test reminders",
            language="English"
        )
        print(f"✅ Successfully formatted main_system prompt: {len(test_format)} characters")
        
        # Test other prompts
        file_access = prompts.file_access_reminder
        print(f"✅ file_access_reminder length: {len(file_access)} characters")
        
        repo_content = prompts.repo_content_prefix
        print(f"✅ repo_content_prefix length: {len(repo_content)} characters")
        
        print("🎉 SUCCESS: No KeyError found! All prompts accessible.")
        return True
        
    except KeyError as e:
        print(f"❌ FAILED: KeyError still present: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED: Other error: {e}")
        return False

if __name__ == "__main__":
    success = test_keyerror_fix()
    sys.exit(0 if success else 1)
