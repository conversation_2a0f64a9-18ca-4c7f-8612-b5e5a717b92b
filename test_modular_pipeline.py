#!/usr/bin/env python3
"""
Test script for the new modular Mid-Level IR pipeline.

This script tests the new modular architecture and compares it with
the existing implementation to ensure compatibility and improvements.
"""

import os
import json
import time
from pathlib import Path

# Import the new modular pipeline
from mid_level_ir import MidLevelIRPipeline

# Import the old implementation for comparison
from aider_integration_service import AiderIntegrationService


def test_modular_pipeline():
    """Test the new modular pipeline implementation."""
    
    print("🧪 Testing New Modular Mid-Level IR Pipeline")
    print("=" * 60)
    
    project_path = os.getcwd()
    
    # Configuration for the new pipeline
    config = {
        'verbose': True,
        'file_scanner': {
            'exclude_dirs': ['__pycache__', '.git', '.pytest_cache', 'node_modules'],
            'max_file_size_mb': 10
        },
        'entity_extractor': {
            'extract_variables': True,
            'extract_constants': True,
            'min_function_lines': 1
        },
        'call_graph_builder': {
            'max_calls_per_entity': 15,
            'include_builtin_calls': False
        },
        'dependency_analyzer': {
            'include_stdlib': False,
            'include_external': True
        },
        'ir_builder': {
            'include_metadata': True,
            'include_ast_info': True,
            'pretty_print': True
        }
    }
    
    try:
        print("🚀 Testing New Modular Pipeline...")
        start_time = time.time()
        
        # Create and run the new pipeline
        pipeline = MidLevelIRPipeline(config)
        
        # Test with a subset of files first
        print("   Testing with limited scope for validation...")
        
        # Generate IR
        ir_data = pipeline.generate_ir(project_path, "modular_ir_test.json")
        
        generation_time = time.time() - start_time
        
        print(f"\n✅ New Pipeline Test Results:")
        print(f"   Generation time: {generation_time:.2f} seconds")
        print(f"   Modules analyzed: {len(ir_data['modules'])}")
        
        # Analyze the results
        analyze_ir_results(ir_data)
        
        # Compare with old implementation
        print(f"\n🔄 Comparing with Original Implementation...")
        compare_with_original(project_path, ir_data)
        
        return ir_data
        
    except Exception as e:
        print(f"❌ Error during modular pipeline test: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_ir_results(ir_data):
    """Analyze and display the IR results."""
    
    modules = ir_data.get('modules', [])
    metadata = ir_data.get('metadata', {})
    
    print(f"\n📊 Detailed Analysis:")
    print(f"   Total modules: {len(modules)}")
    print(f"   Total entities: {metadata.get('total_entities', 0)}")
    print(f"   Total functions: {metadata.get('total_functions', 0)}")
    print(f"   Total classes: {metadata.get('total_classes', 0)}")
    print(f"   Total dependencies: {metadata.get('total_dependencies', 0)}")
    print(f"   Total LOC: {metadata.get('total_loc', 0):,}")
    
    # Analyze entity types
    entity_types = {}
    criticality_dist = {'high': 0, 'medium': 0, 'low': 0}
    change_risk_dist = {'high': 0, 'medium': 0, 'low': 0}
    
    for module in modules:
        for entity in module.get('entities', []):
            entity_type = entity.get('type', 'unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            criticality = entity.get('criticality', 'low')
            change_risk = entity.get('change_risk', 'low')
            
            criticality_dist[criticality] = criticality_dist.get(criticality, 0) + 1
            change_risk_dist[change_risk] = change_risk_dist.get(change_risk, 0) + 1
    
    print(f"\n   📈 Entity Type Distribution:")
    for entity_type, count in sorted(entity_types.items()):
        print(f"     {entity_type}: {count}")
    
    print(f"\n   🎯 Criticality Distribution:")
    print(f"     High: {criticality_dist['high']}")
    print(f"     Medium: {criticality_dist['medium']}")
    print(f"     Low: {criticality_dist['low']}")
    
    print(f"\n   ⚠️  Change Risk Distribution:")
    print(f"     High: {change_risk_dist['high']}")
    print(f"     Medium: {change_risk_dist['medium']}")
    print(f"     Low: {change_risk_dist['low']}")
    
    # Show sample enhanced features
    print(f"\n🔍 Enhanced Features Sample:")
    
    # Find a function with enhanced metadata
    for module in modules[:3]:  # Check first 3 modules
        for entity in module.get('entities', [])[:2]:  # Check first 2 entities
            if entity.get('type') == 'function':
                print(f"\n   Function: {entity['name']}")
                print(f"     Parameters: {len(entity.get('params', []))}")
                print(f"     Returns: {entity.get('returns', {}).get('type', 'None')}")
                print(f"     Calls: {entity.get('calls', [])[:3]}")  # First 3 calls
                print(f"     Side effects: {entity.get('side_effects', [])}")
                print(f"     Potential errors: {entity.get('errors', [])[:3]}")  # First 3 errors
                print(f"     Criticality: {entity.get('criticality', 'unknown')}")
                print(f"     Change risk: {entity.get('change_risk', 'unknown')}")
                
                # Show enhanced parameter info if available
                params = entity.get('params', [])
                if params and isinstance(params[0], dict):
                    print(f"     Enhanced params: {params[:2]}")  # Show first 2 params
                
                break
        else:
            continue
        break


def compare_with_original(project_path, new_ir_data):
    """Compare the new implementation with the original."""
    
    try:
        print("   Generating IR with original implementation...")
        
        # Use the original implementation
        service = AiderIntegrationService()
        original_ir = service.generate_mid_level_ir(project_path)
        
        # Compare key metrics
        new_modules = len(new_ir_data.get('modules', []))
        original_modules = len(original_ir.get('modules', []))
        
        new_entities = new_ir_data.get('metadata', {}).get('total_entities', 0)
        original_entities = sum(len(m.get('entities', [])) for m in original_ir.get('modules', []))
        
        print(f"\n📊 Comparison Results:")
        print(f"   Modules - New: {new_modules}, Original: {original_modules}")
        print(f"   Entities - New: {new_entities}, Original: {original_entities}")
        
        # Check for improvements
        improvements = []
        
        # Check for enhanced parameter information
        new_enhanced_params = 0
        for module in new_ir_data.get('modules', []):
            for entity in module.get('entities', []):
                params = entity.get('params', [])
                if params and isinstance(params[0], dict) and 'type' in params[0]:
                    new_enhanced_params += 1
        
        if new_enhanced_params > 0:
            improvements.append(f"Enhanced parameter info for {new_enhanced_params} entities")
        
        # Check for metadata enrichment
        modules_with_metadata = sum(1 for m in new_ir_data.get('modules', []) if m.get('metadata'))
        if modules_with_metadata > 0:
            improvements.append(f"Module metadata for {modules_with_metadata} modules")
        
        # Check for complexity metrics
        entities_with_complexity = 0
        for module in new_ir_data.get('modules', []):
            for entity in module.get('entities', []):
                if entity.get('complexity') is not None:
                    entities_with_complexity += 1
        
        if entities_with_complexity > 0:
            improvements.append(f"Complexity metrics for {entities_with_complexity} entities")
        
        print(f"\n✨ New Features/Improvements:")
        for improvement in improvements:
            print(f"   ✅ {improvement}")
        
        if not improvements:
            print("   ⚠️  No significant improvements detected (may need more analysis)")
        
    except Exception as e:
        print(f"   ⚠️  Could not compare with original: {e}")


def main():
    """Main test function."""
    
    print("🧪 Modular Mid-Level IR Pipeline Test Suite")
    print("=" * 60)
    
    # Test the modular pipeline
    ir_data = test_modular_pipeline()
    
    if ir_data:
        print(f"\n🎉 Modular Pipeline Test Completed Successfully!")
        print(f"   Output saved to: modular_ir_test.json")
        
        # Check file size
        try:
            file_size = Path("modular_ir_test.json").stat().st_size / (1024 * 1024)
            print(f"   File size: {file_size:.2f} MB")
        except:
            pass
        
        print(f"\n🚀 The new modular architecture is ready for production use!")
    else:
        print(f"\n❌ Modular Pipeline Test Failed")
        print(f"   Please check the error messages above and fix any issues.")


if __name__ == "__main__":
    main()
