"""
Criticality Scorer - Calculates criticality and change risk for entities.

This module analyzes entities to determine their importance (criticality)
and the risk associated with modifying them (change risk).
"""

from typing import Dict, List, Any

from .ir_context import IRContext


class CriticalityScorer:
    """
    Calculates criticality and change risk scores for entities.
    
    This analyzer evaluates:
    - Usage frequency across modules
    - Dependency complexity
    - Code complexity
    - Error potential
    - Side effect impact
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the criticality scorer with configuration.
        
        Args:
            config: Configuration dictionary for scoring options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Scoring weights
        self.usage_weight = config.get('usage_weight', 0.3)
        self.complexity_weight = config.get('complexity_weight', 0.2)
        self.side_effect_weight = config.get('side_effect_weight', 0.2)
        self.error_weight = config.get('error_weight', 0.15)
        self.dependency_weight = config.get('dependency_weight', 0.15)
    
    def score(self, context: IRContext) -> IRContext:
        """
        Calculate criticality and change risk scores for all entities.
        
        Args:
            context: The IR context containing modules with entities
            
        Returns:
            Updated context with criticality and change risk scores
        """
        if self.verbose:
            print(f"   Calculating criticality scores for entities")
        
        # First pass: collect usage statistics
        usage_stats = self._collect_usage_statistics(context)
        
        # Second pass: calculate scores for each entity
        total_scored = 0
        for module_info in context.modules.values():
            for entity in module_info.entities:
                criticality, change_risk = self._calculate_entity_scores(
                    entity, module_info, context, usage_stats
                )
                entity.criticality = criticality
                entity.change_risk = change_risk
                total_scored += 1
        
        if self.verbose:
            print(f"   Calculated scores for {total_scored} entities")
        
        return context
    
    def _collect_usage_statistics(self, context: IRContext) -> Dict[str, Dict[str, Any]]:
        """
        Collect usage statistics for all entities across the codebase.
        
        Args:
            context: IR context to analyze
            
        Returns:
            Dictionary mapping entity names to usage statistics
        """
        usage_stats = {}
        
        # Count how many times each entity is called
        for module_info in context.modules.values():
            for entity in module_info.entities:
                entity_key = f"{module_info.name}.{entity.name}"
                
                # Initialize stats
                if entity_key not in usage_stats:
                    usage_stats[entity_key] = {
                        'call_count': 0,
                        'used_by_modules': set(),
                        'calls_others': len(entity.calls),
                        'is_public': not entity.name.startswith('_')
                    }
                
                # Count usage by other entities
                for other_module in context.modules.values():
                    for other_entity in other_module.entities:
                        if entity.name in other_entity.calls:
                            usage_stats[entity_key]['call_count'] += 1
                            usage_stats[entity_key]['used_by_modules'].add(other_module.name)
        
        return usage_stats
    
    def _calculate_entity_scores(self, entity, module_info, context: IRContext, 
                                usage_stats: Dict[str, Dict[str, Any]]) -> tuple[str, str]:
        """
        Calculate criticality and change risk scores for a single entity.
        
        Args:
            entity: EntityInfo object to score
            module_info: ModuleInfo containing the entity
            context: IR context for global analysis
            usage_stats: Usage statistics for all entities
            
        Returns:
            Tuple of (criticality_level, change_risk_level)
        """
        entity_key = f"{module_info.name}.{entity.name}"
        stats = usage_stats.get(entity_key, {})
        
        # Calculate individual score components
        usage_score = self._calculate_usage_score(stats)
        complexity_score = self._calculate_complexity_score(entity)
        side_effect_score = self._calculate_side_effect_score(entity)
        error_score = self._calculate_error_score(entity)
        dependency_score = self._calculate_dependency_score(entity, module_info)
        
        # Calculate weighted criticality score
        criticality_score = (
            usage_score * self.usage_weight +
            complexity_score * self.complexity_weight +
            side_effect_score * self.side_effect_weight +
            error_score * self.error_weight +
            dependency_score * self.dependency_weight
        )
        
        # Calculate change risk score (emphasizes different factors)
        change_risk_score = (
            usage_score * 0.4 +  # High usage = high risk to change
            complexity_score * 0.3 +  # Complex code = risky to change
            side_effect_score * 0.2 +  # Side effects = risky to change
            dependency_score * 0.1  # Dependencies = some risk
        )
        
        # Convert scores to categorical levels
        criticality_level = self._score_to_level(criticality_score)
        change_risk_level = self._score_to_level(change_risk_score)
        
        return criticality_level, change_risk_level
    
    def _calculate_usage_score(self, stats: Dict[str, Any]) -> float:
        """Calculate score based on usage patterns."""
        call_count = stats.get('call_count', 0)
        used_by_modules = len(stats.get('used_by_modules', set()))
        is_public = stats.get('is_public', False)
        
        # Normalize scores
        usage_score = min(call_count / 10.0, 1.0)  # Cap at 10 calls
        module_score = min(used_by_modules / 5.0, 1.0)  # Cap at 5 modules
        public_score = 0.5 if is_public else 0.0
        
        return (usage_score + module_score + public_score) / 3.0
    
    def _calculate_complexity_score(self, entity) -> float:
        """Calculate score based on code complexity."""
        if entity.complexity is not None:
            # Normalize complexity (typical range 1-20)
            return min(entity.complexity / 20.0, 1.0)
        
        # Fallback: estimate based on entity type and calls
        if entity.type == 'class':
            return 0.6  # Classes are generally more complex
        elif len(entity.calls) > 10:
            return 0.8  # Functions with many calls are complex
        elif len(entity.calls) > 5:
            return 0.5
        else:
            return 0.2
    
    def _calculate_side_effect_score(self, entity) -> float:
        """Calculate score based on side effects."""
        if not entity.side_effects or entity.side_effects == ["none"]:
            return 0.0
        
        # Weight different side effects
        high_impact_effects = {'modifies_global', 'database_io', 'network_io', 'modifies_file'}
        medium_impact_effects = {'modifies_state', 'writes_log', 'modifies_container'}
        
        score = 0.0
        for effect in entity.side_effects:
            if effect in high_impact_effects:
                score += 0.8
            elif effect in medium_impact_effects:
                score += 0.4
            else:
                score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_error_score(self, entity) -> float:
        """Calculate score based on potential errors."""
        if not entity.errors or entity.errors == ["RuntimeError"]:
            return 0.2  # Default low risk
        
        # Weight different error types
        high_risk_errors = {'FileNotFoundError', 'NetworkError', 'DatabaseError'}
        medium_risk_errors = {'ValueError', 'TypeError', 'AttributeError'}
        
        score = 0.0
        for error in entity.errors:
            if error in high_risk_errors:
                score += 0.6
            elif error in medium_risk_errors:
                score += 0.3
            else:
                score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_dependency_score(self, entity, module_info) -> float:
        """Calculate score based on module dependencies."""
        # Count strong dependencies
        strong_deps = len([dep for dep in module_info.dependencies if dep.strength == "strong"])
        total_deps = len(module_info.dependencies)
        
        if total_deps == 0:
            return 0.0
        
        # Normalize based on dependency count and strength
        dependency_ratio = strong_deps / total_deps
        dependency_count_score = min(total_deps / 10.0, 1.0)
        
        return (dependency_ratio + dependency_count_score) / 2.0
    
    def _score_to_level(self, score: float) -> str:
        """Convert a numeric score to a categorical level."""
        if score >= 0.7:
            return "high"
        elif score >= 0.4:
            return "medium"
        else:
            return "low"
