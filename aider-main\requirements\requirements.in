pydub
configargparse
GitPython
jsonschema
rich
prompt_toolkit
backoff
pathspec
diskcache
grep_ast
packaging
sounddevice
soundfile
beautifulsoup4
PyYAML
diff-match-patch
pypandoc
litellm
flake8
importlib_resources
pyperclip
posthog
mixpanel
pexpect
json5
psutil
watchfiles
socksio
pillow
shtab
oslex
google-generativeai

# The proper dependency is networkx[default], but this brings
# in matplotlib and a bunch of other deps
# https://github.com/networkx/networkx/blob/d7132daa8588f653eacac7a5bae1ee85a183fa43/pyproject.toml#L57
# We really only need networkx itself and scipy for the repomap.
networkx

# This is the one networkx dependency that we need.
# Including it here explicitly because we
# didn't specify networkx[default] above.
scipy

# GitHub Release action failing on "KeyError: 'home-page'"
# https://github.com/pypa/twine/blob/6fbf880ee60915cf1666348c4bdd78a10415f2ac/twine/__init__.py#L40
# Uses importlib-metadata
importlib-metadata<8.0.0
