#!/usr/bin/env python3
"""
Test script to verify that the LLM understands the mandatory workflow:
1. Must start with MAP_REQUEST
2. Evaluate if map is sufficient
3. Use different keywords if needed
4. Only then use CONTEXT_REQUEST/REQUEST_FILE
"""

import os
import sys
import re

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_prompt_instructions_clarity():
    """Test that the prompt instructions clearly communicate the mandatory workflow."""
    print("🧪 Testing Prompt Instructions Clarity")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check file_access_reminder
        file_access = prompts.file_access_reminder

        print("📋 Checking file_access_reminder instructions...")

        # Check for mandatory workflow keywords
        mandatory_checks = [
            ("MANDATORY WORKFLOW", "✅" if "MANDATORY WORKFLOW" in file_access else "❌"),
            ("STEP 1 (REQUIRED)", "✅" if "STEP 1 (REQUIRED)" in file_access else "❌"),
            ("MAP_REQUEST", "✅" if "MAP_REQUEST" in file_access else "❌"),
            ("CANNOT use CONTEXT_REQUEST", "✅" if "CANNOT use CONTEXT_REQUEST" in file_access else "❌"),
            ("different/broader keywords", "✅" if "different/broader keywords" in file_access else "❌"),
        ]

        for check, status in mandatory_checks:
            print(f"  {status} {check}")

        # Check repo_content_prefix
        repo_content = prompts.repo_content_prefix

        print("\n📋 Checking repo_content_prefix instructions...")

        repo_checks = [
            ("ZERO repository context", "✅" if "ZERO repository context" in repo_content else "❌"),
            ("ALWAYS START with MAP_REQUEST", "✅" if "ALWAYS START with MAP_REQUEST" in repo_content else "❌"),
            ("EVALUATE the received repository map", "✅" if "EVALUATE the received repository map" in repo_content else "❌"),
            ("different/broader keywords", "✅" if "different/broader keywords" in repo_content else "❌"),
            ("MANDATORY FIRST STEP", "✅" if "MANDATORY FIRST STEP" in repo_content else "❌"),
        ]

        for check, status in repo_checks:
            print(f"  {status} {check}")

        # Count how many checks passed
        all_checks = mandatory_checks + repo_checks
        passed = sum(1 for _, status in all_checks if status == "✅")
        total = len(all_checks)

        print(f"\n📊 Instructions clarity: {passed}/{total} checks passed")

        return passed == total

    except Exception as e:
        print(f"❌ Error testing prompt instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_simulation():
    """Simulate the LLM workflow to ensure it follows the correct order."""
    print("\n🧪 Testing Workflow Simulation")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")

        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )

        print("✅ Coder instance created successfully")

        # Test 1: LLM starts with zero repository map
        repo_content = coder.get_repo_map()
        if repo_content is None:
            print("✅ LLM starts with zero repository context")
        else:
            print("❌ LLM still gets default repository map")
            return False

        # Test 2: Simulate correct workflow - MAP_REQUEST first
        print("\n🔍 Simulating correct workflow...")

        # Step 1: MAP_REQUEST (correct first step)
        map_request_content = '''I need to understand how authentication works in this codebase.

{MAP_REQUEST: {"keywords": ["authentication", "login", "auth", "user"], "type": "implementation", "max_results": 8}}'''

        user_message = "How does authentication work in this system?"

        cleaned_content, map_prompt = coder.process_map_requests(map_request_content, user_message)

        if map_prompt:
            print("✅ MAP_REQUEST processed successfully (Step 1 complete)")
        else:
            print("❌ MAP_REQUEST not processed")
            return False

        # Test 3: Simulate incorrect workflow - CONTEXT_REQUEST without MAP_REQUEST
        print("\n🔍 Simulating incorrect workflow (should be prevented)...")

        context_request_content = '''I need to see the authentication implementation.

{CONTEXT_REQUEST: {"original_user_query_context": "authentication", "symbols_of_interest": [{"type": "class_definition", "name": "AuthManager"}], "reason_for_request": "need auth details"}}'''

        # This should work because we have the infrastructure, but the LLM should be instructed not to do this
        cleaned_content, context_prompt = coder.process_context_requests(context_request_content, user_message)

        # The system will process it, but the LLM should be instructed not to use it without MAP_REQUEST first
        print("⚠️  CONTEXT_REQUEST would be processed, but LLM should be instructed not to use it without MAP_REQUEST first")

        # Test 4: Check repo messages instruct the LLM correctly
        repo_messages = coder.get_repo_messages()

        print(f"\n📋 Repository messages check:")
        for i, msg in enumerate(repo_messages):
            print(f"  {i+1}. {msg['role']}: {msg['content'][:100]}...")
            if "MAP_REQUEST" in msg['content']:
                print("    ✅ Contains MAP_REQUEST instruction")
            if "on-demand" in msg['content']:
                print("    ✅ Mentions on-demand context")

        return True

    except Exception as e:
        print(f"❌ Error testing workflow simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keyword_evaluation_logic():
    """Test that the system can handle different keyword strategies."""
    print("\n🧪 Testing Keyword Evaluation Logic")
    print("=" * 60)

    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )

        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )

        print("✅ SmartMapRequestHandler created successfully")

        # Test different keyword strategies
        test_cases = [
            {
                "name": "Broad keywords",
                "keywords": ["authentication", "login", "user"],
                "expected_min_results": 1
            },
            {
                "name": "Specific keywords",
                "keywords": ["AuthManager", "login_required", "authenticate"],
                "expected_min_results": 0  # May not find specific classes
            },
            {
                "name": "Technical terms",
                "keywords": ["repository", "mapping", "repomap"],
                "expected_min_results": 3
            },
            {
                "name": "File-based keywords",
                "keywords": ["base_coder", "prompts", "models"],
                "expected_min_results": 2
            }
        ]

        for test_case in test_cases:
            print(f"\n🔍 Testing {test_case['name']}: {test_case['keywords']}")

            map_request = {
                "keywords": test_case["keywords"],
                "type": "implementation",
                "scope": "all",
                "max_results": 8
            }

            focused_map = handler.handle_map_request(map_request)

            # Count results in the focused map
            result_count = focused_map.count("**Files Found**: ")
            if result_count > 0:
                # Extract the number after "Files Found":
                import re
                match = re.search(r'\*\*Files Found\*\*:\s*(\d+)', focused_map)
                if match:
                    found_files = int(match.group(1))
                    print(f"  📊 Found {found_files} files")

                    if found_files >= test_case["expected_min_results"]:
                        print(f"  ✅ Meets minimum expectation ({test_case['expected_min_results']})")
                    else:
                        print(f"  ⚠️  Below expectation ({test_case['expected_min_results']})")
                else:
                    print("  ❌ Could not parse result count")
            else:
                print("  ❌ No results found")

        return True

    except Exception as e:
        print(f"❌ Error testing keyword evaluation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_guidance_system():
    """Test that the system provides smart guidance when files/context cannot be found."""
    print("\n🧪 Testing Smart Guidance System")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")

        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )

        print("✅ Coder instance created successfully")

        # Test 1: File not found guidance
        print("\n🔍 Testing file not found guidance...")

        # Simulate a file request for a non-existent file
        success, message = coder.add_requested_file("nonexistent_auth_manager.py", reason="need authentication logic")

        if not success and message:
            print("✅ File not found message generated")
            if "MAP_REQUEST" in message:
                print("✅ Message includes MAP_REQUEST guidance")
            else:
                print("❌ Message missing MAP_REQUEST guidance")
                print(f"Message: {message[:200]}...")
        else:
            print("❌ No guidance message generated for missing file")

        # Test 2: Smart guidance message generation
        print("\n🔍 Testing smart guidance message generation...")

        guidance_msg = coder._generate_smart_guidance_message(
            "file",
            ["auth_manager.py", "login_handler.py"],
            "need authentication and login functionality"
        )

        required_elements = [
            "File Request Failed",
            "MAP_REQUEST",
            "keywords",
            "repository map",
            "different/broader keywords"
        ]

        passed_checks = 0
        for element in required_elements:
            if element in guidance_msg:
                print(f"  ✅ Contains: {element}")
                passed_checks += 1
            else:
                print(f"  ❌ Missing: {element}")

        print(f"\n📊 Guidance message quality: {passed_checks}/{len(required_elements)} checks passed")

        # Test 3: Context request guidance
        print("\n🔍 Testing context request guidance...")

        context_guidance_msg = coder._generate_smart_guidance_message(
            "context",
            ["AuthManager.authenticate", "UserModel.login"],
            "need to understand authentication flow"
        )

        if "Context Request Failed" in context_guidance_msg:
            print("✅ Context-specific guidance generated")
        else:
            print("❌ Context-specific guidance not generated")

        # Test 4: Keyword extraction from reason
        print("\n🔍 Testing keyword extraction...")

        # The guidance message should extract keywords from the reason
        if "authentication" in guidance_msg.lower() and "login" in guidance_msg.lower():
            print("✅ Keywords extracted from reason")
        else:
            print("❌ Keywords not extracted from reason")

        return passed_checks >= len(required_elements) - 1  # Allow 1 failure

    except Exception as e:
        print(f"❌ Error testing smart guidance system: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all workflow understanding tests."""
    print("🚀 LLM Workflow Understanding Test")
    print("=" * 80)

    tests = [
        ("Prompt Instructions Clarity", test_prompt_instructions_clarity),
        ("Workflow Simulation", test_workflow_simulation),
        ("Keyword Evaluation Logic", test_keyword_evaluation_logic),
        ("Smart Guidance System", test_smart_guidance_system),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 80)
    print("📊 WORKFLOW UNDERSTANDING TEST SUMMARY")
    print("=" * 80)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! LLM workflow understanding is correctly implemented.")
        print("\n📋 The LLM now understands:")
        print("  1. ✅ Must start with MAP_REQUEST (cannot use CONTEXT_REQUEST/REQUEST_FILE without it)")
        print("  2. ✅ Must evaluate if the received map is sufficient")
        print("  3. ✅ Must use different keywords if the map is not sufficient")
        print("  4. ✅ Only then proceed to CONTEXT_REQUEST/REQUEST_FILE")
        print("  5. ✅ Gets smart guidance when files/context cannot be found")
        print("  6. ✅ Guided back to MAP_REQUEST workflow when requests fail")
    else:
        print("⚠️  Some tests failed. Please check the workflow implementation.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
