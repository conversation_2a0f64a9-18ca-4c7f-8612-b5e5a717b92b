#!/usr/bin/env python

import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'aider-main')))

# Import the required modules
try:
    from aider.repomap import RepoMap
    print("✅ Successfully imported RepoMap")
except ImportError as e:
    print(f"❌ Failed to import RepoMap: {e}")
    sys.exit(1)

def test_real_world():
    """Test a real-world example."""
    print("\n=== Testing Real-World Example ===")
    
    # Create a test file
    test_file = "test_file.py"
    with open(test_file, 'w') as f:
        f.write("""
def calculate_position_quantity(
        amount_to_risk: float,
        entry_price: float,
        stop_loss: float,
        contract_size: float,
        max_allowed: float,
        symbol: str
    ) -> float:
    \"\"\"
    Calculate the position quantity based on risk parameters.

    Args:
        amount_to_risk: Amount of money to risk
        entry_price: Entry price of the position
        stop_loss: Stop loss price
        contract_size: Contract size
        max_allowed: Maximum allowed position size
        symbol: Trading symbol

    Returns:
        Position quantity
    \"\"\"
    # Calculate the risk per pip
    price_difference = abs(entry_price - stop_loss)
    if price_difference == 0:
        return 0

    # Calculate the position size based on the risk
    position_size = amount_to_risk / price_difference

    # Adjust for contract size
    if contract_size > 0:
        position_size = position_size / contract_size

    # Ensure we don't exceed the maximum allowed position size
    position_size = min(position_size, max_allowed)

    # Round to 2 decimal places
    position_size = round(position_size, 2)

    return position_size
""")
    
    print(f"Created test file: {test_file}")
    
    try:
        # Create a RepoMap instance
        repo_map = RepoMap(root=".")
        print("Created RepoMap instance")
        
        # Get all files
        all_files = repo_map.get_all_files()
        print("\n=== All Files ===")
        for file in sorted(all_files):
            print(f"- {file}")
        
        # Get the repository overview
        overview = repo_map.get_repo_overview()
        print("\n=== Repository Overview ===")
        print(overview)
        
        # Test the get_tags method
        print("\n=== Testing get_tags ===")
        tags = repo_map.get_tags(test_file, test_file)
        print(f"Tags: {tags}")
        
    except Exception as e:
        print(f"❌ Error in real-world test: {e}")
    finally:
        # Clean up the test file
        try:
            os.remove(test_file)
            print(f"Removed test file: {test_file}")
        except:
            pass
    
    print("\n=== Real-World Test completed! ===")

if __name__ == "__main__":
    test_real_world()
