graph TB
    %% Aider AI Coding Assistant - Business System Architecture

    %% External Actors & Systems
    Developer[👨‍💻 Developer]
    style Developer fill:#e1f5fe,stroke:#0277bd,color:#000
    TeamLead[👩‍💼 Team Lead]
    style TeamLead fill:#e1f5fe,stroke:#0277bd,color:#000
    GitRepo[(🗂️ Git Repository)]
    style GitRepo fill:#fff3e0,stroke:#f57c00,color:#000
    LLMProviders[🧠 LLM Providers<br/>OpenAI, Anthropic, DeepSeek]
    style LLMProviders fill:#f3e5f5,stroke:#7b1fa2,color:#000
    FileSystem[(📁 Local File System)]
    style FileSystem fill:#fff3e0,stroke:#f57c00,color:#000

    %% Aider System Boundary
    subgraph AiderSystem[🤖 Aider AI Coding Assistant]
        direction TB

        subgraph UserInterfaces[User Interfaces]
            CLI[🖥️ Command Line Interface]
            style CLI fill:#e8f5e8,stroke:#2e7d32,color:#000
            WebUI[🌐 Browser Interface]
            style WebUI fill:#e8f5e8,stroke:#2e7d32,color:#000
            StreamlitGUI[📱 Streamlit GUI]
            style StreamlitGUI fill:#e8f5e8,stroke:#2e7d32,color:#000
        end

        subgraph CoreServices[Core Business Services]
            CodeEditor[✏️ AI Code Editor<br/>Smart code modifications]
            style CodeEditor fill:#fff8e1,stroke:#f9a825,color:#000
            ContextEngine[🔍 Context Engine<br/>Intelligent code discovery]
            style ContextEngine fill:#fff8e1,stroke:#f9a825,color:#000
            RepoMapper[🗺️ Repository Mapper<br/>Codebase understanding]
            style RepoMapper fill:#fff8e1,stroke:#f9a825,color:#000
            ChatInterface[💬 Conversational AI<br/>Natural language coding]
            style ChatInterface fill:#fff8e1,stroke:#f9a825,color:#000
        end

        subgraph IntegrationServices[Integration Services]
            GitIntegration[🔄 Git Integration<br/>Auto-commit & branching]
            style GitIntegration fill:#fce4ec,stroke:#c2185b,color:#000
            LLMOrchestrator[🎭 LLM Orchestrator<br/>Multi-provider support]
            style LLMOrchestrator fill:#fce4ec,stroke:#c2185b,color:#000
            FileManager[📄 File Manager<br/>Safe file operations]
            style FileManager fill:#fce4ec,stroke:#c2185b,color:#000
        end

        subgraph Analytics[Analytics & Monitoring]
            UsageAnalytics[📊 Usage Analytics<br/>Performance tracking]
            style UsageAnalytics fill:#e0f2f1,stroke:#00695c,color:#000
            Benchmarking[🏆 Benchmarking<br/>Quality assessment]
            style Benchmarking fill:#e0f2f1,stroke:#00695c,color:#000
        end

    end

    %% Primary Business Workflows
    Developer --> CLI
    Developer --> WebUI
    TeamLead --> StreamlitGUI

    CLI --> ChatInterface
    WebUI --> ChatInterface
    StreamlitGUI --> ChatInterface

    ChatInterface --> ContextEngine
    ChatInterface --> CodeEditor
    ContextEngine --> RepoMapper
    CodeEditor --> FileManager

    %% External Integrations
    LLMOrchestrator <--> LLMProviders
    GitIntegration <--> GitRepo
    FileManager <--> FileSystem
    RepoMapper --> FileSystem

    %% Service Dependencies
    CodeEditor --> LLMOrchestrator
    ContextEngine --> LLMOrchestrator
    CodeEditor --> GitIntegration
    ChatInterface --> UsageAnalytics
    CodeEditor --> Benchmarking

    %% Value Propositions
    classDef valueBox fill:#f1f8e9,stroke:#558b2f,stroke-width:2px,color:#000
    ValueProp1[💡 Accelerated Development<br/>10x faster coding with AI]
    ValueProp2[🎯 Context-Aware Edits<br/>Understands entire codebase]
    ValueProp3[🔒 Safe & Reliable<br/>Git integration & rollback]
    class ValueProp1,ValueProp2,ValueProp3 valueBox
