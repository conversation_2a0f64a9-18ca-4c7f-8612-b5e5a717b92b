# Aider File Request System

## Table of Contents

1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Implementation Details](#implementation-details)
4. [Prompt Design for File Requests](#prompt-design-for-file-requests)
5. [Testing and Validation](#testing-and-validation)
6. [Common Issues and Solutions](#common-issues-and-solutions)
7. [Advanced Customization](#advanced-customization)

## Introduction

The Aider File Request System allows the AI model to explicitly request files it needs to analyze, rather than relying on automatic file mention detection. This document explains how the system works and provides guidance on designing effective prompts for it.

## System Overview

### Key Components

1. **File Request Instructions**: Prompts that instruct the AI on how to request files
2. **File Request Processing**: Code that detects and processes file requests
3. **Automatic File Addition**: System that adds requested files to the conversation
4. **System Prompts**: Instructions that reinforce proper file request behavior

### Request Flow

1. AI receives a question about code
2. AI identifies that it needs to see specific files
3. AI requests the files using the specified format
4. System automatically adds the files to the conversation
5. AI analyzes the files and responds to the question

## Implementation Details

### File Request Format

The AI can request files using two formats:

**Single File Request**:
```
{REQUEST_FILE: path/to/file.py}
```

**Multiple File Request**:
```
{REQUEST_FILES:
- path/to/file1.py
- path/to/file2.py
}
```

### Key Methods

1. **`process_file_requests`**: Detects file requests in AI responses
   ```python
   def process_file_requests(self, content):
       """Process file requests in the AI's response."""
       import re

       # Regex patterns to detect file requests
       single_file_pattern = r'\{REQUEST_FILE:\s*(.*?)\}'
       multi_file_pattern = r'\{REQUEST_FILES:(.*?)\}'

       # Find all file requests
       single_requests = re.findall(single_file_pattern, content, re.DOTALL)
       multi_requests = re.findall(multi_file_pattern, content, re.DOTALL)
       
       # Process requests and add files
       # ...
   ```

2. **`add_requested_file`**: Adds a requested file to the conversation
   ```python
   def add_requested_file(self, rel_fname):
       """Add a file requested by the AI without user confirmation."""
       abs_path = self.abs_root_path(rel_fname)

       # Check if file exists
       if not Path(abs_path).is_file():
           self.io.tool_warning(f"File {rel_fname} not found")
           return False

       # Add as read-only in informative mode, otherwise as editable
       if hasattr(self, 'informative_only') and self.informative_only:
           self.abs_read_only_fnames.add(abs_path)
           self.io.tool_output(f"Added {rel_fname} as read-only (AI requested)")
       else:
           self.abs_fnames.add(abs_path)
           self.io.tool_output(f"Added {rel_fname} to the chat (AI requested)")

       return True
   ```

3. **Integration in `send_message`**: Processes file requests before continuing
   ```python
   # Process file requests only - no file mention detection
   cleaned_content, file_message = self.process_file_requests(content)
   if file_message:
       # Update response and add message
       self.partial_response_content = cleaned_content
       content = cleaned_content

       # Add the file message to reflected_message
       if self.reflected_message:
           self.reflected_message += "\n\n" + file_message
       else:
           self.reflected_message = file_message
       return
   ```

## Prompt Design for File Requests

### Critical Instructions

The following instructions are crucial for effective file request behavior:

1. **Zero Files at Start**:
   ```
   YOU DO NOT HAVE ANY FILES LOADED AT THE START OF A CONVERSATION. You must explicitly request them.
   ```

2. **Request Before Answering**:
   ```
   ALWAYS START by requesting relevant files BEFORE attempting to answer ANY code-related question.
   ```

3. **No Assumptions**:
   ```
   NEVER assume you already have access to any file unless you've explicitly requested it and seen its contents.
   ```

4. **Request Format**:
   ```
   When you need to see the complete content of a file to answer a question or solve a problem, request it using:
   {REQUEST_FILE: exact_file_path}
   ```

### Placement in Prompt Structure

These instructions are placed in multiple locations to ensure the AI follows them:

1. **System Prompt** (`file_access_reminder`):
   - Added to all system prompts
   - First thing the AI sees in every conversation

2. **File Request Instructions** (`file_request_instructions`):
   - Detailed instructions on how to request files
   - Included in repository content messages

3. **Repository Content Prefix** (`repo_content_prefix`):
   - Instructions shown when repository information is provided
   - Reinforces file request behavior

4. **Assistant Response** in `get_repo_messages`:
   - AI acknowledges the instructions
   - Commits to following the file request protocol

### Reinforcement Techniques

1. **Repetition**: Key instructions are repeated in multiple places
2. **Formatting**: Critical instructions use ALL CAPS for emphasis
3. **Numbering**: Instructions are numbered for clarity
4. **Examples**: Clear examples of the request format are provided
5. **Explicit Acknowledgment**: AI acknowledges the instructions in its response

## Testing and Validation

### Test Cases

1. **Basic File Request**:
   - User asks about a specific file
   - AI should request the file before answering

2. **Multiple File Request**:
   - User asks about functionality spanning multiple files
   - AI should request all relevant files

3. **Ambiguous Request**:
   - User asks a question without mentioning specific files
   - AI should identify and request the most likely relevant files

4. **Follow-up Questions**:
   - User asks follow-up questions about previously requested files
   - AI should not request the files again

### Validation Criteria

1. AI requests files before attempting to answer
2. AI uses the correct request format
3. AI does not make assumptions about files it hasn't seen
4. AI provides accurate responses based on the file contents

## Common Issues and Solutions

### Issue: AI Still Makes Assumptions

**Solution**: Strengthen the "No Assumptions" instruction:
```
CRITICAL: NEVER provide ANY information about file contents you haven't seen, even if you think you know what might be in the file based on naming conventions or common patterns.
```

### Issue: AI Doesn't Request Files First

**Solution**: Add a stronger instruction about the order of operations:
```
WORKFLOW ORDER: 1) Identify needed files, 2) Request ALL needed files, 3) ONLY THEN analyze and respond.
```

### Issue: AI Requests Unnecessary Files

**Solution**: Add guidance on efficient file requests:
```
Request ONLY the files you need to answer the specific question. Use the repository map to identify the most relevant files.
```

### Issue: AI Forgets It Has Already Requested Files

**Solution**: Add an instruction about tracking requested files:
```
Keep track of which files you've already requested and received. Don't request the same file multiple times.
```

## Advanced Customization

### Customizing Request Format

You can modify the request format by updating:

1. The instructions in `file_request_instructions`
2. The regex patterns in `process_file_requests`

### Adding Request Prioritization

You could enhance the system to prioritize file requests:

```
When requesting multiple files, indicate priority:
{REQUEST_FILES:
- HIGH: path/to/critical_file.py
- MEDIUM: path/to/helpful_file.py
- LOW: path/to/context_file.py
}
```

### Adding Request Rationale

You could require the AI to explain why it's requesting each file:

```
When requesting a file, explain why you need it:
{REQUEST_FILE: path/to/file.py | Need to examine the implementation of X function}
```

### Limiting Request Volume

You could add instructions to limit the number of files requested at once:

```
Request at most 3 files at a time. If you need more, analyze the first batch first and then request additional files as needed.
```

### Customizing File Addition Behavior

You could modify `add_requested_file` to:

1. Add files as read-only by default
2. Limit the total number of files that can be added
3. Add only portions of files relevant to the question
