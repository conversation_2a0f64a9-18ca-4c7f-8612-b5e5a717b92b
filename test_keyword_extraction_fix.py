#!/usr/bin/env python3
"""
Test script to verify that the LLM now uses correct keywords from user queries
instead of placeholder text.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_prompt_instructions_keyword_clarity():
    """Test that the prompt instructions clearly show how to extract keywords."""
    print("🧪 Testing Keyword Extraction Instructions")
    print("=" * 60)
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Check file_access_reminder
        file_access = prompts.file_access_reminder
        
        print("📋 Checking file_access_reminder for keyword guidance...")
        
        keyword_checks = [
            ("EXTRACT_FROM_USER_QUERY", "✅" if "EXTRACT_FROM_USER_QUERY" in file_access else "❌"),
            ("actual keywords", "✅" if "actual keywords" in file_access.lower() else "❌"),
            ("NOT placeholder text", "✅" if "NOT placeholder text" in file_access else "❌"),
            ("Replace", "✅" if "Replace" in file_access else "❌"),
        ]
        
        for check, status in keyword_checks:
            print(f"  {status} Contains: {check}")
        
        # Check repo_content_prefix
        repo_content = prompts.repo_content_prefix
        
        print("\n📋 Checking repo_content_prefix for keyword guidance...")
        
        repo_checks = [
            ("EXTRACT ACTUAL KEYWORDS", "✅" if "EXTRACT ACTUAL KEYWORDS" in repo_content else "❌"),
            ("DO NOT use placeholder text", "✅" if "DO NOT use placeholder text" in repo_content else "❌"),
            ("ACTUAL_KEYWORDS_FROM_USER_QUERY", "✅" if "ACTUAL_KEYWORDS_FROM_USER_QUERY" in repo_content else "❌"),
            ("Example", "✅" if "Example" in repo_content else "❌"),
        ]
        
        for check, status in repo_checks:
            print(f"  {status} Contains: {check}")
        
        # Count how many checks passed
        all_checks = keyword_checks + repo_checks
        passed = sum(1 for _, status in all_checks if status == "✅")
        total = len(all_checks)
        
        print(f"\n📊 Keyword instruction clarity: {passed}/{total} checks passed")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing keyword instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_example_scenarios():
    """Test example scenarios to show correct keyword extraction."""
    print("\n🧪 Testing Example Scenarios")
    print("=" * 60)
    
    test_cases = [
        {
            "user_query": "how does the close_position_based_on_conditions function work?",
            "expected_keywords": ["close_position_based_on_conditions", "position", "close", "conditions"],
            "wrong_keywords": ["technical", "terms"]
        },
        {
            "user_query": "explain the authentication system",
            "expected_keywords": ["authentication", "auth", "login", "user", "system"],
            "wrong_keywords": ["technical", "terms"]
        },
        {
            "user_query": "how to use the DatabaseManager class?",
            "expected_keywords": ["DatabaseManager", "database", "manager", "class"],
            "wrong_keywords": ["technical", "terms"]
        },
        {
            "user_query": "what does the calculate_profit method do?",
            "expected_keywords": ["calculate_profit", "profit", "calculate", "method"],
            "wrong_keywords": ["technical", "terms"]
        }
    ]
    
    print("📋 Example keyword extractions:")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. User Query: \"{test_case['user_query']}\"")
        print(f"   ✅ Correct keywords: {test_case['expected_keywords']}")
        print(f"   ❌ Wrong keywords: {test_case['wrong_keywords']}")
        print(f"   📝 Correct MAP_REQUEST:")
        print(f"   {{MAP_REQUEST: {{\"keywords\": {test_case['expected_keywords']}, \"type\": \"implementation\", \"max_results\": 8}}}}")
    
    return True

def test_smart_guidance_keyword_examples():
    """Test that smart guidance shows correct keyword examples."""
    print("\n🧪 Testing Smart Guidance Keyword Examples")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test smart guidance message
        guidance_msg = coder._generate_smart_guidance_message(
            "file", 
            ["nonexistent_file.py"], 
            "need to understand close_position_based_on_conditions function"
        )
        
        print("\n📋 Checking smart guidance message...")
        
        guidance_checks = [
            ("EXTRACT_ACTUAL_KEYWORDS_FROM_USER_QUERY", "✅" if "EXTRACT_ACTUAL_KEYWORDS_FROM_USER_QUERY" in guidance_msg else "❌"),
            ("CRITICAL", "✅" if "CRITICAL" in guidance_msg else "❌"),
            ("NOT placeholder text", "✅" if "NOT placeholder text" in guidance_msg else "❌"),
            ("close", "✅" if "close" in guidance_msg else "❌"),  # Should extract from reason
            ("position", "✅" if "position" in guidance_msg else "❌"),  # Should extract from reason
        ]
        
        for check, status in guidance_checks:
            print(f"  {status} Contains: {check}")
        
        passed = sum(1 for _, status in guidance_checks if status == "✅")
        total = len(guidance_checks)
        
        print(f"\n📊 Smart guidance quality: {passed}/{total} checks passed")
        
        return passed >= total - 1  # Allow 1 failure
        
    except Exception as e:
        print(f"❌ Error testing smart guidance: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all keyword extraction tests."""
    print("🚀 Keyword Extraction Fix Test")
    print("=" * 80)
    
    tests = [
        ("Prompt Instructions Keyword Clarity", test_prompt_instructions_keyword_clarity),
        ("Example Scenarios", test_example_scenarios),
        ("Smart Guidance Keyword Examples", test_smart_guidance_keyword_examples),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 KEYWORD EXTRACTION FIX TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Keyword extraction instructions are now clear.")
        print("\n📋 The LLM should now:")
        print("  1. ✅ Extract ACTUAL keywords from user queries")
        print("  2. ✅ NOT use placeholder text like 'technical', 'terms'")
        print("  3. ✅ Use function names, class names, and concepts from the query")
        print("  4. ✅ Get clear examples of correct keyword extraction")
        print("\n🎯 For query: 'how does close_position_based_on_conditions work?'")
        print("  ✅ Should use: ['close_position_based_on_conditions', 'position', 'close', 'conditions']")
        print("  ❌ Should NOT use: ['technical', 'terms']")
    else:
        print("⚠️  Some tests failed. Please check the keyword extraction instructions.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
