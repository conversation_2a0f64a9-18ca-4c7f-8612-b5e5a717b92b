#!/usr/bin/env python3
"""
Corrected Token Budget Analysis

This script analyzes the repository map using the ACTUAL default token limits
from the aider system, not arbitrary values.
"""

import os
import sys
import json
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.repomap import RepoMap, find_src_files
from aider.models import Model
from aider.io import InputOutput


def analyze_actual_token_usage():
    """Analyze token usage with ACTUAL aider defaults"""
    
    print("🔍 Analyzing ACTUAL token configuration...")
    
    # Create model and IO
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Get the ACTUAL default token limit from the model
    actual_default_tokens = model.get_repo_map_tokens()
    print(f"📊 Model's default repo map tokens: {actual_default_tokens}")
    
    # Create RepoMap with DEFAULT settings (no custom token limit)
    repo_map_default = RepoMap(
        # Don't specify map_tokens - let it use the default
        root="aider-main",
        main_model=model,
        io=io,
        verbose=True,
        refresh="always"
    )
    
    print(f"📊 RepoMap default tokens: {repo_map_default.max_map_tokens}")
    print(f"📊 No-files multiplier: {repo_map_default.map_mul_no_files}")
    
    # Get all source files
    all_files = find_src_files("aider-main")
    print(f"📁 Total files found: {len(all_files)}")
    
    # Generate repo map with default settings
    print("🔄 Generating repo map with DEFAULT settings...")
    repo_content = repo_map_default.get_repo_map([], all_files)
    
    if repo_content:
        actual_tokens = int(repo_map_default.token_count(repo_content))
        print(f"📊 Actual token usage: {actual_tokens:,} tokens")
        print(f"📊 Default token limit: {repo_map_default.max_map_tokens:,} tokens")
        
        # Calculate if we're over the limit
        if actual_tokens > repo_map_default.max_map_tokens:
            over_limit = actual_tokens - repo_map_default.max_map_tokens
            over_pct = (over_limit / repo_map_default.max_map_tokens) * 100
            print(f"⚠️  OVER LIMIT by {over_limit:,} tokens ({over_pct:.1f}%)")
        else:
            under_limit = repo_map_default.max_map_tokens - actual_tokens
            under_pct = (under_limit / repo_map_default.max_map_tokens) * 100
            print(f"✅ Under limit by {under_limit:,} tokens ({under_pct:.1f}%)")
        
        # Calculate expanded limit (when no files in chat)
        max_context = getattr(model, 'max_input_tokens', 128000)
        padding = 4096
        expanded_limit = min(
            int(repo_map_default.max_map_tokens * repo_map_default.map_mul_no_files),
            max_context - padding
        )
        print(f"📊 Expanded limit (no files): {expanded_limit:,} tokens")
        
        if actual_tokens > expanded_limit:
            print(f"⚠️  Even EXPANDED limit exceeded!")
        else:
            print(f"✅ Within expanded limit")
        
        # Save results
        results = {
            "model_default_tokens": actual_default_tokens,
            "repomap_default_tokens": repo_map_default.max_map_tokens,
            "actual_usage_tokens": actual_tokens,
            "no_files_multiplier": repo_map_default.map_mul_no_files,
            "expanded_limit": expanded_limit,
            "max_context_window": max_context,
            "files_processed": len(all_files),
            "over_default_limit": actual_tokens > repo_map_default.max_map_tokens,
            "over_expanded_limit": actual_tokens > expanded_limit,
            "default_limit_excess": max(0, actual_tokens - repo_map_default.max_map_tokens),
            "expanded_limit_excess": max(0, actual_tokens - expanded_limit)
        }
        
        with open('actual_token_analysis.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Results saved to: actual_token_analysis.json")
        
        return results
    else:
        print("❌ Failed to generate repo map")
        return None


def analyze_file_filtering_impact():
    """Analyze the impact of file filtering on token usage"""
    
    print("\n🔍 Analyzing file filtering impact...")
    
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Get all files
    all_files = find_src_files("aider-main")
    
    # Categorize files
    source_files = []
    other_files = []
    
    source_extensions = {
        '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.rb', '.go', 
        '.rs', '.php', '.swift', '.kt', '.scala', '.dart', '.ex', '.elm'
    }
    
    exclude_extensions = {
        '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.mp3', '.mp4', 
        '.ttf', '.woff', '.woff2', '.eot', '.pyc', '.pyo', '.class', '.o', 
        '.so', '.dll', '.exe', '.db', '.db-shm', '.db-wal', '.cache'
    }
    
    for file_path in all_files:
        ext = Path(file_path).suffix.lower()
        if ext in exclude_extensions:
            other_files.append(file_path)
        elif ext in source_extensions:
            source_files.append(file_path)
        else:
            # Config, docs, etc. - keep these
            source_files.append(file_path)
    
    print(f"📁 Source + essential files: {len(source_files)}")
    print(f"📁 Excludable files: {len(other_files)}")
    
    # Test with filtered files
    repo_map = RepoMap(
        root="aider-main",
        main_model=model,
        io=io,
        verbose=False,
        refresh="always"
    )
    
    print("🔄 Generating repo map with FILTERED files...")
    filtered_content = repo_map.get_repo_map([], source_files)
    
    if filtered_content:
        filtered_tokens = int(repo_map.token_count(filtered_content))
        print(f"📊 Filtered token usage: {filtered_tokens:,} tokens")
        print(f"📊 Default token limit: {repo_map.max_map_tokens:,} tokens")
        
        if filtered_tokens <= repo_map.max_map_tokens:
            print(f"✅ WITHIN DEFAULT LIMIT after filtering!")
            savings = len(all_files) - len(source_files)
            print(f"💾 Files saved: {savings} ({(savings/len(all_files)*100):.1f}%)")
        else:
            print(f"⚠️  Still over limit even after filtering")
        
        return {
            "filtered_tokens": filtered_tokens,
            "filtered_files": len(source_files),
            "excluded_files": len(other_files),
            "within_limit": filtered_tokens <= repo_map.max_map_tokens
        }
    
    return None


def main():
    """Main analysis function"""
    print("🔍 CORRECTED Token Budget Analysis")
    print("=" * 50)
    
    # Analyze actual usage
    actual_results = analyze_actual_token_usage()
    
    # Analyze filtering impact
    filtering_results = analyze_file_filtering_impact()
    
    if actual_results and filtering_results:
        print("\n" + "=" * 50)
        print("📋 SUMMARY")
        print("=" * 50)
        
        print(f"🎯 REAL Default Limit: {actual_results['repomap_default_tokens']:,} tokens")
        print(f"📊 Current Usage: {actual_results['actual_usage_tokens']:,} tokens")
        
        if actual_results['over_default_limit']:
            excess = actual_results['default_limit_excess']
            print(f"⚠️  PROBLEM: Over limit by {excess:,} tokens")
        else:
            print(f"✅ Within default limit")
        
        if filtering_results['within_limit']:
            print(f"✅ SOLUTION: File filtering brings usage to {filtering_results['filtered_tokens']:,} tokens")
            print(f"💾 Remove {filtering_results['excluded_files']} unnecessary files")
        else:
            print(f"⚠️  File filtering alone insufficient")
        
        print(f"\n🔧 RECOMMENDATION:")
        if filtering_results['within_limit']:
            print("   Implement file type filtering to stay within default limits")
        else:
            print("   Need both file filtering AND increased token limit")


if __name__ == "__main__":
    main()
