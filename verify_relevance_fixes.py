#!/usr/bin/env python3
"""
Simple verification script to test the enhanced relevance matching fixes.
Tests the core functions without complex imports.
"""

import re
import json
from typing import List, Dict, Any, Optional

def test_enhanced_focus_extraction():
    """Test the enhanced focus entity extraction logic."""
    print("🧪 Testing Enhanced Focus Entity Extraction")
    print("=" * 60)
    
    def extract_focus_entities_from_query(user_message):
        """Enhanced focus entity extraction (copy from base_coder.py)"""
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'how', 'what', 'why', 'when', 'where', 'which', 'who'}

        focus_entities = []
        
        # 1. Extract programming-specific patterns (highest priority)
        # Function calls: word()
        function_calls = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', user_message)
        focus_entities.extend(function_calls)
        
        # Class/module references: ClassName.method or module.function
        dotted_names = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)', user_message)
        focus_entities.extend(dotted_names)
        
        # CamelCase identifiers (likely class names)
        camel_case = re.findall(r'\b([A-Z][a-z]+(?:[A-Z][a-z]+)+)\b', user_message)
        focus_entities.extend(camel_case)
        
        # snake_case identifiers (likely function names)
        snake_case = re.findall(r'\b([a-z]+(?:_[a-z]+)+)\b', user_message)
        focus_entities.extend(snake_case)
        
        # 2. Extract quoted terms (user-specified exact terms)
        quoted_terms = re.findall(r'["\']([^"\']+)["\']', user_message)
        focus_entities.extend(quoted_terms)
        
        # 3. Extract remaining meaningful words
        words = re.findall(r'\b\w+\b', user_message.lower())
        meaningful_words = [word for word in words if word not in stop_words and len(word) > 2]
        focus_entities.extend(meaningful_words)
        
        # 4. Clean and prioritize
        seen = set()
        unique_entities = []
        for entity in focus_entities:
            entity_clean = entity.lower().strip()
            if entity_clean and entity_clean not in seen and entity_clean not in stop_words:
                seen.add(entity_clean)
                unique_entities.append(entity_clean)
        
        return unique_entities[:12]
    
    test_queries = [
        "Why is my context_selection() function taking so long?",
        "How does the IntelligentContextSelector.select_optimal_context() method work?", 
        "Fix the bug in parse_request() function",
        "What does the 'calculate_relevance_score' function do?",
        "How do I use ContextRequestHandler class?",
        "Debug the process_ir_context_request method",
        "Analyze the _extract_focus_entities_from_query implementation"
    ]
    
    for query in test_queries:
        entities = extract_focus_entities_from_query(query)
        print(f"Query: '{query}'")
        print(f"Focus entities: {entities}")
        
        # Check if function names are properly extracted
        if "context_selection" in query and "context_selection" in entities:
            print("  ✅ PASS: Function name extracted correctly")
        elif "select_optimal_context" in query and "select_optimal_context" in entities:
            print("  ✅ PASS: Method name extracted correctly")
        elif "parse_request" in query and "parse_request" in entities:
            print("  ✅ PASS: Function name extracted correctly")
        elif "calculate_relevance_score" in query and "calculate_relevance_score" in entities:
            print("  ✅ PASS: Function name extracted correctly")
        elif "ContextRequestHandler" in query and "contextrequesthandler" in entities:
            print("  ✅ PASS: Class name extracted correctly")
        elif "process_ir_context_request" in query and "process_ir_context_request" in entities:
            print("  ✅ PASS: Method name extracted correctly")
        elif "_extract_focus_entities_from_query" in query and "_extract_focus_entities_from_query" in entities:
            print("  ✅ PASS: Method name extracted correctly")
        else:
            print("  ⚠️  PARTIAL: Some entities extracted but may need improvement")
        print()

def test_enhanced_relevance_scoring():
    """Test the enhanced relevance scoring logic."""
    print("🎯 Testing Enhanced Relevance Scoring Logic")
    print("=" * 60)
    
    def calculate_focus_entity_score(entity_name, focus_entities):
        """Enhanced focus entity scoring (copy from intelligent_context_selector.py)"""
        if not focus_entities:
            return 0.0
        
        entity_name = entity_name.lower()
        max_score = 0.0
        
        for focus in focus_entities:
            focus_lower = focus.lower()
            
            # Exact match bonuses (highest priority)
            if focus_lower == entity_name:
                max_score = max(max_score, 5.0)  # Exact entity name match
            elif focus_lower in entity_name and len(focus_lower) >= len(entity_name) * 0.8:
                max_score = max(max_score, 4.0)  # Near-exact match (80%+ of entity name)
            
            # Partial match bonuses
            elif entity_name.startswith(focus_lower) or entity_name.endswith(focus_lower):
                max_score = max(max_score, 3.0)  # Prefix/suffix match
            elif focus_lower in entity_name:
                max_score = max(max_score, 2.5)  # Substring match in entity name
        
        return max_score
    
    def calculate_enhanced_text_relevance(entity_name, task_description):
        """Enhanced text relevance calculation."""
        task_lower = task_description.lower()
        entity_name = entity_name.lower()
        
        # Exact match detection (highest priority)
        exact_score = 0.0
        if entity_name in task_lower:
            # Check if it's a word boundary match (more precise)
            if re.search(r'\b' + re.escape(entity_name) + r'\b', task_lower):
                exact_score = 2.0  # Exact word boundary match
            else:
                exact_score = 1.5  # Substring match
        
        return exact_score
    
    # Test cases with expected exact matches
    test_cases = [
        {
            "entity_name": "context_selection",
            "query": "Why is my context_selection function taking so long?",
            "focus_entities": ["context_selection"],
            "expected_focus_score": 5.0,  # Exact match
            "expected_text_score": 2.0    # Word boundary match
        },
        {
            "entity_name": "select_optimal_context", 
            "query": "How does select_optimal_context work?",
            "focus_entities": ["select_optimal_context"],
            "expected_focus_score": 5.0,
            "expected_text_score": 2.0
        },
        {
            "entity_name": "parse_request",
            "query": "Fix the parse_request bug",
            "focus_entities": ["parse_request"],
            "expected_focus_score": 5.0,
            "expected_text_score": 2.0
        },
        {
            "entity_name": "other_function",
            "query": "Why is my context_selection function taking so long?",
            "focus_entities": ["context_selection"],
            "expected_focus_score": 0.0,  # No match
            "expected_text_score": 0.0    # No match
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['entity_name']} vs '{test_case['query']}'")
        
        focus_score = calculate_focus_entity_score(
            test_case['entity_name'], 
            test_case['focus_entities']
        )
        text_score = calculate_enhanced_text_relevance(
            test_case['entity_name'],
            test_case['query']
        )
        
        print(f"  Focus score: {focus_score} (expected: {test_case['expected_focus_score']})")
        print(f"  Text score: {text_score} (expected: {test_case['expected_text_score']})")
        
        focus_pass = abs(focus_score - test_case['expected_focus_score']) < 0.1
        text_pass = abs(text_score - test_case['expected_text_score']) < 0.1
        
        if focus_pass and text_pass:
            print("  ✅ PASS: Scores match expected values")
        else:
            print("  ❌ FAIL: Scores don't match expected values")
        print()

def main():
    """Run all verification tests."""
    print("🚀 Enhanced Relevance Matching Verification")
    print("=" * 80)
    
    print("\n📋 Test 1: Focus Entity Extraction")
    test_enhanced_focus_extraction()
    
    print("\n📋 Test 2: Relevance Scoring Logic")
    test_enhanced_relevance_scoring()
    
    print("\n" + "=" * 80)
    print("✅ Verification complete!")
    print("\nKey improvements implemented:")
    print("  🎯 Enhanced focus entity extraction with programming patterns")
    print("  🔍 Exact match detection and prioritization")
    print("  📈 Improved relevance scoring with higher text weights")
    print("  🏆 Multi-tier matching (exact, partial, fuzzy)")
    print("\nThese fixes should resolve the critical issue where exact function")
    print("name matches were not being included in LLM-Friendly Packages.")

if __name__ == "__main__":
    main()
