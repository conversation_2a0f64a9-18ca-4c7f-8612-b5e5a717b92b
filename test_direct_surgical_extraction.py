#!/usr/bin/env python

import os
import sys
import time
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from surgical_file_extractor import SurgicalFileExtractor
    print("✅ Successfully imported SurgicalFileExtractor")
    SURGICAL_EXTRACTOR_AVAILABLE = True
except ImportError:
    print("❌ Failed to import SurgicalFileExtractor")
    SURGICAL_EXTRACTOR_AVAILABLE = False


class MockAiderService:
    """Mock AiderService class for testing."""
    
    def find_file_defining_symbol(self, project_path, symbol_name):
        """Find the file that defines the given symbol."""
        if symbol_name == "extract_symbol_content":
            return "surgical_file_extractor.py"
        return None
    
    def extract_symbol_content(self, symbol_name, file_path, project_path):
        """Extract the content of a symbol from a file."""
        return f"def {symbol_name}(self, target_symbol, file_path, project_path):\n    # Mock implementation\n    pass"


def main():
    print("\n=== Testing Direct Surgical Extraction ===")
    
    if not SURGICAL_EXTRACTOR_AVAILABLE:
        print("❌ SurgicalFileExtractor is not available, skipping test")
        return
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the surgical file extractor
    try:
        surgical_extractor = SurgicalFileExtractor(MockAiderService())
        print("✅ Successfully initialized SurgicalFileExtractor")
    except Exception as e:
        print(f"❌ Error initializing SurgicalFileExtractor: {e}")
        return
    
    # Test extracting a symbol
    symbol_name = "extract_symbol_content"
    file_path = "surgical_file_extractor.py"
    
    print(f"\nExtracting symbol: {symbol_name} from file: {file_path}")
    content = surgical_extractor.extract_symbol_content(symbol_name, file_path, project_path)
    
    if content:
        print("\n=== Extracted Content ===")
        print(content)
        print("\n✅ Successfully extracted symbol content")
    else:
        print("\n❌ Failed to extract symbol content")
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
