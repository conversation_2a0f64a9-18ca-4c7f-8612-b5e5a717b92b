#!/usr/bin/env python3
"""
Test script to verify that the LLM no longer asks users to manually add files to chat.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_no_manual_file_request_instructions():
    """Test that the instructions no longer tell LLM to ask users to add files manually."""
    print("🧪 Testing No Manual File Request Instructions")
    print("=" * 60)
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Check files_no_full_files_with_repo_map
        no_files_prompt = prompts.files_no_full_files_with_repo_map
        
        print("📋 Checking files_no_full_files_with_repo_map prompt...")
        
        # Check for old problematic instructions
        old_instructions = [
            ("ask me to add the files", "❌" if "ask me to add the files" in no_files_prompt.lower() else "✅"),
            ("stop so I can add them", "❌" if "stop so I can add them" in no_files_prompt.lower() else "✅"),
            ("add the files to the chat", "❌" if "add the files to the chat" in no_files_prompt.lower() else "✅"),
        ]
        
        print("  🔍 Checking for OLD problematic instructions:")
        for check, status in old_instructions:
            print(f"    {status} Should NOT contain: '{check}'")
        
        # Check for new correct instructions
        new_instructions = [
            ("NEVER ask the user to manually add files", "✅" if "NEVER ask the user to manually add files" in no_files_prompt else "❌"),
            ("MAP_REQUEST", "✅" if "MAP_REQUEST" in no_files_prompt else "❌"),
            ("CONTEXT_REQUEST", "✅" if "CONTEXT_REQUEST" in no_files_prompt else "❌"),
            ("REQUEST_FILE", "✅" if "REQUEST_FILE" in no_files_prompt else "❌"),
            ("Smart Map Request System", "✅" if "Smart Map Request System" in no_files_prompt else "❌"),
        ]
        
        print("  🔍 Checking for NEW correct instructions:")
        for check, status in new_instructions:
            print(f"    {status} Should contain: '{check}'")
        
        # Check the reply message too
        reply_prompt = prompts.files_no_full_files_with_repo_map_reply
        
        print("\n📋 Checking files_no_full_files_with_repo_map_reply...")
        
        reply_checks = [
            ("Smart Map Request System", "✅" if "Smart Map Request System" in reply_prompt else "❌"),
            ("explore the codebase", "✅" if "explore the codebase" in reply_prompt else "❌"),
        ]
        
        for check, status in reply_checks:
            print(f"    {status} Should contain: '{check}'")
        
        # Count how many checks passed
        all_checks = old_instructions + new_instructions + reply_checks
        passed = sum(1 for _, status in all_checks if status == "✅")
        total = len(all_checks)
        
        print(f"\n📊 Manual file request elimination: {passed}/{total} checks passed")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing manual file request instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_after_map_request():
    """Test the expected workflow after MAP_REQUEST is processed."""
    print("\n🧪 Testing Workflow After MAP_REQUEST")
    print("=" * 60)
    
    print("📋 Expected LLM behavior after receiving MAP_REQUEST response:")
    
    scenarios = [
        {
            "scenario": "Map provides sufficient information",
            "expected_behavior": "Answer the user's question directly using the map information",
            "should_not_do": "Ask user to add files to chat"
        },
        {
            "scenario": "Map shows relevant files but need implementation details",
            "expected_behavior": "Use CONTEXT_REQUEST to get specific symbol implementations",
            "should_not_do": "Ask user to add files to chat"
        },
        {
            "scenario": "Need to see entire file contents",
            "expected_behavior": "Use REQUEST_FILE to get the complete file",
            "should_not_do": "Ask user to add files to chat"
        },
        {
            "scenario": "Map is insufficient",
            "expected_behavior": "Use MAP_REQUEST again with different/broader keywords",
            "should_not_do": "Ask user to add files to chat"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['scenario']}:")
        print(f"   ✅ Should: {scenario['expected_behavior']}")
        print(f"   ❌ Should NOT: {scenario['should_not_do']}")
    
    print("\n🎯 Key principle: The LLM should NEVER ask the user to manually add files!")
    print("   The Smart Map Request System provides all necessary context automatically.")
    
    return True

def test_problematic_phrases():
    """Test that problematic phrases are not present in the prompts."""
    print("\n🧪 Testing Problematic Phrases")
    print("=" * 60)
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Get all prompt content
        all_prompts = [
            ("file_access_reminder", prompts.file_access_reminder),
            ("repo_content_prefix", prompts.repo_content_prefix),
            ("files_no_full_files_with_repo_map", prompts.files_no_full_files_with_repo_map),
            ("files_no_full_files_with_repo_map_reply", prompts.files_no_full_files_with_repo_map_reply),
        ]
        
        # Problematic phrases that should NOT appear
        problematic_phrases = [
            "add the files to the chat",
            "ask me to add the files",
            "stop so I can add them",
            "please add the relevant file",
            "add the relevant files",
            "I need you to add",
        ]
        
        print("🔍 Checking for problematic phrases in all prompts...")
        
        issues_found = 0
        
        for prompt_name, prompt_content in all_prompts:
            print(f"\n📋 Checking {prompt_name}:")
            
            for phrase in problematic_phrases:
                if phrase.lower() in prompt_content.lower():
                    print(f"   ❌ Found problematic phrase: '{phrase}'")
                    issues_found += 1
                else:
                    print(f"   ✅ Clean of: '{phrase}'")
        
        print(f"\n📊 Problematic phrases found: {issues_found}")
        
        return issues_found == 0
        
    except Exception as e:
        print(f"❌ Error testing problematic phrases: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all manual file request elimination tests."""
    print("🚀 Manual File Request Elimination Test")
    print("=" * 80)
    
    tests = [
        ("No Manual File Request Instructions", test_no_manual_file_request_instructions),
        ("Workflow After MAP_REQUEST", test_workflow_after_map_request),
        ("Problematic Phrases", test_problematic_phrases),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 MANUAL FILE REQUEST ELIMINATION TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! LLM will no longer ask users to manually add files.")
        print("\n📋 The LLM now understands:")
        print("  1. ✅ NEVER ask users to manually add files to chat")
        print("  2. ✅ Use MAP_REQUEST to explore repository structure")
        print("  3. ✅ Use CONTEXT_REQUEST for specific symbol implementations")
        print("  4. ✅ Use REQUEST_FILE for entire files when needed")
        print("  5. ✅ The Smart Map Request System provides all necessary context")
        print("\n🎯 After MAP_REQUEST, the LLM should:")
        print("  ✅ Answer directly if map is sufficient")
        print("  ✅ Use CONTEXT_REQUEST/REQUEST_FILE for more details")
        print("  ❌ NEVER ask 'Please add the relevant file(s) to the chat'")
    else:
        print("⚠️  Some tests failed. Please check the manual file request instructions.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
