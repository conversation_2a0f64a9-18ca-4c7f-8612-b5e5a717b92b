#!/usr/bin/env python3
"""
Test script to verify that the format consistency fix works correctly.
This tests that all prompt instructions use consistent JSON format examples.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_format_consistency():
    """Test that all prompt instructions use consistent JSON format."""
    print("🧪 Testing Format Consistency")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check all prompt sections for format consistency
        prompt_sections = [
            ("file_access_reminder", prompts.file_access_reminder),
            ("repo_content_prefix", prompts.repo_content_prefix),
            ("main_system", prompts.main_system),
        ]

        issues = []
        correct_formats = 0
        total_checks = 0

        for section_name, content in prompt_sections:
            print(f"\n🔍 Checking {section_name}...")

            # Check for problematic "EXECUTE" language
            if "EXECUTE MAP_REQUEST:" in content:
                issues.append(f"{section_name}: Contains 'EXECUTE MAP_REQUEST:' instead of JSON format")
            elif "EXECUTE CONTEXT_REQUEST:" in content:
                issues.append(f"{section_name}: Contains 'EXECUTE CONTEXT_REQUEST:' instead of JSON format")
            elif "EXECUTE REQUEST_FILE:" in content:
                issues.append(f"{section_name}: Contains 'EXECUTE REQUEST_FILE:' instead of JSON format")
            else:
                print(f"  ✅ No problematic EXECUTE language found")
                correct_formats += 1

            total_checks += 1

            # Check for correct JSON format examples
            if "{{MAP_REQUEST:" in content:
                print(f"  ✅ Contains correct MAP_REQUEST JSON format")
                correct_formats += 1
            else:
                issues.append(f"{section_name}: Missing correct MAP_REQUEST JSON format")

            total_checks += 1

            # Check for consistent language
            if "USE THE REQUEST FORMAT" in content or "Use MAP_REQUEST format" in content:
                print(f"  ✅ Uses consistent format language")
                correct_formats += 1
            elif "EXECUTE THE REQUEST" in content:
                issues.append(f"{section_name}: Still uses 'EXECUTE THE REQUEST' language")
            else:
                correct_formats += 1

            total_checks += 1

        print(f"\n📊 Format consistency: {correct_formats}/{total_checks} checks passed")

        if issues:
            print("\n❌ Issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("\n✅ All format consistency checks passed!")
            return True

    except Exception as e:
        print(f"❌ Error testing format consistency: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_format_examples():
    """Test that JSON format examples are correct and consistent."""
    print("\n🧪 Testing JSON Format Examples")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check for correct JSON format patterns
        content = prompts.repo_content_prefix

        json_format_checks = [
            ("{{MAP_REQUEST:", "Correct MAP_REQUEST format"),
            ("{{CONTEXT_REQUEST:", "Correct CONTEXT_REQUEST format"),
            ("{{REQUEST_FILE:", "Correct REQUEST_FILE format"),
            ('"keywords":', "Correct keywords property"),
            ('"type": "implementation"', "Correct type property"),
            ('"scope": "all"', "Correct scope property"),
            ('"max_results": 8', "Correct max_results property"),
        ]

        passed = 0
        total = len(json_format_checks)

        for pattern, description in json_format_checks:
            if pattern in content:
                print(f"✅ {description}: Found {pattern}")
                passed += 1
            else:
                print(f"❌ {description}: Missing {pattern}")

        print(f"\n📊 JSON format examples: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing JSON format examples: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_conflicting_instructions():
    """Test that there are no conflicting instruction formats."""
    print("\n🧪 Testing No Conflicting Instructions")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()

        # Check all content for conflicting patterns
        all_content = (
            prompts.file_access_reminder + 
            prompts.repo_content_prefix + 
            prompts.main_system
        )

        conflicting_patterns = [
            ("EXECUTE MAP_REQUEST:", "Old execute format"),
            ("EXECUTE CONTEXT_REQUEST:", "Old execute format"),
            ("EXECUTE REQUEST_FILE:", "Old execute format"),
            ("Execute MAP_REQUEST", "Mixed execute language"),
            ("Execute CONTEXT_REQUEST", "Mixed execute language"),
        ]

        conflicts = []

        for pattern, description in conflicting_patterns:
            if pattern in all_content:
                conflicts.append(f"{description}: Found '{pattern}'")

        if conflicts:
            print("❌ Conflicting instructions found:")
            for conflict in conflicts:
                print(f"  - {conflict}")
            return False
        else:
            print("✅ No conflicting instruction formats found!")
            return True

    except Exception as e:
        print(f"❌ Error testing conflicting instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all format consistency tests."""
    print("🚀 Testing Format Consistency Fix")
    print("=" * 80)

    tests = [
        test_format_consistency,
        test_json_format_examples,
        test_no_conflicting_instructions,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 Format consistency fix is working correctly!")
        print("\n📋 The LLM now receives:")
        print("  1. ✅ Consistent JSON format examples: {{MAP_REQUEST: ...}}")
        print("  2. ✅ No conflicting 'EXECUTE' language")
        print("  3. ✅ Clear 'USE THE REQUEST FORMAT' instructions")
        print("  4. ✅ Proper JSON structure in all examples")
        print("  5. ✅ No mixed instruction formats")
    else:
        print("⚠️  Some format consistency fixes need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
