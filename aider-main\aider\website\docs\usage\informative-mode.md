---
parent: Usage
nav_order: 45
description: Run Aider in informative mode to analyze code without modifying files
---

# Informative Mode

Aider's informative mode transforms the application from a code editing tool to an informative application that analyzes and provides insights about code without modifying files.

## Overview

In informative mode, Aider treats all files as read-only and focuses on providing analysis, recommendations, and insights rather than making direct edits. This mode is useful when you want to:

- Understand code structure and patterns
- Get recommendations for improvements without changing files
- Analyze potential issues in your codebase
- Learn about best practices for your specific code

## Using Informative Mode

### Command Line

To run Aider in informative mode, use the `--informative` flag:

```bash
aider --informative [files]
```

You can also combine it with the browser interface:

```bash
aider --informative --browser [files]
```

### Features in Informative Mode

When running in informative mode, Aider provides:

1. **Code Structure Analysis**: Understand the organization and architecture of your code
2. **Best Practices Scanner**: Identify areas where code could be improved
3. **Documentation Insights**: Get suggestions for better documentation
4. **Performance Analysis**: Identify potential bottlenecks and performance issues
5. **Security Insights**: Detect potential security vulnerabilities

### Example Usage

Here's an example of how to use <PERSON><PERSON> in informative mode:

```bash
# Analyze a specific file
aider --informative main.py

# Analyze multiple files
aider --informative src/utils.py src/models.py

# Analyze files in browser mode
aider --informative --browser src/
```

## How It Works

In informative mode, Aider:

1. Loads all files as read-only, regardless of how they're added to the chat
2. Processes the LLM's suggested edits but doesn't apply them
3. Instead, it analyzes the suggested changes and provides detailed information about:
   - What would be changed
   - Why the changes are recommended
   - How the changes would improve the code
   - Potential impacts of the changes

## Visual Indicators

When running in informative mode:

- Terminal: A message indicates that Aider is running in informative mode
- Browser: The interface has a light blue theme to indicate informative mode
- All files are marked as "read-only"

## Benefits

- **Safety**: Experiment with AI suggestions without risking unintended changes
- **Learning**: Understand why certain changes are recommended
- **Exploration**: Explore alternative approaches without commitment
- **Collaboration**: Share analysis with team members before implementing changes

## Limitations

- You cannot apply the suggested changes directly
- Some features that require file modifications are disabled

## Switching Between Modes

You can easily switch between informative mode and regular editing mode by restarting Aider with or without the `--informative` flag.
