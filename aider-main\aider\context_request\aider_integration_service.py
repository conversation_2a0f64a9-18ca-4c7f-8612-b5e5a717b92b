#!/usr/bin/env python

import os
import re
import sys
from typing import Dict, List, Optional, Any
from pathlib import Path

# Try to import the surgical extraction functions
try:
    from simple_extraction_test import (
        SymbolInfo,
        ExtractionRange,
        read_file_content,
        get_file_line_count,
        get_symbols_in_file,
        extract_symbol_range,
        extract_symbol_content
    )

    from simple_enhanced_test import (
        extract_essential_imports,
        extract_containing_class,
        find_symbol_usages,
        extract_enhanced_context,
        format_enhanced_context,
        UsageContext,
        EnhancedCodeContext
    )

    SURGICAL_EXTRACTION_AVAILABLE = True
except ImportError:
    SURGICAL_EXTRACTION_AVAILABLE = False


class AiderIntegrationService:
    """
    Service for integrating with <PERSON><PERSON>'s codebase.

    This class provides methods for finding files, extracting symbols,
    and analyzing code dependencies.
    """

    def __init__(self, coder=None):
        """
        Initialize the integration service.

        Args:
            coder: The Aider coder instance that has access to the repository map
        """
        self.coder = coder
        self.repo_map = None

        # Try to get the repository map from the coder
        if self.coder and hasattr(self.coder, 'repo_map'):
            self.repo_map = self.coder.repo_map

    def get_repo_map(self, project_path: str = None):
        """
        Get the repository map.

        Args:
            project_path: Optional path to the project root

        Returns:
            The repository map, or None if not available
        """
        # If we already have a repo map, return it
        if self.repo_map:
            return self.repo_map

        # If we have a coder, try to get the repo map from it
        if self.coder and hasattr(self.coder, 'repo_map'):
            self.repo_map = self.coder.repo_map
            return self.repo_map

        # If we have a project path, try to create a repo map
        if project_path:
            try:
                from aider.repomap import RepoMap
                self.repo_map = RepoMap(project_path)
                return self.repo_map
            except Exception as e:
                print(f"Error creating repository map: {e}")

        # If all else fails, return None
        print("Repository map not available")
        return None

    def find_file_defining_symbol(self, project_path: str, symbol_name: str) -> Optional[str]:
        """
        Find the file that defines the given symbol.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to find

        Returns:
            Path to the file defining the symbol, or None if not found
        """
        try:
            # If we have a file hint in the symbol name, extract it
            if '|' in symbol_name:
                symbol_name, file_hint = symbol_name.split('|', 1)
                return file_hint.strip()

            # Try to use the repository map first
            repo_map = self.get_repo_map(project_path)
            if repo_map:
                # Get all files from the repository map
                all_files = []
                # Use the get_all_files method if available
                if hasattr(repo_map, 'get_all_files') and callable(getattr(repo_map, 'get_all_files')):
                    # Get all files from the repository map
                    rel_paths = repo_map.get_all_files()
                    # Filter for Python files
                    rel_paths = [path for path in rel_paths if path.endswith('.py')]
                    # Convert to (full_path, rel_path) tuples
                    for rel_path in rel_paths:
                        file_path = os.path.join(project_path, rel_path)
                        all_files.append((file_path, rel_path))
                else:
                    # Fallback to walking the directory
                    for root, dirs, files in os.walk(project_path):
                        for file in files:
                            if file.endswith('.py'):  # Focus on Python files for now
                                file_path = os.path.join(root, file)
                                rel_path = os.path.relpath(file_path, project_path)
                                all_files.append((file_path, rel_path))

                # Extract the class name if it's a method
                if '.' in symbol_name:
                    class_name, method_name = symbol_name.split('.', 1)

                    # Try to find the class or method in the files
                    for file_path, rel_path in all_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Check for class definition
                            if f"class {class_name}" in content:
                                return rel_path

                            # Check for method definition
                            if f"def {method_name}" in content:
                                return rel_path
                        except Exception:
                            continue
                else:
                    # Try to find the symbol directly
                    for file_path, rel_path in all_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Check for function/class definition
                            if f"def {symbol_name}" in content or f"class {symbol_name}" in content:
                                return rel_path
                        except Exception:
                            continue

            # Use the surgical file extractor if available
            if hasattr(self, 'surgical_extractor') and self.surgical_extractor and hasattr(self.surgical_extractor, 'find_file_defining_symbol'):
                file_path = self.surgical_extractor.find_file_defining_symbol(project_path, symbol_name)
                if file_path:
                    return file_path

            # Fallback to a simple implementation if the repository map and surgical extractor are not available
            print("Repository map and surgical file extractor not available, using fallback implementation")

            # Extract the class name if it's a method
            if '.' in symbol_name:
                class_name, method_name = symbol_name.split('.', 1)
            else:
                class_name = symbol_name
                method_name = None

            # Walk through the project directory
            for root, dirs, files in os.walk(project_path):
                # Skip hidden directories and common directories to ignore
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'venv', '.git']]

                # Check each file
                for file in files:
                    # Skip non-Python files for now (this should be extended for other languages)
                    if not file.endswith('.py'):
                        continue

                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Check if the class is defined in this file
                        if f"class {class_name}" in content:
                            return rel_path

                        # Check if the method is defined in this file
                        if method_name and f"def {method_name}" in content:
                            return rel_path

                        # Check if the function is defined in this file
                        if f"def {class_name}" in content:
                            return rel_path
                    except Exception:
                        # Skip files that can't be read
                        continue

            # If we couldn't find the symbol, return None
            return None
        except Exception as e:
            print(f"Error finding file defining symbol: {e}")
            return None

    def extract_symbol_content(self, symbol_name: str, file_path: str, project_path: str) -> str:
        """
        Extract the content of a symbol from a file.

        Args:
            symbol_name: Name of the symbol to extract
            file_path: Path to the file containing the symbol
            project_path: Path to the project root

        Returns:
            Content of the symbol

        Raises:
            ValueError: If the symbol cannot be found or extracted
        """
        # Use the repository map
        repo_map = self.get_repo_map(project_path)
        if not repo_map:
            print(f"Warning: Repository map not available for project: {project_path}")
            return None

        # Get the full path to the file
        full_path = os.path.join(project_path, file_path)
        if not os.path.exists(full_path):
            print(f"Warning: File not found: {full_path}")
            return None

        # Read the file content
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading file {full_path}: {e}")
            return None

        lines = content.splitlines()

        # Extract the class name if it's a method
        if '.' in symbol_name:
            class_name, method_name = symbol_name.split('.', 1)
            target_symbol = method_name
        else:
            target_symbol = symbol_name

        # Get all tags for the file
        abs_file_path = os.path.join(project_path, file_path)
        rel_file_path = repo_map.get_rel_fname(abs_file_path)

        # Get tags for the file
        tags = repo_map.get_tags(abs_file_path, rel_file_path)
        if not tags:
            raise ValueError(f"No tags found for file: {file_path}")

        # Filter for definition tags (both functions and classes)
        def_tags = [tag for tag in tags if tag.kind in ["def", "class"]]
        if not def_tags:
            print(f"Warning: No definition tags found for file: {file_path}")
            return None

        # Find the target symbol
        target_tag = None
        for tag in def_tags:
            if tag.name == target_symbol:
                target_tag = tag
                break

        if not target_tag:
            # Don't raise an error - return None to allow graceful handling
            print(f"Warning: Symbol '{target_symbol}' not found in file: {file_path}")
            return None

        # For class extraction, find the next top-level symbol boundary
        # For function/method extraction, find the next symbol at the same or higher level
        if target_tag.kind == "class":
            # For classes, extract until the next top-level class or function
            next_tags = [t for t in def_tags if t.line > target_tag.line and t.kind in ["class", "def"]]
            # Filter to only top-level symbols (not methods within classes)
            next_tags = [t for t in next_tags if not self._is_method_within_class(lines, t.line)]
        else:
            # For functions/methods, find the next symbol at any level
            next_tags = [t for t in def_tags if t.line > target_tag.line]

        if next_tags:
            # Extract until next symbol
            next_tag = min(next_tags, key=lambda t: t.line)
            end_line = next_tag.line
        else:
            # Extract to end of file
            end_line = len(lines)

        # Extract the implementation (adjusting for 0-based indexing)
        start_line = target_tag.line
        extracted_lines = lines[start_line:end_line]
        return '\n'.join(extracted_lines)

    def extract_essential_imports(self, project_path: str, file_path: str) -> str:
        """
        Extract essential imports from a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file

        Returns:
            Essential imports as a string

        Raises:
            ValueError: If the imports cannot be extracted
        """
        # Get the full path to the file
        full_path = os.path.join(project_path, file_path)
        if not os.path.exists(full_path):
            print(f"Warning: File not found: {full_path}")
            return ""

        # Read the file content
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract import statements
        import_lines = []
        for line in content.splitlines():
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                import_lines.append(line)

        # Return empty string if no import statements found
        return '\n'.join(import_lines)

    def extract_containing_class(self, project_path: str, file_path: str, symbol_name: str) -> str:
        """
        Extract the containing class of a method.

        Args:
            project_path: Path to the project root
            file_path: Path to the file
            symbol_name: Name of the method

        Returns:
            Name of the containing class

        Raises:
            ValueError: If the containing class cannot be found
        """
        # Get the full path to the file
        full_path = os.path.join(project_path, file_path)
        if not os.path.exists(full_path):
            print(f"Warning: File not found: {full_path}")
            return ""

        # Use the repository map to find the containing class
        repo_map = self.get_repo_map(project_path)
        if not repo_map:
            print(f"Warning: Repository map not available for project: {project_path}")
            return ""

        # Get all tags for the file
        abs_file_path = os.path.join(project_path, file_path)
        rel_file_path = repo_map.get_rel_fname(abs_file_path)

        # Get tags for the file
        tags = repo_map.get_tags(abs_file_path, rel_file_path)
        if not tags:
            print(f"Warning: No tags found for file: {file_path}")
            return ""

        # Extract the method name if it includes the class name
        if '.' in symbol_name:
            class_name, method_name = symbol_name.split('.', 1)
            # If the symbol name already includes the class name, just return it
            return class_name
        else:
            method_name = symbol_name

        # Find the method tag
        method_tag = None
        for tag in tags:
            if tag.kind == "def" and tag.name == method_name:
                method_tag = tag
                break

        if not method_tag:
            print(f"Warning: Method '{method_name}' not found in file: {file_path}")
            return ""

        # Find the containing class by looking for a class tag that contains the method
        class_tags = [tag for tag in tags if tag.kind == "class"]

        # Read the file content to determine line numbers
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        lines = content.splitlines()
        method_line = method_tag.line

        # Find the class that contains the method
        containing_class = None
        for class_tag in class_tags:
            class_line = class_tag.line

            # Find the end of the class
            class_end_line = len(lines)
            next_class_tags = [t for t in class_tags if t.line > class_line]
            if next_class_tags:
                next_class_tag = min(next_class_tags, key=lambda t: t.line)
                class_end_line = next_class_tag.line

            # Check if the method is within this class
            if class_line < method_line < class_end_line:
                containing_class = class_tag.name
                break

        if not containing_class:
            print(f"Warning: No containing class found for method '{symbol_name}' in file: {file_path}")
            return ""

        return containing_class

    def extract_usage_contexts(self, project_path: str, symbol_name: str, file_path: str) -> List[Dict[str, Any]]:
        """
        Extract usage contexts of a symbol.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol
            file_path: Path to the file defining the symbol

        Returns:
            List of usage contexts

        Raises:
            ValueError: If the usage contexts cannot be extracted
        """
        # Get the full path to the file
        full_path = os.path.join(project_path, file_path)
        if not os.path.exists(full_path):
            print(f"Warning: File not found: {full_path}")
            return []

        # Use the repository map to find usages
        repo_map = self.get_repo_map(project_path)
        if not repo_map:
            print(f"Warning: Repository map not available for project: {project_path}")
            return []

        # Get all files from the repository map
        all_files = []
        # Use the get_all_files method if available
        if hasattr(repo_map, 'get_all_files') and callable(getattr(repo_map, 'get_all_files')):
            # Get all files from the repository map
            rel_paths = repo_map.get_all_files()
            # Filter for Python files
            rel_paths = [path for path in rel_paths if path.endswith('.py')]
            # Convert to (full_path, rel_path) tuples
            for rel_path in rel_paths:
                file_path = os.path.join(project_path, rel_path)
                all_files.append((file_path, rel_path))
        else:
            # Fallback to walking the directory
            for root, dirs, files in os.walk(project_path):
                for file in files:
                    if file.endswith('.py'):  # Focus on Python files for now
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path, project_path)
                        all_files.append((file_path, rel_path))

        # Get the full symbol name (including class if it's a method)
        full_symbol_name = symbol_name
        if '.' not in symbol_name:
            # Try to find the containing class
            try:
                containing_class = self.extract_containing_class(project_path, file_path, symbol_name)
                if containing_class:
                    full_symbol_name = f"{containing_class}.{symbol_name}"
            except ValueError:
                # If we can't find the containing class, just use the symbol name
                pass

        # Find usages in other files
        usages = []

        # Check each file
        for file_path, rel_path in all_files:
            # Skip the defining file
            if rel_path == file_path:
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check if the symbol is used in this file
                if symbol_name in content:
                    # Find the context around the usage
                    lines = content.splitlines()
                    for i, line in enumerate(lines):
                        if symbol_name in line and not line.strip().startswith('#'):
                            # Extract the context (5 lines before and 5 lines after)
                            start = max(0, i - 5)
                            end = min(len(lines), i + 6)
                            context = '\n'.join(lines[start:end])

                            # Determine the usage type
                            usage_type = "unknown"
                            if f"{symbol_name}(" in line or f"{full_symbol_name}(" in line:
                                usage_type = "function_call"
                            elif "import" in line and symbol_name in line:
                                usage_type = "import"
                            elif "=" in line and symbol_name in line:
                                usage_type = "assignment"

                            # Add the usage context
                            usages.append({
                                "file_path": rel_path,
                                "symbol_name": symbol_name,
                                "content": context,
                                "usage_type": usage_type
                            })

                            # Only add one usage per file
                            break
            except Exception as e:
                # Skip files that can't be read
                print(f"Warning: Error reading file {file_path}: {e}")
                continue

        return usages

    def _is_method_within_class(self, lines: List[str], line_num: int) -> bool:
        """
        Check if a function definition at the given line is a method within a class.

        Args:
            lines: List of all lines in the file
            line_num: 1-based line number of the function definition

        Returns:
            True if the function is a method within a class, False otherwise
        """
        if line_num < 1 or line_num > len(lines):
            return False

        # Convert to 0-based index
        line_idx = line_num - 1

        # Get the indentation of the function
        func_line = lines[line_idx]
        if not func_line.strip().startswith('def '):
            return False

        # Check if the function is indented (indicating it's inside a class)
        return func_line.startswith('    ') or func_line.startswith('\t')
