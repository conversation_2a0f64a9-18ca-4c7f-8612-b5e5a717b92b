#!/usr/bin/env python3
"""
Fixed implementation that uses the REAL ICD system like the working reference.
This combines enhanced inheritance analysis with the actual working ICD extraction.
"""

import os
import sys
import time

def test_fixed_inheritance_with_real_icd():
    """Generate LLM package using the REAL working ICD system with inheritance data."""
    print("🎯 Fixed Inheritance + Real ICD Integration")
    print("=" * 50)
    
    try:
        # Add the aider-main directory to the path (same as working reference)
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)

        # Import the REAL working modules (same as working reference)
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported working IR context request modules")
        
        # Create a context request handler (same as working reference)
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Create the SAME request as the working reference but with inheritance focus
        print("\n🤖 Generating Enhanced LLM Package with Inheritance + Real ICD")
        
        enhanced_request = IRContextRequest(
            user_query="Why is my context selection taking so long?",  # SAME query as working reference
            task_description="Debug performance issues in context selection with inheritance analysis",
            task_type="debugging",
            focus_entities=["performance", "context", "selection", "inheritance", "class"],  # Added inheritance focus
            max_tokens=2000,
            include_ir_slices=True,
            include_code_context=True,  # This is KEY - enables real source code extraction
            # LLM-friendly options (same as working reference)
            llm_friendly=True,
            max_output_chars=30000,
            max_entities=8
        )
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ Enhanced request processed in {processing_time:.2f}s")
        
        # Check that LLM-friendly package was generated (same as working reference)
        if "llm_friendly_package" in result:
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"✅ Enhanced LLM-friendly package generated!")
            print(f"   📦 Package size: {package_size:,} characters")
            print(f"   🤖 Compatibility: {compatibility}")
            
            # Verify it's actually LLM-compatible
            if package_size <= 32000:
                print("   ✅ Package is GPT-4 compatible!")
            elif package_size <= 100000:
                print("   ⚠️  Package requires GPT-4 Turbo or Claude")
            else:
                print("   ❌ Package is still too large for standard LLMs")
                
            # Save the enhanced package
            output_file = "fixed_inheritance_with_real_icd_package.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(result["llm_friendly_package"])
            print(f"   💾 Enhanced package saved to: {output_file}")
            
            # Analyze the package content
            package_content = result["llm_friendly_package"]
            
            # Check for KEY IMPLEMENTATIONS section
            if "## KEY IMPLEMENTATIONS" in package_content:
                # Count functions in KEY IMPLEMENTATIONS
                key_impl_section = package_content.split("## KEY IMPLEMENTATIONS")[1]
                if "## ANALYSIS INSTRUCTIONS" in key_impl_section:
                    key_impl_section = key_impl_section.split("## ANALYSIS INSTRUCTIONS")[0]
                
                function_count = key_impl_section.count("### ")
                print(f"   🔧 KEY IMPLEMENTATIONS: {function_count} functions")
                
                # Check if we have real source code (not placeholders)
                if "def " in key_impl_section and "```python" in key_impl_section:
                    print("   ✅ Contains REAL source code snippets!")
                    
                    # Check for inheritance data in CRITICAL ENTITIES
                    if "Inherits From:" in package_content and "Class Context" in package_content:
                        print("   ✅ Contains enhanced inheritance analysis!")
                        print("   🎉 SUCCESS: Both real ICD and inheritance data working!")
                        return True, output_file
                    else:
                        print("   ⚠️  Missing inheritance data in CRITICAL ENTITIES")
                        return False, output_file
                else:
                    print("   ❌ Contains placeholder code, not real source")
                    return False, output_file
            else:
                print("   ❌ Missing KEY IMPLEMENTATIONS section")
                return False, output_file
                
        else:
            print("❌ LLM-friendly package was not generated!")
            return False, None
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure the aider.context_request module is available")
        return False, None
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def compare_with_original():
    """Compare the fixed implementation with the original working version."""
    print("\n📊 Comparing Fixed Implementation with Original")
    print("=" * 55)
    
    # Check if we have both files
    fixed_file = "fixed_inheritance_with_real_icd_package.txt"
    original_file = "test_llm_friendly_package_with_inheritance.txt"
    
    if not os.path.exists(fixed_file):
        print("❌ Fixed implementation file not found")
        return False
    
    if not os.path.exists(original_file):
        print("❌ Original working file not found")
        return False
    
    # Read both files
    with open(fixed_file, 'r', encoding='utf-8') as f:
        fixed_content = f.read()
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Compare KEY IMPLEMENTATIONS sections
    print("🔍 Analyzing KEY IMPLEMENTATIONS sections...")
    
    # Extract KEY IMPLEMENTATIONS from both
    def extract_key_implementations(content):
        if "## KEY IMPLEMENTATIONS" not in content:
            return None, 0
        
        section = content.split("## KEY IMPLEMENTATIONS")[1]
        if "## ANALYSIS INSTRUCTIONS" in section:
            section = section.split("## ANALYSIS INSTRUCTIONS")[0]
        
        function_count = section.count("### ")
        return section, function_count
    
    fixed_section, fixed_count = extract_key_implementations(fixed_content)
    original_section, original_count = extract_key_implementations(original_content)
    
    print(f"   Original: {original_count} functions")
    print(f"   Fixed: {fixed_count} functions")
    
    # Check for real source code
    original_has_real_code = "def " in original_section and len(original_section) > 1000
    fixed_has_real_code = "def " in fixed_section and len(fixed_section) > 1000
    
    print(f"   Original has real code: {'✅' if original_has_real_code else '❌'}")
    print(f"   Fixed has real code: {'✅' if fixed_has_real_code else '❌'}")
    
    # Check for inheritance data
    original_has_inheritance = "Inherits From:" in original_content and "Class Context" in original_content
    fixed_has_inheritance = "Inherits From:" in fixed_content and "Class Context" in fixed_content
    
    print(f"   Original has inheritance: {'✅' if original_has_inheritance else '❌'}")
    print(f"   Fixed has inheritance: {'✅' if fixed_has_inheritance else '❌'}")
    
    # Overall assessment
    if fixed_count > 0 and fixed_has_real_code and fixed_has_inheritance:
        print("\n🎉 FIXED IMPLEMENTATION SUCCESS!")
        print("✅ Real ICD extraction working")
        print("✅ Enhanced inheritance data included")
        print("✅ Same query format maintained")
        return True
    else:
        print("\n❌ FIXED IMPLEMENTATION NEEDS MORE WORK")
        return False


def main():
    """Main function."""
    print("🔧 Testing Fixed Inheritance + Real ICD Integration")
    print("=" * 60)
    
    # Test the fixed implementation
    success, output_file = test_fixed_inheritance_with_real_icd()
    
    if success:
        print(f"\n✅ FIXED IMPLEMENTATION: SUCCESS")
        print(f"📄 Enhanced package: {output_file}")
        
        # Compare with original
        comparison_success = compare_with_original()
        
        if comparison_success:
            print("\n🎉 COMPLETE SUCCESS!")
            print("✅ Fixed the ICD extraction issue")
            print("✅ Enhanced with inheritance analysis")
            print("✅ Maintained working KEY IMPLEMENTATIONS")
            print("✅ Same query format as original")
            return True
        else:
            print("\n⚠️  PARTIAL SUCCESS")
            print("✅ Fixed implementation works")
            print("❌ Some differences from original detected")
            return True
    else:
        print(f"\n❌ FIXED IMPLEMENTATION: FAILED")
        print("❌ Still need to fix the ICD extraction issue")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 The inheritance-enhanced ICD system is now working correctly!")
        sys.exit(0)
    else:
        print("\n❌ The ICD extraction issue still needs to be resolved.")
        sys.exit(1)
