#!/usr/bin/env python

import os
import re
import unittest
from unittest.mock import MagicMock, patch

from aider_integration_service import AiderIntegrationService
from surgical_context_extractor import (
    SurgicalContextExtractor,
    CodeSnippet,
    UsageContext,
    DefinitionContext,
    ContextType,
    UsageType,
    DefinitionType
)


class TestSurgicalContextExtractor(unittest.TestCase):
    """Test cases for the SurgicalContextExtractor class."""

    def setUp(self):
        """Set up test fixtures."""
        self.service = AiderIntegrationService()
        self.extractor = SurgicalContextExtractor(self.service)
        self.project_path = os.path.abspath(".")

        # Sample code for testing
        self.sample_code = """
class TestClass:
    \"\"\"A test class.\"\"\"

    def __init__(self, value):
        \"\"\"Initialize with a value.\"\"\"
        self.value = value

    def get_value(self):
        \"\"\"Return the value.\"\"\"
        return self.value

    def set_value(self, new_value):
        \"\"\"Set a new value.\"\"\"
        self.value = new_value

def test_function():
    \"\"\"A test function.\"\"\"
    test = TestClass(42)
    return test.get_value()
"""

    def test_determine_context_window_size(self):
        """Test the context window size determination."""
        # Test for class
        size = self.extractor._determine_context_window_size(self.sample_code, 1, "class")
        self.assertGreaterEqual(size, 20)

        # Test for function
        size = self.extractor._determine_context_window_size(self.sample_code, 4, "function")
        self.assertGreaterEqual(size, 10)

        # Test for variable
        size = self.extractor._determine_context_window_size(self.sample_code, 6, "variable")
        self.assertGreaterEqual(size, 5)

    def test_find_surrounding_function(self):
        """Test finding the surrounding function."""
        lines = self.sample_code.splitlines()

        # Test inside a method
        func_name = self.extractor._find_surrounding_function(lines, 6)
        self.assertEqual(func_name, "__init__")

        # Test inside a function
        func_name = self.extractor._find_surrounding_function(lines, 17)
        self.assertEqual(func_name, "test_function")

    @patch('surgical_context_extractor.SurgicalContextExtractor._read_file_content')
    def test_extract_code_snippet(self, mock_read_file):
        """Test extracting a code snippet."""
        mock_read_file.return_value = self.sample_code

        snippet = self.extractor._extract_code_snippet(
            self.project_path, "test.py", 5, 3,
            ContextType.DEFINITION, "__init__"
        )

        self.assertIsNotNone(snippet)
        self.assertEqual(snippet.symbol_name, "__init__")
        self.assertEqual(snippet.context_type, ContextType.DEFINITION)
        self.assertEqual(snippet.file_path, "test.py")
        self.assertGreaterEqual(snippet.end_line - snippet.start_line, 3)

    def test_extract_definition_info(self):
        """Test extracting definition info."""
        # Test for a method definition
        lines = self.sample_code.splitlines()

        # Find the line with __init__ definition
        init_line = 0
        for i, line in enumerate(lines):
            if "def __init__" in line:
                init_line = i
                break

        def_type, signature, docstring = self.extractor._extract_definition_info(
            self.sample_code, init_line + 1, "__init__"
        )

        self.assertEqual(def_type, DefinitionType.METHOD)

    def test_find_definition_line_numbers_direct(self):
        """Test finding definition line numbers directly."""
        # Create a simple pattern match test
        code = "class TestClass:\n    pass\n"
        line_nums = []

        # Look for class definition
        class_pattern = r'class\s+TestClass\s*(\(|:)'

        for i, line in enumerate(code.splitlines()):
            if re.search(class_pattern, line):
                line_nums.append(i + 1)  # Convert to 1-based

        self.assertEqual(len(line_nums), 1)
        self.assertEqual(line_nums[0], 1)


if __name__ == "__main__":
    unittest.main()
