#!/usr/bin/env python3
"""
Test script to verify:
1. Old slicing system is completely disabled/removed
2. LLM receives NO repository maps outside of MAP_REQUEST
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_old_slicing_system_disabled():
    """Test that the old slicing system cannot be used anymore."""
    print("🧪 Testing Old Slicing System Disabled")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap instance created successfully")
        
        # Try to force the old slicing system by calling get_repo_map directly
        result = repo_map.get_repo_map(
            chat_files=set(),
            other_files=["aider-main/aider/coders/base_coder.py", "aider-main/aider/repomap.py"],
            mentioned_fnames=set(),
            mentioned_idents=set(),
            force_refresh=True
        )
        
        if result is None:
            print("✅ Old slicing system is completely disabled - get_repo_map() returns None")
            
            # Try the special method for Smart Map Request
            smart_result = repo_map.get_repo_map_for_smart_request(
                chat_files=set(),
                other_files=["aider-main/aider/coders/base_coder.py"],
                mentioned_fnames=set(),
                mentioned_idents=set(),
                force_refresh=True
            )
            
            if smart_result and len(smart_result) > 0:
                print("✅ Smart Map Request method still works for focused maps")
                print(f"   Smart map length: {len(smart_result)} characters")
                return True
            else:
                print("❌ Smart Map Request method doesn't work")
                return False
        else:
            print("❌ Old slicing system is still active!")
            print(f"   Returned content length: {len(result)} characters")
            print(f"   Content preview: {result[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error testing old slicing system: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_receives_no_default_repo_map():
    """Test that LLM receives NO repository map by default."""
    print("\n🧪 Testing LLM Receives No Default Repository Map")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Get all messages that would be sent to the LLM
        chunks = coder.format_chat_chunks()
        all_messages = chunks.all_messages()
        
        print(f"📊 Total messages to LLM: {len(all_messages)}")
        
        # Check each message for repository map content
        repo_map_found = False
        slicing_indicators_found = False
        
        for i, msg in enumerate(all_messages):
            content = msg.get('content', '')
            
            # Check for repository map indicators
            if any(indicator in content for indicator in ['⋮', '...', 'class ', 'def ', 'import ']):
                if len(content) > 1000:  # Large content that looks like a repo map
                    print(f"❌ Message {i+1} contains what looks like a repository map!")
                    print(f"   Role: {msg.get('role', 'unknown')}")
                    print(f"   Content length: {len(content)} characters")
                    print(f"   Content preview: {content[:200]}...")
                    repo_map_found = True
                    
                    if '⋮' in content:
                        print("   ❌ Contains slicing indicator '⋮'")
                        slicing_indicators_found = True
        
        if not repo_map_found:
            print("✅ No repository maps found in LLM messages")
        
        if not slicing_indicators_found:
            print("✅ No slicing indicators found in LLM messages")
        
        # Check specifically for Smart Map Request instructions
        smart_instructions_found = False
        for msg in all_messages:
            content = msg.get('content', '')
            if 'MAP_REQUEST' in content and 'explore the codebase' in content:
                print("✅ Found Smart Map Request instructions")
                smart_instructions_found = True
                break
        
        if not smart_instructions_found:
            print("⚠️  Smart Map Request instructions not found")
        
        return not repo_map_found and not slicing_indicators_found and smart_instructions_found
        
    except Exception as e:
        print(f"❌ Error testing LLM messages: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_binary_search_algorithm_removal():
    """Test that the binary search slicing algorithm is not executed."""
    print("\n🧪 Testing Binary Search Algorithm Removal")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,  # Small token limit to trigger slicing in old system
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap instance created with small token limit")
        
        # Try to trigger the binary search algorithm
        result = repo_map.get_ranked_tags_map(
            chat_fnames=[],
            other_fnames=["aider-main/aider/coders/base_coder.py", "aider-main/aider/repomap.py"],
            max_map_tokens=500,  # Very small limit
            mentioned_fnames=set(),
            mentioned_idents=set(),
            force_refresh=True
        )
        
        if result is None:
            print("✅ get_ranked_tags_map() returns None - binary search not executed")
            return True
        else:
            print("❌ get_ranked_tags_map() returned content - binary search may have executed")
            print(f"   Content length: {len(result)} characters")
            print(f"   Content preview: {result[:200]}...")
            
            # Check for slicing indicators
            if '⋮' in result:
                print("   ❌ Contains slicing indicator '⋮' - binary search executed!")
                return False
            else:
                print("   ✅ No slicing indicators found")
                return True
        
    except Exception as e:
        print(f"❌ Error testing binary search algorithm: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_elimination_verification():
    """Comprehensive test to verify complete elimination of old repository map system."""
    print("\n🧪 Testing Complete Elimination Verification")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a full coder setup
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,  # Large token limit
            repo=repo
        )
        
        # Test all the methods that could potentially generate repository maps
        test_methods = [
            ("get_repo_map", lambda: coder.get_repo_map()),
            ("get_repo_map with force_refresh", lambda: coder.get_repo_map(force_refresh=True)),
            ("get_repo_messages", lambda: coder.get_repo_messages()),
            ("get_chat_files_messages", lambda: coder.get_chat_files_messages()),
        ]
        
        all_clean = True
        
        for method_name, method_call in test_methods:
            print(f"\n  Testing {method_name}:")
            try:
                result = method_call()
                
                if result is None:
                    print(f"    ✅ {method_name} returns None")
                elif isinstance(result, list):
                    # Check if any message contains repository map content
                    contains_repo_map = False
                    for msg in result:
                        if isinstance(msg, dict) and 'content' in msg:
                            content = msg['content']
                            if len(content) > 1000 and ('⋮' in content or 'class ' in content):
                                contains_repo_map = True
                                break
                    
                    if contains_repo_map:
                        print(f"    ❌ {method_name} contains repository map content")
                        all_clean = False
                    else:
                        print(f"    ✅ {method_name} clean of repository maps")
                else:
                    if len(str(result)) > 1000 and ('⋮' in str(result) or 'class ' in str(result)):
                        print(f"    ❌ {method_name} returned repository map content")
                        all_clean = False
                    else:
                        print(f"    ✅ {method_name} returned clean content")
                        
            except Exception as e:
                print(f"    ❌ {method_name} failed: {e}")
                all_clean = False
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error in complete elimination verification: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all complete repository map elimination tests."""
    print("🚀 Complete Repository Map Elimination Test")
    print("=" * 80)
    
    tests = [
        ("Old Slicing System Disabled", test_old_slicing_system_disabled),
        ("LLM Receives No Default Repository Map", test_llm_receives_no_default_repo_map),
        ("Binary Search Algorithm Removal", test_binary_search_algorithm_removal),
        ("Complete Elimination Verification", test_complete_elimination_verification),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 COMPLETE REPOSITORY MAP ELIMINATION TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 COMPLETE SUCCESS! Repository map system is fully eliminated!")
        print("\n📋 Confirmed:")
        print("  1. ✅ Old slicing system is completely disabled")
        print("  2. ✅ LLM receives NO repository maps by default")
        print("  3. ✅ Binary search algorithm is not executed")
        print("  4. ✅ All repository map generation methods return None/clean content")
        print("\n🎯 ANSWER TO YOUR QUESTIONS:")
        print("  ❌ System does NOT have ability to slice maps the old way")
        print("  ❌ LLM does NOT receive any repository map outside MAP_REQUEST")
        print("  ✅ Smart Map Request System is the ONLY way to get repository context")
    else:
        print("⚠️  Some tests failed. Repository map elimination may be incomplete!")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
