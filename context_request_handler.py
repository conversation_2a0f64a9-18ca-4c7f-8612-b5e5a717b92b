#!/usr/bin/env python

import os
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from aider_integration_service import AiderIntegrationService
from surgical_file_extractor import SurgicalFileExtractor
from surgical_context_extractor import SurgicalContextExtractor


@dataclass
class SymbolRequest:
    """Represents a symbol requested by the LLM."""
    type: str  # method_definition, class_definition, function_definition, etc.
    name: str  # The name of the symbol, e.g., "AuthService.login_user"
    file_hint: Optional[str] = None  # Optional hint about which file contains the symbol


@dataclass
class ContextRequest:
    """Represents a context request from the LLM."""
    original_user_query_context: str
    symbols_of_interest: List[SymbolRequest]
    reason_for_request: str


class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

    def __init__(self, project_path: str, aider_service: Optional[AiderIntegrationService] = None):
        """
        Initialize the context request handler.

        Args:
            project_path: Path to the project root
            aider_service: Optional AiderIntegrationService instance
        """
        self.project_path = project_path
        self.aider_service = aider_service or AiderIntegrationService()
        self.file_extractor = SurgicalFileExtractor(self.aider_service)
        self.context_extractor = SurgicalContextExtractor(self.aider_service)
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 3600  # 1 hour

    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.cache[cache_key]
        return None

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError:
                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                request_data = json.loads(json_str)

            # Create the ContextRequest object
            symbols = []
            for symbol_data in request_data.get('symbols_of_interest', []):
                symbols.append(SymbolRequest(
                    type=symbol_data.get('type', 'unknown'),
                    name=symbol_data.get('name', ''),
                    file_hint=symbol_data.get('file_hint')
                ))

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=request_data.get('reason_for_request', '')
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

    def _find_file_for_symbol(self, symbol: SymbolRequest) -> Optional[str]:
        """
        Find the file that contains the requested symbol.

        Args:
            symbol: The symbol to find

        Returns:
            The path to the file containing the symbol, or None if not found
        """
        # If we have a file hint, try that first
        if symbol.file_hint:
            # Check if the file exists
            file_path = os.path.join(self.project_path, symbol.file_hint)
            if os.path.exists(file_path):
                return symbol.file_hint

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Try to find the file defining the class
            class_file = self.aider_service.find_file_defining_symbol(self.project_path, class_name)
            if class_file:
                return class_file

        # Try to find the file defining the symbol directly
        symbol_name = symbol_parts[-1]  # Use the last part of the symbol name
        symbol_file = self.aider_service.find_file_defining_symbol(self.project_path, symbol_name)
        if symbol_file:
            return symbol_file

        return None

    def _extract_symbol_content(self, symbol: SymbolRequest) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Extract the content of a symbol using the surgical extraction system.

        Args:
            symbol: The symbol to extract

        Returns:
            A tuple of (file_path, symbol_name, content) or (None, None, None) if extraction failed
        """
        # Find the file containing the symbol
        file_path = self._find_file_for_symbol(symbol)
        if not file_path:
            return None, None, None

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Try to extract the method content
            content = self.file_extractor.extract_symbol_content(method_name, file_path, self.project_path)
            if content:
                return file_path, method_name, content

            # If that fails, try to extract the class content
            content = self.file_extractor.extract_symbol_content(class_name, file_path, self.project_path)
            if content:
                return file_path, class_name, content
        else:
            # This is a simple symbol name
            symbol_name = symbol_parts[0]
            content = self.file_extractor.extract_symbol_content(symbol_name, file_path, self.project_path)
            if content:
                return file_path, symbol_name, content

        return None, None, None

    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                continue

            # Extract essential imports
            essential_imports = None
            if hasattr(self.file_extractor, '_extract_essential_imports'):
                essential_imports = self.file_extractor._extract_essential_imports(self.project_path, file_path)

            # Extract containing class signature if it's a method
            containing_class = None
            if '.' in symbol.name and hasattr(self.file_extractor, '_extract_containing_class'):
                # Create a dummy SymbolInfo object
                from surgical_file_extractor import SymbolInfo
                symbol_info = SymbolInfo(
                    name=symbol_name,
                    start_line=0,  # This will be updated by the extractor
                    file_path=file_path,
                    symbol_type="method"
                )
                containing_class = self.file_extractor._extract_containing_class(self.project_path, file_path, symbol_info)

            # Extract usage contexts
            usage_contexts = self.context_extractor.extract_usage_contexts(self.project_path, symbol_name, file_path)

            # Add the extracted symbol to the result
            result["extracted_symbols"].append({
                "symbol_name": symbol.name,
                "file_path": file_path,
                "content": content,
                "essential_imports": essential_imports,
                "containing_class": containing_class
            })

            # Add dependency snippets
            for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                result["dependency_snippets"].append({
                    "file_path": usage.snippet.file_path,
                    "symbol_name": usage.snippet.symbol_name,
                    "content": usage.snippet.content,
                    "usage_type": usage.usage_type.value
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result
