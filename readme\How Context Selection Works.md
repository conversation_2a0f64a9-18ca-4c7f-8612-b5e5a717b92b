# How Context Selection Works

This document explains how the intelligent context selection system works to find relevant symbols for user queries, using the example query: **"Why is my context selection taking so long?"**

## 🔍 **Overview: From 13,025 Entities to 8 Perfect Matches**

The system uses a sophisticated multi-factor scoring algorithm to select the most relevant code entities from a massive codebase, transforming a vague performance question into precise, actionable context.

## **Step 1: Query Analysis & Task Type Detection**

```python
Query: "Why is my context selection taking so long?"

# Automatic task type detection
Task Type: DEBUGGING (detected from "why" + performance complaint)

# Keyword extraction
Keywords: ["context", "selection", "taking", "long", "slow", "performance"]
```

The system automatically:
- **Detects task type** based on question patterns
- **Extracts key concepts** from natural language
- **Maps semantic concepts** ("taking so long" → "performance")

## **Step 2: Multi-Factor Scoring Algorithm**

For **DEBUGGING** tasks, the system uses these weights:

```python
DEBUGGING_WEIGHTS = {
    'criticality': 0.4,        # 40% - High-impact entities
    'error_handling': 0.3,     # 30% - Error-prone code  
    'side_effects': 0.2,       # 20% - I/O operations (slow!)
    'dependency_depth': 0.1    # 10% - Complex dependencies
}
```

Different task types use different weights:
- **Feature Development**: Emphasizes dependencies and change risk
- **Refactoring**: Prioritizes change risk and criticality
- **Code Review**: Balances criticality, risk, and side effects

## **Step 3: Entity Scoring Process**

Each of the **13,025 entities** gets scored using multiple factors:

### **Example: `parse_context_request` function**

```python
# Base scoring factors
criticality_score = 1.0  # "high" criticality = 1.0
side_effects_score = 0.67  # ["network_io", "writes_log"] = 2/3 = 0.67
error_handling_score = 0.6  # Multiple error types
dependency_depth_score = 0.8  # Called by 6 entities

# Text relevance matching
task_words = {"context", "selection", "taking", "long"}
entity_words = {"parse", "context", "request"}
text_overlap = 1/4 = 0.25  # "context" matches

# Final score calculation
score = (0.4 * 1.0) +      # criticality: 0.4
        (0.3 * 0.6) +      # error_handling: 0.18  
        (0.2 * 0.67) +     # side_effects: 0.134
        (0.1 * 0.8) +      # dependency_depth: 0.08
        (0.3 * 0.25)       # text_relevance: 0.075

Total Score = 1.669  # High relevance!
```

### **Example: `process_context_requests` method**

```python
# This scores even higher because:
criticality_score = 1.0     # "high" criticality
side_effects_score = 1.0    # ["writes_log", "network_io", "database_io"] = 3/3
error_handling_score = 0.8  # Many error types
text_overlap = 2/4 = 0.5    # "context" + "process" (implied performance)

Total Score = 1.89  # Even higher relevance!
```

## **Step 4: Priority Classification**

Based on scores, entities get classified:

```python
if score >= 1.5:
    priority = CRITICAL     # Must include
elif score >= 1.0:
    priority = HIGH         # Very important  
elif score >= 0.5:
    priority = MEDIUM       # Include if space
else:
    priority = OPTIONAL     # Skip unless needed
```

## **Step 5: Token Budget Selection**

The system selects entities in priority order within the token budget:

```python
Token Budget: 2000 tokens

# Step 1: Add all CRITICAL entities first
parse_context_request (score: 1.669) ✅ 
process_context_requests (score: 1.89) ✅
process_context_request (score: 1.75) ✅

# Step 2: Add HIGH priority entities  
detect_context_request (score: 1.2) ✅
select_optimal_context (score: 1.1) ✅

# Step 3: Add dependencies of selected entities
_get_from_cache (called by process_context_request) ✅
tool_warning (called by process_context_requests) ✅

# Continue until token budget is exhausted...
```

## **Step 6: Dependency Enhancement**

The system adds related entities to provide complete context:

```python
# For each selected entity, add:
# 1. Functions it calls (dependencies)
# 2. Functions that call it (reverse dependencies)
# 3. Only if they're medium/high criticality

process_context_requests calls:
- tool_warning ✅ (already selected)
- getcwd ✅ (file system operation - potential bottleneck)
- basename ✅ (string processing)
- exists ✅ (file system check)
```

## **🎯 Why These Specific Entities Were Selected**

For **"Why is my context selection taking so long?"**, the top entities were chosen because:

### **1. `parse_context_request`** 
- ✅ **Name match**: Contains "context"
- ✅ **High criticality**: Core parsing function
- ✅ **Side effects**: `network_io`, `writes_log` (potential slowness)
- ✅ **Complexity**: 13 function calls (regex, string operations)

### **2. `process_context_requests`**
- ✅ **Name match**: Contains "context" + "process" (performance-related)
- ✅ **High criticality + High risk**: Core processing logic
- ✅ **Multiple side effects**: `database_io`, `network_io`, `writes_log`
- ✅ **High usage**: Called by 9 different components

### **3. `process_context_request`**
- ✅ **Name match**: Contains "context" + "process"
- ✅ **High criticality**: Core request processing
- ✅ **Cache operations**: `_get_from_cache` (potential performance issue)
- ✅ **High usage**: Used by 10 different components

## **🚀 Why This Selection is Intelligent**

The algorithm correctly identified that for a **performance debugging query**, the most relevant entities are:

1. **Functions with "context" in the name** (direct relevance)
2. **Functions with side effects** (`network_io`, `database_io` - common bottlenecks)
3. **High-criticality functions** (changes here have big impact)
4. **Functions with many dependencies** (complexity = potential slowness)

## **📊 Scoring Factors Breakdown**

| Factor | Weight | Purpose | Example |
|--------|--------|---------|---------|
| **Criticality** | 40% | High-impact entities | `process_context_requests` = high |
| **Error Handling** | 30% | Error-prone code | Functions with many error types |
| **Side Effects** | 20% | I/O operations | `database_io`, `network_io` |
| **Dependency Depth** | 10% | Complex dependencies | Functions called by many others |
| **Text Relevance** | 30% | Keyword matching | "context" in function name |

## **🎯 Result: Perfect Context Selection**

**The result**: Instead of showing random code, the LLM gets exactly the functions most likely to contain performance bottlenecks!

This enables the LLM to provide specific, actionable advice like:
- "Profile the `database_io` side effect in `process_context_requests`"
- "Cache the regex patterns in `parse_context_request`" 
- "The 15 function calls in `process_context_requests` suggest complexity bottlenecks"

The selection algorithm found the actual performance-critical code paths, transforming a vague question into precise, targeted analysis.

## **🔧 Technical Implementation**

The intelligent context selection is implemented in:
- **`intelligent_context_selector.py`** - Core selection algorithm
- **`ir_context.py`** - IR data structures with criticality and side effects
- **`context_request_handler.py`** - Integration with LLM-friendly package generation

This system represents a significant advancement in AI-powered code analysis, providing LLMs with exactly the right context to deliver intelligent, actionable insights.
