#!/usr/bin/env python3
"""
Debug script to test project path detection for IR Context System.
This will help identify why the wrong project path is being used.
"""

import os
import sys

# Add aider path
aider_path = r'C:\Users\<USER>\Documents\aider_project\aider__500\aider-main'
sys.path.insert(0, aider_path)

def test_project_path_detection():
    """Test the project path detection logic."""
    print("🔍 Testing Project Path Detection for IR Context System")
    print("=" * 70)
    
    # Test current working directory
    current_dir = os.getcwd()
    print(f"Current working directory: {current_dir}")
    
    # Test what happens when we simulate aider running from aider-main
    # but targeting live_backtest_dashboard
    
    # Simulate the scenario
    print(f"\n📁 Simulating aider command scenario:")
    print(f"   Command: Set-Location aider-main; python -m aider.main --browser \"C:\\Users\\<USER>\\Documents\\____live_backtest_dashboard_____\"")
    print(f"   Expected: IR should be generated for live_backtest_dashboard")
    print(f"   Current: IR is probably generated for aider-main directory")
    
    # Check if the target directory exists
    target_dir = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
    if os.path.exists(target_dir):
        print(f"✅ Target directory exists: {target_dir}")
        
        # Check if it has Python files
        python_files = []
        for root, dirs, files in os.walk(target_dir):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
                if len(python_files) >= 5:  # Just check first 5
                    break
            if len(python_files) >= 5:
                break
        
        print(f"✅ Found {len(python_files)} Python files in target directory")
        for i, py_file in enumerate(python_files[:3]):
            rel_path = os.path.relpath(py_file, target_dir)
            print(f"   {i+1}. {rel_path}")
        
        # Check for compute_next_boundary function
        compute_boundary_files = []
        for root, dirs, files in os.walk(target_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            if 'compute_next_boundary' in content:
                                compute_boundary_files.append(file_path)
                    except:
                        pass
        
        print(f"✅ Found compute_next_boundary in {len(compute_boundary_files)} files:")
        for file_path in compute_boundary_files:
            rel_path = os.path.relpath(file_path, target_dir)
            print(f"   - {rel_path}")
    else:
        print(f"❌ Target directory does not exist: {target_dir}")
    
    # Test the actual path detection logic from base_coder.py
    print(f"\n🧪 Testing _get_project_path_for_context() logic:")
    
    # Simulate having files from the target directory in abs_fnames
    test_files = [
        os.path.join(target_dir, "market_data", "market_data_repository.py"),
        os.path.join(target_dir, "services", "candle_data_service.py")
    ]
    
    # Test utils.find_common_root
    try:
        from aider import utils
        if all(os.path.exists(f) for f in test_files):
            common_root = utils.find_common_root(test_files)
            print(f"✅ Common root of target files: {common_root}")
            
            if common_root == target_dir:
                print(f"✅ PASS: Common root correctly identifies target directory")
            else:
                print(f"❌ FAIL: Common root should be {target_dir}")
        else:
            print(f"⚠️  Some test files don't exist, cannot test common root")
    except Exception as e:
        print(f"❌ Error testing common root: {e}")
    
    print(f"\n💡 Recommended Fix:")
    print(f"   The issue is likely that when aider runs from aider-main directory,")
    print(f"   it doesn't have files from the target directory in abs_fnames,")
    print(f"   so it falls back to using the current working directory (aider-main)")
    print(f"   instead of the target directory specified in --browser argument.")
    
    print(f"\n🔧 Solution:")
    print(f"   1. Modify _get_project_path_for_context() to check for --browser argument")
    print(f"   2. Or ensure that files from target directory are added to abs_fnames")
    print(f"   3. Or add explicit project path detection for browser mode")

if __name__ == "__main__":
    test_project_path_detection()
