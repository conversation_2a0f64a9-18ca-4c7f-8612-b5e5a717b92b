#!/usr/bin/env python3
"""
Generate IR data for live_backtest_dashboard project and analyze compute_next_boundary function.
"""

import sys
import os
import json

# Add aider path
aider_path = r'C:\Users\<USER>\Documents\aider_project\aider__500'
sys.path.insert(0, aider_path)

def main():
    print('🔍 Starting IR generation for live_backtest_dashboard project...')
    
    try:
        from aider_integration_service import AiderIntegrationService
        service = AiderIntegrationService()
        
        # Generate IR for current directory (live_backtest_dashboard)
        print('📊 Generating Mid-Level IR...')
        ir_data = service.generate_mid_level_ir('.')
        
        print('💾 Saving IR data to file...')
        output_file = 'live_backtest_dashboard_ir.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)
        
        print(f'✅ IR data saved to: {output_file}')
        
        # Quick analysis
        modules = ir_data.get('modules', [])
        total_functions = 0
        found_compute_boundary = []
        
        print('🔍 Searching for compute_next_boundary function...')
        for module in modules:
            functions = module.get('functions', [])
            total_functions += len(functions)
            
            for func in functions:
                func_name = func.get('name', '')
                if 'compute_next_boundary' in func_name.lower():
                    found_compute_boundary.append({
                        'name': func_name,
                        'module': module.get('name'),
                        'file': module.get('file'),
                        'line_start': func.get('line_start'),
                        'line_end': func.get('line_end')
                    })
        
        print(f'📊 IR Analysis Summary:')
        print(f'   Total modules: {len(modules)}')
        print(f'   Total functions: {total_functions}')
        print(f'   compute_next_boundary functions found: {len(found_compute_boundary)}')
        
        if found_compute_boundary:
            print(f'✅ Found compute_next_boundary functions:')
            for func in found_compute_boundary:
                print(f'   - {func["name"]} in {func["file"]}:{func["line_start"]}')
        else:
            print(f'❌ compute_next_boundary function NOT found in IR data!')
            print(f'   This explains why the relevance matching fails.')
            
            # Show some sample functions to verify IR is working
            print(f'\n📋 Sample functions found in IR:')
            sample_count = 0
            for module in modules[:3]:  # First 3 modules
                functions = module.get('functions', [])
                for func in functions[:2]:  # First 2 functions per module
                    print(f'   - {func.get("name")} in {module.get("file")}')
                    sample_count += 1
                    if sample_count >= 6:
                        break
                if sample_count >= 6:
                    break
        
        print('🏁 IR generation and analysis complete!')
        return True
        
    except Exception as e:
        print(f'❌ Error during IR generation: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
