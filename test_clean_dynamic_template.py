#!/usr/bin/env python3

"""
Test to verify that the new clean template is completely dynamic with NO HARDCODED content.
This test verifies that all instructions and content are generated dynamically based on the actual LLM request.
"""

import os
import sys

def test_clean_dynamic_template():
    """Test that the clean template is completely dynamic with no hardcoded content."""
    
    print("🧪 Testing Clean Dynamic Template (NO HARDCODED)")
    print("=" * 60)
    
    try:
        # Import the required modules
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        from aider.context_request.aider_template_renderer import AiderTemplateRenderer
        
        print("✅ Successfully imported template renderer")
        
        # Create a template renderer
        renderer = AiderTemplateRenderer()
        
        # Test 1: Different function name - should be completely dynamic
        print("\n🔍 Test 1: Different function name (calculate_profit)")
        
        original_query1 = "how does the calculate_profit function work?"
        extracted_context1 = {
            "extracted_symbols": [
                {
                    "file_path": "services/profit_calculator.py",
                    "symbol_name": "calculate_profit",
                    "content": "def calculate_profit(self, entry_price, exit_price, volume):\n    return (exit_price - entry_price) * volume",
                    "essential_imports": "from decimal import Decimal\nimport math",
                    "containing_class": "ProfitCalculator"
                }
            ],
            "not_found_symbols": [],
            "dependency_snippets": []
        }
        
        result1 = renderer.render_augmented_prompt(
            original_query=original_query1,
            repo_overview="",
            extracted_context=extracted_context1
        )
        
        # Check for dynamic content
        dynamic_checks1 = [
            "calculate_profit" in result1,
            "services/profit_calculator.py" in result1,
            "ProfitCalculator" in result1,
            "def calculate_profit" in result1,
            "from decimal import Decimal" in result1
        ]
        
        print(f"   Dynamic content checks: {sum(dynamic_checks1)}/5")
        
        # Test 2: Different function name and class - should be completely dynamic
        print("\n🔍 Test 2: Different function name (process_order)")
        
        original_query2 = "explain the process_order method"
        extracted_context2 = {
            "extracted_symbols": [
                {
                    "file_path": "order_management/order_processor.py",
                    "symbol_name": "process_order",
                    "content": "async def process_order(self, order_data):\n    validated_order = self.validator.validate(order_data)\n    return await self.executor.execute(validated_order)",
                    "essential_imports": "import asyncio\nfrom typing import Dict",
                    "containing_class": "OrderProcessor"
                }
            ],
            "not_found_symbols": [],
            "dependency_snippets": []
        }
        
        result2 = renderer.render_augmented_prompt(
            original_query=original_query2,
            repo_overview="",
            extracted_context=extracted_context2
        )
        
        # Check for dynamic content
        dynamic_checks2 = [
            "process_order" in result2,
            "order_management/order_processor.py" in result2,
            "OrderProcessor" in result2,
            "async def process_order" in result2,
            "import asyncio" in result2
        ]
        
        print(f"   Dynamic content checks: {sum(dynamic_checks2)}/5")
        
        # Test 3: Function without class - should be completely dynamic
        print("\n🔍 Test 3: Function without class (utility function)")
        
        original_query3 = "what does the format_currency function do?"
        extracted_context3 = {
            "extracted_symbols": [
                {
                    "file_path": "utils/formatters.py",
                    "symbol_name": "format_currency",
                    "content": "def format_currency(amount, currency='USD'):\n    return f'{currency} {amount:.2f}'",
                    "essential_imports": "",
                    "containing_class": ""
                }
            ],
            "not_found_symbols": [],
            "dependency_snippets": []
        }
        
        result3 = renderer.render_augmented_prompt(
            original_query=original_query3,
            repo_overview="",
            extracted_context=extracted_context3
        )
        
        # Check for dynamic content
        dynamic_checks3 = [
            "format_currency" in result3,
            "utils/formatters.py" in result3,
            "def format_currency" in result3,
            "Class:" not in result3,  # Should not show class section
            "currency='USD'" in result3
        ]
        
        print(f"   Dynamic content checks: {sum(dynamic_checks3)}/5")
        
        # Test 4: Check for NO HARDCODED content
        print("\n🔍 Test 4: Checking for NO HARDCODED content")
        
        hardcoded_content = [
            "close_position_based_on_conditions",  # Should NOT appear in other function tests
            "trade_management/position_exit_manager.py",  # Should NOT appear in other tests
            "PositionCloser"  # Should NOT appear in other tests
        ]
        
        hardcoded_found = []
        for content in hardcoded_content:
            if content in result1 or content in result2 or content in result3:
                hardcoded_found.append(content)
        
        print(f"   Hardcoded content found: {len(hardcoded_found)}/3 (should be 0)")
        
        # Test 5: Check instructions are dynamic
        print("\n🔍 Test 5: Checking dynamic instructions")
        
        instruction_checks = [
            "Answer ONLY the current query about `calculate_profit`" in result1,
            "Answer ONLY the current query about `process_order`" in result2,
            "Answer ONLY the current query about `format_currency`" in result3
        ]
        
        print(f"   Dynamic instruction checks: {sum(instruction_checks)}/3")
        
        # Test 6: Check file paths are dynamic
        print("\n🔍 Test 6: Checking dynamic file paths")
        
        file_path_checks = [
            "**File:** `services/profit_calculator.py`" in result1,
            "**File:** `order_management/order_processor.py`" in result2,
            "**File:** `utils/formatters.py`" in result3
        ]
        
        print(f"   Dynamic file path checks: {sum(file_path_checks)}/3")
        
        # Overall assessment
        total_checks = (
            sum(dynamic_checks1) + sum(dynamic_checks2) + sum(dynamic_checks3) +
            sum(instruction_checks) + sum(file_path_checks)
        )
        max_checks = 5 + 5 + 5 + 3 + 3  # 21 total checks
        
        no_hardcoded = len(hardcoded_found) == 0
        
        print(f"\n📊 Overall Results:")
        print(f"   Dynamic content: {total_checks}/{max_checks} ({total_checks/max_checks*100:.1f}%)")
        print(f"   No hardcoded content: {'✅' if no_hardcoded else '❌'}")
        
        success = total_checks >= 18 and no_hardcoded  # 85% threshold + no hardcoded
        
        if success:
            print("\n🎉 SUCCESS: Template is completely dynamic!")
            print("   ✅ All function names are dynamic")
            print("   ✅ All file paths are dynamic") 
            print("   ✅ All class names are dynamic")
            print("   ✅ All instructions are dynamic")
            print("   ✅ No hardcoded content found")
        else:
            print("\n❌ FAILED: Template still has hardcoded content or insufficient dynamic behavior")
            if hardcoded_found:
                print(f"   Hardcoded content found: {hardcoded_found}")
            if total_checks < 18:
                print(f"   Dynamic checks failed: {total_checks}/{max_checks}")
        
        return success
        
    except ImportError as e:
        print(f"❌ FAILED: Could not import template renderer: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED: Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_clean_dynamic_template()
    
    if success:
        print("\n🎉 CLEAN DYNAMIC TEMPLATE: PASSED")
        print("The template is completely dynamic with NO HARDCODED content:")
        print("  ✅ Function names are extracted from actual requests")
        print("  ✅ File paths are extracted from actual symbols")
        print("  ✅ Class names are extracted from actual context")
        print("  ✅ Instructions are generated dynamically")
        print("  ✅ All content adapts to the specific LLM request")
        print("\nBenefits:")
        print("  - Works for ANY function, not just trading functions")
        print("  - Instructions match the actual query")
        print("  - No confusion from hardcoded examples")
        print("  - Clean, professional presentation")
    else:
        print("\n❌ CLEAN DYNAMIC TEMPLATE: FAILED")
        print("The template still has hardcoded content or insufficient dynamic behavior.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
