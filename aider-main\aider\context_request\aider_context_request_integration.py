#!/usr/bin/env python

import os
import re
from typing import Dict, List, Optional, Any

from .context_request_handler import Con<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContextRequest, SymbolRequest
from .aider_template_renderer import Aider<PERSON><PERSON>plate<PERSON><PERSON><PERSON>
from .aider_integration_service import AiderIntegrationService


class AiderContextRequestIntegration:
    """
    Integrates the context request functionality with <PERSON><PERSON>.
    """

    def __init__(self, project_path: str, aider_service: Optional[AiderIntegrationService] = None, coder=None):
        """
        Initialize the context request integration.

        Args:
            project_path: Path to the project root
            aider_service: Optional AiderIntegrationService instance
            coder: Optional Aider coder instance that has access to the repository map
        """
        self.project_path = project_path
        self.aider_service = aider_service or AiderIntegrationService(coder)
        self.context_request_handler = ContextRequestHandler(project_path, self.aider_service)
        self.template_renderer = AiderTemplateRenderer()
        self.conversation_history = []
        self.current_iteration = 0
        self.max_iterations = 3

    def get_llm_instructions(self) -> str:
        """
        Get the instructions for the LLM on how to use the context request protocol.

        Returns:
            Instructions for the LLM
        """
        return """
You can request specific code context using the CONTEXT_REQUEST protocol:

{CONTEXT_REQUEST: {
  "original_user_query_context": "Brief description of what the user is asking about",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME_FROM_MAP", "file_hint": "path/to/file.py"},
    {"type": "class_definition", "name": "ACTUAL_CLASS_NAME_FROM_MAP", "file_hint": "path/to/file.py"}
  ],
  "reason_for_request": "Why you need this context to answer the user's question"
}}

🚨 **CRITICAL GUIDELINES** for using CONTEXT_REQUEST:
1. Use ONLY the EXACT symbol names shown in your repository map - DO NOT make assumptions about class/function names
2. If you don't see the exact symbol name in the map, you CANNOT request it
3. Only request symbols that are directly relevant to answering the user's question
4. Be specific about the symbols you need (use fully qualified names when possible)
5. Provide file hints when you know them to help locate the symbols
6. Explain why you need this context in the reason_for_request field
7. You can make multiple CONTEXT_REQUEST calls if needed, but try to get all related symbols in one request

The system will respond with surgically extracted implementations of the requested symbols, along with their key dependencies.
"""

    def detect_context_request(self, content: str) -> Optional[ContextRequest]:
        """
        Detect if there's a context request in the content.

        Args:
            content: The content to check

        Returns:
            A ContextRequest object if a context request is detected, None otherwise
        """
        return self.context_request_handler.parse_context_request(content)

    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"

    def process_context_request(self, context_request: ContextRequest, original_user_query: str, repo_overview: str = "") -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview (default: "")

        Returns:
            An augmented prompt with the extracted context
        """
        # DISABLED: Check if we've reached the maximum number of iterations
        # if self.current_iteration >= self.max_iterations:
        #     return f"""
        # I've reached the maximum number of context requests for this query.
        # Please try to answer the question with the information you already have,
        # or ask a more specific question.
        # """

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_request_handler.process_context_request(context_request)

        # If repo_overview is None or empty, use a default message
        if not repo_overview:
            repo_overview = "No repository overview available."

        # Generate the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        return augmented_prompt

    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        # If this is a user message and the last message was also from the user,
        # replace it instead of appending to avoid stacking user queries
        if role == "user" and self.conversation_history and self.conversation_history[-1].get("role") == "user":
            # Replace the last user message instead of adding a new one
            self.conversation_history[-1] = {
                "role": role,
                "content": content
            }
        else:
            # Normal append for assistant messages or if the last message wasn't from the user
            self.conversation_history.append({
                "role": role,
                "content": content
            })

        # Limit the conversation history to the last 6 messages (3 exchanges)
        if len(self.conversation_history) > 6:
            self.conversation_history = self.conversation_history[-6:]

    def reset_iteration_counter(self) -> None:
        """Reset the iteration counter."""
        self.current_iteration = 0
