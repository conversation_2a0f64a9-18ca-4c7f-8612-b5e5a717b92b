#!/usr/bin/env python3
"""
Test script to generate and display Mid-Level IR output.
This script creates a focused test to show the Mid-Level IR generation working.
"""

import os
import json
import time
from aider_integration_service import AiderIntegrationService

def test_mid_level_ir():
    """Test Mid-Level IR generation on a small subset of files."""
    
    print("🔍 Testing Mid-Level IR Generation...")
    
    # Initialize the service
    service = AiderIntegrationService()
    
    # Use current directory as project path
    project_path = os.getcwd()
    
    # Create a simple test by analyzing just this file
    print(f"   Project path: {project_path}")
    
    try:
        # Generate IR for the project (this will be limited to avoid timeout)
        print("   Generating Mid-Level IR...")
        
        # Get the IR generator
        ir_generator = service._get_ir_generator()
        
        # Test with just a few files to show the structure
        test_files = []
        
        # Find a few Python files to test with
        for root, _, files in os.walk(project_path):
            if any(skip in root for skip in ["__pycache__", ".git", ".pytest_cache"]):
                continue
            for file in files:
                if file.endswith('.py') and not file.startswith('.'):
                    test_files.append(os.path.join(root, file))
                    if len(test_files) >= 3:  # Limit to 3 files for quick test
                        break
            if len(test_files) >= 3:
                break
        
        print(f"   Testing with {len(test_files)} files:")
        for f in test_files:
            print(f"     - {os.path.relpath(f, project_path)}")
        
        # Analyze each test file
        modules = []
        for file_path in test_files:
            try:
                print(f"   Analyzing: {os.path.relpath(file_path, project_path)}")
                module_info = ir_generator._analyze_module(project_path, file_path, "gpt-3.5-turbo")
                if module_info:
                    modules.append(module_info)
                    print(f"     ✅ Generated module: {module_info['name']}")
                    print(f"        LOC: {module_info['loc']}")
                    print(f"        Entities: {len(module_info['entities'])}")
            except Exception as e:
                print(f"     ❌ Error: {e}")
                continue
        
        # Create the IR structure
        ir_data = {
            "modules": modules,
            "metadata": {
                "generated_at": time.time(),
                "project_path": project_path,
                "total_modules": len(modules),
                "generator_version": "1.0.0",
                "test_mode": True
            }
        }
        
        print(f"\n✅ Generated IR for {len(modules)} modules")
        
        # Save to file
        output_file = "mid_level_ir_sample.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Sample IR saved to: {output_file}")
        
        # Display the structure
        print("\n📋 Mid-Level IR Structure:")
        print("=" * 50)
        
        for i, module in enumerate(modules, 1):
            print(f"\nModule {i}: {module['name']}")
            print(f"  File: {module['file']}")
            print(f"  Lines of Code: {module['loc']}")
            print(f"  Dependencies: {len(module['dependencies'])}")
            print(f"  Entities: {len(module['entities'])}")
            
            # Show entities
            if module['entities']:
                print("  Entities:")
                for j, entity in enumerate(module['entities'][:2], 1):  # Show first 2
                    print(f"    {j}. {entity['type']}: {entity['name']}")
                    print(f"       Doc: {entity['doc'][:60]}...")
                    print(f"       Params: {entity['params']}")
                    print(f"       Returns: {entity['returns']}")
                    print(f"       Calls: {entity['calls'][:3]}")  # First 3 calls
                    print(f"       Side effects: {entity['side_effects']}")
                    print(f"       Errors: {entity['errors'][:2]}")  # First 2 errors
                    print(f"       Criticality: {entity['criticality']}")
                    print(f"       Change risk: {entity['change_risk']}")
                
                if len(module['entities']) > 2:
                    print(f"    ... and {len(module['entities']) - 2} more entities")
        
        # Show JSON sample
        print(f"\n📄 JSON Sample (first module):")
        print("=" * 50)
        if modules:
            sample_module = modules[0].copy()
            # Limit entities for display
            if len(sample_module['entities']) > 1:
                sample_module['entities'] = sample_module['entities'][:1]
            
            print(json.dumps({"modules": [sample_module]}, indent=2)[:1000] + "...")
        
        print(f"\n🎉 Mid-Level IR generation test completed successfully!")
        print(f"   Full output available in: {output_file}")
        
        return ir_data
        
    except Exception as e:
        print(f"❌ Error during IR generation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_mid_level_ir()
