#!/usr/bin/env python3

"""
Test to verify that the contradictory "missing symbols" note is NOT included
when symbols are successfully extracted.
"""

import sys

def test_missing_symbols_contradiction_fix():
    """Test that missing symbols note is not shown when symbols are successfully extracted."""
    
    print("🧪 Testing Missing Symbols Contradiction Fix")
    print("=" * 55)
    
    try:
        # Import the required modules
        sys.path.insert(0, 'aider-main')
        from aider.context_request.aider_template_renderer import AiderTemplateRenderer
        
        print("✅ Successfully imported template renderer")
        
        # Create a template renderer
        renderer = AiderTemplateRenderer()
        
        # Test 1: Success case - symbols found, should NOT show missing symbols note
        print("\n🔍 Test 1: Success case (symbols found)")
        
        original_query = "how does the close_position_based_on_conditions function work?"
        
        # Mock extracted context with SUCCESSFUL symbol extraction
        extracted_context_success = {
            "extracted_symbols": [
                {
                    "file_path": "trade_management/position_exit_manager.py",
                    "symbol_name": "close_position_based_on_conditions",
                    "content": "async def close_position_based_on_conditions(self, app):\n    # Implementation here\n    return True",
                    "essential_imports": "import MetaTrader5 as mt5",
                    "containing_class": "PositionCloser"
                }
            ],
            "dependency_snippets": [],
            "not_found_symbols": [
                {
                    "symbol_name": "close_position_based_on_conditions",
                    "reason": "Could not find or extract the symbol content."
                }
            ]  # This should be IGNORED when we have extracted_symbols
        }
        
        result_success = renderer.render_augmented_prompt(
            original_query=original_query,
            repo_overview="",
            extracted_context=extracted_context_success
        )
        
        # Check for contradictory content
        has_implementation = "async def close_position_based_on_conditions" in result_success
        has_missing_note = "Some requested symbols were not found" in result_success
        has_contradiction = has_implementation and has_missing_note
        
        print(f"   Has implementation: {'✅' if has_implementation else '❌'}")
        print(f"   Has missing symbols note: {'❌' if not has_missing_note else '✅ (BAD!)'}")
        print(f"   Has contradiction: {'❌' if not has_contradiction else '✅ (BAD!)'}")
        
        success_case_passed = has_implementation and not has_missing_note and not has_contradiction
        print(f"   Success case: {'✅' if success_case_passed else '❌'}")
        
        # Test 2: Failure case - no symbols found, should show missing symbols note
        print("\n🔍 Test 2: Failure case (no symbols found)")
        
        extracted_context_failure = {
            "extracted_symbols": [],
            "dependency_snippets": [],
            "not_found_symbols": [
                {
                    "symbol_name": "nonexistent_function",
                    "reason": "Could not find or extract the symbol content."
                }
            ]
        }
        
        result_failure = renderer.render_augmented_prompt(
            original_query="how does the nonexistent_function work?",
            repo_overview="",
            extracted_context=extracted_context_failure
        )
        
        # Check for appropriate failure handling
        has_no_implementation = "async def" not in result_failure
        has_missing_note_failure = "Some symbols could not be found" in result_failure
        has_suggestions = "Suggestions:" in result_failure
        
        print(f"   Has no implementation: {'✅' if has_no_implementation else '❌'}")
        print(f"   Has missing symbols note: {'✅' if has_missing_note_failure else '❌'}")
        print(f"   Has suggestions: {'✅' if has_suggestions else '❌'}")
        
        failure_case_passed = has_no_implementation and has_missing_note_failure and has_suggestions
        print(f"   Failure case: {'✅' if failure_case_passed else '❌'}")
        
        # Test 3: Check specific content in success case
        print("\n🔍 Test 3: Content verification for success case")
        
        expected_content = [
            "## Current Query: how does the close_position_based_on_conditions function work?",
            "## Function Implementation",
            "**File:** `trade_management/position_exit_manager.py`",
            "**Class:** `PositionCloser`",
            "### Method Implementation",
            "async def close_position_based_on_conditions"
        ]
        
        unexpected_content = [
            "⚠️ Some requested symbols were not found:",
            "Could not find or extract the symbol content",
            "Note on Missing Symbols"
        ]
        
        expected_found = []
        for content in expected_content:
            if content in result_success:
                expected_found.append(content)
        
        unexpected_found = []
        for content in unexpected_content:
            if content in result_success:
                unexpected_found.append(content)
        
        print(f"   Expected content found: {len(expected_found)}/{len(expected_content)}")
        print(f"   Unexpected content found: {len(unexpected_found)}/{len(unexpected_content)} (should be 0)")
        
        content_verification_passed = len(expected_found) == len(expected_content) and len(unexpected_found) == 0
        print(f"   Content verification: {'✅' if content_verification_passed else '❌'}")
        
        if unexpected_found:
            print(f"   ❌ Found unexpected content: {unexpected_found}")
        
        # Overall assessment
        all_tests_passed = success_case_passed and failure_case_passed and content_verification_passed
        
        print(f"\n📊 Overall Results:")
        print(f"   Success case (no contradiction): {'✅' if success_case_passed else '❌'}")
        print(f"   Failure case (proper error handling): {'✅' if failure_case_passed else '❌'}")
        print(f"   Content verification: {'✅' if content_verification_passed else '❌'}")
        
        return all_tests_passed
        
    except ImportError as e:
        print(f"❌ FAILED: Could not import template renderer: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILED: Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_missing_symbols_contradiction_fix()
    
    if success:
        print("\n🎉 MISSING SYMBOLS CONTRADICTION FIX: PASSED")
        print("The fix correctly handles symbol extraction results:")
        print("  ✅ When symbols are found: Shows implementation, NO missing symbols note")
        print("  ✅ When symbols are not found: Shows missing symbols note with suggestions")
        print("  ✅ No contradictory messages confusing the LLM")
        print("  ✅ Clean, logical response structure")
        print("\nBenefits:")
        print("  - LLM receives consistent, non-contradictory information")
        print("  - Success cases are clearly successful")
        print("  - Failure cases provide helpful guidance")
        print("  - No more confusing 'found but not found' messages")
    else:
        print("\n❌ MISSING SYMBOLS CONTRADICTION FIX: FAILED")
        print("The fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
