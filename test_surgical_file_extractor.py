#!/usr/bin/env python

import os
import unittest
from unittest.mock import MagicMock, patch

from aider_integration_service import AiderIntegrationService
from surgical_file_extractor import (
    SurgicalFileExtractor,
    ExtractionRange,
    SymbolInfo
)


class TestSurgicalFileExtractor(unittest.TestCase):
    """Test cases for the SurgicalFileExtractor class."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_aider_service = MagicMock(spec=AiderIntegrationService)
        self.extractor = SurgicalFileExtractor(self.mock_aider_service)
        self.project_path = "/test/project"

        # Sample code for testing
        self.sample_code = """import os
from typing import List, Dict

class TestClass:
    \"\"\"A test class.\"\"\"

    def __init__(self, name: str):
        \"\"\"Initialize with a name.\"\"\"
        self.name = name
        self._value = 0

    def get_value(self) -> int:
        \"\"\"Return the current value.\"\"\"
        return self._value

    def set_value(self, value: int) -> None:
        \"\"\"Set the value.\"\"\"
        self._value = value

def standalone_function(param1: str, param2: int = 0) -> str:
    \"\"\"A standalone function.\"\"\"
    return f"{param1}: {param2}"

# A constant
MAX_VALUE = 100
"""

        # Mock repository map and tags
        self.mock_repo_map = MagicMock()
        # Create a mock coder attribute
        self.mock_aider_service.coder = MagicMock()
        self.mock_aider_service.coder.repo_map = self.mock_repo_map

        # Create mock tags for the sample code
        tag1 = MagicMock()
        tag1.name = "TestClass"
        tag1.kind = "def"
        tag1.line = 3

        tag2 = MagicMock()
        tag2.name = "__init__"
        tag2.kind = "def"
        tag2.line = 6

        tag3 = MagicMock()
        tag3.name = "get_value"
        tag3.kind = "def"
        tag3.line = 11

        tag4 = MagicMock()
        tag4.name = "set_value"
        tag4.kind = "def"
        tag4.line = 15

        tag5 = MagicMock()
        tag5.name = "standalone_function"
        tag5.kind = "def"
        tag5.line = 19

        tag6 = MagicMock()
        tag6.name = "MAX_VALUE"
        tag6.kind = "def"
        tag6.line = 24

        self.mock_tags = [tag1, tag2, tag3, tag4, tag5, tag6]

    @patch('surgical_file_extractor.SurgicalFileExtractor._read_file_content')
    def test_get_file_line_count(self, mock_read_file):
        """Test getting the line count of a file."""
        mock_read_file.return_value = self.sample_code

        line_count = self.extractor._get_file_line_count(self.project_path, "test.py")

        self.assertEqual(line_count, 25)  # Our sample has 25 lines

    @patch('surgical_file_extractor.SurgicalFileExtractor._read_file_content')
    def test_extract_symbol_content(self, mock_read_file):
        """Test extracting a symbol's content."""
        mock_read_file.return_value = self.sample_code

        # Mock the get_tags method to return our test tags
        self.mock_repo_map.get_tags.return_value = self.mock_tags
        self.mock_repo_map.get_rel_fname.return_value = "test.py"

        # Create a mock extraction range
        mock_range = ExtractionRange(
            start_line=7,
            end_line=10,
            total_lines=4,
            symbol_name="__init__",
            file_path="test.py"
        )

        # Mock the extract_symbol_range method
        with patch.object(self.extractor, 'extract_symbol_range', return_value=mock_range):
            content = self.extractor.extract_symbol_content("__init__", "test.py", self.project_path)

            # The content should be the __init__ method (lines 7-10)
            expected_content = """    def __init__(self, name: str):
        \"\"\"Initialize with a name.\"\"\"
        self.name = name
        self._value = 0"""

            self.assertEqual(content, expected_content)

    def test_get_symbols_in_file(self):
        """Test getting all symbols in a file."""
        # Mock the get_tags method to return our test tags
        self.mock_repo_map.get_tags.return_value = self.mock_tags
        self.mock_repo_map.get_rel_fname.return_value = "test.py"

        symbols = self.extractor.get_symbols_in_file(self.project_path, "test.py")

        self.assertEqual(len(symbols), 6)
        self.assertEqual(symbols[0].name, "TestClass")
        self.assertEqual(symbols[0].symbol_type, "class")
        self.assertEqual(symbols[1].name, "__init__")
        self.assertEqual(symbols[1].symbol_type, "method")

    def test_extract_symbol_range(self):
        """Test extracting a symbol's range."""
        # Mock the get_symbols_in_file method
        symbols = [
            SymbolInfo(name="TestClass", start_line=4, file_path="test.py", symbol_type="class"),
            SymbolInfo(name="__init__", start_line=7, file_path="test.py", symbol_type="method"),
            SymbolInfo(name="get_value", start_line=12, file_path="test.py", symbol_type="method"),
            SymbolInfo(name="set_value", start_line=16, file_path="test.py", symbol_type="method"),
            SymbolInfo(name="standalone_function", start_line=20, file_path="test.py", symbol_type="function")
        ]

        with patch.object(self.extractor, 'get_symbols_in_file', return_value=symbols):
            with patch.object(self.extractor, '_get_file_line_count', return_value=25):
                # Test extracting a method in the middle
                range_info = self.extractor.extract_symbol_range("get_value", "test.py", self.project_path)

                self.assertIsNotNone(range_info)
                self.assertEqual(range_info.start_line, 12)
                self.assertEqual(range_info.end_line, 15)  # Should end right before set_value

                # Test extracting the last symbol
                range_info = self.extractor.extract_symbol_range("standalone_function", "test.py", self.project_path)

                self.assertIsNotNone(range_info)
                self.assertEqual(range_info.start_line, 20)
                self.assertEqual(range_info.end_line, 25)  # Should go to end of file


if __name__ == '__main__':
    unittest.main()
