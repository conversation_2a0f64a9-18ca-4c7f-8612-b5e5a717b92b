#!/usr/bin/env python3
"""
Diagnose repository map slicing to understand what the LLM is missing
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.repomap import RepoMap
from aider.models import Model
from aider.io import InputOutput


def diagnose_map_slicing():
    """Diagnose what's being sliced from the repository map"""
    
    print("🔍 Diagnosing Repository Map Slicing")
    print("=" * 60)
    
    # Create model and IO
    model = Model("gpt-3.5-turbo")
    io = InputOutput()
    
    # Create repo map with current settings
    repo_map = RepoMap(
        map_tokens=20202,
        root="aider-main",
        main_model=model,
        io=io,
        verbose=True,
        refresh="always"
    )
    
    print(f"📊 Token Budget: {repo_map.max_map_tokens:,} tokens")
    
    # Get all files
    from aider.repomap import find_src_files
    all_files = find_src_files("aider-main")
    print(f"📁 Total Files: {len(all_files):,}")
    
    # Get ranked tags (this is what gets sliced)
    print("\n🔄 Getting ranked tags...")
    ranked_tags = repo_map.get_ranked_tags(
        chat_fnames=[],  # No chat files
        other_fnames=all_files,
        mentioned_fnames=set(),
        mentioned_idents=set()
    )
    
    print(f"📊 Total Ranked Tags: {len(ranked_tags):,}")
    
    # Now simulate the binary search slicing
    print("\n🔪 Simulating Binary Search Slicing...")
    
    max_map_tokens = repo_map.max_map_tokens
    num_tags = len(ranked_tags)
    
    # This is the initial middle calculation from line 683
    initial_middle = min(int(max_map_tokens // 25), num_tags)
    print(f"📊 Initial Middle (max_tokens // 25): {initial_middle:,}")
    print(f"📊 Percentage of tags: {(initial_middle / num_tags) * 100:.1f}%")
    
    # Test different slice points
    test_points = [
        initial_middle // 4,
        initial_middle // 2,
        initial_middle,
        initial_middle * 2,
        min(initial_middle * 4, num_tags),
        num_tags
    ]
    
    print(f"\n📋 Testing Different Slice Points:")
    print(f"{'Slice Point':<12} {'Tokens':<8} {'% of Tags':<10} {'Status'}")
    print("-" * 50)
    
    for slice_point in test_points:
        if slice_point > num_tags:
            slice_point = num_tags
            
        # Create tree with this slice
        tree = repo_map.to_tree(ranked_tags[:slice_point], set())
        tokens = repo_map.token_count(tree)
        percentage = (slice_point / num_tags) * 100
        
        if tokens <= max_map_tokens:
            status = "✅ Fits"
        else:
            status = "❌ Over"
            
        print(f"{slice_point:<12,} {tokens:<8,} {percentage:<10.1f}% {status}")
    
    # Find the actual slice point used
    print(f"\n🎯 Finding Actual Slice Point Used...")
    
    # Simulate the binary search
    lower_bound = 0
    upper_bound = num_tags
    middle = initial_middle
    best_tree_tokens = 0
    best_middle = 0
    
    iterations = 0
    while lower_bound <= upper_bound and iterations < 20:  # Limit iterations
        tree = repo_map.to_tree(ranked_tags[:middle], set())
        num_tokens = repo_map.token_count(tree)
        
        print(f"   Iteration {iterations + 1}: middle={middle:,}, tokens={num_tokens:,}")
        
        pct_err = abs(num_tokens - max_map_tokens) / max_map_tokens
        ok_err = 0.15
        
        if (num_tokens <= max_map_tokens and num_tokens > best_tree_tokens) or pct_err < ok_err:
            best_tree_tokens = num_tokens
            best_middle = middle
            
            if pct_err < ok_err:
                break
        
        if num_tokens < max_map_tokens:
            lower_bound = middle + 1
        else:
            upper_bound = middle - 1
        
        middle = int((lower_bound + upper_bound) // 2)
        iterations += 1
    
    print(f"\n🎯 Final Results:")
    print(f"   Best slice point: {best_middle:,} tags")
    print(f"   Best tokens used: {best_tree_tokens:,} / {max_map_tokens:,}")
    print(f"   Percentage of tags included: {(best_middle / num_tags) * 100:.1f}%")
    print(f"   Tags excluded: {num_tags - best_middle:,}")
    
    # Analyze what's being excluded
    print(f"\n📊 Analysis of Excluded Content:")
    excluded_tags = ranked_tags[best_middle:]
    
    if excluded_tags:
        print(f"   Excluded tags: {len(excluded_tags):,}")
        
        # Group by file
        excluded_files = set()
        for tag in excluded_tags:
            if len(tag) > 0:
                excluded_files.add(tag[0])
        
        print(f"   Excluded files: {len(excluded_files):,}")
        
        # Show some examples
        print(f"\n📋 Sample of Excluded Files:")
        for i, filename in enumerate(sorted(excluded_files)[:10]):
            print(f"   {i+1:2d}. {filename}")
        
        if len(excluded_files) > 10:
            print(f"   ... and {len(excluded_files) - 10:,} more files")
    
    return {
        'total_tags': num_tags,
        'included_tags': best_middle,
        'excluded_tags': num_tags - best_middle,
        'tokens_used': best_tree_tokens,
        'token_budget': max_map_tokens,
        'inclusion_percentage': (best_middle / num_tags) * 100
    }


def suggest_improvements(results):
    """Suggest improvements based on the analysis"""
    
    print(f"\n🚀 Improvement Suggestions")
    print("=" * 40)
    
    inclusion_pct = results['inclusion_percentage']
    
    if inclusion_pct < 50:
        print("❌ CRITICAL: Less than 50% of repository content is visible to LLM!")
        print("   Recommendations:")
        print("   1. Increase token budget significantly")
        print("   2. Improve file ranking algorithm")
        print("   3. Implement smarter content selection")
    elif inclusion_pct < 75:
        print("⚠️  WARNING: Less than 75% of repository content is visible to LLM")
        print("   Recommendations:")
        print("   1. Increase token budget moderately")
        print("   2. Optimize file prioritization")
    else:
        print("✅ GOOD: Most repository content is visible to LLM")
        print("   Consider:")
        print("   1. Fine-tune ranking algorithm for better prioritization")
    
    print(f"\n🔧 Technical Improvements:")
    print(f"   1. Modify binary search algorithm to be less aggressive")
    print(f"   2. Implement tiered token allocation (core vs. peripheral files)")
    print(f"   3. Add file importance scoring beyond PageRank")
    print(f"   4. Implement dynamic token budgeting based on file types")


def main():
    """Main diagnostic function"""
    
    try:
        results = diagnose_map_slicing()
        suggest_improvements(results)
        
        print(f"\n" + "=" * 60)
        print("✅ DIAGNOSIS COMPLETE")
        print("=" * 60)
        
        print(f"\n📋 Summary:")
        print(f"   Repository coverage: {results['inclusion_percentage']:.1f}%")
        print(f"   Files visible to LLM: {results['included_tags']:,}")
        print(f"   Files hidden from LLM: {results['excluded_tags']:,}")
        print(f"   Token utilization: {results['tokens_used']:,} / {results['token_budget']:,}")
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
