#!/usr/bin/env python

import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'aider-main')))

# Import the required modules
try:
    from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider.context_request.aider_template_renderer import AiderTemplateRenderer
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)

def test_query_distinction():
    """Test the LLM's ability to distinguish between the current query and previous queries."""
    print("\n=== Testing Query Distinction ===")

    # Create a test file with a simple method
    test_file = "test_simple.py"
    with open(test_file, 'w') as f:
        f.write("""
def simple_function(a: int, b: int) -> int:
    \"\"\"
    A simple function that adds two numbers.

    Args:
        a: First number
        b: Second number

    Returns:
        Sum of a and b
    \"\"\"
    return a + b
""")

    print(f"Created test file: {test_file}")

    try:
        # Create a context request handler
        handler = ContextRequestHandler(project_path=".")

        # Create a context request
        context_request = ContextRequest(
            original_user_query_context="User is asking about the simple_function",
            symbols_of_interest=[
                SymbolRequest(
                    type="function_definition",
                    name="simple_function",
                    file_hint="test_simple.py"
                )
            ],
            reason_for_request="To analyze the implementation of the simple_function"
        )

        # Create a conversation history with a previous query
        conversation_history = [
            {"role": "user", "content": "How does the complex_function work?"},
            {"role": "assistant", "content": "I need more information about the complex_function to answer your question."}
        ]

        # Process the context request
        print("Processing context request...")
        result = handler.process_context_request(context_request)

        # Render the augmented prompt
        renderer = AiderTemplateRenderer()
        prompt = renderer.render_augmented_prompt(
            original_query="How does the simple_function work?",
            repo_overview="",
            extracted_context=result,
            conversation_history=conversation_history
        )

        print("\n=== Augmented Prompt Content ===")
        print(prompt)

        # Check if the current query is highlighted
        if "CURRENT USER QUERY (ANSWER THIS QUERY ONLY)" in prompt:
            print("✅ Current query is highlighted")
        else:
            print("❌ Current query is not highlighted")

        # Check if the conversation history is marked as for context only
        if "CONVERSATION HISTORY (FOR REFERENCE ONLY)" in prompt:
            print("✅ Conversation history is marked as for context only")
        else:
            print("❌ Conversation history is not marked as for context only")

        # Check if the focus instructions are included
        if "FOCUS ON CURRENT QUERY ONLY" in prompt:
            print("✅ Focus instructions are included")
        else:
            print("❌ Focus instructions are not included")

    except Exception as e:
        print(f"❌ Error in test: {e}")
    finally:
        # Clean up the test file
        try:
            os.remove(test_file)
            print(f"Removed test file: {test_file}")
        except:
            pass

    print("\n=== Test completed! ===")

if __name__ == "__main__":
    test_query_distinction()
