#!/usr/bin/env python3
"""
Demonstration of the new direct IR context flow.
This shows "Step 1" of the new required flow in action:
User query → system provides LLM-Friendly IR Context Package directly.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def demo_new_vs_old_flow():
    """Demonstrate the difference between old and new flows."""
    
    print("🚀 NEW DIRECT IR CONTEXT FLOW DEMONSTRATION")
    print("=" * 60)
    
    print("\n📋 FLOW COMPARISON:")
    print("\n🔴 OLD FLOW (Traditional):")
    print("   1. User: 'Why is my context selection taking so long?'")
    print("   2. LLM: Generates MAP_REQUEST with keywords")
    print("   3. System: Processes MAP_REQUEST, returns repository map")
    print("   4. LLM: Analyzes map, generates CONTEXT_REQUEST")
    print("   5. System: Processes CONTEXT_REQUEST, extracts code")
    print("   6. LLM: Finally answers user's question")
    print("   📊 Total: 6 steps, 2 LLM calls, manual request generation")
    
    print("\n🟢 NEW FLOW (Direct IR Context):")
    print("   1. User: 'Why is my context selection taking so long?'")
    print("   2. System: Automatically generates IR context package")
    print("   3. LLM: Receives context + query, answers immediately")
    print("   📊 Total: 3 steps, 1 LLM call, automatic context")
    
    print("\n✅ BENEFITS:")
    print("   • 50% fewer steps (3 vs 6)")
    print("   • 50% fewer LLM calls (1 vs 2)")
    print("   • No manual request formatting")
    print("   • Immediate intelligent context")
    print("   • Better context quality with inheritance analysis")

def demo_context_generation():
    """Demonstrate the context generation process."""
    
    print("\n🧠 CONTEXT GENERATION DEMONSTRATION")
    print("=" * 60)
    
    try:
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Test queries that would benefit from the new flow
        test_queries = [
            "Why is my context selection taking so long?",
            "How does authentication work in this system?",
            "What are the main classes and their inheritance relationships?",
            "How do I fix performance issues in the code?",
            "What's the difference between MAP_REQUEST and CONTEXT_REQUEST?"
        ]
        
        print(f"\n📝 Testing {len(test_queries)} sample queries...")
        
        handler = ContextRequestHandler(".")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Query {i}: '{query}'")
            
            # Extract focus entities (simplified version)
            import re
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their'}
            words = re.findall(r'\b\w+\b', query.lower())
            focus_entities = [word for word in words if word not in stop_words and len(word) > 2][:8]
            
            print(f"   🎯 Focus entities: {focus_entities}")
            
            # Create IR context request
            request = IRContextRequest(
                user_query=query,
                task_description=f"Analyze and provide context for: {query}",
                task_type="general_analysis",
                focus_entities=focus_entities,
                max_tokens=2000,
                llm_friendly=True,
                include_code_context=True,
                max_entities=8
            )
            
            print(f"   ⚙️ Created IR context request (max_tokens: {request.max_tokens})")
            
            # Note: We won't actually process all queries to save time
            if i == 1:  # Only process the first one fully
                print("   🔄 Processing IR context request...")
                result = handler.process_ir_context_request(request)
                
                if result and "llm_friendly_package" in result:
                    package_size = len(result["llm_friendly_package"])
                    print(f"   ✅ Generated context package: {package_size} chars")
                    print(f"   📊 Summary: {result.get('summary', {})}")
                else:
                    print("   ❌ Failed to generate context package")
            else:
                print("   ⏭️ Skipping full processing (demo mode)")
        
        print(f"\n✅ Context generation demonstration complete!")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")

def demo_configuration_options():
    """Demonstrate configuration options for the new flow."""
    
    print("\n⚙️ CONFIGURATION OPTIONS")
    print("=" * 60)
    
    print("\n🔧 Environment Variables:")
    print("   AIDER_ENABLE_DIRECT_IR_CONTEXT=true   # Force enable new flow")
    print("   AIDER_DISABLE_DIRECT_IR_CONTEXT=true  # Force disable new flow")
    
    print("\n💻 Programmatic Configuration:")
    print("   coder.enable_direct_ir_context = True   # Enable for this session")
    print("   coder.enable_direct_ir_context = False  # Disable for this session")
    
    print("\n🎛️ Current Configuration:")
    current_disable = os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes')
    current_enable = os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '').lower() in ('true', '1', 'yes')
    
    if current_disable:
        print("   Status: ❌ DISABLED (via AIDER_DISABLE_DIRECT_IR_CONTEXT)")
    elif current_enable:
        print("   Status: ✅ ENABLED (via AIDER_ENABLE_DIRECT_IR_CONTEXT)")
    else:
        print("   Status: ✅ ENABLED (default)")
    
    print("\n📋 Usage Examples:")
    print("   # Disable new flow for testing")
    print("   export AIDER_DISABLE_DIRECT_IR_CONTEXT=true")
    print("   python -m aider")
    print()
    print("   # Enable new flow explicitly")
    print("   export AIDER_ENABLE_DIRECT_IR_CONTEXT=true")
    print("   python -m aider")

def demo_integration_status():
    """Show the integration status and next steps."""
    
    print("\n📊 INTEGRATION STATUS")
    print("=" * 60)
    
    print("\n✅ COMPLETED:")
    print("   • Direct IR context generation method")
    print("   • Focus entity extraction from user queries")
    print("   • Context injection into LLM conversation")
    print("   • Configuration options (env vars)")
    print("   • Fallback to traditional flow on errors")
    print("   • Integration with existing IR pipeline")
    
    print("\n🔄 IN PROGRESS:")
    print("   • Testing with real aider sessions")
    print("   • Performance optimization")
    print("   • Response quality comparison")
    
    print("\n📋 NEXT STEPS:")
    print("   1. Test with real aider session:")
    print("      python -m aider --message 'Why is my context selection taking so long?'")
    print("   2. Compare response quality vs old flow")
    print("   3. Monitor performance metrics")
    print("   4. Gather user feedback")
    print("   5. Fine-tune context generation parameters")
    
    print("\n🎯 SUCCESS CRITERIA:")
    print("   • Faster response times (fewer LLM calls)")
    print("   • Better context relevance")
    print("   • Improved user experience")
    print("   • Maintained response quality")

if __name__ == "__main__":
    print("🎬 NEW DIRECT IR CONTEXT FLOW DEMO")
    print("Implementing 'Step 1' of the new required flow\n")
    
    demo_new_vs_old_flow()
    demo_context_generation()
    demo_configuration_options()
    demo_integration_status()
    
    print("\n🎉 DEMONSTRATION COMPLETE!")
    print("\nThe new direct IR context flow is ready for testing.")
    print("Try it with: python -m aider --message 'Why is my context selection taking so long?'")
