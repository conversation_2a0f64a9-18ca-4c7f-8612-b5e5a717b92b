# USER QUERY
How does the intelligent context selection algorithm work? I need to understand the implementation.

# INTELLIGENT CONTEXT ANALYSIS PACKAGE
# Generated by IR_CONTEXT_REQUEST system

## TASK INFORMATION
- User Query: How does the intelligent context selection algorithm work? I need to understand the implementation.
- Task Description: How does the intelligent context selection algorithm work? I need to understand the implementation.
- Task Type: general_analysis

## CONTEXT SUMMARY
- Total Entities Selected: 40
- Total Tokens Used: 5999
- Critical Entities: 40
- High Priority Entities: 40
- Files Involved: 12
- Token Utilization: 100.0%

## SELECTION RATIONALE
Context Selection Rationale for general_analysis:

Selected 91 entities using 5999 tokens (100.0% of budget).

Priority Distribution:
  - critical: 91 entities

Criticality Distribution:
  - low: 4 entities
  - medium: 81 entities
  - high: 6 entities

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for general_analysis task requirements
- Maintained token budget constraints

## IR ANALYSIS DATA (40 entities)

### 1. parse_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.83974358974359
- **Priority**: critical
- **Calls**: search, strip, group, rstrip, startswith, endswith, replace, loads, sub, get, append, SymbolRequest, ContextRequest
- **Used By**: aider_context_request_integration, test_directory_file_format, test_aider_context_request_integration, test_context_request_missing_reason, test_context_request_format_fix, test_json_parsing_fix
- **Side Effects**: writes_log, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 2. process_context_requests (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.7911538461538465
- **Priority**: critical
- **Calls**: tool_warning, getcwd, basename, exists, join, find_common_root, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, keys
- **Used By**: test_aider_context_request, test_context_request_integration, test_repo_map_compatibility, test_context_request_hang, test_full_aider_integration, test_context_request_code_block_fix, test_llm_workflow_understanding, test_context_request_fix, test_aider_coder_path_fix
- **Side Effects**: modifies_state, writes_log, database_io, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 3. process_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.773076923076923
- **Priority**: critical
- **Calls**: process_context_request, get, render_augmented_prompt
- **Used By**: aider_context_request_integration, test_context_request_with_repo_map, test_augmented_prompt_content, base_coder_old, test_surgical_integration, test_error_handling_fix, test_class_method_extraction, test_context_request_hang, test_complete_function_extraction, test_context_request_integration
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 4. detect_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.748076923076923
- **Priority**: critical
- **Calls**: parse_context_request, join
- **Used By**: test_aider_context_request, test_context_request_integration, base_coder, test_repo_map_compatibility, test_context_request_hang, test_full_aider_integration, base_coder_old, context_request_demo, test_context_request
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 5. process_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.723076923076923
- **Priority**: critical
- **Calls**: join, _get_from_cache, _extract_symbol_content, _extract_essential_imports, SymbolInfo, _extract_containing_class, extract_usage_contexts, append, _update_cache
- **Used By**: aider_context_request_integration, test_context_request_with_repo_map, test_augmented_prompt_content, base_coder_old, test_surgical_integration, test_error_handling_fix, test_class_method_extraction, test_context_request_hang, test_complete_function_extraction, test_context_request_integration
- **Side Effects**: writes_log, database_io, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 6. update_conversation_history (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.706923076923077
- **Priority**: critical
- **Calls**: get, append
- **Used By**: test_context_request_integration, test_repo_map_compatibility, test_context_request_hang, test_aider_context_request_integration, test_full_aider_integration, test_conversation_history_fix, test_context_request_fix
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 7. process_context_requests (function)
- **Module**: base_coder_old
- **File**: aider-main\aider\coders\base_coder_old.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.6961538461538463
- **Priority**: critical
- **Calls**: tool_warning, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, join, keys, sub, process_context_request
- **Used By**: test_aider_context_request, test_context_request_integration, test_repo_map_compatibility, test_context_request_hang, test_full_aider_integration, test_context_request_code_block_fix, test_llm_workflow_understanding, test_context_request_fix, test_aider_coder_path_fix
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: AttributeError, TypeError, KeyError, ImportError

### 8. _read_file_content (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6535897435897438
- **Priority**: critical
- **Calls**: join, read_text
- **Used By**: enhanced_surgical_extractor, surgical_file_extractor, surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 9. _extract_code_snippet (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6235897435897435
- **Priority**: critical
- **Calls**: _read_file_content, splitlines, search, _find_complete_class_body, _find_complete_function_body, _find_surrounding_function, join, CodeSnippet
- **Used By**: simple_demo, test_surgical_context_extractor, surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, ImportError, KeyError

### 10. AiderContextRequestIntegration (class)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_context_request_with_repo_map, base_coder, test_augmented_prompt_content, base_coder_old, test_surgical_integration, test_class_method_extraction, test_context_request_hang, test_complete_function_extraction, test_context_request_integration, test_surgical_extraction_integration
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 11. SymbolRequest (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_context_request_with_repo_map, test_augmented_prompt_content, test_surgical_integration, test_error_handling_fix, test_class_method_extraction, test_context_request_root_fix, test_complete_function_extraction, context_request_handler, test_surgical_extraction_integration, test_context_request_path_fix
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 12. ContextRequest (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_context_request_with_repo_map, test_augmented_prompt_content, test_surgical_integration, test_error_handling_fix, test_class_method_extraction, test_complete_function_extraction, context_request_handler, test_surgical_extraction_integration, test_aider_context_request_integration, test_context_request_availability
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 13. ContextRequestHandler (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_ir_context_request, test_query_distinction, test_error_handling_fix, aider_context_request_integration, test_context_request_path_fix, test_directory_file_format, test_no_dependencies, test_context_request_root_fix, base_coder, test_real_world_ir_workflow
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 14. select_optimal_context (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.605
- **Priority**: critical
- **Calls**: _score_entities_for_task, _select_entities_within_budget, _enhance_with_dependency_context, _build_context_bundle, get_critical_entities
- **Used By**: aider_integration_service, iterative_analysis_engine, intelligent_context_selector
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 15. _get_context_selector (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5911538461538464
- **Priority**: critical
- **Calls**: generate_mid_level_ir, IntelligentContextSelector
- **Used By**: aider_integration_service, test_intelligent_context_selection
- **Side Effects**: modifies_file, modifies_state, writes_log
- **Potential Errors**: KeyError, AttributeError, ValueError, TypeError, ImportError

### 16. get_context_request_summary (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.586410256410256
- **Priority**: critical
- **Calls**: join
- **Used By**: test_context_request_integration, base_coder, test_repo_map_compatibility, test_context_request_hang, test_context_request_availability, test_aider_context_request_integration, test_full_aider_integration, base_coder_old, context_request_demo, test_context_request
- **Side Effects**: none
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 17. select_intelligent_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.582307692307692
- **Priority**: critical
- **Calls**: _get_context_selector, get, lower, select_optimal_context, analyze_context_quality, append, sort
- **Used By**: aider_integration_service, test_intelligent_context_selection
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ImportError, KeyError

### 18. analyze_context_quality (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5799999999999996
- **Priority**: critical
- **Calls**: get
- **Used By**: aider_integration_service, intelligent_context_selector
- **Side Effects**: network_io, modifies_state, modifies_container
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, KeyError

### 19. cmd_copy_context (function)
- **Module**: commands
- **File**: aider-main\aider\commands.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.566153846153846
- **Priority**: critical
- **Calls**: format_chat_chunks, get, copy, tool_output, tool_error
- **Used By**: base_coder, base_coder_old
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, KeyError

### 20. extract_usage_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.561923076923077
- **Priority**: critical
- **Calls**: _get_from_cache, get_files_that_import, append, _find_symbol_line_numbers, _read_file_content, _determine_usage_type, _determine_context_window_size, _extract_code_snippet, UsageContext, _update_cache
- **Used By**: enhanced_surgical_extractor, context_request_handler, test_augmented_prompt_content, surgical_context_extractor
- **Side Effects**: modifies_file, database_io, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 21. integrate_prioritization_with_algorithm (function)
- **Module**: fix_map_slicing
- **File**: fix_map_slicing.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.541153846153846
- **Priority**: critical
- **Calls**: Path, open, read, find, replace, write
- **Used By**: fix_map_slicing
- **Side Effects**: modifies_file, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, ZeroDivisionError, FileNotFoundError, PermissionError, KeyError

### 22. extract_dependency_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5319230769230767
- **Priority**: critical
- **Calls**: _get_from_cache, get_files_imported_by, get_files_that_import, get_symbol_references_between_files, items, _find_symbol_line_numbers, _read_file_content, _determine_context_window_size, _extract_code_snippet, append, _update_cache
- **Used By**: surgical_context_extractor
- **Side Effects**: modifies_file, database_io, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 23. _update_cache (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5285897435897438
- **Priority**: critical
- **Calls**: time
- **Used By**: models, context_request_handler, enhanced_surgical_extractor, test_model_info_manager, surgical_file_extractor, openrouter, surgical_context_extractor
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 24. extract_enhanced_context (function)
- **Module**: enhanced_surgical_extractor
- **File**: enhanced_surgical_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.521153846153846
- **Priority**: critical
- **Calls**: _get_from_cache, get_symbols_in_file, next, extract_symbol_range, extract_symbol_content, _extract_essential_imports, _extract_containing_class, extract_usage_contexts, EnhancedCodeContext, _update_cache
- **Used By**: enhanced_extraction_demo, simple_enhanced_test
- **Side Effects**: database_io, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 25. process_ir_context_requests (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.516153846153846
- **Priority**: critical
- **Calls**: tool_warning, search, strip, group, loads, IRContextRequest, get, tool_output, getcwd, basename, exists, join, find_common_root, ContextRequestHandler, tool_error
- **Used By**: None
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 26. _extract_definition_info (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.51525641025641
- **Priority**: critical
- **Calls**: splitlines, search, escape, strip, _extract_docstring, startswith, endswith, isupper
- **Used By**: test_surgical_context_extractor, surgical_context_extractor
- **Side Effects**: modifies_state
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, KeyError

### 27. get_focused_inheritance_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5128205128205128
- **Priority**: critical
- **Calls**: _get_context_extractor, get_focused_inheritance_context, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 28. extract_definition_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5119230769230767
- **Priority**: critical
- **Calls**: join, _get_from_cache, find_file_defining_symbol, _find_definition_line_numbers, _read_file_content, _extract_definition_info, _determine_context_window_size, _extract_code_snippet, DefinitionContext, append, _update_cache
- **Used By**: surgical_context_extractor
- **Side Effects**: network_io, modifies_state, writes_log, modifies_file, database_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 29. build (function)
- **Module**: context_bundle_builder
- **File**: context_bundle_builder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.506923076923077
- **Priority**: critical
- **Calls**: _score_all_entities, _select_entities_within_budget, _build_enhanced_bundle
- **Used By**: main, iterative_analysis_engine, test_context_bundle_builder
- **Side Effects**: network_io, modifies_state, writes_log
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 30. _update_cache (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5035897435897434
- **Priority**: critical
- **Calls**: time
- **Used By**: models, context_request_handler, enhanced_surgical_extractor, test_model_info_manager, surgical_file_extractor, openrouter, surgical_context_extractor
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 31. get_focused_inheritance_context (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.503076923076923
- **Priority**: critical
- **Calls**: get_base_classes_of, get_derived_classes_of, get, _find_definition_line_numbers, _read_file_content, _extract_definition_info, _determine_context_window_size, _extract_code_snippet, DefinitionContext, append, splitlines, match, group, startswith, search
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: writes_log, modifies_file, modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 32. _enhance_with_dependency_context (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5016666666666665
- **Priority**: critical
- **Calls**: copy, get, append, add
- **Used By**: intelligent_context_selector
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 33. _display_ir_context_response_to_user (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.498974358974359
- **Priority**: critical
- **Calls**: join, get, dumps
- **Used By**: None
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, KeyError

### 34. _get_from_cache (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: get, time
- **Used By**: enhanced_surgical_extractor, context_request_handler, surgical_file_extractor, surgical_context_extractor
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ImportError, KeyError

### 35. get_contextual_dependencies (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4966666666666666
- **Priority**: critical
- **Calls**: _get_context_extractor, get_contextual_dependencies, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 36. get_related_entities (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4855128205128207
- **Priority**: critical
- **Calls**: add, get, explore_dependencies
- **Used By**: test_intelligent_context_selection
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, KeyError

### 37. main (function)
- **Module**: context_request_demo
- **File**: context_request_demo.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: getcwd, AiderIntegrationService, AiderContextRequestIntegration, get_llm_instructions, ContextRequest, SymbolRequest, get_context_request_summary, process_context_request, detect_context_request
- **Used By**: test_main, test_browser, test_deprecated, test_ssl_verification
- **Side Effects**: writes_log, network_io
- **Potential Errors**: IndexError, AttributeError, ValueError, TypeError, KeyError

### 38. get_contextual_dependencies (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: extract_dependency_contexts, get_symbols_defined_in_file, items, extend, extract_usage_contexts, get_files_imported_by, get_symbol_references_between_files, extract_definition_contexts, sort, ContextualDependencyMap
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_state, network_io
- **Potential Errors**: IndexError, AttributeError, TypeError, ZeroDivisionError, ImportError, KeyError

### 39. match (variable)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.36025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: enhanced_surgical_extractor, aider_integration_service, recording_audio, clean_metadata, versionbump, simple_enhanced_test, benchmark, help, simple_extraction_test, surgical_context_extractor
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 40. count (variable)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.267179487179487
- **Priority**: critical
- **Calls**: None
- **Used By**: enhanced_surgical_extractor, test_help, test_coder, simple_enhanced_test, test_map_request_response_format, test_llm_workflow_understanding, search_replace, test_repo_map_leak_detection, debug_map_request_issue
- **Side Effects**: none
- **Potential Errors**: RuntimeError

## SOURCE CODE IMPLEMENTATIONS (38 implementations)

### 1. parse_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.83974358974359

```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError:
                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                request_data = json.loads(json_str)

            # Create the ContextRequest object
            symbols = []
            for symbol_data in request_data.get('symbols_of_interest', []):
                symbols.append(SymbolRequest(
                    type=symbol_data.get('type', 'unknown'),
                    name=symbol_data.get('name', ''),
                    file_hint=symbol_data.get('file_hint')
                ))

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=request_data.get('reason_for_request', '')
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

```

### 2. process_context_requests
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.7911538461538465

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration

                # Determine the correct project path for context requests
                # Priority: 1) Current working directory (where user runs aider)
                #          2) Git repository root (fallback)
                import os
                project_path = os.getcwd()

                # If current working directory is the aider repository itself,
                # and we have files in the chat, use the common root of those files
                if (os.path.basename(project_path) == 'aider' and
                    os.path.exists(os.path.join(project_path, 'aider-main')) and
                    (self.abs_fnames or self.abs_read_only_fnames)):
                    # Use the common root of the files in the chat
                    if self.abs_fnames:
                        project_path = utils.find_common_root(self.abs_fnames)
                    elif self.abs_read_only_fnames:
                        project_path = utils.find_common_root(self.abs_read_only_fnames)

                self.context_request_integration = AiderContextRequestIntegration(project_path, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        # DISABLED: Context request limit check
        # if self.current_query_context_requests >= 300:
        #     self.io.tool_error(f"Maximum number of context requests reached for this query. Current count: {self.current_query_context_requests}")
        #     return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # CRITICAL FIX: Extract the ACTUAL user query from user_message
            # The user_message might contain MAP_REQUEST content, we need the original query
            actual_user_query = self._extract_actual_user_query(user_message)

            # CRITICAL FIX: Do NOT pass repo_overview if it contains MAP_REQUEST content
            # For CONTEXT_REQUEST, we want ONLY the clean template response
            clean_repo_overview = ""
            if repo_overview and not self._contains_map_request_content(repo_overview):
                clean_repo_overview = repo_overview

            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=actual_user_query,
                repo_overview=clean_repo_overview
            )

            # Check if the context request completely failed (no symbols found at all)
            # Only trigger failure guidance if ALL symbols were not found, not just some
            if (augmented_prompt and
                "All requested symbols could not be found" in augmented_prompt or
                ("No extracted symbols were included" in augmented_prompt and "could not find" in augmented_prompt.lower())):

                # Extract requested symbols for guidance
                requested_symbols = []
                try:
                    # context_request is a ContextRequest object, not a dict
                    symbols_of_interest = context_request.symbols_of_interest if hasattr(context_request, 'symbols_of_interest') else []
                    for symbol in symbols_of_interest:
                        if hasattr(symbol, 'name'):
                            requested_symbols.append(symbol.name)
                        elif isinstance(symbol, dict) and 'name' in symbol:
                            requested_symbols.append(symbol['name'])
                        elif isinstance(symbol, str):
                            requested_symbols.append(symbol)
                except Exception:
                    requested_symbols = ["requested symbols"]

                # Generate smart guidance message
                reason = ""
                if hasattr(context_request, 'reason_for_request'):
                    reason = context_request.reason_for_request
                elif hasattr(context_request, 'original_user_query_context'):
                    reason = context_request.original_user_query_context
                guidance_message = self._generate_smart_guidance_message("context", requested_symbols, reason)

                # Replace the augmented prompt with guidance
                augmented_prompt = guidance_message

        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 3. process_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.773076923076923

```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

```

### 4. detect_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.748076923076923

```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

```

### 5. process_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.723076923076923

```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                continue

            # Extract essential imports
            essential_imports = None
            if hasattr(self.file_extractor, '_extract_essential_imports'):
                essential_imports = self.file_extractor._extract_essential_imports(self.project_path, file_path)

            # Extract containing class signature if it's a method
            containing_class = None
            if '.' in symbol.name and hasattr(self.file_extractor, '_extract_containing_class'):
                # Create a dummy SymbolInfo object
                from surgical_file_extractor import SymbolInfo
                symbol_info = SymbolInfo(
                    name=symbol_name,
                    start_line=0,  # This will be updated by the extractor
                    file_path=file_path,
                    symbol_type="method"
                )
                containing_class = self.file_extractor._extract_containing_class(self.project_path, file_path, symbol_info)

            # Extract usage contexts
            usage_contexts = self.context_extractor.extract_usage_contexts(self.project_path, symbol_name, file_path)

            # Add the extracted symbol to the result
            result["extracted_symbols"].append({
                "symbol_name": symbol.name,
                "file_path": file_path,
                "content": content,
                "essential_imports": essential_imports,
                "containing_class": containing_class
            })

            # Add dependency snippets
            for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                result["dependency_snippets"].append({
                    "file_path": usage.snippet.file_path,
                    "symbol_name": usage.snippet.symbol_name,
                    "content": usage.snippet.content,
                    "usage_type": usage.usage_type.value
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result
```

### 6. update_conversation_history
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.706923076923077

```python
    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

```

### 7. process_context_requests
- **File**: aider-main\aider\coders\base_coder_old.py
- **Priority**: critical
- **Relevance Score**: 2.6961538461538463

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration
                self.context_request_integration = AiderContextRequestIntegration(self.root, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        if self.current_query_context_requests >= 300:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=user_message,
                repo_overview=repo_overview
            )
        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 8. _read_file_content
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.6535897435897438

```python
    def _read_file_content(self, project_path: str, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        abs_path = os.path.join(project_path, file_path)
        return self.io.read_text(abs_path)

```

### 9. _extract_code_snippet
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.6235897435897435

```python
    def _extract_code_snippet(self, project_path: str, file_path: str,
                             line_num: int, context_window: int,
                             context_type: ContextType, symbol_name: str) -> Optional[CodeSnippet]:
        """
        Extract a code snippet from a file centered around a specific line.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to extract from
            line_num: The line number to center the snippet around (1-based)
            context_type: The type of context being extracted
            symbol_name: The name of the symbol being referenced

        Returns:
            A CodeSnippet object or None if extraction failed
        """
        content = self._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return None

        # Adjust line_num to 0-based for internal calculations
        line_idx = line_num - 1

        # For class definitions, extract the complete class body
        if context_type == ContextType.DEFINITION and re.search(r'^\s*class\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_class_body(lines, line_idx)
        # For function/method definitions, extract the complete function body
        elif context_type == ContextType.DEFINITION and re.search(r'^\s*def\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_function_body(lines, line_idx)
        else:
            # Use context window for other cases
            start_idx = max(0, line_idx - context_window)
            end_idx = min(len(lines) - 1, line_idx + context_window)

        # Extract the surrounding function/method name if possible
        surrounding_function = self._find_surrounding_function(lines, line_idx)

        # Extract the snippet content
        snippet_lines = lines[start_idx:end_idx + 1]
        snippet_content = '\n'.join(snippet_lines)

        return CodeSnippet(
            content=snippet_content,
            file_path=file_path,
            start_line=start_idx + 1,  # Convert back to 1-based
            end_line=end_idx + 1,      # Convert back to 1-based
            context_type=context_type,
            symbol_name=symbol_name,
            surrounding_function=surrounding_function
        )

```

### 10. AiderContextRequestIntegration
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class AiderContextRequestIntegration:
    """
    Integrates the context request handler with the Aider system.
    """

```

### 11. SymbolRequest
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class SymbolRequest:
    """Represents a symbol requested by the LLM."""
    type: str  # method_definition, class_definition, function_definition, etc.
    name: str  # The name of the symbol, e.g., "AuthService.login_user"
    file_hint: Optional[str] = None  # Optional hint about which file contains the symbol


@dataclass
```

### 12. ContextRequest
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class ContextRequest:
    """Represents a context request from the LLM."""
    original_user_query_context: str
    symbols_of_interest: List[SymbolRequest]
    reason_for_request: str


```

### 13. ContextRequestHandler
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

```

### 14. select_optimal_context
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.605

```python
    def select_optimal_context(self, task_description: str, task_type: TaskType = TaskType.GENERAL_ANALYSIS,
                             focus_entities: Optional[List[str]] = None) -> ContextBundle:
        """
        Select the most relevant code context for a given task.

        Args:
            task_description: Natural language description of the task
            task_type: Type of development task (affects selection strategy)
            focus_entities: Optional list of specific entities to focus on

        Returns:
            ContextBundle containing the selected entities and metadata
        """
        print(f"🎯 Selecting optimal context for: {task_description}")
        print(f"   Task type: {task_type.value}")
        print(f"   Token budget: {self.max_tokens}")

        # Step 1: Score all entities for relevance
        scored_entities = self._score_entities_for_task(task_description, task_type, focus_entities)

        # Step 2: Select entities within token budget
        selected_entities = self._select_entities_within_budget(scored_entities, task_type)

        # Step 3: Enhance selection with dependency context
        enhanced_entities = self._enhance_with_dependency_context(selected_entities, task_type)

        # Step 4: Build the final context bundle
        context_bundle = self._build_context_bundle(
            task_description, task_type, enhanced_entities
        )

        print(f"✅ Context selection complete:")
        print(f"   Selected {len(context_bundle.entities)} entities")
        print(f"   Total tokens: {context_bundle.total_tokens}")
        print(f"   Critical entities: {len(context_bundle.get_critical_entities())}")

        return context_bundle

```

### 15. _get_context_selector
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5911538461538464

```python
    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

```

### 16. get_context_request_summary
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.586410256410256

```python
    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request for logging purposes.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"
```

### 17. select_intelligent_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.582307692307692

```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

```

### 18. analyze_context_quality
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.5799999999999996

```python
    def analyze_context_quality(self, context_bundle: ContextBundle) -> Dict[str, Any]:
        """Analyze the quality and completeness of a context bundle."""
        entities = context_bundle.entities

        # Calculate coverage metrics
        total_entities = len(self.entity_map)
        selected_entities = len(entities)
        coverage_percentage = (selected_entities / total_entities) * 100

        # Analyze priority distribution
        priority_dist = {}
        for priority in ContextPriority:
            count = len([e for e in entities if e.priority == priority])
            priority_dist[priority.value] = count

        # Analyze criticality distribution
        criticality_dist = {}
        for criticality in ['low', 'medium', 'high']:
            count = len([e for e in entities if e.criticality == criticality])
            criticality_dist[criticality] = count

        # Calculate dependency completeness
        missing_deps = 0
        total_deps = 0
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in entities}

        for entity in entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"
            deps = self.dependency_graph.get(entity_name, set())
            total_deps += len(deps)
            missing_deps += len([d for d in deps if d not in selected_names])

        dependency_completeness = ((total_deps - missing_deps) / max(total_deps, 1)) * 100

        return {
            'coverage_percentage': coverage_percentage,
            'selected_entities': selected_entities,
            'total_entities': total_entities,
            'priority_distribution': priority_dist,
            'criticality_distribution': criticality_dist,
            'dependency_completeness': dependency_completeness,
            'token_utilization': (context_bundle.total_tokens / self.max_tokens) * 100,
            'average_relevance_score': sum(e.relevance_score for e in entities) / len(entities) if entities else 0
        }


```

### 19. cmd_copy_context
- **File**: aider-main\aider\commands.py
- **Priority**: critical
- **Relevance Score**: 2.566153846153846

```python
    def cmd_copy_context(self, args=None):
        """Copy the current chat context as markdown, suitable to paste into a web UI"""

        chunks = self.coder.format_chat_chunks()

        markdown = ""

        # Only include specified chunks in order
        for messages in [chunks.repo, chunks.readonly_files, chunks.chat_files]:
            for msg in messages:
                # Only include user messages
                if msg["role"] != "user":
                    continue

                content = msg["content"]

                # Handle image/multipart content
                if isinstance(content, list):
                    for part in content:
                        if part.get("type") == "text":
                            markdown += part["text"] + "\n\n"
                else:
                    markdown += content + "\n\n"

        args = args or ""
        markdown += f"""
Just tell me how to edit the files to make the changes.
Don't give me back entire files.
Just show me the edits I need to make.

{args}
"""

        try:
            pyperclip.copy(markdown)
            self.io.tool_output("Copied code context to clipboard.")
        except pyperclip.PyperclipException as e:
            self.io.tool_error(f"Failed to copy to clipboard: {str(e)}")
            self.io.tool_output(
                "You may need to install xclip or xsel on Linux, or pbcopy on macOS."
            )
        except Exception as e:
            self.io.tool_error(f"An unexpected error occurred while copying to clipboard: {str(e)}")


```

### 20. extract_usage_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.561923076923077

```python
    def extract_usage_contexts(self, project_path: str, symbol_name: str,
                              defining_file: str) -> List[UsageContext]:
        """
        Find all places where a specific symbol is used and extract the context around each usage.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to find usages of
            defining_file: Path to the file that defines the symbol

        Returns:
            A list of UsageContext objects
        """
        cache_key = f"usage_contexts:{project_path}:{symbol_name}:{defining_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Find files that might use this symbol
        files_to_check = self.aider_service.get_files_that_import(project_path, defining_file)

        # Add the defining file itself (for self-references)
        if defining_file not in files_to_check:
            files_to_check.append(defining_file)

        usage_contexts = []

        # Check each file for usages of the symbol
        for file_path in files_to_check:
            # Find line numbers where the symbol is used
            line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol_name)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, file_path)
                if not content:
                    continue

                # Determine the usage type
                usage_type = self._determine_usage_type(content, line_num, symbol_name)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1, "variable" if usage_type == UsageType.VARIABLE_REFERENCE else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, file_path, line_num, adjusted_window,
                    ContextType.USAGE, symbol_name)

                if snippet:
                    usage_context = UsageContext(
                        snippet=snippet,
                        usage_type=usage_type,
                        referenced_symbol=symbol_name,
                        defining_file=defining_file
                    )
                    usage_contexts.append(usage_context)

        # Cache the result
        self._update_cache(cache_key, usage_contexts)

        return usage_contexts

```

### 21. integrate_prioritization_with_algorithm
- **File**: fix_map_slicing.py
- **Priority**: critical
- **Relevance Score**: 2.541153846153846

```python
def integrate_prioritization_with_algorithm():
    """Integrate the prioritization with the ranking algorithm"""
    
    print("\n🔗 Integrating Prioritization with Ranking Algorithm")
    print("=" * 55)
    
    repomap_path = Path("aider-main/aider/repomap.py")
    
    # Read the current content
    with open(repomap_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find where ranked_tags is used in the binary search
    old_usage = "ranked_tags = self.get_ranked_tags("
    new_usage = """ranked_tags = self.get_ranked_tags(
            chat_fnames,
            other_fnames,
            mentioned_fnames,
            mentioned_idents,
            progress=spin.step,
        )
        
        # Apply intelligent prioritization for better file selection
        ranked_tags = apply_intelligent_ranking(ranked_tags, max_map_tokens)"""
    
    # Replace the usage
    if old_usage in content:
        # Find the complete function call
        start_idx = content.find(old_usage)
        if start_idx != -1:
            # Find the end of the function call
            paren_count = 0
            end_idx = start_idx
            for i, char in enumerate(content[start_idx:]):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_idx = start_idx + i + 1
                        break
            
            # Replace the function call
            old_call = content[start_idx:end_idx]
            updated_content = content.replace(old_call, new_usage)
            
            # Write the updated content
            with open(repomap_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ Integrated intelligent prioritization with ranking algorithm")
            return True
    
    print("❌ Could not find ranked_tags usage to modify")
    return False


```

### 22. extract_dependency_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5319230769230767

```python
    def extract_dependency_contexts(self, project_path: str, primary_file: str,
                                  context_window: int = 10) -> Dict[str, List[CodeSnippet]]:
        """
        Extract focused code snippets from all dependencies of a primary file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            context_window: Default number of lines to include above and below each reference

        Returns:
            Dictionary mapping file paths to lists of code snippets
        """
        cache_key = f"dependency_contexts:{project_path}:{primary_file}:{context_window}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # Get files that import the primary file
        importing_files = self.aider_service.get_files_that_import(project_path, primary_file)

        # Combine all related files
        related_files = list(set(imported_files + importing_files))

        # Initialize result dictionary
        result = {file_path: [] for file_path in related_files + [primary_file]}

        # Process each related file
        for file_path in related_files:
            # Get symbol references between the files
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, file_path)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in symbol_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is defined in the target file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.DEFINITION, symbol)

                        if snippet:
                            result[file_path].append(snippet)

            # Also check references from the related file to the primary file
            reverse_refs = self.aider_service.get_symbol_references_between_files(
                project_path, file_path, primary_file)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in reverse_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is used in the source file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.USAGE, symbol)

                        if snippet:
                            result[file_path].append(snippet)

        # Cache the result
        self._update_cache(cache_key, result)

        return result

```

### 23. _update_cache
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5285897435897438

```python
    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value and timestamp."""
        self.context_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

```

### 24. extract_enhanced_context
- **File**: enhanced_surgical_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.521153846153846

```python
    def extract_enhanced_context(self, project_path: str, symbol_name: str, file_path: str) -> Optional[EnhancedCodeContext]:
        """
        Extract enhanced code context for a symbol, combining complete implementation and usage contexts.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to extract
            file_path: Path to the file containing the symbol

        Returns:
            EnhancedCodeContext object or None if extraction failed
        """
        cache_key = f"enhanced_context:{project_path}:{file_path}:{symbol_name}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get symbol information
        symbols = self.file_extractor.get_symbols_in_file(project_path, file_path)
        if not symbols:
            return None

        target_symbol = next((s for s in symbols if s.name == symbol_name), None)
        if not target_symbol:
            return None

        # Extract complete implementation
        extraction_range = self.file_extractor.extract_symbol_range(symbol_name, file_path, project_path)
        implementation = self.file_extractor.extract_symbol_content(symbol_name, file_path, project_path)

        # Extract essential imports
        essential_imports = self._extract_essential_imports(project_path, file_path)

        # Extract containing class signature if it's a method
        containing_class = self._extract_containing_class(project_path, file_path, target_symbol)

        # Extract usage contexts
        usage_contexts = self.context_extractor.extract_usage_contexts(project_path, symbol_name, file_path)

        # Create the enhanced context
        context = EnhancedCodeContext(
            symbol_name=symbol_name,
            file_path=file_path,
            symbol_type=target_symbol.symbol_type,
            complete_implementation=implementation,
            extraction_range=extraction_range,
            essential_imports=essential_imports,
            containing_class_signature=containing_class,
            usage_contexts=usage_contexts
        )

        # Cache the result
        self._update_cache(cache_key, context)

        return context

```

### 25. process_ir_context_requests
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.516153846153846

```python
    def process_ir_context_requests(self, content, user_message):
        """
        Process any IR context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if an IR context request was detected,
            or (content, None) if no IR context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("IR_CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Look for IR_CONTEXT_REQUEST pattern
        patterns = [
            r'\{IR_CONTEXT_REQUEST:\s*(.*?)\}\}',
            r'\{IR_CONTEXT_REQUEST:\s*(.*?)\}',
            r'\{IR_CONTEXT_REQUEST:\s*(.*)'
        ]

        match = None
        ir_request_pattern = None
        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                ir_request_pattern = pattern
                break

        if not match:
            return content, None

        try:
            # Extract and parse the IR context request
            request_json_str = match.group(1).strip()
            request_json = json.loads(request_json_str)

            # Create IRContextRequest object
            ir_request = IRContextRequest(
                user_query=request_json.get("user_query", user_message),
                task_description=request_json.get("task_description", user_message),
                task_type=request_json.get("task_type", "general_analysis"),
                focus_entities=request_json.get("focus_entities", []),
                max_tokens=request_json.get("max_tokens", 8000),
                include_ir_slices=request_json.get("include_ir_slices", True),
                include_code_context=request_json.get("include_code_context", True)
            )

            # Log the IR context request
            self.io.tool_output(f"Processing IR context request: {ir_request.task_description}")

            # Initialize context request handler if not already done
            if not hasattr(self, 'context_request_handler') or self.context_request_handler is None:
                try:
                    from ..context_request import ContextRequestHandler

                    # Determine the correct project path
                    import os
                    project_path = os.getcwd()

                    # If current working directory is the aider repository itself,
                    # and we have files in the chat, use the common root of those files
                    if (os.path.basename(project_path) == 'aider' and
                        os.path.exists(os.path.join(project_path, 'aider-main')) and
                        (self.abs_fnames or self.abs_read_only_fnames)):
                        # Use the common root of the files in the chat
                        if self.abs_fnames:
                            project_path = utils.find_common_root(self.abs_fnames)
                        elif self.abs_read_only_fnames:
                            project_path = utils.find_common_root(self.abs_read_only_fnames)

                    self.context_request_handler = ContextRequestHandler(project_path)
                except Exception as e:
                    self.io.tool_error(f"Failed to initialize context request handler: {e}")
                    return content, None

            # Clean up the content by removing the IR context request
            cleaned_content = re.sub(ir_request_pattern, "", content, flags=re.DOTALL).strip()

            # Process the IR context request
            result = self.context_request_handler.process_ir_context_request(ir_request)

            if "error" in result:
                self.io.tool_error(f"Error processing IR context request: {result['error']}")
                return cleaned_content, None

            # Display the IR context response to the user
            self._display_ir_context_response_to_user(result, ir_request)

            # Create augmented prompt for the LLM
            augmented_prompt = self._create_ir_context_augmented_prompt(result, user_message)

            return cleaned_content, augmented_prompt

        except json.JSONDecodeError as e:
            self.io.tool_error(f"Invalid JSON in IR context request: {e}")
            return content, None
        except Exception as e:
            self.io.tool_error(f"Error processing IR context request: {e}")
            return content, None

```

### 26. _extract_definition_info
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.51525641025641

```python
    def _extract_definition_info(self, content: str, line_num: int, symbol_name: str) -> Tuple[DefinitionType, Optional[str], Optional[str]]:
        """
        Extract information about a symbol definition.

        Args:
            content: The file content
            line_num: The line number where the symbol is defined (1-based)
            symbol_name: The name of the symbol

        Returns:
            A tuple of (definition_type, signature, docstring)
        """
        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return DefinitionType.UNKNOWN, None, None

        line = lines[line_num - 1]

        # Determine definition type
        if re.search(rf'class\s+{re.escape(symbol_name)}', line):
            definition_type = DefinitionType.CLASS
            # Extract class signature
            signature = line.strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'def\s+{re.escape(symbol_name)}', line):
            # Check if this is a method (indented and/or has self parameter)
            if line.startswith(' ') or re.search(r'\(\s*self\s*[,)]', line):
                definition_type = DefinitionType.METHOD
            else:
                definition_type = DefinitionType.FUNCTION

            # Extract function signature
            signature = line.strip()
            if not signature.endswith(':'):
                # Look for continuation lines
                i = line_num
                while i < len(lines) and not signature.endswith(':'):
                    i += 1
                    if i < len(lines):
                        signature += ' ' + lines[i].strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'{re.escape(symbol_name)}\s*=', line):
            # Check if this is a constant (all uppercase)
            if symbol_name.isupper():
                definition_type = DefinitionType.CONSTANT
            else:
                definition_type = DefinitionType.VARIABLE

            # Extract variable assignment
            signature = line.strip()

            return definition_type, signature, None

        else:
            return DefinitionType.UNKNOWN, None, None

```

### 27. get_focused_inheritance_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5128205128205128

```python
    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> Dict:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            A dictionary with inheritance context information
        """
        extractor = self._get_context_extractor()
        inheritance_map = extractor.get_focused_inheritance_context(project_path, class_name, file_path)

        # Convert to a serializable dictionary
        result = {
            'class_name': inheritance_map.class_name,
            'file_path': inheritance_map.file_path,
            'base_classes': [],
            'derived_classes': [],
            'implementation_contexts': []
        }

        # Add base classes
        for base_ctx in inheritance_map.base_classes:
            result['base_classes'].append({
                'file_path': base_ctx.snippet.file_path,
                'start_line': base_ctx.snippet.start_line,
                'end_line': base_ctx.snippet.end_line,
                'content': base_ctx.snippet.content,
                'symbol_name': base_ctx.snippet.symbol_name,
                'signature': base_ctx.signature,
                'docstring': base_ctx.docstring
            })

        # Add derived classes
        for derived_ctx in inheritance_map.derived_classes:
            result['derived_classes'].append({
                'file_path': derived_ctx.snippet.file_path,
                'start_line': derived_ctx.snippet.start_line,
                'end_line': derived_ctx.snippet.end_line,
                'content': derived_ctx.snippet.content,
                'symbol_name': derived_ctx.snippet.symbol_name,
                'signature': derived_ctx.signature,
                'docstring': derived_ctx.docstring
            })

        # Add implementation contexts
        for impl_ctx in inheritance_map.implementation_contexts:
            result['implementation_contexts'].append({
                'file_path': impl_ctx.file_path,
                'start_line': impl_ctx.start_line,
                'end_line': impl_ctx.end_line,
                'content': impl_ctx.content,
                'symbol_name': impl_ctx.symbol_name,
                'surrounding_function': impl_ctx.surrounding_function
            })

        return result

```

### 28. extract_definition_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5119230769230767

```python
    def extract_definition_contexts(self, project_path: str, symbols: List[str],
                                   source_file: str) -> List[DefinitionContext]:
        """
        For symbols referenced by a file, extract their definitions with surrounding context.

        Args:
            project_path: Path to the project root
            symbols: List of symbol names to find definitions for
            source_file: Path to the file that references these symbols

        Returns:
            A list of DefinitionContext objects
        """
        cache_key = f"definition_contexts:{project_path}:{','.join(symbols)}:{source_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        definition_contexts = []

        for symbol in symbols:
            # Find the file that defines this symbol
            defining_file = self.aider_service.find_file_defining_symbol(project_path, symbol)
            if not defining_file:
                continue

            # Find line numbers where the symbol is defined
            line_numbers = self._find_definition_line_numbers(project_path, defining_file, symbol)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, defining_file)
                if not content:
                    continue

                # Determine the definition type
                definition_type, signature, docstring = self._extract_definition_info(
                    content, line_num, symbol)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1,
                    "class" if definition_type == DefinitionType.CLASS else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, defining_file, line_num, adjusted_window,
                    ContextType.DEFINITION, symbol)

                if snippet:
                    definition_context = DefinitionContext(
                        snippet=snippet,
                        definition_type=definition_type,
                        signature=signature,
                        docstring=docstring
                    )
                    definition_contexts.append(definition_context)

        # Cache the result
        self._update_cache(cache_key, definition_contexts)

        return definition_contexts

```

### 29. build
- **File**: context_bundle_builder.py
- **Priority**: critical
- **Relevance Score**: 2.506923076923077

```python
    def build(self, task: str, task_type: str = "general_analysis", 
              focus_entities: Optional[List[str]] = None) -> EnhancedContextBundle:
        """
        Build an enhanced context bundle with detailed scoring.
        
        Args:
            task: Task description
            task_type: Type of analysis task
            focus_entities: Optional focus entities
            
        Returns:
            EnhancedContextBundle with detailed metadata
        """
        print(f"🏗️ Building enhanced context bundle for: {task}")
        
        # Step 1: Score all entities
        scored_entities = self._score_all_entities(task, task_type, focus_entities)
        
        # Step 2: Select entities within budget
        selected_entities = self._select_entities_within_budget(scored_entities)
        
        # Step 3: Build enhanced context bundle
        context_bundle = self._build_enhanced_bundle(
            task, task_type, selected_entities, scored_entities
        )
        
        print(f"✅ Enhanced context bundle built:")
        print(f"   Selected entities: {len(context_bundle.context_bundle)}")
        print(f"   Token estimate: {context_bundle.token_estimate}")
        print(f"   Score distribution: {context_bundle.score_distribution}")
        
        return context_bundle
    
```

### 30. _update_cache
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.5035897435897434

```python
    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

```

### 31. get_focused_inheritance_context
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.503076923076923

```python
    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> InheritanceContextMap:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            An InheritanceContextMap object
        """
        # Get base classes
        base_classes_info = self.aider_service.get_base_classes_of(project_path, class_name, file_path)

        # Get derived classes
        derived_classes_info = self.aider_service.get_derived_classes_of(project_path, class_name, file_path)

        # Extract definition contexts for base classes
        base_class_contexts = []
        for base_class_info in base_classes_info:
            base_class_name = base_class_info.get('class_name')
            base_class_file = base_class_info.get('file_path')

            if base_class_name and base_class_file:
                # Find line numbers where the base class is defined
                line_numbers = self._find_definition_line_numbers(project_path, base_class_file, base_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, base_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, base_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, base_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, base_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        base_class_contexts.append(definition_context)

        # Extract definition contexts for derived classes
        derived_class_contexts = []
        for derived_class_info in derived_classes_info:
            derived_class_name = derived_class_info.get('class_name')
            derived_class_file = derived_class_info.get('file_path')

            if derived_class_name and derived_class_file:
                # Find line numbers where the derived class is defined
                line_numbers = self._find_definition_line_numbers(project_path, derived_class_file, derived_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, derived_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, derived_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, derived_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, derived_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        derived_class_contexts.append(definition_context)

        # Extract implementation contexts (method overrides, etc.)
        implementation_contexts = []

        # Find methods in the class
        class_line_numbers = self._find_definition_line_numbers(project_path, file_path, class_name)
        if class_line_numbers:
            content = self._read_file_content(project_path, file_path)
            if content:
                lines = content.splitlines()
                class_line = class_line_numbers[0] - 1  # Convert to 0-based

                # Find the indentation level of the class
                match = re.match(r'^(\s*)', lines[class_line])
                class_indent = match.group(1) if match else ''
                method_indent = class_indent + '    '  # Assuming 4 spaces for indentation

                # Look for method definitions
                for i in range(class_line + 1, len(lines)):
                    line = lines[i]
                    if line.startswith(method_indent) and 'def ' in line:
                        # This is a method definition
                        method_match = re.search(r'def\s+(\w+)', line)
                        if method_match:
                            method_name = method_match.group(1)

                            # Check if this method overrides a base class method
                            for base_class_info in base_classes_info:
                                base_class_name = base_class_info.get('class_name')
                                base_class_file = base_class_info.get('file_path')

                                if base_class_name and base_class_file:
                                    # Check if the base class has this method
                                    base_content = self._read_file_content(project_path, base_class_file)
                                    if base_content and re.search(rf'def\s+{method_name}\s*\(', base_content):
                                        # This is an overridden method
                                        adjusted_window = self._determine_context_window_size(
                                            content, i, "method")

                                        # Extract the code snippet
                                        snippet = self._extract_code_snippet(
                                            project_path, file_path, i + 1, adjusted_window,
                                            ContextType.IMPLEMENTATION, method_name)

                                        if snippet:
                                            implementation_contexts.append(snippet)

        return InheritanceContextMap(
            class_name=class_name,
            file_path=file_path,
            base_classes=base_class_contexts,
            derived_classes=derived_class_contexts,
            implementation_contexts=implementation_contexts
        )




```

### 32. _enhance_with_dependency_context
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.5016666666666665

```python
    def _enhance_with_dependency_context(self, selected_entities: List[ContextEntity],
                                       task_type: TaskType) -> List[ContextEntity]:
        """Enhance the selection by adding important dependency context."""
        enhanced = selected_entities.copy()
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in selected_entities}

        # Calculate remaining token budget
        current_tokens = sum(e.token_estimate for e in enhanced)
        remaining_tokens = self.max_tokens - current_tokens

        # Add critical dependencies
        for entity in selected_entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"

            # Add entities that this one depends on (calls)
            dependencies = self.dependency_graph.get(entity_name, set())
            for dep_name in dependencies:
                if dep_name not in selected_names and dep_name in self.entity_map:
                    dep_entity = self.entity_map[dep_name]
                    if (remaining_tokens >= dep_entity.token_estimate and
                        dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(dep_entity)
                        selected_names.add(dep_name)
                        remaining_tokens -= dep_entity.token_estimate

            # Add entities that depend on this one (reverse dependencies)
            reverse_deps = self.reverse_dependency_graph.get(entity_name, set())
            for rev_dep_name in reverse_deps:
                if rev_dep_name not in selected_names and rev_dep_name in self.entity_map:
                    rev_dep_entity = self.entity_map[rev_dep_name]
                    if (remaining_tokens >= rev_dep_entity.token_estimate and
                        rev_dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(rev_dep_entity)
                        selected_names.add(rev_dep_name)
                        remaining_tokens -= rev_dep_entity.token_estimate

        return enhanced

```

### 33. _display_ir_context_response_to_user
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.498974358974359

```python
    def _display_ir_context_response_to_user(self, result, ir_request):
        """
        Display the IR_CONTEXT_REQUEST response to the user in the chat.

        Args:
            result: The IR context result
            ir_request: The original IR context request
        """
        if not result:
            return

        # Create a user-friendly header
        header = f"# 🧠 IR_CONTEXT_REQUEST Response\n\n"
        header += f"**Task**: {ir_request.task_description}  \n"
        header += f"**Type**: {ir_request.task_type}  \n"
        header += f"**Max Tokens**: {ir_request.max_tokens}  \n"
        header += f"**Focus Entities**: {', '.join(ir_request.focus_entities) if ir_request.focus_entities else 'None'}  \n\n"

        # Add summary statistics
        summary = result.get("summary", {})
        header += f"**Results Summary**:  \n"
        header += f"- Selected Entities: {result.get('context_bundle', {}).get('total_entities', 0)}  \n"
        header += f"- Token Utilization: {summary.get('token_utilization', 'N/A')}  \n"
        header += f"- Critical Entities: {summary.get('critical_entities', 0)}  \n"
        header += f"- Files Involved: {summary.get('files_involved', 0)}  \n\n"

        # Add selection rationale
        rationale = result.get("context_bundle", {}).get("selection_rationale", "")
        if rationale:
            header += f"**Selection Rationale**: {rationale}  \n\n"

        # Format the IR slices and code context
        display_content = header

        # Add IR slices if available
        if result.get("ir_slices"):
            display_content += "## IR Analysis Slices\n\n```json\n"
            ir_slices_preview = result["ir_slices"][:3]  # Show first 3 for preview
            display_content += json.dumps(ir_slices_preview, indent=2)
            if len(result["ir_slices"]) > 3:
                display_content += f"\n... and {len(result['ir_slices']) - 3} more entities"
            display_content += "\n```\n\n"

        # Add code context if available
        if result.get("code_context"):
            display_content += "## Code Context\n\n"
            for i, code_ctx in enumerate(result["code_context"][:3]):  # Show first 3
                display_content += f"### {code_ctx['entity_name']} ({code_ctx['file_path']})\n"
                display_content += f"**Priority**: {code_ctx['priority']} | **Relevance**: {code_ctx['relevance_score']:.2f}\n\n"
                display_content += "```python\n" + code_ctx["source_code"][:500] + "\n```\n\n"

            if len(result["code_context"]) > 3:
                display_content += f"*... and {len(result['code_context']) - 3} more code contexts*\n\n"

        display_content += "*Complete IR context and code sent to LLM for analysis*\n"

        # Store in reflected_message to get the same formatting as other requests
        if hasattr(self, 'reflected_message') and self.reflected_message:
            self.reflected_message = display_content + "\n\n---\n\n" + self.reflected_message
        else:
            self.reflected_message = display_content

```

### 34. _get_from_cache
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.cache[cache_key]
        return None

```

### 35. get_contextual_dependencies
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4966666666666666

```python
    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> Dict:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A dictionary with contextual dependency information
        """
        extractor = self._get_context_extractor()
        context_map = extractor.get_contextual_dependencies(project_path, primary_file, max_snippets)

        # Convert to a serializable dictionary
        result = {
            'primary_file': context_map.primary_file,
            'related_files': context_map.related_files,
            'usage_contexts': [],
            'definition_contexts': []
        }

        # Add usage contexts
        for usage_ctx in context_map.usage_contexts:
            result['usage_contexts'].append({
                'file_path': usage_ctx.snippet.file_path,
                'start_line': usage_ctx.snippet.start_line,
                'end_line': usage_ctx.snippet.end_line,
                'content': usage_ctx.snippet.content,
                'symbol_name': usage_ctx.snippet.symbol_name,
                'usage_type': usage_ctx.usage_type.value,
                'surrounding_function': usage_ctx.snippet.surrounding_function
            })

        # Add definition contexts
        for def_ctx in context_map.definition_contexts:
            result['definition_contexts'].append({
                'file_path': def_ctx.snippet.file_path,
                'start_line': def_ctx.snippet.start_line,
                'end_line': def_ctx.snippet.end_line,
                'content': def_ctx.snippet.content,
                'symbol_name': def_ctx.snippet.symbol_name,
                'definition_type': def_ctx.definition_type.value,
                'signature': def_ctx.signature,
                'docstring': def_ctx.docstring
            })

        return result

```

### 36. get_related_entities
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.4855128205128207

```python
    def get_related_entities(self, entity_name: str, max_depth: int = 2) -> List[str]:
        """Get entities related to the given entity through dependencies."""
        related = set()
        visited = set()

```

### 37. main
- **File**: context_request_demo.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    """
    Demo script for the context request functionality.
    """
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\n=== LLM Instructions ===")
    print(integration.get_llm_instructions())
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="SurgicalFileExtractor.extract_symbol_content",
                file_hint="surgical_file_extractor.py"
            ),
            SymbolRequest(
                type="class_definition",
                name="SurgicalFileExtractor",
                file_hint="surgical_file_extractor.py"
            )
        ],
        reason_for_request="To understand how the surgical file extractor works"
    )
    
    # Process the context request
    print("\n=== Processing Context Request ===")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt ===")
    print(augmented_prompt)
    
    # Test with a real LLM response
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: { 
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"},
    {"type": "class_definition", "name": "SurgicalFileExtractor", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    
    # Detect the context request
    print("\n=== Detecting Context Request ===")
    detected_request = integration.detect_context_request(llm_response)
    if detected_request:
        print(f"Detected request: {integration.get_context_request_summary(detected_request)}")
    else:
        print("No context request detected")


if __name__ == "__main__":
    main()
```

### 38. get_contextual_dependencies
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> ContextualDependencyMap:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A ContextualDependencyMap object
        """
        # Extract dependency contexts
        dependency_contexts = self.extract_dependency_contexts(project_path, primary_file)

        # Get all symbols defined in the primary file
        symbols_in_primary = self.aider_service.get_symbols_defined_in_file(project_path, primary_file)

        # Flatten the symbols list
        all_symbols = []
        for symbol_type, symbols in symbols_in_primary.items():
            all_symbols.extend(symbols)

        # Get usage contexts for symbols defined in the primary file
        usage_contexts = []
        for symbol in all_symbols:
            symbol_usages = self.extract_usage_contexts(project_path, symbol, primary_file)
            usage_contexts.extend(symbol_usages)

        # Get definition contexts for symbols used in the primary file
        definition_contexts = []

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # For each imported file, get symbols referenced from the primary file
        for imported_file in imported_files:
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, imported_file)

            # Flatten the symbols list
            referenced_symbols = []
            for symbol_type, symbols in symbol_refs.items():
                referenced_symbols.extend(symbols)

            # Get definition contexts for these symbols
            if referenced_symbols:
                symbol_definitions = self.extract_definition_contexts(
                    project_path, referenced_symbols, primary_file)
                definition_contexts.extend(symbol_definitions)

        # Sort contexts by relevance and limit to max_snippets
        usage_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)
        definition_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)

        # Ensure we have a balanced mix of usage and definition contexts
        max_each = max_snippets // 2
        usage_contexts = usage_contexts[:max_each]
        definition_contexts = definition_contexts[:max_each]

        # Get related files
        related_files = list(set(
            [ctx.snippet.file_path for ctx in usage_contexts] +
            [ctx.snippet.file_path for ctx in definition_contexts]
        ))

        return ContextualDependencyMap(
            primary_file=primary_file,
            usage_contexts=usage_contexts,
            definition_contexts=definition_contexts,
            related_files=related_files
        )
```

## INSTRUCTIONS FOR LLM
You have been provided with intelligent context selection based on IR (Intermediate Representation) analysis and ICD (Intelligent Code Discovery).

### Context Quality
- This context was selected using task-specific algorithms
- Entities are prioritized by criticality and relevance
- Dependencies and risk factors have been analyzed
- Token budget has been optimized for maximum value

### Your Task
Please analyze the provided context and respond to the user's query: "How does the intelligent context selection algorithm work? I need to understand the implementation."

Use the IR analysis data to understand:
1. **Entity Criticality**: Focus on high-criticality components
2. **Change Risk**: Consider risk factors when making recommendations
3. **Dependencies**: Understand how components interact
4. **Side Effects**: Be aware of potential impacts
5. **Error Patterns**: Identify potential issues

Provide a comprehensive, accurate response based on this intelligent context selection.
