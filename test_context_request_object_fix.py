#!/usr/bin/env python3
"""
Test script to verify that the ContextRequest object attribute access fix works.
This tests that the system correctly accesses ContextRequest attributes instead of using .get().
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_context_request_object_access():
    """Test that ContextRequest objects are accessed correctly."""
    print("🧪 Testing ContextRequest Object Access Fix")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequest, SymbolRequest

        # Create a ContextRequest object
        context_request = ContextRequest(
            original_user_query_context="Testing object access",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="test_function",
                    file_hint="test.py"
                )
            ],
            reason_for_request="Testing attribute access"
        )

        print("🔍 Testing ContextRequest object attribute access...")

        # Test direct attribute access (this should work)
        success_criteria = []

        try:
            original_context = context_request.original_user_query_context
            success_criteria.append((True, f"✅ original_user_query_context: '{original_context}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ original_user_query_context failed: {e}"))

        try:
            reason = context_request.reason_for_request
            success_criteria.append((True, f"✅ reason_for_request: '{reason}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ reason_for_request failed: {e}"))

        try:
            symbols = context_request.symbols_of_interest
            success_criteria.append((True, f"✅ symbols_of_interest: {len(symbols)} symbols"))
        except Exception as e:
            success_criteria.append((False, f"❌ symbols_of_interest failed: {e}"))

        # Test that .get() method doesn't exist (this should fail)
        try:
            context_request.get('original_user_query_context', '')
            success_criteria.append((False, "❌ .get() method should not exist on ContextRequest"))
        except AttributeError:
            success_criteria.append((True, "✅ .get() method correctly doesn't exist"))
        except Exception as e:
            success_criteria.append((False, f"❌ Unexpected error testing .get(): {e}"))

        # Test hasattr checks (used in our fix)
        try:
            has_symbols = hasattr(context_request, 'symbols_of_interest')
            has_reason = hasattr(context_request, 'reason_for_request')
            has_context = hasattr(context_request, 'original_user_query_context')
            success_criteria.append((has_symbols and has_reason and has_context, 
                                   f"✅ hasattr checks work: symbols={has_symbols}, reason={has_reason}, context={has_context}"))
        except Exception as e:
            success_criteria.append((False, f"❌ hasattr checks failed: {e}"))

        # Print results
        passed = 0
        total = len(success_criteria)

        for condition, message in success_criteria:
            print(f"  {message}")
            if condition:
                passed += 1

        print(f"\n📊 ContextRequest object access: {passed}/{total} tests passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing ContextRequest object access: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_symbol_request_object_access():
    """Test that SymbolRequest objects are accessed correctly."""
    print("\n🧪 Testing SymbolRequest Object Access")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import SymbolRequest

        # Create a SymbolRequest object
        symbol_request = SymbolRequest(
            type="method_definition",
            name="test_function",
            file_hint="test.py"
        )

        print("🔍 Testing SymbolRequest object attribute access...")

        # Test direct attribute access
        success_criteria = []

        try:
            symbol_type = symbol_request.type
            success_criteria.append((True, f"✅ type: '{symbol_type}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ type failed: {e}"))

        try:
            name = symbol_request.name
            success_criteria.append((True, f"✅ name: '{name}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ name failed: {e}"))

        try:
            file_hint = symbol_request.file_hint
            success_criteria.append((True, f"✅ file_hint: '{file_hint}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ file_hint failed: {e}"))

        # Test hasattr checks
        try:
            has_name = hasattr(symbol_request, 'name')
            has_type = hasattr(symbol_request, 'type')
            has_file_hint = hasattr(symbol_request, 'file_hint')
            success_criteria.append((has_name and has_type and has_file_hint,
                                   f"✅ hasattr checks work: name={has_name}, type={has_type}, file_hint={has_file_hint}"))
        except Exception as e:
            success_criteria.append((False, f"❌ hasattr checks failed: {e}"))

        # Print results
        passed = 0
        total = len(success_criteria)

        for condition, message in success_criteria:
            print(f"  {message}")
            if condition:
                passed += 1

        print(f"\n📊 SymbolRequest object access: {passed}/{total} tests passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing SymbolRequest object access: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_code_pattern():
    """Test the specific code pattern that was fixed."""
    print("\n🧪 Testing Fixed Code Pattern")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequest, SymbolRequest

        # Create a ContextRequest object like the one that was causing the error
        context_request = ContextRequest(
            original_user_query_context="how does the close_position_based_on_conditions function work?",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"
                ),
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"
                )
            ],
            reason_for_request="analyze the implementation"
        )

        print("🔍 Testing the fixed code pattern...")

        # Test the old pattern (should fail)
        old_pattern_works = False
        try:
            symbols_of_interest = context_request.get('symbols_of_interest', [])
            old_pattern_works = True
        except AttributeError:
            pass  # Expected to fail

        # Test the new pattern (should work)
        new_pattern_works = False
        requested_symbols = []
        try:
            symbols_of_interest = context_request.symbols_of_interest if hasattr(context_request, 'symbols_of_interest') else []
            for symbol in symbols_of_interest:
                if hasattr(symbol, 'name'):
                    requested_symbols.append(symbol.name)
            new_pattern_works = True
        except Exception as e:
            print(f"New pattern failed: {e}")

        # Test reason extraction
        reason_extraction_works = False
        reason = ""
        try:
            if hasattr(context_request, 'reason_for_request'):
                reason = context_request.reason_for_request
            elif hasattr(context_request, 'original_user_query_context'):
                reason = context_request.original_user_query_context
            reason_extraction_works = True
        except Exception as e:
            print(f"Reason extraction failed: {e}")

        success_criteria = [
            (not old_pattern_works, "✅ Old .get() pattern correctly fails"),
            (new_pattern_works, "✅ New attribute access pattern works"),
            (len(requested_symbols) == 2, f"✅ Extracted {len(requested_symbols)} symbol names"),
            (reason_extraction_works, f"✅ Reason extraction works: '{reason[:50]}...'"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, message in success_criteria:
            print(f"  {message}")
            if condition:
                passed += 1

        if requested_symbols:
            print(f"  📋 Extracted symbols: {', '.join(requested_symbols)}")

        print(f"\n📊 Fixed code pattern: {passed}/{total} tests passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing fixed code pattern: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all ContextRequest object access tests."""
    print("🚀 Testing ContextRequest Object Access Fix")
    print("=" * 80)

    tests = [
        test_context_request_object_access,
        test_symbol_request_object_access,
        test_fixed_code_pattern,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 ContextRequest object access fix is working correctly!")
        print("\n📋 The fix ensures:")
        print("  1. ✅ ContextRequest objects use attribute access, not .get()")
        print("  2. ✅ SymbolRequest objects use attribute access correctly")
        print("  3. ✅ hasattr() checks work properly for safety")
        print("  4. ✅ The specific error 'ContextRequest' object has no attribute 'get' is resolved")
        print("  5. ✅ Symbol extraction works with the new pattern")
    else:
        print("⚠️  Some ContextRequest object access features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
