"""
File request validator for checking if requested files likely contain the code elements mentioned.
"""

import os
import re
from typing import List, Dict, Tu<PERSON>, Set, Optional

from .search_repo import search_repo_for_symbol, extract_code_elements, smart_search_fallback


class FileRequestValidator:
    """
    Validates file requests by checking if the requested files likely contain
    the code elements mentioned in the reason.
    """

    def __init__(self, root_dir: str, io=None):
        """
        Initialize the validator.

        Args:
            root_dir: The root directory of the repository
            io: The IO object for output messages
        """
        self.root_dir = root_dir
        self.io = io
        self.search_cache = {}  # Cache search results to avoid redundant searches
        self.last_search_details = ""  # Store details of the last search
        self.last_search_results = []  # Store the results of the last search

    def validate_file_request(self, file_path: str, reason: str, all_files: List[str]) -> Tuple[bool, List[str], List[str]]:
        """
        Validate a file request by checking if the file likely contains the code elements mentioned.

        Args:
            file_path: The path of the requested file
            reason: The reason for requesting the file
            all_files: List of all available files in the repository

        Returns:
            Tuple of (is_valid, suggested_files, code_elements)
            - is_valid: Whether the file request is valid
            - suggested_files: List of suggested alternative files
            - code_elements: List of code elements mentioned in the reason
        """
        # Normalize file path
        normalized_path = file_path.replace('\\', '/').strip()

        # Check if file exists
        if normalized_path not in all_files:
            if self.io:
                self.io.tool_warning(f"Requested file '{normalized_path}' does not exist in the repository.")

                # Try to find similar file names to suggest
                similar_files = self._find_similar_files(normalized_path, all_files)
                if similar_files:
                    self.io.tool_warning(f"Did you mean one of these files? {', '.join(similar_files)}")

            return False, [], []

        # Extract code elements from reason
        code_elements = extract_code_elements(reason)

        # If no code elements mentioned, the request is valid
        if not code_elements:
            return True, [], []

        # Search for each code element
        suggested_files = []
        found_elements = []

        for element in code_elements:
            # Check if we already searched for this element
            if element in self.search_cache:
                results = self.search_cache[element]
            else:
                results = search_repo_for_symbol(self.root_dir, element)
                self.search_cache[element] = results

            if results:
                found_elements.append(element)

                # Check if the requested file contains this element
                element_in_file = any(result['file'] == normalized_path for result in results)

                if not element_in_file:
                    # Find files that contain this element (prioritize definitions)
                    definition_files = [result['file'] for result in results if result['type'] == 'definition']
                    reference_files = [result['file'] for result in results if result['type'] == 'reference']

                    # Add definition files first, then reference files
                    for file in definition_files + reference_files:
                        if file not in suggested_files and file != normalized_path:
                            suggested_files.append(file)

                    if self.io:
                        if definition_files:
                            self.io.tool_warning(f"Code element '{element}' was not found in '{normalized_path}', but is defined in: {', '.join(definition_files)}")
                        elif reference_files:
                            self.io.tool_warning(f"Code element '{element}' was not found in '{normalized_path}', but is referenced in: {', '.join(reference_files)}")

        # If we found code elements but none of them are in the requested file,
        # and we have suggestions, the request might be invalid
        if found_elements and suggested_files:
            return False, suggested_files, found_elements

        # If we didn't find any code elements, try a smart search fallback
        if not found_elements and reason:
            smart_search_files = self.smart_search_for_query(reason, all_files)
            if smart_search_files:
                # We found some potentially relevant files through smart search
                return False, smart_search_files, code_elements

        # Otherwise, the request is valid
        return True, [], found_elements

    def get_suggested_files_message(self, suggested_files: List[str], code_elements: List[str]) -> str:
        """
        Generate a message about suggested files.

        Args:
            suggested_files: List of suggested files
            code_elements: List of code elements

        Returns:
            Message about suggested files
        """
        if not suggested_files:
            return ""

        # Limit to top 2 files to prevent context overload
        limited_files = suggested_files[:2]
        files_str = ", ".join([f"'{file}'" for file in limited_files])

        # Check if these are likely filename matches or content-based matches
        # We'll assume they're filename matches if code_elements is empty and we have a small number of suggestions
        if not code_elements and len(suggested_files) <= 5:
            return f"The requested file was not found. Based on filename similarity, I've added these files instead: {files_str}"
        elif code_elements:
            # If we have specific code elements, mention them
            elements_str = ", ".join([f"'{element}'" for element in code_elements])
            return f"Based on your request for {elements_str}, I've added these relevant files instead: {files_str}"
        else:
            # If we don't have specific code elements (smart search fallback)
            return f"Based on your request, I've added these relevant files instead: {files_str}"

    def smart_search_for_query(self, query: str, all_files: List[str]) -> List[str]:
        """
        Perform a smart search for a query when no specific code elements are found.

        Args:
            query: The search query
            all_files: List of all available files in the repository

        Returns:
            List of suggested files
        """
        if self.io:
            self.io.tool_output(f"No specific code elements found. Performing smart search for: '{query}'")

        # Clear previous search details
        self.last_search_details = ""

        # Perform smart search
        search_results = smart_search_fallback(self.root_dir, query, self.io)

        # Store the full search results
        self.last_search_results = search_results

        if not search_results:
            if self.io:
                self.io.tool_warning(f"Smart search found no relevant files for '{query}'")
            self.last_search_details = f"- Search query: '{query}'\n- No relevant files found"
            return []

        # Extract file paths from search results
        suggested_files = []
        search_details = []
        search_details.append(f"- Search query: '{query}'")

        # Include total number of results found
        search_details.append(f"- Total results found: {len(search_results)}")

        # Note about file limit
        if len(search_results) > 2:
            search_details.append(f"- Note: Only the top 2 most relevant files will be added to the conversation to prevent context overload")

        for result in search_results:
            file_path = result['file']
            if file_path in all_files and file_path not in suggested_files:
                suggested_files.append(file_path)
                search_details.append(f"- Found '{file_path}' (relevance score: {result['relevance_score']:.2f})")

                if self.io:
                    self.io.tool_output(f"Smart search found relevant file: '{file_path}' (relevance score: {result['relevance_score']:.2f})")

        # Store search details
        self.last_search_details = "\n".join(search_details)

        return suggested_files

    def get_last_search_details(self) -> str:
        """
        Get details about the last search performed.

        Returns:
            String with details about the last search
        """
        return self.last_search_details

    def extract_code_elements(self, text: str) -> List[str]:
        """
        Extract potential code elements (functions, classes, methods, variables) from text.

        Args:
            text: Text to extract code elements from

        Returns:
            List of potential code element names
        """
        # Use the imported function from search_repo.py
        return extract_code_elements(text)

    def _find_similar_files(self, requested_file: str, all_files: List[str]) -> List[str]:
        """
        Find files with similar names to the requested file.

        Args:
            requested_file: The file that was requested but not found
            all_files: List of all available files

        Returns:
            List of files with similar names, prioritized by relevance
        """
        import os
        from difflib import SequenceMatcher

        # Get the requested file components
        requested_dir = os.path.dirname(requested_file)
        basename = os.path.basename(requested_file)
        name_without_ext, ext = os.path.splitext(basename)

        # Store files with scores for better ranking
        scored_files = []

        # Log the search if we have IO
        if self.io:
            self.io.tool_output(f"Looking for files similar to '{requested_file}'")
            self.io.tool_output(f"  - Directory: '{requested_dir}'")
            self.io.tool_output(f"  - Filename: '{basename}'")
            self.io.tool_output(f"  - Name without extension: '{name_without_ext}'")
            self.io.tool_output(f"  - Extension: '{ext}'")

        # Process each file and calculate similarity scores
        for file_path in all_files:
            file_dir = os.path.dirname(file_path)
            file_basename = os.path.basename(file_path)
            file_name, file_ext = os.path.splitext(file_basename)

            # Calculate similarity scores for different components
            score = 0.0

            # 1. Exact basename match (highest priority)
            if file_basename.lower() == basename.lower():
                score += 100.0

            # 2. Exact name without extension match
            elif file_name.lower() == name_without_ext.lower():
                score += 80.0

            # 3. Exact extension match
            if file_ext.lower() == ext.lower():
                score += 20.0

            # 4. Directory path similarity
            if requested_dir and file_dir:
                # If directories match exactly
                if file_dir.lower() == requested_dir.lower():
                    score += 30.0
                # If requested directory is part of the file directory or vice versa
                elif requested_dir.lower() in file_dir.lower() or file_dir.lower() in requested_dir.lower():
                    score += 15.0

            # 5. Partial name matches using sequence matcher for fuzzy matching
            if score == 0:  # Only if no exact matches were found
                # Compare name without extension
                name_similarity = SequenceMatcher(None, name_without_ext.lower(), file_name.lower()).ratio()

                # Higher score for higher similarity
                if name_similarity > 0.8:  # Very similar
                    score += 60.0 * name_similarity
                elif name_similarity > 0.5:  # Moderately similar
                    score += 40.0 * name_similarity
                elif name_similarity > 0.3:  # Somewhat similar
                    score += 20.0 * name_similarity

                # Check if one name is contained within the other
                if name_without_ext.lower() in file_name.lower():
                    score += 30.0
                elif file_name.lower() in name_without_ext.lower():
                    score += 20.0

            # Only include files with a minimum score
            if score > 0:
                scored_files.append((file_path, score))

        # Sort files by score (highest first)
        scored_files.sort(key=lambda x: x[1], reverse=True)

        # Log the top matches if we have IO
        if self.io and scored_files:
            self.io.tool_output(f"Top filename matches:")
            for file_path, score in scored_files[:5]:
                self.io.tool_output(f"  - '{file_path}' (score: {score:.2f})")

        # Extract just the file paths
        similar_files = [file_path for file_path, _ in scored_files]

        # Limit to top matches (we'll limit to 2 in the caller)
        return similar_files
