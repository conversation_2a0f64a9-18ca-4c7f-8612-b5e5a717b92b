#!/usr/bin/env python3
"""
Targeted test to detect if repository maps are leaking to the LLM.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_repo_map_leak_simple():
    """Simple test to check if repository maps are leaking to LLM."""
    print("🔍 Repository Map Leak Detection")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test individual message methods that we know work
        print("\n🧪 Testing get_repo_messages:")
        repo_messages = coder.get_repo_messages()
        
        if repo_messages:
            total_chars = 0
            for i, msg in enumerate(repo_messages):
                content = msg.get('content', '')
                content_length = len(content)
                total_chars += content_length
                role = msg.get('role', 'unknown')
                
                print(f"   Message {i+1} ({role}): {content_length} characters")
                print(f"   Content: {content}")
                
                # Check for repository map indicators
                if content_length > 1000 or '⋮' in content or content.count('class ') > 5:
                    print(f"   ❌ POTENTIAL REPOSITORY MAP DETECTED!")
                    return False
            
            print(f"   ✅ Total characters in repo messages: {total_chars}")
            if total_chars > 1000:
                print(f"   ⚠️  Large amount of content in repo messages")
        else:
            print("   ✅ No repo messages")
        
        # Test get_readonly_files_messages
        print("\n🧪 Testing get_readonly_files_messages:")
        readonly_messages = coder.get_readonly_files_messages()
        
        if readonly_messages:
            for i, msg in enumerate(readonly_messages):
                content = msg.get('content', '')
                content_length = len(content)
                role = msg.get('role', 'unknown')
                
                print(f"   Message {i+1} ({role}): {content_length} characters")
                
                if content_length > 5000:
                    print(f"   ❌ LARGE CONTENT DETECTED!")
                    print(f"   Preview: {content[:200]}...")
                    return False
        else:
            print("   ✅ No readonly messages")
        
        # Test if get_repo_map is being called and returns content
        print("\n🧪 Testing get_repo_map directly:")
        repo_map = coder.get_repo_map()
        
        if repo_map:
            print(f"   ❌ get_repo_map() returned content: {len(repo_map)} characters")
            print(f"   Preview: {repo_map[:200]}...")
            print("   ❌ REPOSITORY MAP IS BEING GENERATED!")
            
            # Check if this is being used anywhere
            print("\n🔍 Checking if repository map is used in messages...")
            
            # The key question: Is this repository map being sent to the LLM?
            # Let's check the get_chat_files_messages method more carefully
            
            return False  # Repository map exists, need to check if it's sent to LLM
        else:
            print("   ✅ get_repo_map() returns None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in repository map leak detection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_files_messages_carefully():
    """Carefully test get_chat_files_messages to see if it contains repository maps."""
    print("\n🔍 Careful Chat Files Messages Test")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Check the conditions that trigger different behaviors in get_chat_files_messages
        print("🧪 Checking get_chat_files_messages conditions:")
        
        abs_fnames = coder.abs_fnames
        print(f"   abs_fnames: {abs_fnames}")
        
        repo_map = coder.get_repo_map()
        print(f"   get_repo_map() result: {'None' if repo_map is None else f'{len(repo_map)} characters'}")
        
        files_no_full_files_with_repo_map = coder.gpt_prompts.files_no_full_files_with_repo_map
        print(f"   files_no_full_files_with_repo_map length: {len(files_no_full_files_with_repo_map)} characters")
        
        # The critical condition: elif self.get_repo_map() and self.gpt_prompts.files_no_full_files_with_repo_map:
        if not abs_fnames and repo_map and files_no_full_files_with_repo_map:
            print("   ❌ CRITICAL: Condition met for using repository map in chat files messages!")
            print("   This means the repository map WOULD be referenced in LLM messages!")
            return False
        else:
            print("   ✅ Repository map condition NOT met for chat files messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in careful chat files messages test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repository_map_usage_in_prompts():
    """Test if repository maps are used in any prompts or message generation."""
    print("\n🔍 Repository Map Usage in Prompts")
    print("=" * 60)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Check all prompt-related attributes
        prompts_to_check = [
            ("files_no_full_files_with_repo_map", coder.gpt_prompts.files_no_full_files_with_repo_map),
            ("files_no_full_files_with_repo_map_reply", coder.gpt_prompts.files_no_full_files_with_repo_map_reply),
            ("main_system", coder.gpt_prompts.main_system),
        ]
        
        for prompt_name, prompt_content in prompts_to_check:
            print(f"\n🧪 Checking {prompt_name}:")
            print(f"   Length: {len(prompt_content)} characters")
            print(f"   Preview: {prompt_content[:200]}...")
            
            # Check if this prompt contains repository map references
            repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ']
            repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in prompt_content)
            
            if repo_indicator_count > 10:
                print(f"   ❌ REPOSITORY MAP CONTENT DETECTED! ({repo_indicator_count} indicators)")
                return False
            else:
                print(f"   ✅ Clean prompt ({repo_indicator_count} indicators)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing repository map usage in prompts: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run targeted repository map leak detection tests."""
    print("🚀 Repository Map Leak Detection")
    print("=" * 80)
    
    tests = [
        ("Repository Map Leak Simple", test_repo_map_leak_simple),
        ("Chat Files Messages Careful", test_chat_files_messages_carefully),
        ("Repository Map Usage in Prompts", test_repository_map_usage_in_prompts),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 REPOSITORY MAP LEAK DETECTION SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 NO REPOSITORY MAP LEAKS DETECTED!")
        print("\n📋 Confirmed:")
        print("  ✅ Repository maps are NOT sent to LLM outside MAP_REQUEST")
        print("  ✅ All message generation methods are clean")
        print("  ✅ All prompts are clean")
        print("\n🎯 ANSWER: Repository maps are NOT being sent to LLM outside MAP_REQUEST")
    else:
        print("⚠️  REPOSITORY MAP LEAKS DETECTED!")
        print("   Repository maps are being sent to the LLM outside MAP_REQUEST")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
