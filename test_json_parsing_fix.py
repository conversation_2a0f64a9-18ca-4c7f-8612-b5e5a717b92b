#!/usr/bin/env python3
"""
Test script to verify that the JSON parsing fix for backslash escaping works correctly.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_backslash_parsing_fix():
    """Test that backslash file paths are correctly handled in CONTEXT_REQUEST."""
    print("🧪 Testing Backslash File Path Parsing Fix")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

        # Create a handler (we don't need a real project path for this test)
        handler = ContextRequestHandler(".")

        # Test the problematic request that was failing
        problematic_request = '''
        {CONTEXT_REQUEST: {"original_user_query_context": "How does the close_position_based_on_conditions function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "file_hint": "trade_management\\position_exit_manager.py"}]}}
        '''

        print("🔍 Testing problematic request with backslashes...")
        print(f"Request: {problematic_request.strip()}")

        # Try to parse the request
        context_request = handler.parse_context_request(problematic_request)

        if context_request:
            print("✅ Successfully parsed CONTEXT_REQUEST with backslashes!")
            print(f"   Original query context: {context_request.original_user_query_context}")
            print(f"   Number of symbols: {len(context_request.symbols_of_interest)}")
            
            if context_request.symbols_of_interest:
                symbol = context_request.symbols_of_interest[0]
                print(f"   Symbol name: {symbol.name}")
                print(f"   Symbol type: {symbol.type}")
                print(f"   File hint (corrected): {symbol.file_hint}")
                
                # Check that backslashes were converted to forward slashes
                if '\\' not in symbol.file_hint and '/' in symbol.file_hint:
                    print("✅ Backslashes correctly converted to forward slashes!")
                    return True
                else:
                    print("❌ Backslashes were not properly converted")
                    return False
            else:
                print("❌ No symbols found in parsed request")
                return False
        else:
            print("❌ Failed to parse CONTEXT_REQUEST")
            return False

    except Exception as e:
        print(f"❌ Error testing backslash parsing fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_correct_format_still_works():
    """Test that correctly formatted requests still work."""
    print("\n🧪 Testing Correct Format Still Works")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler

        handler = ContextRequestHandler(".")

        # Test a correctly formatted request
        correct_request = '''
        {CONTEXT_REQUEST: {"original_user_query_context": "How does the function work?", "symbols_of_interest": [{"type": "method_definition", "name": "test_function", "file_hint": "path/to/file.py"}]}}
        '''

        print("🔍 Testing correctly formatted request...")
        print(f"Request: {correct_request.strip()}")

        context_request = handler.parse_context_request(correct_request)

        if context_request:
            print("✅ Successfully parsed correctly formatted CONTEXT_REQUEST!")
            print(f"   Original query context: {context_request.original_user_query_context}")
            print(f"   Number of symbols: {len(context_request.symbols_of_interest)}")
            
            if context_request.symbols_of_interest:
                symbol = context_request.symbols_of_interest[0]
                print(f"   Symbol name: {symbol.name}")
                print(f"   Symbol type: {symbol.type}")
                print(f"   File hint: {symbol.file_hint}")
                return True
            else:
                print("❌ No symbols found in parsed request")
                return False
        else:
            print("❌ Failed to parse correctly formatted CONTEXT_REQUEST")
            return False

    except Exception as e:
        print(f"❌ Error testing correct format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixed_path_formats():
    """Test various path formats to ensure robustness."""
    print("\n🧪 Testing Mixed Path Formats")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler

        handler = ContextRequestHandler(".")

        test_cases = [
            {
                "name": "Windows backslashes",
                "request": '''{"original_user_query_context": "test", "symbols_of_interest": [{"type": "function", "name": "test", "file_hint": "folder\\subfolder\\file.py"}]}''',
                "expected_path": "folder/subfolder/file.py"
            },
            {
                "name": "Unix forward slashes",
                "request": '''{"original_user_query_context": "test", "symbols_of_interest": [{"type": "function", "name": "test", "file_hint": "folder/subfolder/file.py"}]}''',
                "expected_path": "folder/subfolder/file.py"
            },
            {
                "name": "Mixed slashes",
                "request": '''{"original_user_query_context": "test", "symbols_of_interest": [{"type": "function", "name": "test", "file_hint": "folder\\subfolder/file.py"}]}''',
                "expected_path": "folder/subfolder/file.py"
            }
        ]

        passed = 0
        total = len(test_cases)

        for test_case in test_cases:
            print(f"\n🔍 Testing {test_case['name']}...")
            
            # Wrap in CONTEXT_REQUEST format
            full_request = f"{{CONTEXT_REQUEST: {test_case['request']}}}"
            
            context_request = handler.parse_context_request(full_request)
            
            if context_request and context_request.symbols_of_interest:
                actual_path = context_request.symbols_of_interest[0].file_hint
                expected_path = test_case['expected_path']
                
                if actual_path == expected_path:
                    print(f"✅ {test_case['name']}: {actual_path}")
                    passed += 1
                else:
                    print(f"❌ {test_case['name']}: Expected {expected_path}, got {actual_path}")
            else:
                print(f"❌ {test_case['name']}: Failed to parse")

        print(f"\n📊 Mixed path formats: {passed}/{total} tests passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing mixed path formats: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all JSON parsing fix tests."""
    print("🚀 Testing JSON Parsing Fix for Backslash Escaping")
    print("=" * 80)

    tests = [
        test_backslash_parsing_fix,
        test_correct_format_still_works,
        test_mixed_path_formats,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 All JSON parsing fixes are working correctly!")
        print("\n📋 The system can now handle:")
        print("  1. ✅ Windows backslash file paths in CONTEXT_REQUEST")
        print("  2. ✅ Unix forward slash file paths")
        print("  3. ✅ Mixed slash formats")
        print("  4. ✅ Automatic conversion to forward slashes for JSON compatibility")
        print("  5. ✅ Proper error reporting for debugging")
    else:
        print("⚠️  Some JSON parsing fixes need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
