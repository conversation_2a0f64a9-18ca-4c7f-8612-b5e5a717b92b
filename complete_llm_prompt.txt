================================================================================
COMPLETE PROMPT THAT WOULD BE SENT TO LLM
================================================================================
Total messages: 1
Query: How does position management work in the trading system?
================================================================================

=============== MESSAGE 1: SYSTEM ===============
Length: 10,140 characters
Preview (first 10 lines):
--------------------------------------------------
  1: # Intelligent Context for Your Query
  2: 
  3: # USER QUERY
  4: How does position management work in the trading system?
  5: 
  6: # INTELLIGENT CONTEXT ANALYSIS
  7: ## Task: general_analysis
  8: ## Focus: Analyze and provide context for: How does position management work in the trading system?
  9: 
 10: ## CRITICAL ENTITIES (8 most important)
 11: ... (269 more lines)
--------------------------------------------------
🎯 THIS IS THE IR CONTEXT MESSAGE!
✅ Contains trading system functions
📊 Contains 16 entities

📊 TOTAL PROMPT SIZE: 10,140 characters
================================================================================