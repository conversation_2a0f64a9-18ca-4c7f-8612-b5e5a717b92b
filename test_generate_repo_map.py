#!/usr/bin/env python3
"""
Test the new --generate-repo-map functionality.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def test_generate_repo_map():
    """Test the --generate-repo-map command line argument."""
    print("🔍 Testing --generate-repo-map functionality")
    print("=" * 80)
    
    # Create a temporary file for the output
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Test the command
        print(f"📝 Testing command with output file: {temp_filename}")
        
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--generate-repo-map", temp_filename,
            "C:\\Users\\<USER>\\Documents\\____live_backtest_dashboard_____"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60  # 60 second timeout
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        # Check if the file was created
        if os.path.exists(temp_filename):
            file_size = os.path.getsize(temp_filename)
            print(f"✅ Repository map file created: {temp_filename}")
            print(f"📊 File size: {file_size} bytes")
            
            # Read and show a preview of the content
            with open(temp_filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                word_count = len(content.split())
                
                print(f"📊 Content stats:")
                print(f"   Lines: {len(lines)}")
                print(f"   Characters: {len(content)}")
                print(f"   Words: {word_count}")
                
                print(f"\n📋 Content preview (first 20 lines):")
                print("-" * 60)
                for i, line in enumerate(lines[:20]):
                    print(f"{i+1:3d}: {line}")
                if len(lines) > 20:
                    print(f"... and {len(lines) - 20} more lines")
                print("-" * 60)
                
                return True
        else:
            print(f"❌ Repository map file was not created: {temp_filename}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)
        
        # Clean up temporary file
        try:
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
                print(f"🧹 Cleaned up temporary file: {temp_filename}")
        except Exception as e:
            print(f"⚠️  Could not clean up temporary file: {e}")

def test_generate_repo_map_simple():
    """Test with a simpler command without the target directory."""
    print("\n🔍 Testing --generate-repo-map with simpler command")
    print("=" * 80)
    
    # Create a temporary file for the output
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Test the command
        print(f"📝 Testing simple command with output file: {temp_filename}")
        
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run the command (generate repo map for the aider project itself)
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--generate-repo-map", temp_filename
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60  # 60 second timeout
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        # Check if the file was created
        if os.path.exists(temp_filename):
            file_size = os.path.getsize(temp_filename)
            print(f"✅ Repository map file created: {temp_filename}")
            print(f"📊 File size: {file_size} bytes")
            
            # Read and show a preview of the content
            with open(temp_filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                word_count = len(content.split())
                
                print(f"📊 Content stats:")
                print(f"   Lines: {len(lines)}")
                print(f"   Characters: {len(content)}")
                print(f"   Words: {word_count}")
                
                print(f"\n📋 Content preview (first 15 lines):")
                print("-" * 60)
                for i, line in enumerate(lines[:15]):
                    print(f"{i+1:3d}: {line}")
                if len(lines) > 15:
                    print(f"... and {len(lines) - 15} more lines")
                print("-" * 60)
                
                return True
        else:
            print(f"❌ Repository map file was not created: {temp_filename}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)
        
        # Clean up temporary file
        try:
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
                print(f"🧹 Cleaned up temporary file: {temp_filename}")
        except Exception as e:
            print(f"⚠️  Could not clean up temporary file: {e}")

def test_help_output():
    """Test that the new argument appears in help output."""
    print("\n🔍 Testing --help output includes new argument")
    print("=" * 80)
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Run help command
        cmd = [sys.executable, "-m", "aider.main", "--help"]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if "--generate-repo-map" in result.stdout:
            print("✅ --generate-repo-map argument found in help output")
            
            # Extract the help text for our argument
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if "--generate-repo-map" in line:
                    print(f"📋 Help text:")
                    print(f"   {line}")
                    if i + 1 < len(lines) and lines[i + 1].strip():
                        print(f"   {lines[i + 1]}")
                    break
            return True
        else:
            print("❌ --generate-repo-map argument NOT found in help output")
            print("📤 Help output preview:")
            print(result.stdout[:1000] + "..." if len(result.stdout) > 1000 else result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ Error checking help output: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def main():
    """Run all tests for the --generate-repo-map functionality."""
    print("🚀 Testing --generate-repo-map Functionality")
    print("=" * 100)
    
    tests = [
        ("Help Output Test", test_help_output),
        ("Simple Repo Map Generation", test_generate_repo_map_simple),
        ("Target Directory Repo Map", test_generate_repo_map),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 --generate-repo-map TESTING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 --generate-repo-map FUNCTIONALITY WORKING!")
        print("\n📋 Usage examples:")
        print("  # Generate repo map for current directory:")
        print("  Set-Location aider-main; python -m aider.main --model ollama_chat/qwen3:1.7b --generate-repo-map repo_map.txt")
        print("\n  # Generate repo map for specific directory:")
        print("  Set-Location aider-main; python -m aider.main --model ollama_chat/qwen3:1.7b --generate-repo-map repo_map.txt \"C:\\path\\to\\project\"")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("   Check the output above for details")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
