#!/usr/bin/env python3
"""
Test script for the Intelligent Context Selection Engine integration.

This script demonstrates the new Phase 2 feature that uses AI-powered analysis
to select the most relevant code context for any given development task.
"""

import os
import json
import time
from aider_integration_service import AiderIntegrationService


def test_intelligent_context_integration():
    """Test the integration of Intelligent Context Selection with AiderIntegrationService."""
    print("🧪 Testing Intelligent Context Selection Integration")
    print("=" * 70)
    
    # Initialize the service
    service = AiderIntegrationService()
    project_path = os.getcwd()
    
    print(f"📁 Project path: {project_path}")
    
    # Define comprehensive test scenarios
    test_scenarios = [
        {
            'name': 'Bug Fix Scenario',
            'task': 'Fix a critical bug in the file parsing and processing logic that causes crashes',
            'type': 'debugging',
            'focus': ['file', 'parse', 'process', 'error'],
            'max_tokens': 3000
        },
        {
            'name': 'Feature Development Scenario',
            'task': 'Implement a new advanced code analysis feature with dependency tracking',
            'type': 'feature_development',
            'focus': ['analysis', 'dependency', 'tracking'],
            'max_tokens': 4000
        },
        {
            'name': 'Refactoring <PERSON><PERSON><PERSON>',
            'task': 'Refactor the entire dependency management and context extraction system',
            'type': 'refactoring',
            'focus': ['dependency', 'context', 'extraction'],
            'max_tokens': 3500
        },
        {
            'name': 'Code Review Scenario',
            'task': 'Review error handling and exception management throughout the codebase',
            'type': 'code_review',
            'focus': ['error', 'exception', 'handling'],
            'max_tokens': 2500
        },
        {
            'name': 'Testing Scenario',
            'task': 'Create comprehensive unit tests for the IR generation pipeline',
            'type': 'testing',
            'focus': ['test', 'unit', 'pipeline', 'ir'],
            'max_tokens': 2000
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 Test {i}: {scenario['name']}")
        print(f"   Task: {scenario['task']}")
        print(f"   Type: {scenario['type']}")
        print(f"   Token Budget: {scenario['max_tokens']}")
        
        start_time = time.time()
        
        try:
            # Select intelligent context
            context_result = service.select_intelligent_context(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['type'],
                focus_entities=scenario['focus'],
                max_tokens=scenario['max_tokens']
            )
            
            selection_time = time.time() - start_time
            
            if 'error' not in context_result:
                print(f"✅ Context selection successful in {selection_time:.2f}s")
                
                # Extract key metrics
                metrics = context_result['quality_metrics']
                
                print(f"📊 Results:")
                print(f"   Selected entities: {context_result['total_entities']}")
                print(f"   Token utilization: {metrics['token_utilization']:.1f}%")
                print(f"   Dependency completeness: {metrics['dependency_completeness']:.1f}%")
                print(f"   Average relevance score: {metrics['average_relevance_score']:.2f}")
                
                # Show priority distribution
                priority_dist = metrics['priority_distribution']
                print(f"   Priority distribution: {priority_dist}")
                
                # Show top 5 entities
                top_entities = context_result['entities'][:5]
                print(f"🏆 Top 5 Selected Entities:")
                for j, entity in enumerate(top_entities, 1):
                    print(f"     {j}. {entity['module_name']}.{entity['entity_name']}")
                    print(f"        Score: {entity['relevance_score']:.2f}, "
                          f"Priority: {entity['priority']}, "
                          f"Criticality: {entity['criticality']}")
                
                # Store results for analysis
                results.append({
                    'scenario': scenario['name'],
                    'success': True,
                    'selection_time': selection_time,
                    'metrics': metrics,
                    'entity_count': context_result['total_entities']
                })
                
            else:
                print(f"❌ Context selection failed: {context_result['error']}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False,
                    'error': context_result['error']
                })
                
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    # Print summary analysis
    print(f"\n📈 Summary Analysis")
    print("=" * 50)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if successful_tests:
        avg_selection_time = sum(r['selection_time'] for r in successful_tests) / len(successful_tests)
        avg_entities = sum(r['entity_count'] for r in successful_tests) / len(successful_tests)
        avg_token_util = sum(r['metrics']['token_utilization'] for r in successful_tests) / len(successful_tests)
        avg_relevance = sum(r['metrics']['average_relevance_score'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n📊 Performance Metrics:")
        print(f"   Average selection time: {avg_selection_time:.2f}s")
        print(f"   Average entities selected: {avg_entities:.1f}")
        print(f"   Average token utilization: {avg_token_util:.1f}%")
        print(f"   Average relevance score: {avg_relevance:.2f}")
    
    if failed_tests:
        print(f"\n⚠️ Failed Test Details:")
        for test in failed_tests:
            print(f"   - {test['scenario']}: {test['error']}")
    
    # Test specific entity lookup
    print(f"\n🔍 Testing Entity Lookup and Related Entities")
    try:
        # Get the context selector for direct testing
        selector = service._get_context_selector(project_path)
        
        if selector:
            # Test entity details lookup
            sample_entity = "aider_integration_service.AiderIntegrationService"
            entity_details = selector.get_entity_details(sample_entity)
            
            if entity_details:
                print(f"✅ Entity details for {sample_entity}:")
                print(f"   Criticality: {entity_details.criticality}")
                print(f"   Change risk: {entity_details.change_risk}")
                print(f"   Calls: {len(entity_details.calls)} functions")
                print(f"   Used by: {len(entity_details.used_by)} entities")
                
                # Get related entities
                related = selector.get_related_entities(sample_entity, max_depth=2)
                print(f"   Related entities (depth 2): {len(related)}")
                if related:
                    print(f"   Sample related: {related[:3]}")
            else:
                print(f"⚠️ Entity {sample_entity} not found")
        else:
            print("⚠️ Context selector not available for direct testing")
            
    except Exception as e:
        print(f"⚠️ Entity lookup test failed: {e}")
    
    print(f"\n🎉 Intelligent Context Selection Integration Test Complete!")
    
    return len(successful_tests) == len(results)


if __name__ == "__main__":
    success = test_intelligent_context_integration()
    exit(0 if success else 1)
