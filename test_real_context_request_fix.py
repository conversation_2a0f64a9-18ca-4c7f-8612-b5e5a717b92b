#!/usr/bin/env python3

"""
Test to verify that the real context request fix works correctly.
This test verifies that when aider is run with a project directory argument,
the context request system uses that directory instead of the git repository root.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_real_context_request_fix():
    """Test that context request system uses the project directory from command line."""

    print("🧪 Testing Real Context Request Fix")
    print("=" * 50)

    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")

        # Create the trading project structure
        trading_dir = os.path.join(temp_dir, "live_backtest_dashboard")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)

        # Initialize a git repository in the trading project
        import subprocess
        subprocess.run(["git", "init"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=trading_dir, capture_output=True)

        # Create the actual trading files
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""

    def __init__(self):
        self.observers = []

    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.

        Args:
            app: The application context

        Returns:
            bool: True if position was closed, False otherwise
        """
        return self._evaluate_market_conditions(app)

    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''

        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""

    def __init__(self):
        self.active_positions = []

    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.

        Args:
            app: The application context containing market data and position info

        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True

        return False

    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder

    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''

        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")

        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)

        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)

        # Add files to git
        subprocess.run(["git", "add", "."], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=trading_dir, capture_output=True)

        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path}")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📁 Git repository initialized in: {trading_dir}")

        # Test the GitRepo and Coder initialization
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.repo import GitRepo
            from aider.coders.base_coder import Coder
            from aider.models import Model
            from aider.io import InputOutput

            print("✅ Successfully imported aider modules")

            # Create a GitRepo with the trading directory as git_dname
            io = InputOutput()
            repo = GitRepo(
                io=io,
                fnames=[],
                git_dname=trading_dir,  # This simulates the command line argument
                aider_ignore_file=None
            )

            print(f"📁 GitRepo root: {repo.root}")
            print(f"📁 GitRepo original git_dname: {getattr(repo, '_original_git_dname', 'NOT SET')}")

            # Verify that the original git_dname is stored
            if hasattr(repo, '_original_git_dname') and repo._original_git_dname == trading_dir:
                print("✅ SUCCESS: GitRepo correctly stores original git_dname")
            else:
                print("❌ FAILED: GitRepo does not store original git_dname correctly")
                return False

            # Create a Coder instance
            model = Model("gpt-3.5-turbo")
            coder = Coder.create(
                main_model=model,
                edit_format="informative",  # Use the correct edit format
                io=io,
                repo=repo,
                fnames=[],
                use_git=True,
                map_tokens=1000
            )

            print(f"📁 Coder root: {coder.root}")

            # Check if context request integration is initialized correctly
            if hasattr(coder, 'context_request_integration'):
                context_root = getattr(coder.context_request_integration, 'project_path', None)
                print(f"📁 Context request root: {context_root}")

                # The context request system should use the original trading directory
                if context_root == trading_dir:
                    print("✅ SUCCESS: Context request system uses the correct project directory!")

                    # Test that the context request can find the trading files
                    from aider.context_request.context_request_handler import SymbolRequest

                    symbol1 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="services/position_observer.py"
                    )

                    symbol2 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="trade_management/position_exit_manager.py"
                    )

                    # Test file resolution
                    handler = coder.context_request_integration.handler
                    file1 = handler._find_file_for_symbol(symbol1)
                    file2 = handler._find_file_for_symbol(symbol2)

                    print(f"🔍 File 1 resolution: {file1}")
                    print(f"🔍 File 2 resolution: {file2}")

                    if file1 and file2:
                        print("✅ SUCCESS: Both trading files found with the fixed context request system!")
                        return True
                    else:
                        print("❌ FAILED: Files not found even with fixed context request system")
                        return False
                else:
                    print(f"❌ FAILED: Context request system uses wrong directory")
                    print(f"   Expected: {trading_dir}")
                    print(f"   Got: {context_root}")
                    return False
            else:
                print("❌ FAILED: Context request integration not initialized")
                return False

        except ImportError as e:
            print(f"❌ FAILED: Could not import aider modules: {e}")
            return False
        except Exception as e:
            print(f"❌ FAILED: Error during testing: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main test function."""
    success = test_real_context_request_fix()

    if success:
        print("\n🎉 REAL CONTEXT REQUEST FIX: PASSED")
        print("The fix correctly uses the project directory from command line arguments.")
    else:
        print("\n❌ REAL CONTEXT REQUEST FIX: FAILED")
        print("The fix needs further investigation.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
