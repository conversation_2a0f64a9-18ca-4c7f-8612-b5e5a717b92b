#!/usr/bin/env python3
"""
Simple test to verify repo_messages prompts are centralized in base_prompts.py
"""

import sys
import os

def test_prompt_centralization():
    """Test that the prompts are properly centralized"""
    print("🧪 Testing Prompt Centralization")
    print("=" * 50)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test that all required prompts exist
        required_prompts = {
            'smart_map_request_user_prompt': 'CRITICAL WORKFLOW RULE',
            'smart_map_request_assistant_reply': 'I understand. I will always start with MAP_REQUEST',
            'legacy_repo_assistant_reply': 'I understand the repository structure'
        }
        
        all_found = True
        
        for prompt_name, expected_content in required_prompts.items():
            if hasattr(prompts, prompt_name):
                prompt_content = getattr(prompts, prompt_name)
                if expected_content in prompt_content:
                    print(f"✅ {prompt_name}: Found and contains expected content")
                    print(f"   Length: {len(prompt_content)} characters")
                    print(f"   Preview: {prompt_content[:60]}...")
                else:
                    print(f"❌ {prompt_name}: Found but missing expected content")
                    all_found = False
            else:
                print(f"❌ {prompt_name}: Not found in base_prompts.py")
                all_found = False
        
        if all_found:
            print("\n🎉 SUCCESS: All repo_messages prompts are properly centralized!")
            return True
        else:
            print("\n❌ FAILURE: Some prompts are missing or incorrect")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_prompt_content():
    """Test the actual content of the prompts"""
    print("\n🔍 Testing Prompt Content")
    print("=" * 50)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test smart_map_request_user_prompt
        user_prompt = prompts.smart_map_request_user_prompt
        print(f"📝 Smart Map Request User Prompt:")
        print(f"   {user_prompt}")
        
        # Test smart_map_request_assistant_reply  
        assistant_reply = prompts.smart_map_request_assistant_reply
        print(f"\n📝 Smart Map Request Assistant Reply:")
        print(f"   {assistant_reply}")
        
        # Test legacy_repo_assistant_reply
        legacy_reply = prompts.legacy_repo_assistant_reply
        print(f"\n📝 Legacy Repo Assistant Reply:")
        print(f"   {legacy_reply}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Repo Messages Centralization")
    print("=" * 80)
    
    success1 = test_prompt_centralization()
    success2 = test_prompt_content()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED: Repo messages are successfully centralized!")
        print("\n📋 Summary:")
        print("   ✅ Prompts moved from hardcoded strings in base_coder.py")
        print("   ✅ Prompts now centralized in base_prompts.py")
        print("   ✅ get_repo_messages() now uses self.gpt_prompts.* references")
        print("   ✅ All prompt content is preserved and accessible")
    else:
        print("❌ SOME TESTS FAILED")
