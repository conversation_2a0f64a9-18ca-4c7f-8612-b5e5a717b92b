#!/usr/bin/env python3
"""
Test script for the focused repository map functionality.
This script tests the new symbol filtering feature in SmartMapRequestHandler.
"""

import os
import sys
import json

# Add the aider directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_focused_map():
    """Test the focused map generation with symbol filtering."""
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        print("🧪 Testing Focused Repository Map Generation")
        print("=" * 60)

        # Create necessary components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create RepoMap instance
        repo_map = RepoMap(
            map_tokens=4096,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=True
        )

        # Create SmartMapRequestHandler
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )

        print(f"✅ SmartMapRequestHandler created successfully")
        print(f"   Symbol filtering enabled: {handler.config.get('enable_symbol_filtering', False)}")

        # Test 1: Search for specific functionality (similar to the problematic search)
        print("\n🔍 Test 1: Searching for position/trade functionality")
        test_request_1 = {
            "keywords": ["close_position_based_on_conditions", "conditions", "trade", "order", "position"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        result_1 = handler.handle_map_request(test_request_1)
        print(f"✅ Test 1 completed")
        print(f"   Result length: {len(result_1)} characters")
        print(f"   Contains 'SmartMapRequestHandler': {'SmartMapRequestHandler' in result_1}")

        # Test 2: Search for class names
        print("\n🔍 Test 2: Searching for 'RepoMap' class")
        test_request_2 = {
            "keywords": ["RepoMap", "get_ranked_tags"],
            "type": "implementation",
            "scope": "all",
            "max_results": 3
        }

        result_2 = handler.handle_map_request(test_request_2)
        print(f"✅ Test 2 completed")
        print(f"   Result length: {len(result_2)} characters")
        print(f"   Contains 'get_ranked_tags': {'get_ranked_tags' in result_2}")

        # Test 3: Compare with symbol filtering disabled
        print("\n🔍 Test 3: Testing with symbol filtering disabled")
        handler.config["enable_symbol_filtering"] = False

        result_3 = handler.handle_map_request(test_request_1)
        print(f"✅ Test 3 completed")
        print(f"   Result length: {len(result_3)} characters")
        print(f"   Filtering disabled result is larger: {len(result_3) > len(result_1)}")

        # Show comparison
        print("\n📊 Comparison Results:")
        print(f"   With filtering:    {len(result_1):,} characters")
        print(f"   Without filtering: {len(result_3):,} characters")
        print(f"   Size reduction:    {((len(result_3) - len(result_1)) / len(result_3) * 100):.1f}%")

        # Test cache statistics
        cache_stats = handler.get_cache_stats()
        print(f"\n💾 Cache Statistics:")
        for key, value in cache_stats.items():
            print(f"   {key}: {value}")

        print("\n🎉 All tests completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_symbol_filtering():
    """Test the symbol filtering logic specifically."""
    try:
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        print("\n🧪 Testing Symbol Filtering Logic")
        print("=" * 60)

        # Create handler
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo_map = RepoMap(map_tokens=1024, root="aider-main", main_model=model, io=io)
        handler = SmartMapRequestHandler(repo_map=repo_map, root_dir="aider-main", io=io)

        # Mock relevant files with matched symbols
        relevant_files = [
            {
                'file': 'aider-main/aider/smart_map_request_handler.py',
                'relevance_score': 10.0,
                'match_types': ['class', 'function'],
                'symbols': ['SmartMapRequestHandler', 'handle_map_request'],
                'matched_symbols': [
                    {'name': 'SmartMapRequestHandler', 'line': 34, 'kind': 'class'},
                    {'name': 'handle_map_request', 'line': 88, 'kind': 'function'}
                ]
            }
        ]

        # Mock ranked tags (simplified)
        class MockTag:
            def __init__(self, rel_fname, name, kind, line):
                self.rel_fname = rel_fname
                self.name = name
                self.kind = kind
                self.line = line

        ranked_tags = [
            MockTag('aider/smart_map_request_handler.py', 'SmartMapRequestHandler', 'class', 34),
            MockTag('aider/smart_map_request_handler.py', 'handle_map_request', 'function', 88),
            MockTag('aider/smart_map_request_handler.py', 'other_method', 'function', 150),
            ('aider/smart_map_request_handler.py',)  # File-only tag
        ]

        # Test filtering
        filtered_tags = handler._filter_tags_by_matched_symbols(ranked_tags, relevant_files)

        print(f"✅ Symbol filtering test completed")
        print(f"   Original tags: {len(ranked_tags)}")
        print(f"   Filtered tags: {len(filtered_tags)}")
        print(f"   Expected reduction: Should filter out 'other_method'")

        # Verify filtering worked
        filtered_names = [tag.name for tag in filtered_tags if hasattr(tag, 'name')]
        print(f"   Filtered symbol names: {filtered_names}")

        expected_symbols = {'SmartMapRequestHandler', 'handle_map_request'}
        actual_symbols = set(filtered_names)

        if expected_symbols.issubset(actual_symbols) and 'other_method' not in actual_symbols:
            print("✅ Symbol filtering working correctly!")
            return True
        else:
            print("❌ Symbol filtering not working as expected")
            return False

    except Exception as e:
        print(f"❌ Symbol filtering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Focused Repository Map Tests")
    print("=" * 60)

    success = True

    # Run main functionality test
    if not test_focused_map():
        success = False

    # Run symbol filtering test
    if not test_symbol_filtering():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! The focused repository map feature is working correctly.")
        print("\n📝 Summary of improvements:")
        print("   ✅ Symbol filtering reduces map size significantly")
        print("   ✅ Only matched symbols are included in the output")
        print("   ✅ Fallback to original behavior when filtering is disabled")
        print("   ✅ Proper error handling and logging")
    else:
        print("❌ Some tests failed. Please check the implementation.")

    sys.exit(0 if success else 1)
