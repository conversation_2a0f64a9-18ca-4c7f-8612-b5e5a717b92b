"""
Smart Map Request Handler - Intelligent query-driven repository map generation.

This module implements the Smart Map Request System that replaces blind repository
slicing with intelligent, semantic search-driven context retrieval.
"""

import json
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import defaultdict, Counter

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    REAL_EMBEDDINGS = True
except ImportError:
    REAL_EMBEDDINGS = False
    # Mock implementation for environments without sentence-transformers
    class MockSentenceTransformer:
        def encode(self, text):
            return [0.1] * 384  # Mock 384-dimensional embedding

        def similarity(self, a, b):
            return 0.5  # Mock similarity score

from .repomap import RepoMap
from .search_repo import extract_code_elements


class SmartMapRequestHandler:
    """
    Handles intelligent map requests from LLMs using semantic search and
    hierarchical ranking to provide focused repository context.
    """

    def __init__(self, repo_map: RepoMap, root_dir: str, io=None):
        """
        Initialize the Smart Map Request Handler.

        Args:
            repo_map: RepoMap instance for repository analysis
            root_dir: Root directory of the repository
            io: Optional IO object for logging
        """
        self.repo_map = repo_map
        self.root_dir = root_dir
        self.io = io

        # Initialize semantic search model
        if REAL_EMBEDDINGS:
            self.model = SentenceTransformer('all-MiniLM-L6-v2')
        else:
            self.model = MockSentenceTransformer()
            if self.io:
                self.io.tool_warning("sentence-transformers not available, using basic keyword matching")

        # Configuration
        self.config = {
            "similarity_threshold": 0.5,
            "max_results_default": 10,
            "search_timeout": 5.0,
            "enable_symbol_filtering": True,  # Enable focused symbol filtering
            "hierarchical_weights": {
                "filename": 3.0,
                "classname": 2.0,
                "function": 1.5,
                "content": 1.0
            }
        }

        # Cache for embeddings and search results
        self._embedding_cache = {}
        self._search_cache = {}

        # Performance optimization caches
        self._repository_files_cache = None
        self._repository_terms_cache = None
        self._ranked_tags_cache = None
        self._cache_timestamp = None





    def handle_map_request(self, request_json: Dict[str, Any]) -> str:
        """
        Process a map request from the LLM and return a focused repository map.

        Args:
            request_json: JSON object containing the map request

        Returns:
            Focused repository map as markdown string
        """
        start_time = time.time()

        if self.io:
            self.io.tool_output(f"Processing Smart Map Request...")

        # Extract request parameters
        keywords = request_json.get("keywords", [])
        request_type = request_json.get("type", "implementation")
        scope = request_json.get("scope", "all")
        max_results = request_json.get("max_results", self.config["max_results_default"])
        include_dependencies = request_json.get("include_dependencies", True)

        # Enhanced debugging output
        if self.io:
            self.io.tool_output(f"MAP_REQUEST Details:")
            self.io.tool_output(f"   Keywords: {keywords}")
            self.io.tool_output(f"   Type: {request_type}")
            self.io.tool_output(f"   Scope: {scope}")
            self.io.tool_output(f"   Max Results: {max_results}")
            self.io.tool_output(f"   Include Dependencies: {include_dependencies}")

        if not keywords:
            return self._generate_error_response("No keywords provided in map request")

        # Perform hierarchical search with detailed timing
        try:
            search_start = time.time()
            relevant_files = self._smart_hierarchical_search(
                keywords, request_type, scope, max_results
            )
            search_time = (time.time() - search_start) * 1000

            if self.io:
                self.io.tool_output(f"Search Results (found in {search_time:.1f}ms):")
                for i, file_info in enumerate(relevant_files[:5], 1):  # Show top 5
                    self.io.tool_output(f"   {i}. {file_info['file']} (score: {file_info['relevance_score']:.1f})")
                    self.io.tool_output(f"      Match types: {', '.join(file_info['match_types'])}")
                    if file_info['symbols']:
                        self.io.tool_output(f"      Symbols: {', '.join(file_info['symbols'][:3])}")
                if len(relevant_files) > 5:
                    self.io.tool_output(f"   ... and {len(relevant_files) - 5} more files")

            if not relevant_files:
                return self._generate_no_results_response(keywords)

            # Include dependencies if requested
            if include_dependencies:
                dependency_start = time.time()
                relevant_files = self._include_dependencies(relevant_files)
                dependency_time = (time.time() - dependency_start) * 1000
                if self.io:
                    self.io.tool_output(f"Dependencies processed in {dependency_time:.1f}ms")

            # Generate focused map
            map_start = time.time()
            focused_map = self._generate_focused_map(relevant_files, keywords)
            map_time = (time.time() - map_start) * 1000

            total_time = (time.time() - start_time) * 1000

            if self.io:
                # Show detailed performance breakdown
                cache_stats = self.get_cache_stats()
                self.io.tool_output(f"Performance Breakdown:")
                self.io.tool_output(f"   Search: {search_time:.1f}ms")
                self.io.tool_output(f"   Map generation: {map_time:.1f}ms")
                self.io.tool_output(f"   Total: {total_time:.1f}ms")
                self.io.tool_output(f"Cache Status:")
                self.io.tool_output(f"   Files cached: {cache_stats['repository_files_cached']}")
                self.io.tool_output(f"   Terms cached: {cache_stats['repository_terms_cached']}")
                self.io.tool_output(f"   Tags cached: {cache_stats['ranked_tags_cached']}")
                self.io.tool_output(f"   Embeddings: {cache_stats['embedding_cache_size']} cached")
                self.io.tool_output(
                    f"Smart Map Request completed: {len(relevant_files)} files found in {total_time:.1f}ms"
                )

                # Show the actual map slice being sent to LLM
                self.io.tool_output("=" * 80)
                self.io.tool_output("REPOSITORY MAP SLICE SENT TO LLM:")
                self.io.tool_output("=" * 80)
                self.io.tool_output(focused_map)
                self.io.tool_output("=" * 80)
                self.io.tool_output(f"Map slice stats: {len(focused_map)} characters, {len(focused_map.split())} words")
                self.io.tool_output("=" * 80)

            return focused_map

        except Exception as e:
            if self.io:
                self.io.tool_error(f"Error processing map request: {e}")
            return self._generate_error_response(f"Search error: {str(e)}")

    def _smart_hierarchical_search(
        self,
        keywords: List[str],
        request_type: str,
        scope: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Perform hierarchical search with semantic expansion.

        Returns:
            List of relevant files with relevance scores
        """
        # Expand keywords using semantic similarity
        expanded_keywords = self._expand_keywords(keywords)

        if self.io:
            self.io.tool_output(f"Keyword Expansion:")
            self.io.tool_output(f"   Original: {keywords}")
            self.io.tool_output(f"   Expanded: {expanded_keywords}")

        # Get all repository files and tags
        all_files = self._get_repository_files()

        # PERFORMANCE FIX: Use cached tags or generate if symbol filtering is enabled
        if self._ranked_tags_cache is not None:
            ranked_tags = self._ranked_tags_cache
            if self.io:
                self.io.tool_output("Using cached ranked tags")
        elif self.config.get("enable_symbol_filtering", True):
            # Generate tags when symbol filtering is enabled for better results
            ranked_tags = self._get_ranked_tags(all_files)
            if self.io:
                self.io.tool_output("Generated ranked tags for symbol filtering")
        else:
            # Skip expensive tag generation for filename-only search
            ranked_tags = []
            if self.io:
                self.io.tool_output("Skipping expensive tag generation for performance")

        if self.io:
            self.io.tool_output(f"Repository Stats:")
            self.io.tool_output(f"   Total files: {len(all_files)}")
            self.io.tool_output(f"   Total tags: {len(ranked_tags)}")

        # Hierarchical search strategy
        results = []

        # REMOVED: Filename matches to prevent massive repository maps
        # Focus only on actual symbol matches for precise results

        # Priority 1: Class name matches (only if we have cached tags)
        if ranked_tags:
            class_matches = self._search_class_names(expanded_keywords, ranked_tags)
            results.extend(class_matches)

        # Priority 2: Function name matches (only if we have cached tags)
        if ranked_tags:
            function_matches = self._search_function_names(expanded_keywords, ranked_tags)
            results.extend(function_matches)

        # Priority 3: Content/context matches (if scope allows and we have cached tags)
        if scope in ["all", "content"] and ranked_tags:
            content_matches = self._search_content(expanded_keywords, ranked_tags)
            results.extend(content_matches)

        # Deduplicate and rank results
        final_results = self._deduplicate_and_rank(results, max_results)

        return final_results

    def _expand_keywords(self, keywords: List[str]) -> List[str]:
        """Expand keywords using semantic similarity with performance optimizations."""
        if not REAL_EMBEDDINGS:
            return keywords  # Return original keywords if no embeddings available

        # Check cache first for the entire keyword set
        cache_key = tuple(sorted(keywords))
        if cache_key in self._search_cache:
            if self.io:
                self.io.tool_output("Using cached keyword expansion")
            return self._search_cache[cache_key]

        start_time = time.time()
        expanded = set(keywords)

        # Get repository terms for similarity matching (cached)
        repo_terms = self._get_repository_terms()

        # Limit repository terms to avoid excessive computation
        max_terms = min(len(repo_terms), 500)  # Limit to 500 most relevant terms
        repo_terms = repo_terms[:max_terms]

        if self.io:
            self.io.tool_output(f"Expanding {len(keywords)} keywords against {len(repo_terms)} repository terms")

        # Pre-compute embeddings for all repository terms in batch if possible
        uncached_terms = [term for term in repo_terms if term not in self._embedding_cache]
        if uncached_terms:
            if self.io:
                self.io.tool_output(f"Computing embeddings for {len(uncached_terms)} new terms")
            # Batch encode for better performance
            try:
                batch_embeddings = self.model.encode(uncached_terms)
                for term, embedding in zip(uncached_terms, batch_embeddings):
                    self._embedding_cache[term] = embedding
            except Exception:
                # Fallback to individual encoding
                for term in uncached_terms:
                    self._embedding_cache[term] = self.model.encode(term)

        # Process keywords with early termination
        for keyword in keywords:
            if keyword in self._embedding_cache:
                keyword_embedding = self._embedding_cache[keyword]
            else:
                keyword_embedding = self.model.encode(keyword)
                self._embedding_cache[keyword] = keyword_embedding

            # Find similar terms with vectorized operations when possible
            similarities = []
            for term in repo_terms:
                if term in self._embedding_cache:
                    term_embedding = self._embedding_cache[term]
                    # Calculate cosine similarity
                    similarity = np.dot(keyword_embedding, term_embedding) / (
                        np.linalg.norm(keyword_embedding) * np.linalg.norm(term_embedding)
                    )
                    if similarity > self.config["similarity_threshold"]:
                        expanded.add(term)
                        similarities.append((term, similarity))

            # Limit expansion per keyword to avoid explosion
            if len(similarities) > 10:
                similarities.sort(key=lambda x: x[1], reverse=True)
                # Keep only top 10 similar terms per keyword
                for term, _ in similarities[:10]:
                    expanded.add(term)

        result = list(expanded)

        # Cache the result
        self._search_cache[cache_key] = result

        expansion_time = (time.time() - start_time) * 1000
        if self.io:
            self.io.tool_output(f"Keyword expansion completed in {expansion_time:.1f}ms: {len(keywords)} -> {len(result)} terms")

        return result

    def _get_repository_files(self) -> List[str]:
        """Get all repository files, excluding non-code files with caching."""
        # Check if we have a cached version
        if self._repository_files_cache is not None:
            if self.io:
                self.io.tool_output("Using cached repository files")
            return self._repository_files_cache

        start_time = time.time()
        if self.io:
            self.io.tool_output("Building repository files cache...")

        # Define comprehensive exclusion list
        exclude_extensions = {
            # Media files
            '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.bmp', '.tiff',
            '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.ogg',
            '.wav', '.flac', '.aac', '.m4a',

            # Font files
            '.ttf', '.woff', '.woff2', '.eot', '.otf',

            # Build artifacts
            '.pyc', '.pyo', '.class', '.o', '.so', '.dll', '.exe', '.obj',
            '.lib', '.a', '.dylib',

            # Database files
            '.db', '.db-shm', '.db-wal', '.sqlite', '.sqlite3',

            # Cache files
            '.cache', '.tmp', '.temp', '.log',

            # Documentation files (as requested)
            '.txt', '.pdf', '.doc', '.docx', '.rtf',

            # Archive files
            '.zip', '.tar', '.gz', '.rar', '.7z', '.bz2',

            # IDE/Editor files
            '.swp', '.swo', '.bak', '.orig', '.rej',

            # OS files
            '.DS_Store', 'Thumbs.db',

            # Binary data
            '.bin', '.dat', '.dump'
        }

        try:
            from .repomap import find_src_files
            all_files = find_src_files(self.root_dir)
        except Exception:
            # Fallback to basic file discovery
            all_files = []
            for root, _, filenames in os.walk(self.root_dir):
                for filename in filenames:
                    rel_path = os.path.relpath(os.path.join(root, filename), self.root_dir)
                    all_files.append(rel_path)

        # Filter out excluded files and files that don't exist on filesystem
        filtered_files = []
        excluded_count = 0
        missing_count = 0

        for file_path in all_files:
            # Check if file actually exists on filesystem
            # Handle case where find_src_files returns paths that already include root_dir prefix
            if file_path.startswith(self.root_dir):
                # Path already includes root directory, use as-is
                full_path = file_path
            elif os.path.isabs(file_path):
                # Absolute path, use as-is
                full_path = file_path
            else:
                # Relative path, join with root directory
                full_path = os.path.join(self.root_dir, file_path)

            if not os.path.isfile(full_path):
                missing_count += 1
                continue

            # Get file extension (including the dot)
            _, ext = os.path.splitext(file_path.lower())

            # Check if file should be excluded
            if ext in exclude_extensions:
                excluded_count += 1
                continue

            # Also exclude specific cache directories and patterns
            if any(pattern in file_path.lower() for pattern in [
                '__pycache__', '.cache', '.git/', 'node_modules/',
                '.aider.tags.cache', '.pytest_cache', '.mypy_cache'
            ]):
                excluded_count += 1
                continue

            filtered_files.append(file_path)

        # Cache the result
        self._repository_files_cache = filtered_files

        build_time = (time.time() - start_time) * 1000
        if self.io:
            self.io.tool_output(f"Repository files cache built in {build_time:.1f}ms: {len(all_files)} total, {len(filtered_files)} after filtering")
            self.io.tool_output(f"   Excluded {excluded_count} non-code files")
            if missing_count > 0:
                self.io.tool_output(f"   Skipped {missing_count} missing files (exist in git but not filesystem)")

        return filtered_files

    def _get_ranked_tags(self, all_files: List[str]) -> List:
        """Get ranked tags from repository map with caching."""
        # Check if we have a cached version
        if self._ranked_tags_cache is not None:
            if self.io:
                self.io.tool_output("Using cached ranked tags")
            return self._ranked_tags_cache

        start_time = time.time()
        if self.io:
            self.io.tool_output("Building ranked tags cache...")

        try:
            # Convert file paths to absolute paths for RepoMap (same as _ensure_complete_repository_map)
            # This prevents the "file not found" warnings
            abs_files = []
            for file_path in all_files:
                if file_path.startswith(self.root_dir):
                    # Path already includes root directory, use as-is
                    abs_path = os.path.abspath(file_path)
                elif os.path.isabs(file_path):
                    # Absolute path, use as-is
                    abs_path = file_path
                else:
                    # Relative path, join with root directory
                    abs_path = os.path.join(self.root_dir, file_path)
                    abs_path = os.path.abspath(abs_path)

                # Only include files that actually exist on filesystem
                if os.path.isfile(abs_path):
                    abs_files.append(abs_path)

            if self.io:
                self.io.tool_output(f"Converted {len(all_files)} files to {len(abs_files)} existing absolute paths for RepoMap")
                if all_files and abs_files:
                    self.io.tool_output(f"   Example: {all_files[0] if all_files else 'N/A'} -> {abs_files[0] if abs_files else 'N/A'}")

            ranked_tags = self.repo_map.get_ranked_tags(
                chat_fnames=[],
                other_fnames=abs_files,
                mentioned_fnames=set(),
                mentioned_idents=set()
            )

            # Cache the result
            self._ranked_tags_cache = ranked_tags

            build_time = (time.time() - start_time) * 1000
            if self.io:
                self.io.tool_output(f"Ranked tags cache built in {build_time:.1f}ms: {len(ranked_tags)} tags")

            return ranked_tags

        except Exception as e:
            if self.io:
                self.io.tool_error(f"Error getting ranked tags: {e}")
            return []

    def _get_repository_terms(self) -> List[str]:
        """Extract terms from repository for semantic matching with caching."""
        # Check if we have a cached version
        current_time = time.time()
        cache_valid_duration = 300  # 5 minutes cache

        if (self._repository_terms_cache is not None and
            self._cache_timestamp is not None and
            current_time - self._cache_timestamp < cache_valid_duration):
            if self.io:
                self.io.tool_output("Using cached repository terms")
            return self._repository_terms_cache

        start_time = time.time()
        if self.io:
            self.io.tool_output("Building repository terms cache...")

        terms = set()

        # Get terms from file names (cached)
        all_files = self._get_repository_files()
        for file_path in all_files:
            # Extract terms from file path
            path_parts = Path(file_path).stem.split('_')
            terms.update(part.lower() for part in path_parts if len(part) > 2)

        # Skip expensive tag processing for better performance
        # Instead, extract terms from file content directly (limited sampling)
        for file_path in all_files[:50]:  # Only sample first 50 files for speed
            try:
                # Convert to absolute path
                if file_path.startswith(self.root_dir):
                    abs_path = file_path
                elif os.path.isabs(file_path):
                    abs_path = file_path
                else:
                    abs_path = os.path.join(self.root_dir, file_path)

                if os.path.isfile(abs_path) and abs_path.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.go', '.rs')):
                    # Quick scan of first 20 lines for common patterns
                    with open(abs_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f):
                            if line_num >= 20:  # Only first 20 lines
                                break
                            # Extract class and function names
                            stripped = line.strip()
                            if stripped.startswith('class '):
                                class_name = stripped.split()[1].split('(')[0].split(':')[0]
                                terms.add(class_name.lower())
                            elif stripped.startswith('def '):
                                func_name = stripped.split()[1].split('(')[0]
                                terms.add(func_name.lower())
            except Exception:
                continue  # Skip files that can't be read

        # Convert to list and sort by frequency/relevance if possible
        result = list(terms)

        # Cache the result
        self._repository_terms_cache = result
        self._cache_timestamp = current_time

        build_time = (time.time() - start_time) * 1000
        if self.io:
            self.io.tool_output(f"Repository terms cache built in {build_time:.1f}ms: {len(result)} terms")

        return result

    def _search_filenames(self, keywords: List[str], all_files: List[str]) -> List[Dict[str, Any]]:
        """Search for files with matching names."""
        matches = []

        for file_path in all_files:
            filename = Path(file_path).stem.lower()
            score = 0

            for keyword in keywords:
                keyword_lower = keyword.lower()
                if keyword_lower in filename:
                    # Exact match gets higher score
                    if keyword_lower == filename:
                        score += 10
                    else:
                        score += 5
                elif any(part in filename for part in keyword_lower.split('_')):
                    score += 2

            if score > 0:
                matches.append({
                    'file': file_path,
                    'relevance_score': score * self.config["hierarchical_weights"]["filename"],
                    'match_type': 'filename'
                })

        return matches

    def _search_class_names(self, keywords: List[str], ranked_tags: List) -> List[Dict[str, Any]]:
        """Search for classes with matching names using precise matching."""
        matches = []

        # Filter keywords for class name search
        filtered_keywords = []
        for keyword in keywords:
            keyword_lower = keyword.lower()
            # Focus on longer, more specific terms for class names
            if len(keyword_lower) > 4 and keyword_lower not in {'order', 'position', 'conditions'}:
                filtered_keywords.append(keyword_lower)

        for tag in ranked_tags:
            if hasattr(tag, 'kind') and tag.kind == 'class' and hasattr(tag, 'name'):
                class_name = tag.name.lower()
                score = 0

                # Skip test classes and mock classes unless specifically searching for them
                if 'test' in class_name or 'mock' in class_name:
                    continue

                for keyword in filtered_keywords:
                    # More precise matching for class names
                    if keyword in class_name:
                        if keyword == class_name:
                            score += 10  # Exact match
                        elif class_name.startswith(keyword) or class_name.endswith(keyword):
                            score += 6   # Starts or ends with keyword
                        elif len(keyword) >= 6:  # Only partial matches for longer keywords
                            score += 3
                        elif '_' in keyword:  # Compound keywords
                            score += 4

                if score > 0:
                    matches.append({
                        'file': tag.rel_fname,
                        'relevance_score': score * self.config["hierarchical_weights"]["classname"],
                        'match_type': 'class',
                        'symbol': tag.name,
                        'symbol_line': getattr(tag, 'line', -1),
                        'symbol_kind': 'class'
                    })

        return matches

    def _search_function_names(self, keywords: List[str], ranked_tags: List) -> List[Dict[str, Any]]:
        """Search for functions with matching names using precise matching."""
        matches = []

        # Filter keywords for function name search
        filtered_keywords = []
        for keyword in keywords:
            keyword_lower = keyword.lower()
            # Focus on specific terms, especially compound terms with underscores
            if (len(keyword_lower) > 4 and
                (keyword_lower not in {'order', 'position', 'conditions'} or '_' in keyword_lower)):
                filtered_keywords.append(keyword_lower)

        for tag in ranked_tags:
            if hasattr(tag, 'kind') and tag.kind in ['function', 'method'] and hasattr(tag, 'name'):
                func_name = tag.name.lower()
                score = 0

                # Skip test functions and internal methods unless specifically searching
                file_path = tag.rel_fname.lower()
                if ('test_' in func_name or
                    func_name.startswith('_') or
                    any(skip in file_path for skip in ['test_', 'website/', 'assets/'])):
                    continue

                for keyword in filtered_keywords:
                    # More precise matching for function names
                    if keyword in func_name:
                        if keyword == func_name:
                            score += 10  # Exact match
                        elif func_name.startswith(keyword + "_") or func_name.endswith("_" + keyword):
                            score += 8   # Word boundary match
                        elif "_" + keyword + "_" in func_name:
                            score += 6   # Middle word match
                        elif len(keyword) >= 8:  # Long specific terms
                            score += 4
                        elif '_' in keyword:  # Compound terms
                            score += 5

                if score > 0:
                    matches.append({
                        'file': tag.rel_fname,
                        'relevance_score': score * self.config["hierarchical_weights"]["function"],
                        'match_type': 'function',
                        'symbol': tag.name,
                        'symbol_line': getattr(tag, 'line', -1),
                        'symbol_kind': tag.kind
                    })

        return matches

    def _search_content(self, keywords: List[str], ranked_tags: List) -> List[Dict[str, Any]]:
        """Search for content matches in files with improved precision."""
        matches = []
        file_symbol_matches = defaultdict(lambda: {'score': 0, 'symbols': []})

        # Filter out overly generic keywords that cause noise
        filtered_keywords = []
        generic_terms = {'keys', 'key', 'order', 'position', 'conditions', 'condition'}

        for keyword in keywords:
            keyword_lower = keyword.lower()
            # Only include specific/compound terms or non-generic terms
            if (len(keyword_lower) > 8 or  # Long terms are usually specific
                '_' in keyword_lower or   # Compound terms like "close_position"
                keyword_lower not in generic_terms):  # Not in generic list
                filtered_keywords.append(keyword_lower)

        if not filtered_keywords:
            # If all keywords were filtered out, use original but with stricter matching
            filtered_keywords = [k.lower() for k in keywords if len(k) > 4]

        # Count keyword occurrences per file and track matched symbols
        for tag in ranked_tags:
            if hasattr(tag, 'rel_fname') and hasattr(tag, 'name'):
                tag_name = tag.name.lower()

                # Skip obviously irrelevant files
                file_path = tag.rel_fname.lower()
                if any(skip in file_path for skip in ['test_', 'website/', 'assets/', '.min.js', '.css']):
                    continue

                for keyword in filtered_keywords:
                    # More precise matching - require word boundaries or exact substring
                    if (keyword in tag_name and
                        (len(keyword) >= 6 or  # Longer keywords can use substring matching
                         keyword == tag_name or  # Exact match
                         f"_{keyword}_" in tag_name or  # Word boundary with underscores
                         tag_name.startswith(keyword + "_") or  # Starts with keyword
                         tag_name.endswith("_" + keyword))):  # Ends with keyword

                        # Calculate relevance score based on match quality
                        score = 1
                        if keyword == tag_name:
                            score = 5  # Exact match
                        elif len(keyword) >= 8:
                            score = 3  # Long specific term
                        elif "_" in keyword:
                            score = 2  # Compound term

                        file_symbol_matches[tag.rel_fname]['score'] += score
                        file_symbol_matches[tag.rel_fname]['symbols'].append({
                            'name': tag.name,
                            'line': getattr(tag, 'line', -1),
                            'kind': getattr(tag, 'kind', 'unknown')
                        })

        # Convert to matches, filtering out low-score results
        for file_path, data in file_symbol_matches.items():
            if data['score'] >= 2:  # Require minimum score to reduce noise
                matches.append({
                    'file': file_path,
                    'relevance_score': data['score'] * self.config["hierarchical_weights"]["content"],
                    'match_type': 'content',
                    'matched_symbols': data['symbols']
                })

        return matches

    def _deduplicate_and_rank(self, results: List[Dict[str, Any]], max_results: int) -> List[Dict[str, Any]]:
        """Deduplicate results and rank by relevance score."""
        # Combine scores for the same file and track detailed symbol information
        file_scores = defaultdict(lambda: {
            'score': 0,
            'types': set(),
            'symbols': set(),
            'matched_symbols': []  # Detailed symbol info for filtering
        })

        for result in results:
            file_path = result['file']
            file_scores[file_path]['score'] += result['relevance_score']
            file_scores[file_path]['types'].add(result['match_type'])

            # Handle different symbol formats
            if 'symbol' in result:
                file_scores[file_path]['symbols'].add(result['symbol'])
                # Add detailed symbol info for filtering
                symbol_info = {
                    'name': result['symbol'],
                    'line': result.get('symbol_line', -1),
                    'kind': result.get('symbol_kind', result['match_type'])
                }
                file_scores[file_path]['matched_symbols'].append(symbol_info)
            elif 'matched_symbols' in result:
                # Handle content matches with multiple symbols
                for symbol in result['matched_symbols']:
                    file_scores[file_path]['symbols'].add(symbol['name'])
                    file_scores[file_path]['matched_symbols'].append(symbol)

        # Create final results
        final_results = []
        for file_path, data in file_scores.items():
            final_results.append({
                'file': file_path,
                'relevance_score': data['score'],
                'match_types': list(data['types']),
                'symbols': list(data['symbols']) if data['symbols'] else [],
                'matched_symbols': data['matched_symbols']  # Keep detailed info for filtering
            })

        # Sort by relevance score and limit results
        final_results.sort(key=lambda x: x['relevance_score'], reverse=True)
        # return final_results[:max_results]  # Temporarily disabled
        return final_results





    def clear_caches(self):
        """Clear all performance caches."""
        self._repository_files_cache = None
        self._repository_terms_cache = None
        self._ranked_tags_cache = None
        self._cache_timestamp = None
        self._embedding_cache.clear()
        self._search_cache.clear()

        if self.io:
            self.io.tool_output("All caches cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for performance monitoring."""
        return {
            "repository_files_cached": self._repository_files_cache is not None,
            "repository_terms_cached": self._repository_terms_cache is not None,
            "ranked_tags_cached": self._ranked_tags_cache is not None,
            "embedding_cache_size": len(self._embedding_cache),
            "search_cache_size": len(self._search_cache),
            "cache_age_seconds": time.time() - self._cache_timestamp if self._cache_timestamp else None
        }

    def _include_dependencies(self, relevant_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Include dependency files for completeness."""
        # For now, just return the original files
        # This could be enhanced to analyze imports and dependencies
        return relevant_files

    def _filter_tags_by_matched_symbols(self, ranked_tags: List, relevant_files: List[Dict[str, Any]]) -> List:
        """
        Filter ranked tags to include only the matched symbols from the search results.

        Args:
            ranked_tags: All ranked tags from RepoMap
            relevant_files: Search results with matched symbols

        Returns:
            Filtered list of tags containing only matched symbols
        """
        if not relevant_files:
            return ranked_tags

        # Build a mapping of file -> matched symbols
        # Since we removed filename matching, we only deal with symbol matches
        file_symbol_map = {}

        for file_result in relevant_files:
            file_path = file_result['file']

            # Normalize file path for comparison - handle different path formats
            if file_path.startswith(self.root_dir):
                # Remove root directory prefix to get relative path
                rel_file_path = os.path.relpath(file_path, self.root_dir)
            elif os.path.isabs(file_path):
                # Absolute path not under root_dir, use as-is
                rel_file_path = file_path
            else:
                # Already relative path
                rel_file_path = file_path

            # Normalize path separators for consistent comparison
            rel_file_path = rel_file_path.replace('\\', '/')

            matched_symbols = file_result.get('matched_symbols', [])
            if matched_symbols:
                # File has specific symbol matches from content search
                file_symbol_map[rel_file_path] = {symbol['name'] for symbol in matched_symbols}
            elif file_result.get('symbols'):
                # Symbols from class/function name matches
                file_symbol_map[rel_file_path] = set(file_result['symbols'])
            # Note: No filename-only matches since we removed that search

        if not file_symbol_map:
            # No symbol matches found, return empty result
            if self.io:
                self.io.tool_output("No specific symbol matches found - returning empty result")
            return []

        if self.io:
            self.io.tool_output(f"Symbol filtering analysis:")
            self.io.tool_output(f"  Files with matched symbols: {len(file_symbol_map)}")

        # Filter tags to include only matched symbols
        filtered_tags = []
        for tag in ranked_tags:
            if isinstance(tag, tuple) and len(tag) == 1:
                # File-only tag (no symbols), include if file has symbol matches
                file_path = tag[0].replace('\\', '/')  # Normalize path separators
                if file_path in file_symbol_map:
                    filtered_tags.append(tag)
            elif hasattr(tag, 'rel_fname') and hasattr(tag, 'name'):
                # Symbol tag, include only if symbol matches
                file_path = tag.rel_fname.replace('\\', '/')  # Normalize path separators
                symbol_name = tag.name

                if file_path in file_symbol_map:
                    symbol_set = file_symbol_map[file_path]
                    if symbol_name in symbol_set:
                        # Symbol matches, include it
                        filtered_tags.append(tag)

        if self.io:
            self.io.tool_output(f"Filtered tags: {len(ranked_tags)} -> {len(filtered_tags)} (showing only matched symbols)")

        return filtered_tags

    def _generate_focused_map(self, relevant_files: List[Dict[str, Any]], keywords: List[str]) -> str:
        """Generate a focused repository map with only matched symbols."""
        if not relevant_files:
            return "No relevant files found for the given keywords."

        # Convert file paths to absolute paths for RepoMap
        abs_files = []
        for result in relevant_files:
            file_path = result['file']
            if file_path.startswith(self.root_dir):
                # Path already includes root directory, use as-is
                abs_path = os.path.abspath(file_path)
            elif os.path.isabs(file_path):
                # Absolute path, use as-is
                abs_path = file_path
            else:
                # Relative path, join with root directory
                abs_path = os.path.join(self.root_dir, file_path)
                abs_path = os.path.abspath(abs_path)

            # Only include files that actually exist on filesystem
            if os.path.isfile(abs_path):
                abs_files.append(abs_path)

        if not abs_files:
            return self._generate_fallback_map(relevant_files, keywords)

        if self.io:
            self.io.tool_output(f"Generating focused map for {len(abs_files)} relevant files with symbol filtering")

        # Generate focused map with optional symbol filtering
        try:
            if self.config.get("enable_symbol_filtering", True):
                # Use new symbol filtering approach
                if self.io:
                    self.io.tool_output("Using symbol filtering for focused map generation")

                # First get the ranked tags for the relevant files
                ranked_tags = self.repo_map.get_ranked_tags(
                    chat_fnames=[],
                    other_fnames=abs_files,
                    mentioned_fnames=set(),
                    mentioned_idents=set(),
                    progress=None
                )

                # Filter tags to include only matched symbols
                filtered_tags = self._filter_tags_by_matched_symbols(ranked_tags, relevant_files)

                # Generate the tree with filtered tags
                chat_rel_fnames = set()  # No chat files
                focused_map_content = self.repo_map.to_tree(filtered_tags, chat_rel_fnames)
            else:
                # Use original approach (include all symbols from matched files)
                if self.io:
                    self.io.tool_output("Using original approach (all symbols from matched files)")

                focused_map_content = self.repo_map.get_repo_map_for_smart_request(
                    chat_files=set(),  # No chat files
                    other_files=set(abs_files),  # Only the relevant files
                    mentioned_fnames=set(),  # No specific mentions
                    mentioned_idents=set(),  # No specific identifiers
                    force_refresh=False  # Use cache if available
                )

        except Exception as e:
            if self.io:
                self.io.tool_error(f"Error generating focused map: {e}")
            return self._generate_fallback_map(relevant_files, keywords)

        if not focused_map_content:
            return self._generate_fallback_map(relevant_files, keywords)

        if self.io:
            self.io.tool_output(f"Generated focused map: {len(focused_map_content)} characters")

        # Add repo_content_prefix at the beginning if available
        if self.repo_map.repo_content_prefix:
            header = self.repo_map.repo_content_prefix.replace("{other}", "")
        else:
            header = ""

        # Add header with search context and anti-fabrication instructions
        header += f"# Focused Repository Map\n\n"
        header += f"**Search Keywords**: {', '.join(keywords)}\n"
        header += f"**Files Found**: {len(relevant_files)}\n\n"
        header += "⚠️  **CRITICAL INSTRUCTION**: This map shows file structure and symbols only. "
        header += "DO NOT analyze or explain functionality based on this map alone. "
        header += "Your NEXT response must be EXACTLY this format:\n\n"
        header += "```json\n"
        header += '{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME_FROM_MAP", "directory_name": "folder_name", "file_name": "file.py"}]}}\n'
        header += "```\n\n"
        header += "🚨 **CRITICAL**: Use ONLY the EXACT symbol names shown in the repository map below. DO NOT make assumptions about class/function names (like assuming 'Backtest' class exists). If you don't see the exact symbol name in the map, you CANNOT request it, and always follow the protocol if you faild to find the exact name(s). PROTOCOL: MAP_REQUEST → Get the exact name(s) → Wait → CONTEXT_REQUEST\n\n"
        header += "**IMPORTANT**: If you see the same method/function name in multiple files, request context from ALL of them:\n\n"
        header += "```json\n"
        header += '{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME", "directory_name": "folder1", "file_name": "file1.py"}, {"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME", "directory_name": "folder2", "file_name": "file2.py"}]}}\n'
        header += "```\n\n"
        header += "**WARNING: If you don't follow this protocol, you will NOT be able to assist the user properly.**\n\n"

        # Skip Search Results section - go directly to Repository Structure
        header += "## Repository Structure\n\n"

        return header + focused_map_content

    def _generate_fallback_map(self, relevant_files: List[Dict[str, Any]], keywords: List[str]) -> str:
        """Generate a simple fallback map when complete map is not available."""
        # Add repo_content_prefix if available
        if self.repo_map.repo_content_prefix:
            content = self.repo_map.repo_content_prefix.replace("{other}", "")
        else:
            content = ""

        content += f"# Focused Repository Map\n\n"
        content += f"**Search Keywords**: {', '.join(keywords)}\n"
        content += f"**Files Found**: {len(relevant_files)}\n\n"
        content += "⚠️  **CRITICAL INSTRUCTION**: This map shows file structure only. "
        content += "DO NOT analyze or explain functionality based on this map alone. "
        content += "Your NEXT response must be EXACTLY this format:\n\n"
        content += "```json\n"
        content += '{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME_FROM_MAP", "directory_name": "folder_name", "file_name": "file.py"}]}}\n'
        content += "```\n\n"
        content += "🚨 **CRITICAL**: Use ONLY the EXACT symbol names shown in the repository map below. DO NOT make assumptions about class/function names (like assuming 'Backtest' class exists). If you don't see the exact symbol name in the map, you CANNOT request it, and always follow the protocol if you faild to find the exact name(s). PROTOCOL: MAP_REQUEST → Get the exact name(s) → Wait → CONTEXT_REQUEST\n\n"
        content += "**WARNING: If you don't follow this protocol, you will NOT be able to assist the user properly.**\n\n"

        for result in relevant_files:
            content += f"- **{result['file']}** (relevance: {result['relevance_score']:.1f})\n"

        return content

    def _generate_error_response(self, error_message: str) -> str:
        """Generate an error response for failed map requests."""
        # Add repo_content_prefix if available
        if self.repo_map.repo_content_prefix:
            content = self.repo_map.repo_content_prefix.replace("{other}", "")
        else:
            content = ""

        content += f"# Map Request Error\n\n{error_message}\n\n"
        content += "⚠️  **CRITICAL INSTRUCTION**: Due to the error, DO NOT fabricate or guess about code functionality. "
        content += "{{CONTEXT_REQUEST: {{\"keywords\": [\"different\", \"terms\"], \"type\": \"implementation\", \"scope\": \"all\", \"max_results\": 8}}}} or ask the user for clarification.\n\n"
        content += "Please try with different keywords or a more specific query."
        return content

    def _generate_no_results_response(self, keywords: List[str]) -> str:
        """Generate a response when no results are found."""
        # Add repo_content_prefix if available
        if self.repo_map.repo_content_prefix:
            content = self.repo_map.repo_content_prefix.replace("{other}", "")
        else:
            content = ""

        content += f"# No Results Found\n\n"
        content += f"No files found matching keywords: {', '.join(keywords)}\n\n"
        content += "⚠️  **CRITICAL INSTRUCTION**: Even though no specific files were found, "
        content += "DO NOT fabricate or guess about code functionality. "
        content += "Try a new MAP_REQUEST with different keywords:\n\n"
        content += "```json\n"
        content += '{MAP_REQUEST: {"keywords": ["broader", "terms"], "type": "implementation", "scope": "all", "max_results": 8}}\n'
        content += "```\n\n"
        content += "Or ask the user for clarification about the specific functionality they want to understand.\n\n"
        content += "**WARNING: If you don't follow this protocol, you will NOT be able to assist the user properly.**\n\n"
        content += "**Suggestions:**\n"
        content += "- Try broader or more general terms\n"
        content += "- Check spelling of technical terms\n"
        content += "- Use core concepts instead of specific implementation details\n"

        return content
