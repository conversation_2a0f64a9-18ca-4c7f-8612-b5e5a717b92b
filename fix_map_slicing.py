#!/usr/bin/env python3
"""
Fix the aggressive repository map slicing algorithm
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))


def fix_binary_search_algorithm():
    """Fix the overly aggressive binary search algorithm in repomap.py"""
    
    print("🔧 Fixing Repository Map Slicing Algorithm")
    print("=" * 60)
    
    repomap_path = Path("aider-main/aider/repomap.py")
    
    # Read the current content
    with open(repomap_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the problematic binary search section
    old_algorithm = '''        middle = min(int(max_map_tokens // 25), num_tags)
        while lower_bound <= upper_bound:
            # dump(lower_bound, middle, upper_bound)

            if middle > 1500:
                show_tokens = f"{middle / 1000.0:.1f}K"
            else:
                show_tokens = str(middle)
            spin.step(f"{UPDATING_REPO_MAP_MESSAGE}: {show_tokens} tokens")

            tree = self.to_tree(ranked_tags[:middle], chat_rel_fnames)
            num_tokens = self.token_count(tree)

            pct_err = abs(num_tokens - max_map_tokens) / max_map_tokens
            ok_err = 0.15
            if (num_tokens <= max_map_tokens and num_tokens > best_tree_tokens) or pct_err < ok_err:
                best_tree = tree
                best_tree_tokens = num_tokens

                if pct_err < ok_err:
                    break

            if num_tokens < max_map_tokens:
                lower_bound = middle + 1
            else:
                upper_bound = middle - 1

            middle = int((lower_bound + upper_bound) // 2)'''
    
    # New, less aggressive algorithm
    new_algorithm = '''        # Improved algorithm: less aggressive slicing, better coverage
        # Start with a higher percentage of tags for better repository coverage
        initial_coverage = 0.75  # Start with 75% of tags instead of ~28%
        middle = min(int(num_tags * initial_coverage), num_tags)
        
        # Allow up to 25% over budget for comprehensive coverage
        flexible_budget = int(max_map_tokens * 1.25)
        
        while lower_bound <= upper_bound:
            # dump(lower_bound, middle, upper_bound)

            if middle > 1500:
                show_tokens = f"{middle / 1000.0:.1f}K"
            else:
                show_tokens = str(middle)
            spin.step(f"{UPDATING_REPO_MAP_MESSAGE}: {show_tokens} tokens")

            tree = self.to_tree(ranked_tags[:middle], chat_rel_fnames)
            num_tokens = self.token_count(tree)

            # More flexible error tolerance for better coverage
            pct_err = abs(num_tokens - max_map_tokens) / max_map_tokens
            ok_err = 0.25  # Increased from 0.15 to 0.25 for more flexibility
            
            # Accept solutions within flexible budget or with acceptable error
            within_flexible_budget = num_tokens <= flexible_budget
            acceptable_error = pct_err < ok_err
            better_coverage = num_tokens > best_tree_tokens
            
            if (within_flexible_budget and better_coverage) or acceptable_error:
                best_tree = tree
                best_tree_tokens = num_tokens

                # Only break early if we have good coverage and reasonable tokens
                coverage_ratio = middle / num_tags
                if acceptable_error and coverage_ratio > 0.6:  # At least 60% coverage
                    break

            if num_tokens < max_map_tokens:
                lower_bound = middle + 1
            else:
                upper_bound = middle - 1

            middle = int((lower_bound + upper_bound) // 2)'''
    
    # Replace the algorithm
    if old_algorithm in content:
        updated_content = content.replace(old_algorithm, new_algorithm)
        
        # Write the updated content
        with open(repomap_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Successfully updated binary search algorithm")
        print("📊 Changes made:")
        print("   • Initial coverage: 28% → 75%")
        print("   • Flexible budget: 100% → 125% of token limit")
        print("   • Error tolerance: 15% → 25%")
        print("   • Minimum coverage requirement: 60%")
        
        return True
    else:
        print("❌ Could not find the binary search algorithm to replace")
        return False


def add_intelligent_prioritization():
    """Add intelligent file prioritization beyond PageRank"""
    
    print("\n🧠 Adding Intelligent File Prioritization")
    print("=" * 50)
    
    repomap_path = Path("aider-main/aider/repomap.py")
    
    # Read the current content
    with open(repomap_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add intelligent prioritization function
    prioritization_function = '''

def calculate_file_importance_score(file_path, file_size=None):
    """
    Calculate importance score for a file beyond PageRank
    Higher scores = more important files
    """
    score = 1.0
    path_lower = file_path.lower()
    
    # Core architecture files get highest priority
    if any(keyword in path_lower for keyword in ['main', 'core', 'base', 'app', 'index']):
        score *= 3.0
    
    # API and interface files are very important
    if any(keyword in path_lower for keyword in ['api', 'interface', 'service', 'controller']):
        score *= 2.5
    
    # Model and data files are important
    if any(keyword in path_lower for keyword in ['model', 'schema', 'entity', 'data']):
        score *= 2.0
    
    # Configuration files are important
    if any(keyword in path_lower for keyword in ['config', 'setting', 'env']):
        score *= 1.8
    
    # Utility and helper files are moderately important
    if any(keyword in path_lower for keyword in ['util', 'helper', 'tool', 'lib']):
        score *= 1.5
    
    # Test files get lower priority (but still included)
    if any(keyword in path_lower for keyword in ['test', 'spec', 'mock']):
        score *= 0.3
    
    # Documentation and example files get lowest priority
    if any(keyword in path_lower for keyword in ['doc', 'readme', 'example', 'demo']):
        score *= 0.2
    
    # Cache and build files get very low priority
    if any(keyword in path_lower for keyword in ['cache', 'build', 'dist', 'tmp']):
        score *= 0.1
    
    # Prefer Python files in a Python project
    if path_lower.endswith('.py'):
        score *= 1.2
    
    # Penalize very large files (likely generated or data files)
    if file_size and file_size > 100000:  # > 100KB
        score *= 0.5
    
    return score


def apply_intelligent_ranking(ranked_tags, max_map_tokens):
    """
    Apply intelligent ranking to improve file selection
    """
    if not ranked_tags:
        return ranked_tags
    
    # Group tags by file and calculate importance scores
    file_scores = {}
    file_tags = {}
    
    for tag in ranked_tags:
        if len(tag) > 0:
            file_path = tag[0]
            if file_path not in file_scores:
                file_scores[file_path] = calculate_file_importance_score(file_path)
                file_tags[file_path] = []
            file_tags[file_path].append(tag)
    
    # Sort files by importance score (descending)
    sorted_files = sorted(file_scores.items(), key=lambda x: x[1], reverse=True)
    
    # Rebuild ranked_tags with intelligent prioritization
    prioritized_tags = []
    
    # First pass: Add high-priority files (score > 2.0)
    for file_path, score in sorted_files:
        if score > 2.0:
            prioritized_tags.extend(file_tags[file_path])
    
    # Second pass: Add medium-priority files (score 1.0-2.0)
    for file_path, score in sorted_files:
        if 1.0 <= score <= 2.0:
            prioritized_tags.extend(file_tags[file_path])
    
    # Third pass: Add low-priority files (score < 1.0)
    for file_path, score in sorted_files:
        if score < 1.0:
            prioritized_tags.extend(file_tags[file_path])
    
    return prioritized_tags
'''
    
    # Add the function before the class definition
    class_definition = "class RepoMap:"
    if class_definition in content:
        insertion_point = content.find(class_definition)
        updated_content = content[:insertion_point] + prioritization_function + "\n\n" + content[insertion_point:]
        
        # Write the updated content
        with open(repomap_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Added intelligent file prioritization functions")
        return True
    else:
        print("❌ Could not find insertion point for prioritization functions")
        return False


def integrate_prioritization_with_algorithm():
    """Integrate the prioritization with the ranking algorithm"""
    
    print("\n🔗 Integrating Prioritization with Ranking Algorithm")
    print("=" * 55)
    
    repomap_path = Path("aider-main/aider/repomap.py")
    
    # Read the current content
    with open(repomap_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find where ranked_tags is used in the binary search
    old_usage = "ranked_tags = self.get_ranked_tags("
    new_usage = """ranked_tags = self.get_ranked_tags(
            chat_fnames,
            other_fnames,
            mentioned_fnames,
            mentioned_idents,
            progress=spin.step,
        )
        
        # Apply intelligent prioritization for better file selection
        ranked_tags = apply_intelligent_ranking(ranked_tags, max_map_tokens)"""
    
    # Replace the usage
    if old_usage in content:
        # Find the complete function call
        start_idx = content.find(old_usage)
        if start_idx != -1:
            # Find the end of the function call
            paren_count = 0
            end_idx = start_idx
            for i, char in enumerate(content[start_idx:]):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_idx = start_idx + i + 1
                        break
            
            # Replace the function call
            old_call = content[start_idx:end_idx]
            updated_content = content.replace(old_call, new_usage)
            
            # Write the updated content
            with open(repomap_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ Integrated intelligent prioritization with ranking algorithm")
            return True
    
    print("❌ Could not find ranked_tags usage to modify")
    return False


def main():
    """Main function to fix the map slicing issues"""
    
    print("🚀 Fixing Repository Map Slicing Issues")
    print("=" * 60)
    
    success_count = 0
    
    # 1. Fix the binary search algorithm
    if fix_binary_search_algorithm():
        success_count += 1
    
    # 2. Add intelligent prioritization
    if add_intelligent_prioritization():
        success_count += 1
    
    # 3. Integrate prioritization with algorithm
    if integrate_prioritization_with_algorithm():
        success_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"✅ FIXES APPLIED: {success_count}/3")
    print("=" * 60)
    
    if success_count == 3:
        print("🎉 All fixes applied successfully!")
        print("\n📊 Expected Improvements:")
        print("   • Repository coverage: 37% → 75%+")
        print("   • Better file prioritization")
        print("   • More flexible token budgeting")
        print("   • Intelligent ranking beyond PageRank")
        
        print("\n📋 Next Steps:")
        print("   1. Test with: python diagnose_map_slicing.py")
        print("   2. Run aider to see improved coverage")
        print("   3. Monitor LLM response quality")
    else:
        print("⚠️  Some fixes failed - check the output above")
    
    return success_count == 3


if __name__ == "__main__":
    main()
