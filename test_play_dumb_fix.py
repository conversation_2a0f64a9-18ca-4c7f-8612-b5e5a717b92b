#!/usr/bin/env python3
"""
Test script to verify the "Play Dumb" rule is properly implemented
"""

import sys
import os

def test_play_dumb_prompts():
    """Test that the Play Dumb prompts are correctly formatted"""
    print("🧪 Testing Play Dumb Rule Implementation")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test 1: Check user prompt content
        user_prompt = prompts.smart_map_request_user_prompt
        print("📝 Smart Map Request User Prompt:")
        print(f"   Length: {len(user_prompt)} characters")
        print(f"   Content: {user_prompt}")
        
        # Check for key "Play Dumb" elements
        user_checks = [
            ("ZERO repository context", "✅" if "ZERO repository context" in user_prompt else "❌"),
            ("ZERO knowledge about this codebase", "✅" if "ZERO knowledge about this codebase" in user_prompt else "❌"),
            ("PLAY DUMB RULE", "✅" if "PLAY DUMB RULE" in user_prompt else "❌"),
            ("You know NOTHING", "✅" if "You know NOTHING" in user_prompt else "❌"),
            ("Do NOT make assumptions", "✅" if "Do NOT make assumptions" in user_prompt else "❌"),
            ("discover everything through MAP_REQUEST", "✅" if "discover everything through MAP_REQUEST" in user_prompt else "❌"),
            ("mandatory, not optional", "✅" if "mandatory, not optional" in user_prompt else "❌")
        ]
        
        print("\n🔍 User Prompt Checks:")
        for check, status in user_checks:
            print(f"   {status} {check}")
        
        # Test 2: Check assistant reply content
        assistant_reply = prompts.smart_map_request_assistant_reply
        print(f"\n📝 Smart Map Request Assistant Reply:")
        print(f"   Length: {len(assistant_reply)} characters")
        print(f"   Content: {assistant_reply}")
        
        # Check for key agreement elements
        assistant_checks = [
            ("I understand", "✅" if "I understand" in assistant_reply else "❌"),
            ("ZERO knowledge", "✅" if "ZERO knowledge" in assistant_reply else "❌"),
            ("PLAY DUMB", "✅" if "PLAY DUMB" in assistant_reply else "❌"),
            ("always start with MAP_REQUEST", "✅" if "always start with MAP_REQUEST" in assistant_reply else "❌"),
            ("not make any assumptions", "✅" if "not make any assumptions" in assistant_reply else "❌")
        ]
        
        print("\n🔍 Assistant Reply Checks:")
        for check, status in assistant_checks:
            print(f"   {status} {check}")
        
        # Test 3: Check message order is correct
        print(f"\n🔄 Message Order Test:")
        print(f"   User prompt should contain instructions: {'✅' if 'CRITICAL WORKFLOW RULE' in user_prompt else '❌'}")
        print(f"   Assistant reply should contain agreement: {'✅' if 'I understand' in assistant_reply else '❌'}")
        
        # Test 4: Check for grammar/spelling issues
        print(f"\n📝 Grammar Check:")
        grammar_issues = []
        if "unswer" in user_prompt or "unswer" in assistant_reply:
            grammar_issues.append("'unswer' should be 'answer'")
        if "exept" in user_prompt or "exept" in assistant_reply:
            grammar_issues.append("'exept' should be 'except'")
        if "i must" in assistant_reply:  # Should be "I must" (capitalized)
            grammar_issues.append("'i must' should be 'I must'")
            
        if grammar_issues:
            for issue in grammar_issues:
                print(f"   ❌ {issue}")
        else:
            print("   ✅ No grammar issues found")
        
        # Overall assessment
        all_user_checks_pass = all(status == "✅" for _, status in user_checks)
        all_assistant_checks_pass = all(status == "✅" for _, status in assistant_checks)
        no_grammar_issues = len(grammar_issues) == 0
        
        if all_user_checks_pass and all_assistant_checks_pass and no_grammar_issues:
            print("\n🎉 SUCCESS: Play Dumb rule is properly implemented!")
            return True
        else:
            print("\n❌ ISSUES FOUND: Play Dumb rule needs fixes")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_repo_messages_integration():
    """Test that the Play Dumb prompts work correctly in get_repo_messages"""
    print("\n🔗 Testing Integration with get_repo_messages")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        from aider.coders.base_coder import SMART_MAP_REQUEST_AVAILABLE
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
            
        print("✅ Smart Map Request System is available")
        
        # Test the prompts are accessible
        prompts = CoderPrompts()
        
        user_prompt = prompts.smart_map_request_user_prompt
        assistant_reply = prompts.smart_map_request_assistant_reply
        
        print(f"✅ User prompt loaded: {len(user_prompt)} characters")
        print(f"✅ Assistant reply loaded: {len(assistant_reply)} characters")
        
        # Simulate what get_repo_messages would create
        simulated_repo_messages = [
            dict(role="user", content=user_prompt),
            dict(role="assistant", content=assistant_reply),
        ]
        
        print(f"\n📋 Simulated repo_messages structure:")
        for i, msg in enumerate(simulated_repo_messages):
            role = msg['role']
            content_preview = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
            print(f"   {i+1}. {role}: {content_preview}")
        
        # Check that the conversation flow makes sense
        user_msg = simulated_repo_messages[0]
        assistant_msg = simulated_repo_messages[1]
        
        flow_checks = [
            ("User message has role 'user'", "✅" if user_msg['role'] == 'user' else "❌"),
            ("Assistant message has role 'assistant'", "✅" if assistant_msg['role'] == 'assistant' else "❌"),
            ("User gives instructions", "✅" if "CRITICAL WORKFLOW RULE" in user_msg['content'] else "❌"),
            ("Assistant acknowledges", "✅" if "I understand" in assistant_msg['content'] else "❌"),
            ("Play Dumb rule present", "✅" if "PLAY DUMB" in user_msg['content'] else "❌"),
            ("Assistant commits to Play Dumb", "✅" if "PLAY DUMB" in assistant_msg['content'] else "❌")
        ]
        
        print(f"\n🔍 Conversation Flow Checks:")
        for check, status in flow_checks:
            print(f"   {status} {check}")
        
        all_flow_checks_pass = all(status == "✅" for _, status in flow_checks)
        
        if all_flow_checks_pass:
            print("\n🎉 SUCCESS: Play Dumb integration works correctly!")
            return True
        else:
            print("\n❌ ISSUES: Integration has problems")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Play Dumb Rule Implementation")
    print("=" * 80)
    
    success1 = test_play_dumb_prompts()
    success2 = test_repo_messages_integration()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED: Play Dumb rule is working correctly!")
        print("\n📋 Summary of fixes:")
        print("   ✅ Fixed message order (user instruction → assistant agreement)")
        print("   ✅ Added comprehensive PLAY DUMB RULE")
        print("   ✅ Fixed grammar and spelling issues")
        print("   ✅ Clear, unambiguous instructions")
        print("   ✅ Proper integration with get_repo_messages")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
