#!/usr/bin/env python

import os
import sys
import time
import re
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""

    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []

    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")

    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


class MockRepoMap:
    """Mock RepoMap class for testing."""

    def __init__(self, version="files"):
        self.version = version
        self.files = {
            "file1.py": "content1",
            "file2.py": "content2",
            "file3.py": "content3"
        }

    def get_all_files(self):
        """Get all files in the repository."""
        if self.version == "get_all_files":
            return list(self.files.keys())
        else:
            raise AttributeError("'MockRepoMap' object has no attribute 'get_all_files'")

    def get_repo_overview(self):
        """Get an overview of the repository."""
        if self.version == "get_repo_overview":
            return """
file1.py:
│class Class1:
│    def method1(self):
│    def method2(self):
file2.py:
│class Class2:
│    def method3(self):
│    def method4(self):
file3.py:
│class Class3:
│    def method5(self):
│    def method6(self):
"""
        else:
            raise AttributeError("'MockRepoMap' object has no attribute 'get_repo_overview'")


class MockCoder:
    """Mock Coder class for testing."""

    def __init__(self, repo_map_version="files"):
        self.io = MockIO()
        self.root = os.getcwd()
        self.current_query_context_requests = 0
        self.repo_map = MockRepoMap(repo_map_version)

        # Initialize context request integration
        try:
            self.context_request_integration = AiderContextRequestIntegration(self.root)
        except Exception as e:
            self.io.tool_error(f"Failed to initialize context request integration: {e}")

    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )

        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)

        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt


def test_repo_map_version(version):
    """Test with a specific repo map version."""
    print(f"\n=== Testing with repo_map_version='{version}' ===")

    # Create a mock coder with the specified repo map version
    coder = MockCoder(version)

    # Simulate a user message
    user_message = "How does the surgical file extractor work?"
    print(f"\nUser: {user_message}")

    # Simulate an LLM response with a context request
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: {
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    print(f"\nLLM: {llm_response}")

    # Process the context request
    print("\nProcessing context request...")
    cleaned_content, context_prompt = coder.process_context_requests(llm_response, user_message)

    # Check if a context request was detected
    if context_prompt:
        print("✅ Context request detected and processed!")
        print("\nAugmented prompt:")
        print(context_prompt[:200] + "..." if len(context_prompt) > 200 else context_prompt)
        return True
    else:
        print("❌ Context request was not detected or processed")
        return False


def main():
    print("\n=== Testing Repository Map Compatibility ===")

    # Test with different repo map versions
    versions = ["files", "get_all_files", "get_repo_overview"]

    for version in versions:
        success = test_repo_map_version(version)
        if not success:
            print(f"❌ Test failed for repo_map_version='{version}'")

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
