#!/usr/bin/env python3
"""
Test script to verify UI formatting for MAP_REQUEST responses.
This simulates what the user sees in the UI when a MAP_REQUEST is processed.
"""

import sys
import os
import json

# Add the aider directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.smart_map_request_handler import SmartMapRequestHandler
from aider.io import InputOutput
from aider.repo import GitRepo
from aider.repomap import RepoMap

class MockIO:
    """Mock IO class to capture output for testing."""
    def __init__(self):
        self.outputs = []
        self.assistant_outputs = []

    def tool_output(self, message, log_only=False):
        self.outputs.append(f"TOOL: {message}")
        print(f"🔧 TOOL: {message}")

    def tool_error(self, message):
        self.outputs.append(f"ERROR: {message}")
        print(f"❌ ERROR: {message}")

    def tool_warning(self, message):
        self.outputs.append(f"WARNING: {message}")
        print(f"⚠️  WARNING: {message}")

    def assistant_output(self, message, pretty=None):
        """This simulates what the user sees in the UI."""
        self.assistant_outputs.append(message)
        print("\n" + "="*80)
        print("📱 UI DISPLAY (What user sees):")
        print("="*80)
        print(message)
        print("="*80)

def test_ui_formatting():
    """Test the UI formatting for MAP_REQUEST responses."""
    print("🧪 Testing UI Formatting for MAP_REQUEST")
    print("="*80)

    try:
        # Initialize components
        mock_io = MockIO()

        # Create a mock coder class with the display method
        class MockCoder:
            def __init__(self):
                self.io = mock_io

            def _display_map_response_to_user(self, focused_map, request_json):
                """
                This is the actual method from base_coder.py that we fixed.
                """
                if not focused_map:
                    return

                # Extract key information from the request
                keywords = request_json.get('keywords', [])
                max_results = request_json.get('max_results', 'N/A')
                request_type = request_json.get('type', 'N/A')

                # Create a user-friendly header
                header = f"📍 **MAP_REQUEST Response**\n"
                header += f"**Keywords**: {', '.join(keywords) if keywords else 'N/A'}\n"
                header += f"**Type**: {request_type}\n"
                header += f"**Max Results**: {max_results}\n\n"

                # Extract file count from the focused map if possible
                import re
                files_found_match = re.search(r'\*\*Files Found\*\*:\s*(\d+)', focused_map)
                if files_found_match:
                    files_count = files_found_match.group(1)
                    header += f"**Files Found**: {files_count}\n\n"

                # Extract search results section for better preview
                search_results_match = re.search(r'## Search Results\n\n(.*?)(?=\n## Repository Structure|\n$)', focused_map, re.DOTALL)
                repo_structure_match = re.search(r'## Repository Structure\n\n(.*?)$', focused_map, re.DOTALL)

                # Create a structured preview
                preview_content = header

                if search_results_match:
                    search_results = search_results_match.group(1).strip()
                    preview_content += "**Search Results**:\n"
                    # Show first 5 lines of search results
                    search_lines = search_results.split('\n')[:8]
                    for line in search_lines:
                        if line.strip():
                            preview_content += f"{line}\n"
                    if len(search_results.split('\n')) > 8:
                        preview_content += "... (more results)\n"
                    preview_content += "\n"

                if repo_structure_match:
                    repo_structure = repo_structure_match.group(1).strip()
                    preview_content += "**Repository Structure Preview**:\n"
                    # Show first few files from repository structure
                    structure_lines = repo_structure.split('\n')[:12]
                    for line in structure_lines:
                        if line.strip():
                            preview_content += f"{line}\n"
                    if len(repo_structure.split('\n')) > 12:
                        preview_content += "... (more files and symbols)\n"

                preview_content += "\n*Full repository map sent to LLM for analysis*"

                # Use tool_output to display in chat - this works correctly in both terminal and GUI
                self.io.tool_output("MAP_REQUEST processed - Repository map generated and sent to LLM")

                # Display the preview using tool_output which renders correctly in both terminal and GUI
                # Split the content into lines and display each line to ensure proper formatting
                preview_lines = preview_content.split('\n')
                for line in preview_lines:
                    if line.strip():  # Only display non-empty lines
                        self.io.tool_output(line)

        # Create mock coder
        coder = MockCoder()

        # Create a sample focused map (like what would be generated)
        sample_focused_map = """# Focused Repository Map

**Search Keywords**: close_position_based_on_conditions, conditions, trade, order, position
**Files Found**: 5

⚠️  **CRITICAL INSTRUCTION**: This map shows file structure and symbols only.

## Search Results

1. **strategy/gbp_strategy.py** (score: 13.0)
   - Match types: content
   - Symbols: calculate_conditions, GBPConditionStrategy, _check_sell_close_conditions

2. **strategy/eur_strategy.py** (score: 13.0)
   - Match types: content
   - Symbols: calculate_conditions, EURConditionStrategy, _check_sell_close_conditions

3. **trade_management/position_exit_manager.py** (score: 8.0)
   - Match types: content
   - Symbols: close_position_based_on_conditions

... and 2 more files

## Repository Structure

strategy/gbp_strategy.py:
⋮
│class GBPConditionStrategy(BaseStrategyWithRetry):
│    def calculate_conditions(self, targets: Dict[str, Any], price_data: Dict[str, Any]):
⋮
│    def _check_sell_close_conditions(self, current_price: float, current_low: float):
⋮

trade_management/position_exit_manager.py:
⋮
│class PositionCloser:
│    async def close_position_based_on_conditions(self, app):
⋮
"""

        # Create sample request JSON
        sample_request = {
            "keywords": ["close_position_based_on_conditions", "conditions", "trade", "order", "position"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        print("📝 Testing with sample MAP_REQUEST response...")
        print(f"Request: {json.dumps(sample_request, indent=2)}")
        print("\n" + "-"*80)

        # Test the display method
        coder._display_map_response_to_user(sample_focused_map, sample_request)

        print("\n" + "-"*80)
        print("✅ UI Formatting Test Completed!")
        print(f"📊 Captured {len(mock_io.assistant_outputs)} assistant outputs")
        print(f"📊 Captured {len(mock_io.outputs)} tool outputs")

        # Show what was captured
        if mock_io.assistant_outputs:
            print("\n📱 Final UI Output Preview:")
            print("-"*40)
            print(mock_io.assistant_outputs[0][:500] + "..." if len(mock_io.assistant_outputs[0]) > 500 else mock_io.assistant_outputs[0])
            print("-"*40)

        return True

    except Exception as e:
        print(f"❌ Error in UI formatting test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ui_formatting()
    if success:
        print("\n🎉 UI formatting test passed!")
    else:
        print("\n💥 UI formatting test failed!")
        sys.exit(1)
