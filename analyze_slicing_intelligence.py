#!/usr/bin/env python3
"""
Analyze the intelligence of the current repository map slicing system
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))


def analyze_pagerank_intelligence():
    """Analyze how intelligent the PageRank algorithm is"""
    
    print("🧠 Analyzing PageRank Intelligence")
    print("=" * 50)
    
    print("✅ SMART ASPECTS:")
    print("   1. Uses NetworkX PageRank algorithm")
    print("   2. Builds dependency graph from symbol references")
    print("   3. Weights edges by reference frequency")
    print("   4. Gives 50x boost to files in chat context")
    print("   5. Uses square root to prevent high-frequency dominance")
    print("   6. Handles personalization for mentioned files/identifiers")
    
    print("\n❌ LIMITATIONS:")
    print("   1. Only considers symbol-level dependencies")
    print("   2. No file-type awareness (treats all .py files equally)")
    print("   3. No architectural pattern recognition")
    print("   4. No consideration of file size or complexity")
    print("   5. No semantic understanding of code importance")
    print("   6. No project-specific customization")


def analyze_binary_search_intelligence():
    """Analyze how intelligent the binary search slicing is"""
    
    print("\n🔪 Analyzing Binary Search Slicing Intelligence")
    print("=" * 55)
    
    print("✅ SMART ASPECTS:")
    print("   1. Uses binary search for efficient token fitting")
    print("   2. Respects PageRank ordering (includes highest-ranked first)")
    print("   3. Has error tolerance (15% over budget acceptable)")
    print("   4. Iterative refinement to find optimal cutoff")
    
    print("\n❌ MAJOR FLAWS:")
    print("   1. BLIND TRUNCATION: Cuts off at arbitrary point")
    print("   2. NO SEMANTIC AWARENESS: May cut critical dependencies")
    print("   3. NO FILE COMPLETENESS: May include partial file coverage")
    print("   4. NO ARCHITECTURAL UNDERSTANDING: May exclude core files")
    print("   5. PURELY TOKEN-DRIVEN: Ignores code relationships")
    print("   6. NO FALLBACK STRATEGIES: All-or-nothing inclusion")


def analyze_what_gets_excluded():
    """Analyze what actually gets excluded and why it's problematic"""
    
    print("\n📊 Analyzing What Gets Excluded")
    print("=" * 40)
    
    try:
        from aider.repomap import RepoMap, find_src_files
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create repo map
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo_map = RepoMap(
            map_tokens=20202,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        # Get ranked tags
        all_files = find_src_files("aider-main")
        ranked_tags = repo_map.get_ranked_tags(
            chat_fnames=[],
            other_fnames=all_files,
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        # Simulate current slicing
        max_map_tokens = repo_map.max_map_tokens
        num_tags = len(ranked_tags)
        
        # Find current cutoff point
        lower_bound = 0
        upper_bound = num_tags
        middle = min(int(max_map_tokens // 25), num_tags)
        best_middle = 0
        
        for _ in range(10):  # Simulate binary search
            tree = repo_map.to_tree(ranked_tags[:middle], set())
            num_tokens = repo_map.token_count(tree)
            
            if num_tokens <= max_map_tokens:
                best_middle = middle
                lower_bound = middle + 1
            else:
                upper_bound = middle - 1
            
            middle = int((lower_bound + upper_bound) // 2)
            if lower_bound > upper_bound:
                break
        
        # Analyze included vs excluded
        included_tags = ranked_tags[:best_middle]
        excluded_tags = ranked_tags[best_middle:]
        
        print(f"📊 Current Slicing Results:")
        print(f"   Included: {len(included_tags):,} tags ({(len(included_tags)/num_tags)*100:.1f}%)")
        print(f"   Excluded: {len(excluded_tags):,} tags ({(len(excluded_tags)/num_tags)*100:.1f}%)")
        
        # Analyze file coverage
        included_files = set()
        excluded_files = set()
        
        for tag in included_tags:
            if len(tag) > 0:
                included_files.add(tag[0])
        
        for tag in excluded_tags:
            if len(tag) > 0:
                excluded_files.add(tag[0])
        
        print(f"\n📁 File Coverage:")
        print(f"   Included files: {len(included_files):,}")
        print(f"   Excluded files: {len(excluded_files):,}")
        print(f"   Partially covered files: {len(included_files & excluded_files):,}")
        
        # Analyze what types of files get excluded
        excluded_only = excluded_files - included_files
        
        print(f"\n🚨 CRITICAL ISSUES:")
        print(f"   Completely excluded files: {len(excluded_only):,}")
        
        # Sample excluded files
        print(f"\n📋 Sample of Completely Excluded Files:")
        for i, filename in enumerate(sorted(excluded_only)[:10]):
            file_type = "Core" if any(x in filename.lower() for x in ['main', 'core', 'base']) else "Other"
            print(f"   {i+1:2d}. {filename} ({file_type})")
        
        # Check for critical exclusions
        critical_patterns = ['main.py', 'core', 'base', 'app.py', '__init__.py']
        critical_excluded = []
        
        for filename in excluded_only:
            if any(pattern in filename.lower() for pattern in critical_patterns):
                critical_excluded.append(filename)
        
        if critical_excluded:
            print(f"\n💥 CRITICAL FILES EXCLUDED:")
            for filename in critical_excluded[:5]:
                print(f"   ⚠️  {filename}")
        
        return {
            'included_tags': len(included_tags),
            'excluded_tags': len(excluded_tags),
            'included_files': len(included_files),
            'excluded_files': len(excluded_files),
            'completely_excluded': len(excluded_only),
            'critical_excluded': len(critical_excluded)
        }
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return None


def propose_intelligent_improvements():
    """Propose intelligent improvements to the slicing algorithm"""
    
    print("\n🚀 Intelligent Improvement Proposals")
    print("=" * 45)
    
    print("1. 🎯 SEMANTIC FILE PRIORITIZATION")
    print("   • Recognize architectural patterns (MVC, microservices)")
    print("   • Prioritize entry points, APIs, core business logic")
    print("   • Deprioritize tests, docs, build files")
    print("   • File-type specific importance scoring")
    
    print("\n2. 🔗 DEPENDENCY-AWARE SLICING")
    print("   • Include complete dependency chains")
    print("   • Never break critical import relationships")
    print("   • Ensure interface completeness")
    print("   • Maintain architectural coherence")
    
    print("\n3. 📦 TIERED INCLUSION STRATEGY")
    print("   • Tier 1: Core architecture (always include)")
    print("   • Tier 2: Business logic (high priority)")
    print("   • Tier 3: Utilities and helpers (medium priority)")
    print("   • Tier 4: Tests and docs (low priority)")
    
    print("\n4. 🧠 CONTEXT-AWARE OPTIMIZATION")
    print("   • Adapt to query context")
    print("   • Include relevant subsystems")
    print("   • Dynamic token allocation")
    print("   • Smart partial file inclusion")
    
    print("\n5. 📊 QUALITY METRICS")
    print("   • Measure architectural coverage")
    print("   • Track dependency completeness")
    print("   • Monitor LLM response quality")
    print("   • Adaptive threshold tuning")


def main():
    """Main analysis function"""
    
    print("🔍 Repository Map Slicing Intelligence Analysis")
    print("=" * 60)
    
    # Analyze current intelligence
    analyze_pagerank_intelligence()
    analyze_binary_search_intelligence()
    
    # Analyze what gets excluded
    results = analyze_what_gets_excluded()
    
    # Propose improvements
    propose_intelligent_improvements()
    
    print("\n" + "=" * 60)
    print("📋 CONCLUSION")
    print("=" * 60)
    
    if results:
        exclusion_rate = (results['excluded_tags'] / (results['included_tags'] + results['excluded_tags'])) * 100
        
        print(f"🎯 CURRENT SYSTEM ASSESSMENT:")
        print(f"   Intelligence Level: MODERATE")
        print(f"   PageRank Algorithm: ✅ Smart")
        print(f"   Binary Search Slicing: ❌ Dumb")
        print(f"   Exclusion Rate: {exclusion_rate:.1f}% (TOO HIGH)")
        print(f"   Critical Files Lost: {results.get('critical_excluded', 0)}")
        
        print(f"\n🚨 KEY PROBLEMS:")
        print(f"   1. Blind truncation ignores code semantics")
        print(f"   2. No architectural awareness")
        print(f"   3. Breaks dependency relationships")
        print(f"   4. Excludes potentially critical files")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Replace binary search with intelligent tiered inclusion")
        print(f"   Implement semantic file prioritization")
        print(f"   Add dependency-aware slicing")
        print(f"   Focus on architectural completeness over token limits")
    
    print("\n🎯 The system is HALF-SMART: Great ranking, terrible slicing!")


if __name__ == "__main__":
    main()
