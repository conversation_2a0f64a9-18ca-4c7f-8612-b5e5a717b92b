#!/usr/bin/env python

import os
import sys
import argparse
from pathlib import Path

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from aider_context_request_integration import AiderContextRequestIntegration
    from context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider_template_renderer import AiderTemplateRender<PERSON>
    from aider_integration_service import AiderIntegrationService
except ImportError:
    print("Error: Could not import the required modules.")
    print("Make sure you have implemented the surgical extraction modules.")
    sys.exit(1)


def parse_args():
    parser = argparse.ArgumentParser(description="Test the CONTEXT_REQUEST functionality")
    parser.add_argument(
        "--symbol",
        type=str,
        default="SurgicalFileExtractor.extract_symbol_content",
        help="Symbol to extract (default: SurgicalFileExtractor.extract_symbol_content)",
    )
    parser.add_argument(
        "--file-hint",
        type=str,
        default="surgical_file_extractor.py",
        help="File hint for the symbol (default: surgical_file_extractor.py)",
    )
    parser.add_argument(
        "--reason",
        type=str,
        default="To understand how the surgical file extractor works",
        help="Reason for the context request",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\n=== LLM Instructions ===")
    print(integration.get_llm_instructions())
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name=args.symbol,
                file_hint=args.file_hint
            )
        ],
        reason_for_request=args.reason
    )
    
    # Process the context request
    print("\n=== Processing Context Request ===")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt ===")
    print(augmented_prompt)
    
    # Test with a real LLM response
    llm_response = f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{ 
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {{"type": "method_definition", "name": "{args.symbol}", "file_hint": "{args.file_hint}"}}
  ],
  "reason_for_request": "{args.reason}"
}}}}
"""
    
    # Detect the context request
    print("\n=== Detecting Context Request ===")
    detected_request = integration.detect_context_request(llm_response)
    if detected_request:
        print(f"Detected request: {integration.get_context_request_summary(detected_request)}")
        
        # Process the detected request
        print("\n=== Processing Detected Context Request ===")
        augmented_prompt = integration.process_context_request(
            context_request=detected_request,
            original_user_query="How does the surgical file extractor work?",
            repo_overview=repo_overview
        )
        
        # Print the augmented prompt
        print("\n=== Augmented Prompt from Detected Request ===")
        print(augmented_prompt)
    else:
        print("No context request detected")


if __name__ == "__main__":
    main()
