{"total_size_bytes": 61253, "total_size_kb": 59.8173828125, "total_size_mb": 0.05841541290283203, "num_files_mapped": 833, "num_symbols_tracked": 637, "memory_usage_mb": 224.95703125, "total_tokens": 17453.554054054057, "tokens_per_file": {}, "token_efficiency_ratio": 0.28494202821174563, "context_window_usage_pct": 13.635589104729732, "symbol_detection_accuracy_pct": 100.0, "missing_symbols": [], "incorrectly_mapped_symbols": [], "error_rate_pct": 0.0, "generation_time_seconds": 6.3683319091796875, "cache_hit_rate_pct": 23.931849890451108, "processing_bottlenecks": ["Slow generation time", "High memory usage"], "codebase_coverage_pct": 117.79661016949152, "file_types_included": ["", ".c", ".chatito", ".cpp", ".cs", ".css", ".d", ".dart", ".db", ".db-shm", ".db-wal", ".el", ".elm", ".env", ".ex", ".gleam", ".go", ".html", ".ico", ".in", ".ini", ".ino", ".java", ".jek<PERSON>l", ".jpg", ".js", ".json", ".j<PERSON>l", ".kt", ".lisp", ".lua", ".md", ".ml", ".mli", ".mp3", ".mp4", ".php", ".png", ".pony", ".properties", ".ps1", ".py", ".ql", ".r", ".rb", ".rkt", ".rs", ".rules", ".scala", ".scm", ".scss", ".sh", ".sol", ".svg", ".swift", ".tf", ".toml", ".ts", ".tsx", ".ttf", ".txt", ".val", ".webmanifest", ".xml", ".yaml", ".yml"], "file_types_excluded": [], "dependencies_captured": 0}