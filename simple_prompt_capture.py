#!/usr/bin/env python3
"""
Simple script to capture the messages that would be sent to the LLM.
This works by inspecting cur_messages after the IR context is injected.
"""

import sys
import os
import json

# Add aider to path
sys.path.insert(0, "aider-main")

def capture_simple_prompt():
    """Capture the messages in cur_messages after IR context injection."""
    
    print("🔍 SIMPLE PROMPT CAPTURE")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use external project path
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        repo = GitRepo(io, [], external_project)
        
        print(f"📁 Using project path: {external_project}")
        
        # Create coder
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192,
            verbose=True,
            dry_run=True
        )
        
        # Test query
        test_query = "How does position management work in the trading system?"
        print(f"\n📝 Testing with query: '{test_query}'")
        
        # Initialize before message
        coder.init_before_message()
        
        # Process the direct IR context
        print("🔄 Processing direct IR context...")
        ir_result = coder.process_direct_ir_context(test_query)
        print(f"✅ IR context result: {ir_result}")
        
        # Check what's in cur_messages
        if hasattr(coder, 'cur_messages') and coder.cur_messages:
            messages = coder.cur_messages
            print(f"\n📨 Found {len(messages)} messages in cur_messages")
            
            # Create human-readable prompt
            prompt_parts = []
            prompt_parts.append("=" * 80)
            prompt_parts.append("COMPLETE PROMPT THAT WOULD BE SENT TO LLM")
            prompt_parts.append("=" * 80)
            prompt_parts.append(f"Total messages: {len(messages)}")
            prompt_parts.append(f"Query: {test_query}")
            prompt_parts.append("=" * 80)
            
            total_chars = 0
            for i, msg in enumerate(messages, 1):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                total_chars += len(content)
                
                prompt_parts.append(f"\n{'='*15} MESSAGE {i}: {role.upper()} {'='*15}")
                prompt_parts.append(f"Length: {len(content):,} characters")
                
                # Show first few lines for preview
                lines = content.split('\n')
                if len(lines) > 10:
                    preview_lines = lines[:10] + [f"... ({len(lines)-10} more lines)"]
                else:
                    preview_lines = lines
                
                prompt_parts.append("Preview (first 10 lines):")
                prompt_parts.append("-" * 50)
                for line_num, line in enumerate(preview_lines, 1):
                    prompt_parts.append(f"{line_num:3d}: {line}")
                prompt_parts.append("-" * 50)
                
                # Check for key content
                if 'Intelligent Context for Your Query' in content:
                    prompt_parts.append("🎯 THIS IS THE IR CONTEXT MESSAGE!")
                    
                    # Extract key information
                    if 'process_all_positions' in content:
                        prompt_parts.append("✅ Contains trading system functions")
                    if 'show_send_output' in content:
                        prompt_parts.append("❌ Contains aider functions (unexpected)")
                    
                    # Count entities
                    entity_count = content.count('### ')
                    prompt_parts.append(f"📊 Contains {entity_count} entities")
                
                prompt_parts.append("")
            
            prompt_parts.append(f"📊 TOTAL PROMPT SIZE: {total_chars:,} characters")
            prompt_parts.append("=" * 80)
            
            # Save the complete prompt
            full_prompt = "\n".join(prompt_parts)
            
            with open("complete_llm_prompt.txt", "w", encoding="utf-8") as f:
                f.write(full_prompt)
            
            # Save the raw messages
            with open("raw_messages.json", "w", encoding="utf-8") as f:
                json.dump(messages, f, indent=2, ensure_ascii=False)
            
            # Save each message separately for easier reading
            for i, msg in enumerate(messages, 1):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                filename = f"message_{i}_{role}.txt"
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(f"MESSAGE {i}: {role.upper()}\n")
                    f.write("=" * 50 + "\n")
                    f.write(content)
                print(f"💾 Saved message {i} ({role}) to: {filename}")
            
            print(f"\n💾 Saved complete prompt to: complete_llm_prompt.txt")
            print(f"💾 Saved raw messages to: raw_messages.json")
            
            # Show summary
            print(f"\n📊 PROMPT SUMMARY:")
            for i, msg in enumerate(messages, 1):
                role = msg.get('role', 'unknown')
                content_len = len(msg.get('content', ''))
                print(f"   {i}. {role}: {content_len:,} chars")
            
            print(f"\n🎯 KEY FINDINGS:")
            ir_found = any('Intelligent Context for Your Query' in msg.get('content', '') for msg in messages)
            trading_found = any('process_all_positions' in msg.get('content', '') for msg in messages)
            aider_found = any('show_send_output' in msg.get('content', '') for msg in messages)
            
            print(f"   • IR Context found: {'✅' if ir_found else '❌'}")
            print(f"   • Trading system context: {'✅' if trading_found else '❌'}")
            print(f"   • Aider context (bad): {'❌' if aider_found else '✅'}")
            
            return True
        else:
            print("❌ No messages found in cur_messages")
            return False
        
    except Exception as e:
        print(f"❌ Error during capture: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 SIMPLE LLM PROMPT CAPTURE")
    print("This captures the messages that would be sent to the LLM\n")
    
    success = capture_simple_prompt()
    
    if success:
        print("\n🎉 Prompt capture completed successfully!")
        print("\n📁 Files created:")
        print("   • complete_llm_prompt.txt - Full prompt overview")
        print("   • raw_messages.json - Raw message data")
        print("   • message_1_system.txt - Individual message files")
        print("   • message_2_system.txt")
        print("   • message_3_user.txt")
        print("   • etc...")
        print("\n🔍 Check these files to see exactly what the LLM receives!")
    else:
        print("\n❌ Prompt capture failed.")
