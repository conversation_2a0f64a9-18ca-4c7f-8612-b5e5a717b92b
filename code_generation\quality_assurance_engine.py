"""
Quality Assurance Engine

Validates generated code quality, style consistency, and integration safety.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import ast
import re

from .architectural_pattern_analyzer import ArchitecturalAnalysis


@dataclass
class QualityReport:
    """Quality assessment report for generated code."""
    overall_score: float
    style_score: float
    complexity_score: float
    maintainability_score: float
    security_score: float
    issues: List[str]
    recommendations: List[str]


class QualityAssuranceEngine:
    """
    Validates and assesses the quality of generated code.
    
    This engine:
    - Performs static code analysis
    - Validates style consistency
    - Checks for security issues
    - Assesses maintainability
    - Provides improvement recommendations
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the quality assurance engine."""
        self.config = config
        self.verbose = config.get('verbose', False)
        
        # Quality thresholds
        self.thresholds = {
            'min_overall_score': config.get('min_overall_score', 0.7),
            'max_complexity': config.get('max_complexity', 10),
            'min_documentation': config.get('min_documentation', 0.8)
        }
    
    def assess_quality(self, code: str, 
                      architectural_analysis: ArchitecturalAnalysis) -> QualityReport:
        """
        Perform comprehensive quality assessment of generated code.
        
        Args:
            code: The generated code to assess
            architectural_analysis: Analysis of existing codebase architecture
            
        Returns:
            Quality assessment report
        """
        if self.verbose:
            print("📊 Performing quality assessment")
        
        issues = []
        recommendations = []
        
        # Style assessment
        style_score = self._assess_style(code, architectural_analysis, issues, recommendations)
        
        # Complexity assessment
        complexity_score = self._assess_complexity(code, issues, recommendations)
        
        # Maintainability assessment
        maintainability_score = self._assess_maintainability(code, issues, recommendations)
        
        # Security assessment
        security_score = self._assess_security(code, issues, recommendations)
        
        # Calculate overall score
        overall_score = (
            style_score * 0.25 +
            complexity_score * 0.25 +
            maintainability_score * 0.25 +
            security_score * 0.25
        )
        
        if self.verbose:
            print(f"   Overall quality score: {overall_score:.2f}")
            print(f"   Issues found: {len(issues)}")
        
        return QualityReport(
            overall_score=overall_score,
            style_score=style_score,
            complexity_score=complexity_score,
            maintainability_score=maintainability_score,
            security_score=security_score,
            issues=issues,
            recommendations=recommendations
        )
    
    def _assess_style(self, code: str, architectural_analysis: ArchitecturalAnalysis,
                     issues: List[str], recommendations: List[str]) -> float:
        """Assess code style consistency."""
        score = 0.8  # Base score
        
        # Check line length
        lines = code.split('\n')
        max_line_length = architectural_analysis.coding_style.line_length
        long_lines = [i for i, line in enumerate(lines, 1) 
                     if len(line) > max_line_length and line.strip()]
        
        if long_lines:
            issues.append(f"Lines exceed maximum length ({max_line_length}): {long_lines[:3]}")
            score -= 0.1
        
        # Check naming conventions
        naming_issues = self._check_naming_conventions(code, architectural_analysis.coding_style.naming_conventions)
        if naming_issues:
            issues.extend(naming_issues)
            score -= 0.1
        
        # Check indentation consistency
        indentation_issues = self._check_indentation(code, architectural_analysis.coding_style.indentation)
        if indentation_issues:
            issues.extend(indentation_issues)
            score -= 0.1
        
        return max(0.0, score)
    
    def _assess_complexity(self, code: str, issues: List[str], 
                          recommendations: List[str]) -> float:
        """Assess code complexity."""
        score = 1.0
        
        try:
            tree = ast.parse(code)
            complexity_analyzer = ComplexityAnalyzer()
            complexity_analyzer.visit(tree)
            
            max_complexity = max(complexity_analyzer.complexities) if complexity_analyzer.complexities else 0
            avg_complexity = sum(complexity_analyzer.complexities) / len(complexity_analyzer.complexities) if complexity_analyzer.complexities else 0
            
            if max_complexity > self.thresholds['max_complexity']:
                issues.append(f"High complexity function detected: {max_complexity}")
                recommendations.append("Consider breaking down complex functions")
                score -= 0.3
            
            if avg_complexity > 5:
                score -= 0.1
                
        except SyntaxError:
            issues.append("Code contains syntax errors")
            score = 0.0
        
        return max(0.0, score)
    
    def _assess_maintainability(self, code: str, issues: List[str],
                               recommendations: List[str]) -> float:
        """Assess code maintainability."""
        score = 0.7  # Base score
        
        # Check for docstrings
        docstring_coverage = self._calculate_docstring_coverage(code)
        if docstring_coverage < self.thresholds['min_documentation']:
            issues.append(f"Low documentation coverage: {docstring_coverage:.1%}")
            recommendations.append("Add comprehensive docstrings")
            score -= 0.2
        else:
            score += 0.1
        
        # Check for type hints
        type_hint_coverage = self._calculate_type_hint_coverage(code)
        if type_hint_coverage > 0.5:
            score += 0.1
        
        # Check for magic numbers
        magic_numbers = self._find_magic_numbers(code)
        if magic_numbers:
            issues.append(f"Magic numbers found: {magic_numbers[:3]}")
            recommendations.append("Replace magic numbers with named constants")
            score -= 0.1
        
        return max(0.0, score)
    
    def _assess_security(self, code: str, issues: List[str],
                        recommendations: List[str]) -> float:
        """Assess code security."""
        score = 1.0
        
        # Check for potential security issues
        security_patterns = [
            (r'eval\s*\(', "Use of eval() can be dangerous"),
            (r'exec\s*\(', "Use of exec() can be dangerous"),
            (r'__import__\s*\(', "Dynamic imports should be carefully reviewed"),
            (r'open\s*\([^)]*["\']w["\']', "File writing operations should be validated"),
        ]
        
        for pattern, message in security_patterns:
            if re.search(pattern, code):
                issues.append(message)
                recommendations.append("Review security implications")
                score -= 0.2
        
        return max(0.0, score)
    
    def _check_naming_conventions(self, code: str, naming_conventions: Dict[str, str]) -> List[str]:
        """Check naming convention compliance."""
        issues = []
        
        # Check class names
        class_pattern = r'class\s+(\w+)'
        class_names = re.findall(class_pattern, code)
        expected_class_style = naming_conventions.get('class', 'PascalCase')
        
        for name in class_names:
            if expected_class_style == 'PascalCase' and not name[0].isupper():
                issues.append(f"Class '{name}' should use PascalCase")
            elif expected_class_style == 'snake_case' and not ('_' in name and name.islower()):
                issues.append(f"Class '{name}' should use snake_case")
        
        # Check function names
        function_pattern = r'def\s+(\w+)'
        function_names = re.findall(function_pattern, code)
        expected_function_style = naming_conventions.get('function', 'snake_case')
        
        for name in function_names:
            if expected_function_style == 'snake_case' and name != name.lower():
                issues.append(f"Function '{name}' should use snake_case")
            elif expected_function_style == 'camelCase' and '_' in name:
                issues.append(f"Function '{name}' should use camelCase")
        
        return issues
    
    def _check_indentation(self, code: str, expected_indentation: str) -> List[str]:
        """Check indentation consistency."""
        issues = []
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            if line.startswith(' ') or line.startswith('\t'):
                if expected_indentation == '4_spaces' and line.startswith('\t'):
                    issues.append(f"Line {i}: Uses tabs instead of 4 spaces")
                elif expected_indentation == 'tabs' and line.startswith(' '):
                    issues.append(f"Line {i}: Uses spaces instead of tabs")
                elif expected_indentation == '2_spaces' and line.startswith('    '):
                    issues.append(f"Line {i}: Uses 4 spaces instead of 2")
        
        return issues
    
    def _calculate_docstring_coverage(self, code: str) -> float:
        """Calculate percentage of functions/classes with docstrings."""
        try:
            tree = ast.parse(code)
            total_items = 0
            documented_items = 0
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                    total_items += 1
                    if (node.body and isinstance(node.body[0], ast.Expr) and
                        isinstance(node.body[0].value, ast.Constant) and
                        isinstance(node.body[0].value.value, str)):
                        documented_items += 1
            
            return documented_items / total_items if total_items > 0 else 1.0
        except SyntaxError:
            return 0.0
    
    def _calculate_type_hint_coverage(self, code: str) -> float:
        """Calculate percentage of functions with type hints."""
        try:
            tree = ast.parse(code)
            total_functions = 0
            typed_functions = 0
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    total_functions += 1
                    if node.returns or any(arg.annotation for arg in node.args.args):
                        typed_functions += 1
            
            return typed_functions / total_functions if total_functions > 0 else 1.0
        except SyntaxError:
            return 0.0
    
    def _find_magic_numbers(self, code: str) -> List[int]:
        """Find magic numbers in the code."""
        magic_numbers = []
        
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Constant) and isinstance(node.value, (int, float)):
                    # Ignore common non-magic numbers
                    if node.value not in [0, 1, -1, 2, 10, 100]:
                        magic_numbers.append(node.value)
        except SyntaxError:
            pass
        
        return list(set(magic_numbers))


class ComplexityAnalyzer(ast.NodeVisitor):
    """AST visitor to calculate cyclomatic complexity."""
    
    def __init__(self):
        self.complexities = []
        self.current_complexity = 1
    
    def visit_FunctionDef(self, node):
        old_complexity = self.current_complexity
        self.current_complexity = 1
        self.generic_visit(node)
        self.complexities.append(self.current_complexity)
        self.current_complexity = old_complexity
    
    def visit_AsyncFunctionDef(self, node):
        self.visit_FunctionDef(node)
    
    def visit_If(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_While(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_For(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
    
    def visit_ExceptHandler(self, node):
        self.current_complexity += 1
        self.generic_visit(node)
