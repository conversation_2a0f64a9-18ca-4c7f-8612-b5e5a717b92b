#!/usr/bin/env python3
"""
Debug script to check the IR data structure and inheritance data.
"""

import sys
import os
import json

# Add aider to path
sys.path.insert(0, "aider-main")

def debug_ir_data_structure():
    """Debug the IR data structure to see where inheritance data is lost."""
    
    print("🔍 DEBUGGING IR DATA STRUCTURE")
    print("=" * 60)
    
    try:
        # Import the standalone IR service
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        
        print(f"📁 Analyzing project: {external_project}")
        print("🔄 Generating IR data...")
        
        ir_data = service.generate_mid_level_ir(external_project)
        
        if not ir_data:
            print("❌ Failed to generate IR data")
            return False
        
        print(f"📊 IR data keys: {list(ir_data.keys())}")
        
        # Check the structure
        if "entities" in ir_data:
            entities = ir_data["entities"]
            print(f"📊 Found {len(entities)} entities")
            
            # Look for entities with inheritance data
            entities_with_inheritance = []
            
            for i, entity in enumerate(entities):
                entity_name = entity.get("entity_name", "unknown")
                entity_type = entity.get("entity_type", "unknown")
                
                # Check for inheritance fields
                inherits_from = entity.get("inherits_from", [])
                method_overrides = entity.get("method_overrides", [])
                calls_super = entity.get("calls_super", False)
                overridden_by = entity.get("overridden_by", [])
                class_name = entity.get("class_name")
                
                if inherits_from or method_overrides or calls_super or overridden_by or class_name:
                    entities_with_inheritance.append({
                        "index": i,
                        "name": entity_name,
                        "type": entity_type,
                        "class_name": class_name,
                        "inherits_from": inherits_from,
                        "method_overrides": method_overrides,
                        "calls_super": calls_super,
                        "overridden_by": overridden_by,
                        "file_path": entity.get("file_path", "")
                    })
            
            print(f"📊 Entities with inheritance data: {len(entities_with_inheritance)}")
            
            if entities_with_inheritance:
                print(f"\n🏗️ ENTITIES WITH INHERITANCE DATA:")
                for entity in entities_with_inheritance[:10]:  # Show first 10
                    print(f"   {entity['index']}. {entity['name']} ({entity['type']})")
                    print(f"      File: {entity['file_path']}")
                    if entity['class_name']:
                        print(f"      Class: {entity['class_name']}")
                    if entity['inherits_from']:
                        print(f"      Inherits from: {entity['inherits_from']}")
                    if entity['method_overrides']:
                        print(f"      Overrides: {entity['method_overrides']}")
                    if entity['calls_super']:
                        print(f"      Calls super: {entity['calls_super']}")
                    if entity['overridden_by']:
                        print(f"      Overridden by: {entity['overridden_by']}")
                    print()
            else:
                print("❌ No entities with inheritance data found in IR!")
                
                # Show a sample entity structure
                if entities:
                    print(f"\n📄 SAMPLE ENTITY STRUCTURE:")
                    sample_entity = entities[0]
                    print(f"Keys: {list(sample_entity.keys())}")
                    print(f"Sample entity: {json.dumps(sample_entity, indent=2)[:1000]}...")
            
            # Save IR data for inspection
            with open("debug_ir_data.json", "w", encoding="utf-8") as f:
                json.dump(ir_data, f, indent=2)
            print(f"💾 Saved IR data to: debug_ir_data.json")
            
            return len(entities_with_inheritance) > 0
        else:
            print("❌ No 'entities' key found in IR data")
            print(f"Available keys: {list(ir_data.keys())}")
            return False
        
    except Exception as e:
        print(f"❌ Error during IR structure debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_specific_classes():
    """Check specific classes that should have inheritance."""
    
    print("\n🔍 CHECKING SPECIFIC CLASSES FOR INHERITANCE")
    print("=" * 60)
    
    try:
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        ir_data = service.generate_mid_level_ir(external_project)
        
        if not ir_data or "entities" not in ir_data:
            print("❌ Failed to get IR data")
            return False
        
        entities = ir_data["entities"]
        
        # Look for specific classes that should have inheritance
        target_classes = [
            "BacktestTradeLogger",
            "Position", 
            "TradeLog",
            "TradeExecution",
            "BTCConditionStrategy",
            "ETHConditionStrategy",
            "BaseConditionStrategy",
            "BaseCloseStrategy"
        ]
        
        found_classes = []
        
        for entity in entities:
            entity_name = entity.get("entity_name", "")
            entity_type = entity.get("entity_type", "")
            
            if entity_type == "class" and entity_name in target_classes:
                found_classes.append({
                    "name": entity_name,
                    "inherits_from": entity.get("inherits_from", []),
                    "file_path": entity.get("file_path", ""),
                    "full_entity": entity
                })
        
        print(f"📊 Found {len(found_classes)} target classes:")
        
        for cls in found_classes:
            print(f"\n🏗️ {cls['name']} ({cls['file_path']})")
            print(f"   inherits_from: {cls['inherits_from']}")
            
            # Show all inheritance-related fields
            entity = cls['full_entity']
            inheritance_fields = [
                'inherits_from', 'method_overrides', 'calls_super', 
                'overridden_by', 'class_name'
            ]
            
            for field in inheritance_fields:
                value = entity.get(field)
                if value:
                    print(f"   {field}: {value}")
        
        return len(found_classes) > 0
        
    except Exception as e:
        print(f"❌ Error checking specific classes: {e}")
        return False

if __name__ == "__main__":
    print("🔍 DEBUGGING IR DATA STRUCTURE AND INHERITANCE")
    print("This will check if inheritance data exists in the raw IR data\n")
    
    # Check IR data structure
    has_inheritance_in_ir = debug_ir_data_structure()
    
    # Check specific classes
    has_specific_classes = check_specific_classes()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Inheritance data in IR: {'✅ YES' if has_inheritance_in_ir else '❌ NO'}")
    print(f"   Specific classes found: {'✅ YES' if has_specific_classes else '❌ NO'}")
    
    if not has_inheritance_in_ir:
        print("\n🚨 ISSUE: Inheritance data is missing from the IR entities!")
        print("This suggests the problem is in the IR generation pipeline.")
    else:
        print("\n✅ Inheritance data exists in IR - the issue is in the context selector.")
    
    print(f"\n📁 Files created:")
    print(f"   • debug_ir_data.json - Full IR data for inspection")
