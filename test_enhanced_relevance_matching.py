#!/usr/bin/env python3
"""
Test script to verify enhanced relevance matching in IR Context System.
Tests exact function name matching, improved query processing, and relevance scoring.
"""

import os
import sys
import json
from typing import List, Dict, Any

def test_focus_entity_extraction():
    """Test the enhanced focus entity extraction."""
    print("🧪 Testing Enhanced Focus Entity Extraction")
    print("=" * 60)
    
    # Import the enhanced method
    sys.path.append('aider-main/aider/coders')
    from base_coder import BaseCoder
    
    # Create a mock coder to test the method
    class MockCoder(BaseCoder):
        def __init__(self):
            pass
    
    coder = MockCoder()
    
    test_queries = [
        "Why is my context_selection() function taking so long?",
        "How does the IntelligentContextSelector.select_optimal_context() method work?",
        "Fix the bug in parse_request() function",
        "What does the 'calculate_relevance_score' function do?",
        "How do I use ContextRequestHandler class?",
        "Debug the process_ir_context_request method",
        "Analyze the _extract_focus_entities_from_query implementation"
    ]
    
    for query in test_queries:
        entities = coder._extract_focus_entities_from_query(query)
        print(f"Query: '{query}'")
        print(f"Focus entities: {entities}")
        print()
    
    return True

def test_relevance_scoring():
    """Test the enhanced relevance scoring with exact matches."""
    print("🎯 Testing Enhanced Relevance Scoring")
    print("=" * 60)
    
    try:
        # Import required modules
        from intelligent_context_selector import IntelligentContextSelector, ContextEntity, TaskType
        
        # Create mock IR data with test entities
        mock_ir_data = {
            "modules": [
                {
                    "module_name": "test_module",
                    "functions": [
                        {
                            "name": "context_selection",
                            "line_start": 1,
                            "line_end": 10,
                            "complexity": "medium",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": []
                        },
                        {
                            "name": "select_optimal_context", 
                            "line_start": 11,
                            "line_end": 20,
                            "complexity": "high",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": []
                        },
                        {
                            "name": "parse_request",
                            "line_start": 21,
                            "line_end": 30,
                            "complexity": "low",
                            "calls": [],
                            "called_by": [],
                            "errors": [],
                            "side_effects": []
                        }
                    ],
                    "classes": []
                }
            ]
        }
        
        # Create context selector
        selector = IntelligentContextSelector(mock_ir_data, max_tokens=2000)
        
        # Test exact match scenarios
        test_cases = [
            {
                "query": "Why is my context_selection function taking so long?",
                "focus_entities": ["context_selection"],
                "expected_top_match": "context_selection"
            },
            {
                "query": "How does select_optimal_context work?",
                "focus_entities": ["select_optimal_context"],
                "expected_top_match": "select_optimal_context"
            },
            {
                "query": "Fix the parse_request bug",
                "focus_entities": ["parse_request"],
                "expected_top_match": "parse_request"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test Case {i}: {test_case['query']}")
            
            # Select context
            context_bundle = selector.select_optimal_context(
                task_description=test_case['query'],
                task_type=TaskType.DEBUGGING,
                focus_entities=test_case['focus_entities']
            )
            
            # Check if expected function is in top results
            if context_bundle.entities:
                top_entity = context_bundle.entities[0]
                print(f"  Top match: {top_entity.entity_name} (score: {top_entity.relevance_score:.2f})")
                
                if top_entity.entity_name == test_case['expected_top_match']:
                    print(f"  ✅ PASS: Exact match found as top result")
                else:
                    print(f"  ❌ FAIL: Expected '{test_case['expected_top_match']}' but got '{top_entity.entity_name}'")
            else:
                print(f"  ❌ FAIL: No entities selected")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_relevance():
    """Test end-to-end relevance matching with real IR data."""
    print("🔄 Testing End-to-End Relevance Matching")
    print("=" * 60)
    
    try:
        # Import required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Test with current directory
        handler = ContextRequestHandler(".")
        
        # Test queries that should find exact matches
        test_queries = [
            "How does the IntelligentContextSelector work?",
            "What is the ContextRequestHandler doing?",
            "Fix the select_optimal_context method",
            "Debug the _calculate_relevance_score function"
        ]
        
        for query in test_queries:
            print(f"Query: '{query}'")
            
            # Create IR context request
            request = IRContextRequest(
                user_query=query,
                task_description=f"Analyze: {query}",
                task_type="debugging",
                max_tokens=2000,
                llm_friendly=True,
                include_code_context=True,
                max_entities=5
            )
            
            # Process request
            result = handler.process_ir_context_request(request)
            
            if "error" in result:
                print(f"  ❌ Error: {result['error']}")
            else:
                context_bundle = result.get("context_bundle", {})
                entities = context_bundle.get("entities", [])
                
                if entities:
                    print(f"  ✅ Found {len(entities)} relevant entities:")
                    for entity in entities[:3]:  # Show top 3
                        entity_data = entity.get("entity_data", {})
                        name = entity_data.get("name", "unknown")
                        score = entity_data.get("relevance_score", 0)
                        print(f"    - {name} (score: {score:.2f})")
                else:
                    print(f"  ❌ No relevant entities found")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all relevance matching tests."""
    print("🚀 Enhanced Relevance Matching Test Suite")
    print("=" * 80)
    
    tests = [
        ("Focus Entity Extraction", test_focus_entity_extraction),
        ("Relevance Scoring", test_relevance_scoring),
        ("End-to-End Relevance", test_end_to_end_relevance)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Test Results Summary:")
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced relevance matching is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    main()
