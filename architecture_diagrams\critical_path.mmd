graph TD
    %% Critical Path - High Impact Components

    aider["aider<br/>Score: 68.70<br/>Critical: 0<br/>Used by: 687"]
    style aider fill:#ff4757,stroke:#ff3742,color:#fff
    aider_integration_service["aider_integrati...<br/>Score: 2.21<br/>Critical: 4<br/>Used by: 22"]
    style aider_integration_service fill:#ff4757,stroke:#ff3742,color:#fff
    ir_context["ir_context<br/>Score: 1.10<br/>Critical: 0<br/>Used by: 11"]
    style ir_context fill:#ff4757,stroke:#ff3742,color:#fff
    dump["dump<br/>Score: 0.90<br/>Critical: 0<br/>Used by: 9"]
    style dump fill:#ff6348,stroke:#ff4757,color:#fff
    context_request_handler["context_request...<br/>Score: 0.82<br/>Critical: 2<br/>Used by: 8"]
    style context_request_handler fill:#ff6348,stroke:#ff4757,color:#fff
    aider_template_renderer["aider_template_...<br/>Score: 0.80<br/>Critical: 0<br/>Used by: 8"]
    style aider_template_renderer fill:#ff6348,stroke:#ff4757,color:#fff
    aider_context_request_integration["aider_context_r...<br/>Score: 0.70<br/>Critical: 0<br/>Used by: 7"]
    style aider_context_request_integration fill:#ff6348,stroke:#ff4757,color:#fff
    surgical_file_extractor["surgical_file_e...<br/>Score: 0.70<br/>Critical: 0<br/>Used by: 7"]
    style surgical_file_extractor fill:#ff6348,stroke:#ff4757,color:#fff
    surgical_context_extractor["surgical_contex...<br/>Score: 0.50<br/>Critical: 0<br/>Used by: 5"]
    style surgical_context_extractor fill:#fffa65,stroke:#ffdd59,color:#000
    context_request["context_request<br/>Score: 0.40<br/>Critical: 0<br/>Used by: 4"]
    style context_request fill:#fffa65,stroke:#ffdd59,color:#000
    repomap["repomap<br/>Score: 0.31<br/>Critical: 5<br/>Used by: 3"]
    style repomap fill:#fffa65,stroke:#ffdd59,color:#000
    smart_map_request_handler["smart_map_reque...<br/>Score: 0.30<br/>Critical: 1<br/>Used by: 3"]
    style smart_map_request_handler fill:#fffa65,stroke:#ffdd59,color:#000
    base_coder["base_coder<br/>Score: 0.11<br/>Critical: 20<br/>Used by: 1"]
    style base_coder fill:#fffa65,stroke:#ffdd59,color:#000
    io["io<br/>Score: 0.03<br/>Critical: 11<br/>Used by: 0"]
    style io fill:#fffa65,stroke:#ffdd59,color:#000
    base_coder_old["base_coder_old<br/>Score: 0.02<br/>Critical: 19<br/>Used by: 0"]
    style base_coder_old fill:#fffa65,stroke:#ffdd59,color:#000

    %% Critical Dependencies
    aider -.-> aider
    aider_context_request_integration -.-> context_request_handler
    aider_context_request_integration -.-> aider_template_renderer
    aider_context_request_integration -.-> aider_integration_service
    aider_integration_service --> aider
    aider_integration_service --> aider
    aider_integration_service --> aider
    aider_integration_service -.-> surgical_context_extractor
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder ==> aider
    base_coder -.-> dump
    base_coder --> context_request
    base_coder --> smart_map_request_handler
    base_coder ==> aider
    base_coder --> smart_map_request_handler
    base_coder --> context_request
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old ==> aider
    base_coder_old -.-> dump
    base_coder_old --> context_request
    base_coder_old ==> aider
    base_coder_old --> context_request
    context_request -.-> context_request_handler
    context_request -.-> aider_template_renderer
    context_request -.-> aider_context_request_integration
    context_request -.-> aider_integration_service
    context_request_handler -.-> aider_integration_service
    context_request_handler --> surgical_file_extractor
    context_request_handler -.-> surgical_context_extractor
    context_request_handler --> surgical_file_extractor
    io -.-> aider
    io -.-> dump
    repomap --> aider
    repomap --> aider
    repomap --> aider
    smart_map_request_handler --> repomap
    smart_map_request_handler --> repomap
    surgical_context_extractor -.-> aider_integration_service
    surgical_context_extractor --> aider
    surgical_context_extractor --> aider
    surgical_file_extractor -.-> aider_integration_service
    surgical_file_extractor --> aider
    surgical_file_extractor --> aider