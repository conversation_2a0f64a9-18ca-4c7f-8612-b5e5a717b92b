# IR Context System with Inheritance Analysis - Complete Implementation Guide

## 📋 Overview

The **IR Context System** is an advanced intelligent code analysis and context generation system built for aider. It provides automatic, inheritance-aware code understanding that generates LLM-friendly context packages for better code comprehension and development assistance.

### 🎯 Purpose

- **Intelligent Code Discovery**: Automatically analyze codebases to understand structure, relationships, and patterns
- **Inheritance-Aware Analysis**: Deep understanding of OOP patterns, class hierarchies, and method relationships
- **LLM-Optimized Context**: Generate compact, focused context packages optimized for LLM consumption
- **Automatic Archiving**: Permanently save all generated context for review and analysis
- **Multi-Flow Architecture**: Support multiple context request patterns for different use cases

## ✨ Key Features

### 🏗️ **Inheritance Analysis**
- **Class Hierarchy Mapping**: Complete inheritance chains and relationships
- **Method Override Detection**: Identify which methods override parent implementations
- **Super() Call Analysis**: Track calls to parent class methods
- **OOP Pattern Recognition**: Understand complex object-oriented design patterns

### 🤖 **LLM-Friendly Packages**
- **Token-Optimized**: Intelligent content selection within token budgets
- **Structured Format**: Consistent, readable format for LLM consumption
- **Context-Rich**: Include inheritance data, dependencies, and implementation details
- **Compact Design**: Maximum information density with minimal token usage

### 💾 **Automatic Archiving**
- **Permanent Storage**: Every generated package automatically saved
- **Descriptive Naming**: Timestamp + task description for easy identification
- **Complete Metadata**: Full request details and generation context
- **Review-Ready**: Formatted for easy human review and analysis

### 🔄 **Multi-Flow Architecture**
- **Direct IR Flow**: Automatic context generation for user queries
- **MAP_REQUEST**: On-demand repository structure analysis
- **CONTEXT_REQUEST**: Traditional context package generation
- **IR_CONTEXT_REQUEST**: Enhanced IR-based context packages

## 🏛️ Architecture

The IR Context System operates through **three parallel flows** that work together seamlessly:

### 1. 🆕 **Direct IR Context Flow (Automatic)**
```python
# Triggered automatically for user queries
enable_new_flow = getattr(self, 'enable_direct_ir_context', True)
if enable_new_flow:
    ir_context_result = self.process_direct_ir_context(user_message)
    if ir_context_result:
        self.io.tool_output("🎯 Using new direct IR context flow")
```

**When**: Automatically processes user queries  
**Priority**: Proactive (before LLM response)  
**Output**: LLM-friendly IR context package  

### 2. 🗺️ **MAP_REQUEST Flow (LLM-Triggered)**
```python
# LLM requests: {MAP_REQUEST: {"keywords": ["position", "management"], "type": "focused"}}
cleaned_content, map_prompt = self.process_map_requests(content, inp)
if map_prompt:
    self.reflected_message = map_prompt
```

**When**: LLM requests `{MAP_REQUEST: {...}}`  
**Priority**: Highest (processed first in responses)  
**Output**: Focused repository structure maps  

### 3. 📋 **CONTEXT_REQUEST Flow (LLM-Triggered)**
```python
# LLM requests: {CONTEXT_REQUEST: {"entities": ["PositionManager"], "max_tokens": 1000}}
cleaned_content, context_prompt = self.process_context_requests(content, inp)
if context_prompt:
    self.reflected_message = context_prompt
```

**When**: LLM requests `{CONTEXT_REQUEST: {...}}`  
**Priority**: Second (after MAP_REQUEST)  
**Output**: Traditional context packages with inheritance data  

## 🛠️ Installation & Setup

### Prerequisites
```bash
# Required Python packages
pip install aider-chat
pip install ast
pip install pathlib
pip install json
```

### Configuration
```python
# Enable/disable direct IR context flow
export AIDER_ENABLE_DIRECT_IR_CONTEXT=true   # Enable (default)
export AIDER_DISABLE_DIRECT_IR_CONTEXT=true  # Disable

# In code
coder.enable_direct_ir_context = True  # Enable
coder.enable_direct_ir_context = False # Disable
```

### File Structure Setup
```
your_project/
├── ICA_package/                    # Auto-created for saved packages
│   ├── 20241201_143022_position_management_analysis.txt
│   ├── 20241201_143045_strategy_inheritance_patterns.txt
│   └── ...
├── your_code_files.py
└── ...
```

## 🚀 Usage Examples

### Example 1: Automatic Direct IR Context
```python
# User query (automatic processing)
user_query = "How does position management work in the trading system?"

# System automatically generates IR context
# Output: LLM-friendly package with inheritance data
# Saved to: ICA_package/20241201_143022_position_management_analysis.txt
```

### Example 2: LLM-Triggered MAP_REQUEST
```python
# LLM response includes:
{MAP_REQUEST: {
    "keywords": ["position", "management", "trading"],
    "type": "focused",
    "max_results": 20
}}

# System generates focused repository map
# Output: Repository structure focused on position management
```

### Example 3: LLM-Triggered CONTEXT_REQUEST
```python
# LLM response includes:
{CONTEXT_REQUEST: {
    "entities": ["PositionManager", "TradeExecutor"],
    "max_tokens": 1500,
    "include_inheritance": true
}}

# System generates context package with inheritance data
# Output: Detailed context with class hierarchies and method overrides
```

### Example 4: Enhanced IR_CONTEXT_REQUEST
```python
# LLM response includes:
{IR_CONTEXT_REQUEST: "Analyze strategy class inheritance patterns and OOP design"}

# System generates IR-based context package
# Output: Inheritance-focused analysis with OOP patterns
# Auto-saved to: ICA_package/timestamp_strategy_inheritance_analysis.txt
```

## 📁 File Structure & Naming

### ICA_package Folder Structure
```
ICA_package/
├── 20241201_143022_position_management_analysis.txt
├── 20241201_143045_strategy_inheritance_patterns.txt
├── 20241201_143102_debug_trading_execution.txt
└── ...
```

### Filename Convention
```
Format: YYYYMMDD_HHMMSS_task_description.txt

Examples:
- 20241201_143022_position_management_analysis.txt
- 20241201_143045_strategy_inheritance_patterns.txt
- 20241201_143102_debug_trading_execution.txt
```

### File Content Structure
```markdown
# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2024-12-01 14:30:22
# Project: /path/to/your/project
# User Query: How does position management work?
# Task Description: Analyze position management workflow
# Task Type: general_analysis
# Max Tokens: 2000
# Focus Entities: position, management, trading
# Package Size: 8,067 characters

================================================================================

[LLM-friendly package content with inheritance data]
```

## 📚 API Reference

### Core Classes

#### `ContextRequestHandler`
```python
class ContextRequestHandler:
    def __init__(self, project_path: str)
    def process_ir_context_request(self, request: IRContextRequest) -> dict
    def _auto_save_llm_package(self, llm_package: str, request: IRContextRequest) -> None
```

#### `IRContextRequest`
```python
class IRContextRequest:
    user_query: str
    task_description: str
    task_type: str
    focus_entities: List[str]
    max_tokens: int
    llm_friendly: bool
    include_ir_slices: bool
    include_code_context: bool
    max_entities: int
```

#### `IntelligentContextSelector`
```python
class IntelligentContextSelector:
    def __init__(self, ir_data: dict, max_tokens: int)
    def select_optimal_context(self, task_description: str, task_type: TaskType, 
                              focus_entities: List[str]) -> ContextBundle
```

### Key Methods

#### `process_direct_ir_context`
```python
def process_direct_ir_context(self, user_message: str) -> bool:
    """
    NEW FLOW: Process user query directly to generate IR context package.
    
    Args:
        user_message: The user's original query
    
    Returns:
        True if IR context was generated and injected, False otherwise
    """
```

#### `_auto_save_llm_package`
```python
def _auto_save_llm_package(self, llm_package: str, request: IRContextRequest) -> None:
    """
    Automatically save the LLM-friendly package to the ICA_package folder.

    Args:
        llm_package: The LLM-friendly package content to save
        request: The original IR context request for metadata
    """
```

## 🏗️ Inheritance Enhancement

### Overview
The IR Context System includes comprehensive inheritance analysis that provides deep understanding of object-oriented patterns in your codebase.

### 🔍 **Inheritance Data Captured**

#### **Class-Level Inheritance**
```python
# Example class analysis
class BTCConditionStrategy(BaseStrategyWithRetry):
    def execute_strategy(self):
        super().execute_strategy()  # Calls parent method
        # Custom implementation
```

**Captured Data**:
- `inherits_from`: `["BaseStrategyWithRetry"]`
- `inheritance_chain`: Full hierarchy path
- `child_classes`: Classes that inherit from this one

#### **Method-Level Inheritance**
```python
# Method override detection
def execute_strategy(self):  # Overrides parent method
    super().execute_strategy()  # Calls parent implementation
    # Additional logic
```

**Captured Data**:
- `class_name`: Which class the method belongs to
- `method_overrides`: Parent methods this overrides
- `calls_super`: Whether it calls parent implementation
- `overridden_by`: Child classes that override this method

### 📊 **Inheritance Analysis Results**

#### **Before Enhancement** (Traditional Context)
```markdown
## Function: execute_strategy
- **Type**: function
- **File**: strategy/btc_strategy.py
- **Dependencies**: 5 calls, 3 used by
```

#### **After Enhancement** (Inheritance-Aware Context)
```markdown
## Function: execute_strategy
- **Type**: function
- **File**: strategy/btc_strategy.py
- **Belongs to Class**: BTCConditionStrategy
- **Inherits From**: BaseStrategyWithRetry → BaseConditionStrategy → ABC
- **Method Overrides**: ["execute_strategy"]
- **Calls Super**: true
- **Overridden By**: []

### 🔁 Class Inheritance Context
- **Class**: BTCConditionStrategy
- **Parent Classes**: BaseStrategyWithRetry, BaseConditionStrategy, ABC
- **Child Classes**: None
- **Inheritance Pattern**: Strategy Pattern with Template Method

### 🧩 Method Details
- **Override Behavior**: Extends parent functionality
- **Super() Usage**: Calls parent implementation first
- **Customization**: Adds BTC-specific trading logic
```

### 🎯 **OOP Pattern Recognition**

The system automatically identifies common OOP patterns:

#### **Strategy Pattern**
```python
# Detected pattern
BaseConditionStrategy (ABC)
├── BTCConditionStrategy
├── ETHConditionStrategy
└── XAUConditionStrategy
```

#### **Template Method Pattern**
```python
# Base template
class BaseStrategyWithRetry:
    def execute_strategy(self):  # Template method
        self.prepare()
        self.execute()
        self.cleanup()

    def execute(self):  # Abstract method
        raise NotImplementedError
```

### 📈 **Inheritance Metrics**

The system provides comprehensive inheritance statistics:

```python
# Example output
📊 Inheritance Analysis Results:
   Classes with inheritance: 35
   Methods with overrides: 25
   Methods calling super(): 23
   Inheritance depth (max): 4 levels
   Most inherited class: BaseConditionStrategy (5 children)
```

## 💾 Automatic Archiving

### 🎯 **Auto-Save Feature**

Every LLM-friendly package is automatically saved without user intervention:

```python
# Automatic saving implementation
def _auto_save_llm_package(self, llm_package: str, request: IRContextRequest) -> None:
    # Create ICA_package folder if it doesn't exist
    ica_folder = Path(self.project_path) / "ICA_package"
    ica_folder.mkdir(exist_ok=True)

    # Generate descriptive filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    sanitized_desc = self._sanitize_task_description(request.task_description)
    filename = f"{timestamp}_{sanitized_desc}.txt"

    # Save with metadata header
    self._save_package_with_metadata(ica_folder / filename, llm_package, request)
```

### 📁 **Archive Organization**

#### **Folder Structure**
```
your_project/
└── ICA_package/
    ├── 20241201_143022_position_management_analysis.txt
    ├── 20241201_143045_strategy_inheritance_patterns.txt
    ├── 20241201_143102_debug_trading_execution.txt
    ├── 20241201_144530_error_handling_investigation.txt
    └── 20241201_145015_performance_optimization_review.txt
```

#### **File Naming Logic**
```python
# Filename generation
timestamp = "20241201_143022"  # YYYYMMDD_HHMMSS
task_desc = "Analyze position management workflow and components"
sanitized = "analyze_position_management_workflow_and_component"  # Max 50 chars
filename = f"{timestamp}_{sanitized}.txt"
```

### 📄 **Package Content Structure**

Each saved package includes comprehensive metadata:

```markdown
# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2024-12-01 14:30:22
# Project: /Users/<USER>/trading_system
# User Query: How does position management work in the trading system?
# Task Description: Analyze position management workflow and components
# Task Type: general_analysis
# Max Tokens: 2000
# Focus Entities: position, management, trading, workflow
# Package Size: 8,067 characters

================================================================================

# 🎯 LLM-Friendly IR Context Package

## 📊 Context Summary
- **Selected Entities**: 22 entities
- **Token Utilization**: 99.5% (1,993/2,000 tokens)
- **Critical Entities**: 22
- **Inheritance Data**: ✅ Included

## 🏗️ Key Components

### 1. PositionManager (Class)
- **File**: trade_management/position_manager.py
- **Type**: class
- **Inherits From**: BaseManager
- **Child Classes**: BacktestPositionManager, LivePositionManager
- **Key Methods**: open_position, close_position, update_position

#### 🔁 Class Inheritance Context
- **Inheritance Chain**: PositionManager → BaseManager → ABC
- **Pattern**: Manager Pattern with Template Methods
- **Polymorphism**: Different implementations for backtest vs live trading

### 2. open_position (Method)
- **File**: trade_management/position_manager.py
- **Belongs to Class**: PositionManager
- **Method Overrides**: ["open_position"]
- **Calls Super**: false
- **Overridden By**: ["BacktestPositionManager.open_position", "LivePositionManager.open_position"]

#### 🧩 Method Details
- **Override Pattern**: Template method overridden by subclasses
- **Polymorphic Behavior**: Different position opening logic for different environments
- **Dependencies**: Calls TradeExecutor, validates with RiskManager

[... continued with full context analysis ...]
```

### 🔍 **Archive Benefits**

#### **For Developers**
- **Historical Analysis**: Track how understanding evolves over time
- **Pattern Recognition**: Identify recurring questions and focus areas
- **Knowledge Base**: Build permanent repository of code insights
- **Team Collaboration**: Share specific analysis packages with team members

#### **For Debugging**
- **Context Comparison**: Compare different queries about the same code
- **Issue Tracking**: Maintain history of problem investigation
- **Solution Documentation**: Archive successful problem-solving approaches

#### **For Code Review**
- **Architecture Understanding**: Deep analysis of system design
- **Inheritance Patterns**: Document OOP design decisions
- **Refactoring Planning**: Identify areas for improvement

## 🔧 Troubleshooting

### Common Issues and Solutions

#### ❌ **Issue: No ICA_package folder created**
```bash
# Symptoms
- Auto-save messages appear but no folder exists
- Packages not being saved

# Solutions
1. Check project path permissions
2. Verify CONTEXT_REQUEST_AVAILABLE is True
3. Ensure project_path is correctly set
```

#### ❌ **Issue: Inheritance data shows "No inheritance detected"**
```bash
# Symptoms
- All entities show empty inheritance fields
- Missing class context in packages

# Solutions
1. Verify IR generation includes inheritance analysis
2. Check that modular pipeline is being used
3. Ensure inheritance_analyzer module is working
```

#### ❌ **Issue: Direct IR context not triggering**
```bash
# Symptoms
- Traditional MAP_REQUEST/CONTEXT_REQUEST flow used instead
- No automatic context generation

# Solutions
1. Check enable_direct_ir_context setting
2. Verify CONTEXT_REQUEST_AVAILABLE is True
3. Ensure query length > 3 words
```

#### ❌ **Issue: Token limit exceeded**
```bash
# Symptoms
- Context packages too large for LLM
- "Large context LLM required" warnings

# Solutions
1. Reduce max_tokens parameter
2. Use more specific focus_entities
3. Decrease max_entities limit
```

### Debug Commands

#### **Check System Status**
```python
# Verify IR context system availability
from aider.context_request import CONTEXT_REQUEST_AVAILABLE
print(f"Context system available: {CONTEXT_REQUEST_AVAILABLE}")

# Check direct IR context setting
print(f"Direct IR enabled: {coder.enable_direct_ir_context}")
```

#### **Test Auto-Save Functionality**
```python
# Manual test of auto-save
from aider.context_request import ContextRequestHandler, IRContextRequest

handler = ContextRequestHandler("your_project_path")
request = IRContextRequest(
    user_query="Test query",
    task_description="Test auto-save functionality",
    task_type="debugging",
    focus_entities=["test"],
    max_tokens=500,
    llm_friendly=True
)

result = handler.process_ir_context_request(request)
# Check ICA_package folder for new file
```

#### **Verify Inheritance Analysis**
```python
# Test inheritance detection
from aider_integration_service import AiderIntegrationService

service = AiderIntegrationService()
ir_data = service.generate_mid_level_ir("your_project_path")

# Check for inheritance data
inheritance_count = 0
for module in ir_data['modules']:
    for entity in module.get('entities', []):
        if entity.get('inherits_from') or entity.get('class_name'):
            inheritance_count += 1

print(f"Entities with inheritance data: {inheritance_count}")
```

## 🔗 Integration

### Aider Integration Points

#### **Base Coder Integration**
```python
# File: aider/coders/base_coder.py

# Direct IR context flow (lines 991-1003)
if enable_new_flow:
    ir_context_result = self.process_direct_ir_context(user_message)
    if ir_context_result:
        self.io.tool_output("🎯 Using new direct IR context flow")

# LLM response processing (lines 1789-1914)
# 1. MAP_REQUEST processing (highest priority)
# 2. CONTEXT_REQUEST processing (second priority)
# 3. IR_CONTEXT_REQUEST processing (third priority)
```

#### **Context Request Handler Integration**
```python
# File: aider/context_request/context_request_handler.py

# Auto-save integration (lines 1035-1036)
if request.llm_friendly:
    llm_package = self._create_llm_friendly_package(result, request)
    result["llm_friendly_package"] = llm_package

    # Automatically save LLM package to ICA_package folder
    self._auto_save_llm_package(llm_package, request)
```

#### **Intelligent Context Selector Integration**
```python
# File: intelligent_context_selector.py

# Inheritance data extraction (lines 177-181)
class_name=entity.get('class_name'),
inherits_from=entity.get('inherits_from', []),
method_overrides=entity.get('method_overrides', []),
calls_super=entity.get('calls_super', False),
overridden_by=entity.get('overridden_by', []),
```

### Backward Compatibility

#### **Legacy System Support**
- ✅ **MAP_REQUEST**: Fully functional alongside new system
- ✅ **CONTEXT_REQUEST**: Enhanced with inheritance data
- ✅ **Traditional workflows**: Unchanged behavior for existing users
- ✅ **Configuration**: Optional enable/disable for new features

#### **Migration Path**
```python
# Gradual adoption approach
# 1. Enable direct IR context (default)
coder.enable_direct_ir_context = True

# 2. LLMs can still use traditional requests
{MAP_REQUEST: {"keywords": ["component"], "type": "focused"}}
{CONTEXT_REQUEST: {"entities": ["ClassName"], "max_tokens": 1000}}

# 3. New IR context requests available
{IR_CONTEXT_REQUEST: "Analyze inheritance patterns"}
```

## ⚡ Performance

### Token Usage Optimization

#### **Intelligent Token Management**
```python
# Token budget allocation
max_tokens = 2000
target_utilization = 0.995  # 99.5% utilization

# Smart entity selection
selected_entities = selector.select_optimal_context(
    task_description=task_description,
    task_type=task_type,
    focus_entities=focus_entities,
    max_tokens=max_tokens
)

# Result: 99.5% token utilization (1,993/2,000 tokens)
```

#### **Content Optimization Strategies**
- **Inheritance-aware filtering**: Prioritize entities with inheritance data
- **Dependency-based selection**: Include related entities automatically
- **Criticality scoring**: Focus on most important entities first
- **Token-efficient formatting**: Compact representation without losing information

### Caching Strategy

#### **Multi-Level Caching**
```python
# 1. IR Data Cache (1-hour TTL)
ContextRequestHandler._ir_cache = {
    "project_path": ir_data,
    # Shared across all handler instances
}

# 2. Context Request Cache (1-hour TTL)
handler.cache = {
    "ir_context_request:hash": result,
    # Instance-level caching
}

# 3. Entity Selection Cache
selector.entity_map = {
    "module.entity": ContextEntity,
    # Pre-built entity mappings
}
```

#### **Performance Metrics**
```python
# Typical performance results
📊 Performance Metrics:
   IR generation: 3.5s (120 modules, 3,998 entities)
   Context selection: 0.2s (22 entities selected)
   Package generation: 0.1s (8,067 characters)
   Auto-save: 0.05s (metadata + file write)
   Total time: 3.85s
```

### Memory Management

#### **Efficient Data Structures**
- **Lazy loading**: IR data loaded only when needed
- **Weak references**: Prevent memory leaks in caches
- **Garbage collection**: Automatic cleanup of expired cache entries
- **Streaming processing**: Handle large codebases without memory issues

#### **Resource Optimization**
```python
# Memory-efficient processing
def process_large_codebase(project_path):
    # Stream processing for large projects
    for module_batch in stream_modules(project_path, batch_size=50):
        process_module_batch(module_batch)
        # Memory released after each batch

    # Incremental IR building
    ir_builder.build_incremental(modules)
```

## 🎯 Best Practices

### Usage Recommendations

#### **For Maximum Effectiveness**
1. **Use descriptive queries**: "How does position management work?" vs "position"
2. **Specify focus areas**: Include relevant keywords in queries
3. **Leverage inheritance**: Ask about class relationships and OOP patterns
4. **Review saved packages**: Use ICA_package archive for learning and debugging

#### **Query Optimization**
```python
# ✅ Good queries (trigger inheritance analysis)
"How do strategy classes inherit from base classes?"
"What's the inheritance hierarchy for position management?"
"How are trading strategies implemented using OOP patterns?"

# ❌ Less effective queries
"fix bug"
"help"
"what is this?"
```

#### **Token Budget Management**
```python
# Adjust based on use case
small_analysis = IRContextRequest(max_tokens=800)    # Quick overview
medium_analysis = IRContextRequest(max_tokens=2000)  # Detailed analysis
large_analysis = IRContextRequest(max_tokens=4000)   # Comprehensive review
```

### Development Workflow

#### **Recommended Integration**
1. **Start with automatic context**: Let direct IR flow provide initial understanding
2. **Request specific details**: Use MAP_REQUEST for focused exploration
3. **Deep dive analysis**: Use IR_CONTEXT_REQUEST for inheritance patterns
4. **Archive important insights**: Review ICA_package for key discoveries

#### **Team Collaboration**
```bash
# Share analysis packages
cp ICA_package/20241201_143022_position_management_analysis.txt \
   team_shared/architecture_docs/

# Version control integration
git add ICA_package/
git commit -m "Add position management analysis package"
```

## 📈 Future Enhancements

### Planned Features

#### **Enhanced Analysis**
- **Cross-module inheritance**: Track inheritance across different modules
- **Design pattern detection**: Automatic identification of common patterns
- **Refactoring suggestions**: AI-powered improvement recommendations
- **Performance impact analysis**: Inheritance overhead assessment

#### **Improved Integration**
- **IDE plugins**: Direct integration with popular development environments
- **CI/CD integration**: Automatic analysis in build pipelines
- **Documentation generation**: Auto-generate architecture docs from analysis
- **Metrics dashboard**: Visual representation of inheritance patterns

#### **Advanced Features**
- **Interactive exploration**: Web-based interface for browsing inheritance
- **Comparison analysis**: Compare inheritance patterns across versions
- **Custom pattern definitions**: User-defined pattern recognition
- **Export formats**: JSON, XML, PlantUML diagram generation

---

## 📚 Conclusion

The **IR Context System with Inheritance Analysis** represents a significant advancement in intelligent code understanding. By combining automatic context generation, comprehensive inheritance analysis, and permanent archiving, it provides developers with unprecedented insight into their codebases.

### Key Benefits Summary

- 🎯 **Automatic Intelligence**: No manual context requests needed
- 🏗️ **Inheritance Awareness**: Deep OOP pattern understanding
- 💾 **Permanent Archive**: Every analysis automatically saved
- 🔄 **Multi-Flow Support**: Works with existing aider workflows
- ⚡ **High Performance**: Optimized for speed and efficiency
- 🔧 **Easy Integration**: Seamless addition to existing projects

### Getting Started

1. **Enable the system**: Ensure `AIDER_ENABLE_DIRECT_IR_CONTEXT=true`
2. **Ask questions**: Start with natural language queries about your code
3. **Review packages**: Check the `ICA_package` folder for saved analysis
4. **Explore inheritance**: Focus on OOP patterns and class relationships
5. **Share insights**: Use archived packages for team collaboration

The IR Context System transforms how developers understand and work with complex codebases, making inheritance patterns and architectural decisions transparent and accessible.

---

*For technical support or feature requests, please refer to the aider documentation or submit issues to the project repository.*
