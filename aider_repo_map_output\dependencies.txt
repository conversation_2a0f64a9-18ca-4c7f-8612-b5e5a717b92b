# File Dependencies

This file shows which files reference code defined in other files.


## aider\__main__.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\analytics.py depends on:
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- aider\models.py (1 references)
- aider\coders\search_replace.py (1 references)
- benchmark\over_time.py (1 references)
- aider\mdstream.py (1 references)

## aider\args.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\deprecated.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\coders\__init__.py depends on:
- aider\coders\editor_diff_fenced_coder.py (1 references)
- aider\coders\udiff_simple.py (1 references)
- aider\coders\patch_coder.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- aider\coders\wholefile_coder.py (1 references)
- aider\coders\architect_coder.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\coders\editor_editblock_coder.py (1 references)
- aider\coders\editor_whole_coder.py (1 references)
- aider\llm.py (1 references)
- aider\coders\base_coder.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\coders\help_coder.py (1 references)
- aider\coders\ask_coder.py (1 references)
- aider\__init__.py (1 references)
- aider\coders\editblock_fenced_coder.py (1 references)

## aider\coders\architect_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\coders\architect_prompts.py (1 references)
- scripts\blame.py (1 references)
- aider\commands.py (1 references)
- tests\basic\test_onboarding.py (1 references)
- aider\mdstream.py (1 references)

## aider\coders\architect_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\ask_coder.py depends on:
- aider\coders\ask_prompts.py (1 references)

## aider\coders\ask_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\base_coder.py depends on:
- aider\io.py (19 references)
- tests\scrape\test_playwright_disable.py (14 references)
- aider\repo.py (10 references)
- aider\utils.py (10 references)
- aider\commands.py (9 references)
- aider\models.py (8 references)
- aider\file_request_validator.py (6 references)
- aider\linter.py (6 references)
- tests\basic\test_onboarding.py (6 references)
- aider\history.py (5 references)
- aider\repomap.py (5 references)
- aider\gui.py (5 references)
- aider\coders\chat_chunks.py (4 references)
- aider\exceptions.py (4 references)
- aider\waiting.py (4 references)
- aider\coders\wholefile_func_coder.py (3 references)
- aider\copypaste.py (3 references)
- aider\reasoning_tags.py (3 references)
- tests\manual\test_file_requests_manual.py (3 references)
- aider\analytics.py (3 references)
- aider\coders\single_wholefile_func_coder.py (3 references)
- aider\watch.py (3 references)
- aider\coders\context_coder.py (2 references)
- tests\basic\test_watch.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\coders\editblock_func_coder.py (2 references)
- aider\mdstream.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\architect_coder.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\help.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- aider\prompts.py (1 references)
- aider\coders\wholefile_coder.py (1 references)
- aider\scrape.py (1 references)
- scripts\blame.py (1 references)
- aider\onboarding.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\run_cmd.py (1 references)

## aider\coders\base_prompts.py depends on:
- aider\coders\shell.py (3 references)

## aider\coders\context_coder.py depends on:
- aider\coders\base_coder.py (5 references)
- tests\scrape\test_playwright_disable.py (3 references)
- tests\basic\test_watch.py (2 references)
- aider\io.py (2 references)
- aider\repomap.py (2 references)
- aider\linter.py (2 references)
- aider\mdstream.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\search_replace.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\copypaste.py (1 references)
- aider\help.py (1 references)
- aider\exceptions.py (1 references)
- aider\gui.py (1 references)
- aider\coders\editblock_func_coder.py (1 references)
- aider\waiting.py (1 references)
- aider\repo.py (1 references)
- aider\utils.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\analytics.py (1 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- aider\coders\context_prompts.py (1 references)
- aider\coders\wholefile_func_coder.py (1 references)
- aider\file_request_validator.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## aider\coders\context_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\editblock_coder.py depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)
- aider\coders\udiff_coder.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\linter.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\coders\patch_coder.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_prompts.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\repo.py (1 references)
- aider\utils.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\coders\wholefile_coder.py (1 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- aider\repomap.py (1 references)
- benchmark\over_time.py (1 references)
- aider\coders\help_coder.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\coders\editblock_fenced_coder.py depends on:
- aider\coders\editblock_fenced_prompts.py (1 references)

## aider\coders\editblock_func_coder.py depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\coders\base_coder.py (3 references)
- aider\coders\search_replace.py (2 references)
- aider\gui.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\editblock_func_prompts.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\copypaste.py (1 references)
- aider\help.py (1 references)
- aider\exceptions.py (1 references)
- aider\mdstream.py (1 references)
- tests\basic\test_onboarding.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\waiting.py (1 references)
- aider\repo.py (1 references)
- aider\utils.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\analytics.py (1 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- aider\coders\wholefile_func_coder.py (1 references)
- aider\file_request_validator.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## aider\coders\editblock_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\editor_diff_fenced_coder.py depends on:
- aider\coders\editor_diff_fenced_prompts.py (1 references)

## aider\coders\editor_diff_fenced_prompts.py depends on:
- aider\coders\shell.py (3 references)
- aider\coders\editblock_fenced_prompts.py (1 references)

## aider\coders\editor_editblock_coder.py depends on:
- aider\coders\editor_editblock_prompts.py (1 references)

## aider\coders\editor_editblock_prompts.py depends on:
- aider\coders\shell.py (3 references)
- aider\coders\editblock_prompts.py (1 references)

## aider\coders\editor_whole_coder.py depends on:
- aider\coders\editor_whole_prompts.py (1 references)

## aider\coders\editor_whole_prompts.py depends on:
- aider\coders\wholefile_prompts.py (1 references)

## aider\coders\help_coder.py depends on:
- aider\coders\help_prompts.py (1 references)

## aider\coders\help_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\patch_coder.py depends on:
- aider\io.py (4 references)
- tests\scrape\test_playwright_disable.py (4 references)
- tests\basic\test_onboarding.py (2 references)
- aider\gui.py (2 references)
- aider\coders\patch_prompts.py (1 references)
- aider\repo.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\base_coder.py (1 references)

## aider\coders\search_replace.py depends on:
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\repo.py (1 references)
- aider\utils.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\coders\single_wholefile_func_coder.py depends on:
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\io.py (3 references)
- aider\repo.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\single_wholefile_func_prompts.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\copypaste.py (1 references)
- aider\help.py (1 references)
- aider\exceptions.py (1 references)
- aider\mdstream.py (1 references)
- aider\gui.py (1 references)
- aider\coders\editblock_func_coder.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\waiting.py (1 references)
- aider\utils.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\analytics.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- aider\diffs.py (1 references)
- aider\coders\wholefile_func_coder.py (1 references)
- aider\file_request_validator.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## aider\coders\single_wholefile_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\coders\udiff_coder.py depends on:
- aider\coders\search_replace.py (3 references)
- aider\io.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\repo.py (1 references)
- aider\coders\base_coder.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\coders\udiff_prompts.py (1 references)

## aider\coders\udiff_simple.py depends on:
- aider\coders\udiff_simple_prompts.py (1 references)

## aider\coders\udiff_simple_prompts.py depends on:
- aider\coders\udiff_prompts.py (1 references)

## aider\coders\wholefile_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\io.py (2 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- aider\coders\patch_coder.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- aider\coders\wholefile_prompts.py (1 references)
- aider\repo.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\diffs.py (1 references)
- aider\coders\help_coder.py (1 references)

## aider\coders\wholefile_func_coder.py depends on:
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\repo.py (2 references)
- aider\coders\single_wholefile_func_coder.py (2 references)
- aider\io.py (2 references)
- aider\coders\search_replace.py (2 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\wholefile_func_prompts.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\copypaste.py (1 references)
- aider\help.py (1 references)
- aider\exceptions.py (1 references)
- aider\mdstream.py (1 references)
- aider\gui.py (1 references)
- aider\coders\editblock_func_coder.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\waiting.py (1 references)
- aider\utils.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\analytics.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- aider\diffs.py (1 references)
- aider\file_request_validator.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## aider\coders\wholefile_func_prompts.py depends on:
- aider\coders\base_prompts.py (1 references)

## aider\commands.py depends on:
- aider\coders\base_coder.py (15 references)
- tests\scrape\test_playwright_disable.py (14 references)
- aider\repo.py (12 references)
- aider\models.py (11 references)
- aider\io.py (11 references)
- tests\basic\test_onboarding.py (5 references)
- aider\linter.py (4 references)
- aider\scrape.py (4 references)
- aider\repomap.py (3 references)
- aider\help.py (3 references)
- aider\gui.py (3 references)
- tests\manual\test_file_requests_manual.py (2 references)
- aider\search_repo.py (2 references)
- scripts\blame.py (2 references)
- aider\onboarding.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\report.py (2 references)
- aider\voice.py (2 references)
- aider\coders\search_replace.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\utils.py (1 references)
- aider\editor.py (1 references)
- scripts\update-history.py (1 references)
- aider\format_settings.py (1 references)
- aider\analytics.py (1 references)
- scripts\versionbump.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\run_cmd.py (1 references)

## aider\copypaste.py depends on:
- aider\io.py (3 references)
- aider\waiting.py (3 references)
- aider\watch.py (3 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\deprecated.py depends on:
- aider\gui.py (1 references)
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- tests\basic\test_onboarding.py (1 references)

## aider\diffs.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\dump.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\editor.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\file_request_validator.py depends on:
- aider\search_repo.py (3 references)
- tests\basic\test_onboarding.py (2 references)
- aider\io.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\gui.py (2 references)

## aider\gui.py depends on:
- tests\scrape\test_playwright_disable.py (7 references)
- aider\coders\base_coder.py (6 references)
- aider\io.py (6 references)
- tests\basic\test_onboarding.py (3 references)
- aider\scrape.py (3 references)
- aider\repo.py (1 references)
- aider\commands.py (1 references)

## aider\help.py depends on:
- aider\io.py (1 references)
- aider\coders\search_replace.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\utils.py (1 references)

## aider\history.py depends on:
- aider\models.py (4 references)
- tests\manual\test_file_requests_manual.py (2 references)
- aider\io.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- aider\repomap.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\io.py depends on:
- tests\scrape\test_playwright_disable.py (6 references)
- aider\commands.py (5 references)
- tests\basic\test_onboarding.py (4 references)
- aider\gui.py (3 references)
- aider\watch.py (3 references)
- aider\waiting.py (2 references)
- aider\coders\base_coder.py (2 references)
- aider\copypaste.py (2 references)
- aider\mdstream.py (2 references)
- tests\basic\test_watch.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\history.py (1 references)
- aider\utils.py (1 references)
- aider\repomap.py (1 references)
- scripts\blame.py (1 references)
- aider\editor.py (1 references)
- aider\linter.py (1 references)

## aider\linter.py depends on:
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\coders\base_coder.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\args.py (2 references)
- aider\commands.py (2 references)
- aider\run_cmd.py (2 references)
- aider\coders\search_replace.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## aider\llm.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\main.py depends on:
- aider\io.py (11 references)
- aider\models.py (10 references)
- tests\scrape\test_playwright_disable.py (7 references)
- aider\commands.py (7 references)
- tests\basic\test_onboarding.py (6 references)
- aider\coders\base_coder.py (6 references)
- aider\analytics.py (6 references)
- aider\gui.py (4 references)
- aider\onboarding.py (4 references)
- aider\copypaste.py (3 references)
- aider\utils.py (3 references)
- aider\versioncheck.py (3 references)
- aider\watch.py (3 references)
- aider\waiting.py (2 references)
- aider\repo.py (2 references)
- aider\format_settings.py (2 references)
- tests\manual\test_file_requests_manual.py (2 references)
- aider\history.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\report.py (2 references)
- aider\args.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\chat_chunks.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- aider\deprecated.py (1 references)
- scripts\dl_icons.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\llm.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## aider\mdstream.py depends on:
- aider\waiting.py (2 references)
- aider\copypaste.py (2 references)
- aider\watch.py (2 references)
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\models.py depends on:
- aider\io.py (5 references)
- aider\openrouter.py (5 references)
- tests\scrape\test_playwright_disable.py (4 references)
- aider\exceptions.py (3 references)
- aider\gui.py (3 references)
- tests\manual\test_file_requests_manual.py (2 references)
- aider\coders\search_replace.py (2 references)
- tests\basic\test_onboarding.py (2 references)
- aider\sendchat.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\reasoning_tags.py (1 references)
- aider\utils.py (1 references)
- aider\coders\base_coder.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\commands.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- aider\args.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## aider\onboarding.py depends on:
- tests\basic\test_onboarding.py (7 references)
- aider\io.py (7 references)
- tests\scrape\test_playwright_disable.py (6 references)
- aider\gui.py (3 references)
- aider\waiting.py (2 references)
- aider\watch.py (2 references)
- aider\copypaste.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\analytics.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\openrouter.py depends on:
- aider\io.py (3 references)
- aider\models.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\coders\search_replace.py (1 references)
- tests\basic\test_openrouter.py (1 references)

## aider\prompts.py depends on:
- aider\history.py (1 references)

## aider\repo.py depends on:
- tests\scrape\test_playwright_disable.py (5 references)
- aider\io.py (4 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)
- aider\models.py (2 references)
- aider\coders\base_coder.py (2 references)
- aider\waiting.py (1 references)
- aider\utils.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\repomap.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\mdstream.py (1 references)

## aider\repomap.py depends on:
- aider\io.py (6 references)
- tests\scrape\test_playwright_disable.py (6 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)
- aider\waiting.py (3 references)
- aider\dump.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\models.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\args.py (1 references)
- aider\coders\base_coder.py (1 references)
- aider\linter.py (1 references)
- aider\special.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)

## aider\report.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\run_cmd.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\scrape.py depends on:
- tests\scrape\test_playwright_disable.py (5 references)
- aider\io.py (4 references)
- tests\basic\test_onboarding.py (3 references)
- aider\gui.py (3 references)
- aider\utils.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\search_repo.py depends on:
- aider\gui.py (3 references)
- aider\io.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- tests\basic\test_onboarding.py (2 references)

## aider\sendchat.py depends on:
- aider\coders\base_coder.py (1 references)
- aider\utils.py (1 references)

## aider\utils.py depends on:
- aider\io.py (6 references)
- tests\scrape\test_playwright_disable.py (6 references)
- aider\waiting.py (6 references)
- aider\gui.py (5 references)
- tests\basic\test_onboarding.py (4 references)
- aider\coders\base_coder.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- aider\copypaste.py (1 references)
- aider\help.py (1 references)
- aider\exceptions.py (1 references)
- aider\mdstream.py (1 references)
- aider\coders\editblock_func_coder.py (1 references)
- aider\coders\context_coder.py (1 references)
- aider\repo.py (1 references)
- tests\basic\test_reasoning.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- aider\analytics.py (1 references)
- aider\coders\single_wholefile_func_coder.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- aider\coders\wholefile_func_coder.py (1 references)
- aider\file_request_validator.py (1 references)
- aider\voice.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\languages\python\test.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## aider\versioncheck.py depends on:
- tests\basic\test_onboarding.py (3 references)
- aider\io.py (3 references)
- tests\scrape\test_playwright_disable.py (3 references)
- aider\gui.py (3 references)
- aider\utils.py (1 references)
- tests\basic\test_openrouter.py (1 references)

## aider\voice.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## aider\waiting.py depends on:
- aider\copypaste.py (3 references)
- aider\watch.py (3 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## aider\watch.py depends on:
- aider\io.py (5 references)
- tests\scrape\test_playwright_disable.py (5 references)
- aider\copypaste.py (3 references)
- aider\waiting.py (3 references)
- aider\onboarding.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\linter.py (2 references)
- tests\basic\test_onboarding.py (2 references)
- aider\gui.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\analytics.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- aider\args.py (1 references)
- aider\repomap.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## benchmark\benchmark.py depends on:
- aider\io.py (6 references)
- aider\models.py (5 references)
- aider\coders\base_coder.py (4 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\main.py (2 references)
- aider\dump.py (1 references)
- aider\gui.py (1 references)
- aider\mdstream.py (1 references)
- benchmark\rungrid.py (1 references)
- benchmark\plots.py (1 references)
- aider\repo.py (1 references)
- benchmark\problem_stats.py (1 references)
- aider\coders\search_replace.py (1 references)
- scripts\blame.py (1 references)
- aider\commands.py (1 references)

## benchmark\over_time.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\analytics.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## benchmark\plots.py depends on:
- aider\dump.py (1 references)
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## benchmark\problem_stats.py depends on:
- aider\io.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- aider\coders\search_replace.py (1 references)
- benchmark\benchmark.py (1 references)

## benchmark\refactor_tools.py depends on:
- aider\io.py (2 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## benchmark\rungrid.py depends on:
- scripts\blame.py (2 references)
- aider\commands.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## benchmark\swe_bench.py depends on:
- aider\dump.py (1 references)

## benchmark\test_benchmark.py depends on:
- benchmark\benchmark.py (1 references)

## scripts\30k-image.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## scripts\blame.py depends on:
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## scripts\clean_metadata.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## scripts\dl_icons.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\homepage.py depends on:
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\issues.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\gui.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\logo_svg.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\my_models.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## scripts\recording_audio.py depends on:
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\gui.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## scripts\redact-cast.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\tsl_pack_langs.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## scripts\update-history.py depends on:
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\gui.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## scripts\versionbump.py depends on:
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## scripts\yank-old-versions.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- tests\basic\test_openrouter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_analytics.py depends on:
- aider\analytics.py (7 references)
- aider\onboarding.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- tests\basic\test_onboarding.py (1 references)

## tests\basic\test_aws_credentials.py depends on:
- aider\models.py (1 references)
- aider\mdstream.py (1 references)

## tests\basic\test_coder.py depends on:
- aider\coders\base_coder.py (16 references)
- aider\io.py (3 references)
- aider\repo.py (3 references)
- aider\coders\context_coder.py (2 references)
- aider\waiting.py (2 references)
- aider\utils.py (2 references)
- aider\watch.py (2 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- aider\coders\architect_coder.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\copypaste.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\chat_chunks.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\gui.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\sendchat.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\basic\test_commands.py depends on:
- aider\commands.py (23 references)
- aider\io.py (5 references)
- tests\scrape\test_playwright_disable.py (4 references)
- aider\coders\base_coder.py (4 references)
- aider\utils.py (4 references)
- aider\repo.py (3 references)
- aider\dump.py (1 references)
- aider\gui.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\models.py (1 references)
- aider\coders\chat_chunks.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\repomap.py (1 references)
- aider\linter.py (1 references)

## tests\basic\test_deprecated.py depends on:
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\deprecated.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_editblock.py depends on:
- aider\coders\editblock_coder.py (5 references)
- aider\coders\base_coder.py (2 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\io.py (2 references)
- aider\commands.py (2 references)
- aider\coders\search_replace.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\utils.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\basic\test_editor.py depends on:
- aider\editor.py (5 references)

## tests\basic\test_exceptions.py depends on:
- aider\exceptions.py (3 references)

## tests\basic\test_file_requests.py depends on:
- aider\coders\base_coder.py (5 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\utils.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_find_or_blocks.py depends on:
- aider\coders\editblock_coder.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_history.py depends on:
- aider\history.py (5 references)
- aider\io.py (1 references)
- aider\prompts.py (1 references)

## tests\basic\test_io.py depends on:
- aider\io.py (14 references)
- aider\history.py (2 references)
- aider\commands.py (2 references)
- tests\basic\test_onboarding.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\gui.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\utils.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- aider\args.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\basic\test_linter.py depends on:
- aider\linter.py (5 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- tests\basic\test_watch.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\commands.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- aider\repomap.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\run_cmd.py (1 references)

## tests\basic\test_main.py depends on:
- aider\main.py (4 references)
- aider\utils.py (4 references)
- aider\io.py (4 references)
- aider\copypaste.py (3 references)
- aider\waiting.py (3 references)
- aider\coders\base_coder.py (3 references)
- aider\watch.py (3 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\args.py (2 references)
- aider\commands.py (2 references)
- aider\coders\search_replace.py (2 references)
- tests\scrape\test_playwright_disable.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\repo.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\basic\test_model_info_manager.py depends on:
- aider\models.py (4 references)
- aider\openrouter.py (2 references)
- aider\io.py (1 references)
- aider\utils.py (1 references)
- aider\mdstream.py (1 references)

## tests\basic\test_models.py depends on:
- aider\models.py (12 references)
- aider\io.py (2 references)
- aider\main.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\openrouter.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_onboarding.py depends on:
- aider\onboarding.py (9 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_openrouter.py depends on:
- aider\openrouter.py (2 references)
- aider\models.py (2 references)

## tests\basic\test_reasoning.py depends on:
- aider\coders\base_coder.py (4 references)
- aider\models.py (3 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\reasoning_tags.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_repo.py depends on:
- aider\repo.py (7 references)
- aider\io.py (2 references)
- aider\dump.py (1 references)
- aider\gui.py (1 references)
- aider\models.py (1 references)
- aider\utils.py (1 references)

## tests\basic\test_repomap.py depends on:
- aider\utils.py (2 references)
- aider\models.py (2 references)
- aider\io.py (2 references)
- aider\repomap.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\repo.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_run_cmd.py depends on:
- aider\run_cmd.py (1 references)
- aider\linter.py (1 references)

## tests\basic\test_sanity_check_repo.py depends on:
- aider\gui.py (1 references)
- aider\repo.py (1 references)
- aider\io.py (1 references)
- aider\main.py (1 references)

## tests\basic\test_scripting.py depends on:
- aider\coders\base_coder.py (2 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\utils.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\basic\test_sendchat.py depends on:
- aider\models.py (3 references)
- aider\exceptions.py (2 references)
- aider\sendchat.py (1 references)

## tests\basic\test_special.py depends on:
- aider\special.py (2 references)

## tests\basic\test_ssl_verification.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- aider\mdstream.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_udiff.py depends on:
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\basic\test_voice.py depends on:
- aider\voice.py (5 references)

## tests\basic\test_watch.py depends on:
- aider\watch.py (5 references)
- aider\io.py (2 references)

## tests\basic\test_wholefile.py depends on:
- aider\coders\base_coder.py (6 references)
- aider\io.py (3 references)
- aider\coders\wholefile_coder.py (3 references)
- aider\coders\single_wholefile_func_coder.py (2 references)
- aider\models.py (2 references)
- scripts\blame.py (2 references)
- benchmark\rungrid.py (2 references)
- aider\commands.py (2 references)
- aider\coders\search_replace.py (2 references)
- aider\coders\editblock_coder.py (2 references)
- aider\dump.py (1 references)
- benchmark\refactor_tools.py (1 references)
- aider\coders\patch_coder.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\coders\editblock_func_coder.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\coders\udiff_coder.py (1 references)
- scripts\redact-cast.py (1 references)
- aider\coders\wholefile_func_coder.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- aider\coders\help_coder.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\browser\test_browser.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\fixtures\languages\python\test.py depends on:
- aider\io.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)

## tests\fixtures\sample-code-base\sample.py depends on:
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\help\test_help.py depends on:
- aider\commands.py (3 references)
- aider\help.py (3 references)
- aider\models.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- aider\coders\base_coder.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)

## tests\manual\test_file_requests_manual.py depends on:
- aider\coders\base_coder.py (2 references)
- aider\io.py (2 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\scrape.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
- aider\commands.py (1 references)

## tests\scrape\test_playwright_disable.py depends on:
- aider\commands.py (2 references)
- aider\scrape.py (2 references)
- tests\basic\test_onboarding.py (1 references)

## tests\scrape\test_scrape.py depends on:
- aider\scrape.py (6 references)
- aider\commands.py (3 references)
- benchmark\refactor_tools.py (1 references)
- scripts\recording_audio.py (1 references)
- aider\main.py (1 references)
- aider\coders\search_replace.py (1 references)
- aider\coders\editblock_coder.py (1 references)
- aider\linter.py (1 references)
- scripts\logo_svg.py (1 references)
- aider\copypaste.py (1 references)
- benchmark\benchmark.py (1 references)
- scripts\dl_icons.py (1 references)
- tests\scrape\test_playwright_disable.py (1 references)
- aider\waiting.py (1 references)
- scripts\tsl_pack_langs.py (1 references)
- tests\manual\test_file_requests_manual.py (1 references)
- scripts\update-history.py (1 references)
- scripts\versionbump.py (1 references)
- aider\models.py (1 references)
- aider\watch.py (1 references)
- aider\history.py (1 references)
- scripts\blame.py (1 references)
- scripts\yank-old-versions.py (1 references)
- aider\diffs.py (1 references)
- scripts\issues.py (1 references)
- aider\onboarding.py (1 references)
- scripts\homepage.py (1 references)
- aider\io.py (1 references)
- scripts\redact-cast.py (1 references)
- benchmark\rungrid.py (1 references)
- aider\report.py (1 references)
- tests\fixtures\sample-code-base\sample.py (1 references)
- aider\args.py (1 references)
- benchmark\over_time.py (1 references)
- scripts\clean_metadata.py (1 references)
