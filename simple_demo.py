#!/usr/bin/env python

import os
from pprint import pprint

from surgical_context_extractor import (
    SurgicalContextExtractor, 
    CodeSnippet, 
    UsageContext, 
    DefinitionContext,
    ContextType,
    UsageType,
    DefinitionType
)

def format_snippet(content, max_lines=10):
    """Format a code snippet for display with line numbers."""
    lines = content.split('\n')
    if len(lines) > max_lines:
        # Show first few lines and last few lines
        first_half = lines[:max_lines//2]
        last_half = lines[-(max_lines//2):]
        formatted_lines = [f"{i+1}: {line}" for i, line in enumerate(first_half)]
        formatted_lines.append("...")
        formatted_lines.extend([f"{len(lines)-(len(last_half)-i)+1}: {line}" for i, line in enumerate(last_half)])
    else:
        formatted_lines = [f"{i+1}: {line}" for i, line in enumerate(lines)]
    
    return '\n'.join(formatted_lines)

def demo_context_window_sizing():
    """Demonstrate the smart context window sizing feature."""
    print("\n=== SMART CONTEXT WINDOW SIZING DEMO ===")
    
    # Create a sample code snippet
    sample_code = """
class TestClass:
    \"\"\"A test class.\"\"\"
    
    def __init__(self, value):
        \"\"\"Initialize with a value.\"\"\"
        self.value = value
        
    def get_value(self):
        \"\"\"Return the value.\"\"\"
        return self.value
        
    def set_value(self, new_value):
        \"\"\"Set a new value.\"\"\"
        self.value = new_value
        
def test_function():
    \"\"\"A test function.\"\"\"
    test = TestClass(42)
    return test.get_value()
"""
    
    # Create a mock extractor
    extractor = SurgicalContextExtractor(None)
    
    # Test different context window sizes
    test_cases = [
        ("Class definition", 2, "class"),
        ("Method definition", 5, "method"),
        ("Function definition", 17, "function"),
        ("Variable reference", 19, "variable")
    ]
    
    for description, line_num, symbol_type in test_cases:
        window_size = extractor._determine_context_window_size(sample_code, line_num, symbol_type)
        print(f"\n{description} (line {line_num}, type '{symbol_type}'):")
        print(f"Determined context window size: {window_size} lines")

def demo_code_snippet_extraction():
    """Demonstrate the code snippet extraction feature."""
    print("\n=== CODE SNIPPET EXTRACTION DEMO ===")
    
    # Create a sample file
    sample_file = "sample_code.py"
    sample_code = """#!/usr/bin/env python

import os
import re
from typing import List, Dict, Optional

class SampleClass:
    \"\"\"A sample class for demonstration.\"\"\"
    
    def __init__(self, name: str):
        \"\"\"Initialize with a name.\"\"\"
        self.name = name
        self.value = 0
        
    def set_value(self, value: int) -> None:
        \"\"\"Set the value.\"\"\"
        self.value = value
        
    def get_value(self) -> int:
        \"\"\"Get the value.\"\"\"
        return self.value
        
    def process_data(self, data: List[str]) -> Dict[str, int]:
        \"\"\"Process a list of data.\"\"\"
        result = {}
        for item in data:
            result[item] = len(item)
        return result

def helper_function(text: str) -> str:
    \"\"\"A helper function.\"\"\"
    return text.upper()

def main():
    \"\"\"Main function.\"\"\"
    sample = SampleClass("test")
    sample.set_value(42)
    data = ["apple", "banana", "cherry"]
    result = sample.process_data(data)
    print(f"Result: {result}")
    print(f"Helper: {helper_function('hello')}")

if __name__ == "__main__":
    main()
"""
    
    # Write the sample code to a file
    with open(sample_file, "w") as f:
        f.write(sample_code)
    
    try:
        # Create a mock extractor
        extractor = SurgicalContextExtractor(None)
        
        # Extract code snippets with different context types and window sizes
        test_cases = [
            ("Class definition", 7, 10, ContextType.DEFINITION, "SampleClass"),
            ("Method definition", 15, 5, ContextType.DEFINITION, "set_value"),
            ("Function call", 38, 3, ContextType.USAGE, "process_data"),
            ("Variable reference", 37, 2, ContextType.USAGE, "sample")
        ]
        
        for description, line_num, window_size, context_type, symbol_name in test_cases:
            # Mock the file reading
            extractor._read_file_content = lambda project_path, file_path: sample_code
            
            # Extract the snippet
            snippet = extractor._extract_code_snippet(
                ".", sample_file, line_num, window_size, context_type, symbol_name
            )
            
            if snippet:
                print(f"\n{description} (line {line_num}, window {window_size}, symbol '{symbol_name}'):")
                print(f"File: {snippet.file_path}")
                print(f"Lines: {snippet.start_line}-{snippet.end_line}")
                print(f"Context type: {snippet.context_type.value}")
                print(f"Surrounding function: {snippet.surrounding_function or 'N/A'}")
                print(f"Snippet:\n{format_snippet(snippet.content)}")
            else:
                print(f"\n{description}: Failed to extract snippet")
    
    finally:
        # Clean up the sample file
        if os.path.exists(sample_file):
            os.remove(sample_file)

if __name__ == "__main__":
    print("=== SURGICAL CONTEXT EXTRACTION DEMO ===")
    print("This demo showcases the core functionality of the SurgicalContextExtractor")
    
    # Demonstrate smart context window sizing
    demo_context_window_sizing()
    
    # Demonstrate code snippet extraction
    demo_code_snippet_extraction()
    
    print("\nDemo completed successfully!")
