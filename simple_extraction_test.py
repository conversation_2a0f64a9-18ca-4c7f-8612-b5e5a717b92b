#!/usr/bin/env python

import os
from typing import List, Optional

# Mock classes to simulate the behavior of the SurgicalFileExtractor
class SymbolInfo:
    """Information about a symbol in the codebase."""
    def __init__(self, name: str, start_line: int, file_path: str, symbol_type: str):
        self.name = name
        self.start_line = start_line
        self.file_path = file_path
        self.symbol_type = symbol_type

class ExtractionRange:
    """Represents a range of lines to extract from a file."""
    def __init__(self, start_line: int, end_line: int, total_lines: int, symbol_name: str, file_path: str):
        self.start_line = start_line
        self.end_line = end_line
        self.total_lines = total_lines
        self.symbol_name = symbol_name
        self.file_path = file_path

def read_file_content(file_path: str) -> Optional[str]:
    """Read the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return None

def get_file_line_count(file_path: str) -> int:
    """Get the total number of lines in a file."""
    content = read_file_content(file_path)
    if content is None:
        return 0
    return len(content.splitlines())

def get_symbols_in_file(file_path: str) -> List[SymbolInfo]:
    """
    Get all symbols defined in a file with their line numbers.
    
    This is a simplified version that uses regex to find function and class definitions.
    """
    import re
    
    content = read_file_content(file_path)
    if not content:
        return []
    
    lines = content.splitlines()
    symbols = []
    
    # Simple regex patterns to find function and class definitions
    function_pattern = re.compile(r'^\s*def\s+(\w+)\s*\(')
    class_pattern = re.compile(r'^\s*class\s+(\w+)')
    
    for i, line in enumerate(lines):
        # Check for function definitions
        function_match = function_pattern.match(line)
        if function_match:
            func_name = function_match.group(1)
            symbols.append(SymbolInfo(
                name=func_name,
                start_line=i + 1,  # Convert to 1-based line numbers
                file_path=file_path,
                symbol_type="function"
            ))
            continue
        
        # Check for class definitions
        class_match = class_pattern.match(line)
        if class_match:
            class_name = class_match.group(1)
            symbols.append(SymbolInfo(
                name=class_name,
                start_line=i + 1,  # Convert to 1-based line numbers
                file_path=file_path,
                symbol_type="class"
            ))
    
    # Sort by line number
    symbols.sort(key=lambda s: s.start_line)
    return symbols

def extract_symbol_range(target_symbol: str, file_path: str) -> Optional[ExtractionRange]:
    """
    Extract the line range for a specific symbol in a file.
    """
    # Get all symbols in the file
    file_symbols = get_symbols_in_file(file_path)
    if not file_symbols:
        return None
    
    # Find target symbol
    target = next((s for s in file_symbols if s.name == target_symbol), None)
    if not target:
        return None
    
    # Find next symbol boundary
    next_symbols = [s for s in file_symbols if s.start_line > target.start_line]
    
    if next_symbols:
        # Extract until next symbol
        next_boundary = min(next_symbols, key=lambda s: s.start_line)
        end_line = next_boundary.start_line - 1
    else:
        # Extract to end of file
        end_line = get_file_line_count(file_path)
    
    return ExtractionRange(
        start_line=target.start_line,
        end_line=end_line,
        total_lines=end_line - target.start_line + 1,
        symbol_name=target_symbol,
        file_path=file_path
    )

def extract_symbol_content(target_symbol: str, file_path: str) -> Optional[str]:
    """
    Extract the complete content of a symbol from a file.
    """
    extraction_range = extract_symbol_range(target_symbol, file_path)
    if not extraction_range:
        return None
    
    content = read_file_content(file_path)
    if not content:
        return None
    
    lines = content.splitlines()
    if extraction_range.start_line > len(lines) or extraction_range.start_line < 1:
        return None
    
    # Adjust end_line to not exceed file length
    end_line = min(extraction_range.end_line, len(lines))
    
    # Extract the lines (adjusting for 0-based indexing)
    extracted_lines = lines[extraction_range.start_line - 1:end_line]
    return '\n'.join(extracted_lines)

def format_extraction_output(symbol_name, file_path, content, extraction_range):
    """Format the extracted content for display."""
    header = f"# 🎯 OPTIMIZED: Send only what's needed\n\n"
    header += f"### REQUESTED FILE CONTEXT (Surgical Extraction)\n\n"
    header += f"--- File: {file_path} (Targeted extraction for {symbol_name}) ---\n\n"
    
    # Count lines
    lines = content.splitlines() if content else []
    line_count = len(lines)
    
    output = f"{header}"
    output += f"# THE COMPLETE TARGET FUNCTION ({line_count} lines)\n"
    output += f"```python\n{content}\n```\n\n"
    
    # Add extraction metadata
    if extraction_range:
        output += f"### EXTRACTION METADATA\n"
        output += f"- Symbol: {symbol_name}\n"
        output += f"- File: {file_path}\n"
        output += f"- Start Line: {extraction_range.start_line}\n"
        output += f"- End Line: {extraction_range.end_line}\n"
        output += f"- Total Lines: {extraction_range.total_lines}\n"
    
    return output

def main():
    """Main function to demonstrate the simplified Surgical File Extractor."""
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python simple_extraction_test.py <file_path> <symbol_name>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    symbol_name = sys.argv[2]
    
    # Get all symbols in the file
    print(f"\nSymbols defined in {file_path}:")
    symbols = get_symbols_in_file(file_path)
    if symbols:
        for symbol in symbols:
            print(f"  - {symbol.name} ({symbol.symbol_type}) at line {symbol.start_line}")
    else:
        print("  No symbols found.")
    
    # Extract the symbol range
    print(f"\nExtracting symbol: {symbol_name}")
    extraction_range = extract_symbol_range(symbol_name, file_path)
    if extraction_range:
        print(f"  Found at lines {extraction_range.start_line}-{extraction_range.end_line} ({extraction_range.total_lines} lines)")
    else:
        print(f"  Symbol not found in {file_path}")
        return
    
    # Extract the symbol content
    content = extract_symbol_content(symbol_name, file_path)
    if content:
        # Format and display the output
        output = format_extraction_output(symbol_name, file_path, content, extraction_range)
        print("\n" + output)
    else:
        print(f"  Failed to extract content for {symbol_name}")

if __name__ == "__main__":
    main()
