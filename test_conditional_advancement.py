#!/usr/bin/env python3
"""
Test script to verify the conditional advancement logic in the game system
"""

import sys
import os

def test_conditional_advancement_logic():
    """Test that conditional advancement logic is properly implemented"""
    print("🎮 Testing Conditional Advancement Logic")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        main_system = prompts.main_system
        
        print("📋 Checking Conditional Advancement Elements:")
        
        # Conditional advancement elements
        conditional_elements = [
            ("SUCCESS VALIDATION", "✅" if "SUCCESS VALIDATION" in main_system else "❌"),
            ("LEVEL COMPLETION CRITERIA", "✅" if "LEVEL COMPLETION CRITERIA" in main_system else "❌"),
            ("STAY AT CURRENT LEVEL", "✅" if "STAY AT CURRENT LEVEL" in main_system else "❌"),
            ("CONDITIONAL ADVANCEMENT LOGIC", "✅" if "CONDITIONAL ADVANCEMENT LOGIC" in main_system else "❌"),
            ("Evaluate results", "✅" if "Evaluate results" in main_system else "❌"),
            ("If insufficient information", "✅" if "If insufficient information" in main_system else "❌")
        ]
        
        print("\n🔍 Conditional Elements Check:")
        for element, status in conditional_elements:
            print(f"   {status} {element}")
        
        # Specific advancement criteria
        criteria_elements = [
            ("advance only if MAP_REQUEST returns relevant", "✅" if "advance only if MAP_REQUEST returns relevant" in main_system else "❌"),
            ("advance only if CONTEXT_REQUEST returns the actual", "✅" if "advance only if CONTEXT_REQUEST returns the actual" in main_system else "❌"),
            ("stay at current level and make another request", "✅" if "stay at current level and make another request" in main_system else "❌")
        ]
        
        print("\n📊 Advancement Criteria Check:")
        for element, status in criteria_elements:
            print(f"   {status} {element}")
        
        # Check assistant reply understanding
        assistant_reply = prompts.smart_map_request_assistant_reply
        assistant_elements = [
            ("advance to the next level ONLY if", "✅" if "advance to the next level ONLY if" in assistant_reply else "❌"),
            ("Evaluate if the MAP_REQUEST results", "✅" if "Evaluate if the MAP_REQUEST results" in assistant_reply else "❌"),
            ("If insufficient, stay at LEVEL 0", "✅" if "If insufficient, stay at LEVEL 0" in assistant_reply else "❌")
        ]
        
        print("\n🤖 Assistant Understanding Check:")
        for element, status in assistant_elements:
            print(f"   {status} {element}")
        
        # Overall assessment
        all_conditional = all(status == "✅" for _, status in conditional_elements)
        all_criteria = all(status == "✅" for _, status in criteria_elements)
        all_assistant = all(status == "✅" for _, status in assistant_elements)
        
        if all_conditional and all_criteria and all_assistant:
            print("\n🎉 SUCCESS: Conditional advancement logic is properly implemented!")
            return True
        else:
            print("\n❌ ISSUES: Some conditional elements are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_scenarios():
    """Test expected scenarios with conditional advancement"""
    print("\n🎯 Expected Scenarios with Conditional Advancement")
    print("=" * 70)
    
    print("📝 Scenario 1: Successful MAP_REQUEST")
    print("   User asks: 'How does close_position_based_on_conditions work?'")
    print("   AI at LEVEL 0 → Uses MAP_REQUEST")
    print("   MAP_REQUEST returns: Relevant files with position management")
    print("   AI evaluates: ✅ Sufficient information received")
    print("   AI advances to: LEVEL 1 (Repository Explorer)")
    
    print("\n📝 Scenario 2: Insufficient MAP_REQUEST")
    print("   User asks: 'How does close_position_based_on_conditions work?'")
    print("   AI at LEVEL 0 → Uses MAP_REQUEST")
    print("   MAP_REQUEST returns: Irrelevant files or no matches")
    print("   AI evaluates: ❌ Insufficient information received")
    print("   AI stays at: LEVEL 0 and tries different keywords")
    
    print("\n📝 Scenario 3: Successful CONTEXT_REQUEST")
    print("   AI at LEVEL 1 → Uses CONTEXT_REQUEST")
    print("   CONTEXT_REQUEST returns: Actual function implementation")
    print("   AI evaluates: ✅ Sufficient code context received")
    print("   AI advances to: LEVEL 2 (Code Analyst)")
    
    print("\n📝 Scenario 4: Insufficient CONTEXT_REQUEST")
    print("   AI at LEVEL 1 → Uses CONTEXT_REQUEST")
    print("   CONTEXT_REQUEST returns: Partial/irrelevant code snippets")
    print("   AI evaluates: ❌ Insufficient code context received")
    print("   AI stays at: LEVEL 1 and tries different symbols")
    
    return True

def test_expected_ai_responses():
    """Test what AI responses should look like with conditional logic"""
    print("\n🤖 Expected AI Responses with Conditional Logic")
    print("=" * 70)
    
    print("📝 Example 1: AI stays at LEVEL 0 after insufficient MAP_REQUEST")
    
    insufficient_response = """🎮 **MY CURRENT LEVEL**: LEVEL 0 - ZERO KNOWLEDGE

I attempted to advance to LEVEL 1 but the MAP_REQUEST results were insufficient for the user's query about 'close_position_based_on_conditions'.

**Evaluation of MAP_REQUEST results:**
❌ The returned files don't contain relevant position management functions
❌ No clear matches for the specific function name

**Staying at LEVEL 0 and trying different approach:**

{MAP_REQUEST: {"keywords": ["position", "close", "trading", "management", "conditions"], "type": "implementation", "scope": "all", "max_results": 10}}"""
    
    print(insufficient_response)
    
    print("\n📝 Example 2: AI successfully advances to LEVEL 1")
    
    successful_response = """🎮 **MY CURRENT LEVEL**: LEVEL 1 - REPOSITORY EXPLORER

✅ Successfully advanced from LEVEL 0! The MAP_REQUEST returned relevant repository information.

**Evaluation of MAP_REQUEST results:**
✅ Found files related to position management
✅ Identified potential location of close_position_based_on_conditions function

**Now at LEVEL 1, I can review the repository map and proceed to LEVEL 2:**

{CONTEXT_REQUEST: {"original_user_query_context": "How does close_position_based_on_conditions function work", "symbols_of_interest": [{"type": "function", "name": "close_position_based_on_conditions", "file_name": "position_manager.py"}]}}"""
    
    print(successful_response)
    
    print(f"\n🔍 Key Behaviors:")
    print(f"   ✅ AI evaluates the quality of received information")
    print(f"   ✅ AI only advances when it has sufficient information")
    print(f"   ✅ AI stays at current level if information is insufficient")
    print(f"   ✅ AI tries different keywords/approaches when stuck")
    print(f"   ✅ AI clearly announces level changes and reasoning")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Conditional Advancement Logic")
    print("=" * 80)
    
    success1 = test_conditional_advancement_logic()
    success2 = test_expected_scenarios()
    success3 = test_expected_ai_responses()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: Conditional advancement logic is complete!")
        print("\n🎮 Key Features:")
        print("   ✅ AI evaluates information quality before advancing")
        print("   ✅ AI stays at current level if information is insufficient")
        print("   ✅ AI tries different approaches when stuck")
        print("   ✅ Clear advancement criteria for each level")
        print("   ✅ Transparent evaluation and reasoning")
        print("\n🎯 Expected Behavior:")
        print("   - AI won't automatically advance just by making requests")
        print("   - AI will evaluate if received information is sufficient")
        print("   - AI will stay at current level until goal is truly achieved")
        print("   - AI will try different keywords/approaches if needed")
        print("   - AI will clearly communicate its evaluation and decisions")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
