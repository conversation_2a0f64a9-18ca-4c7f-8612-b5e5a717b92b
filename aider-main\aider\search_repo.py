"""
Repository search functionality for finding functions, classes, and methods.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Set, Optional

def search_repo_for_symbol(root_dir: str, symbol_name: str, case_sensitive: bool = False) -> List[Dict[str, str]]:
    """
    Search the repository for a specific symbol (function, class, method).

    Args:
        root_dir: The root directory of the repository
        symbol_name: The name of the symbol to search for
        case_sensitive: Whether the search should be case sensitive

    Returns:
        A list of dictionaries containing file paths and line numbers where the symbol was found
    """
    results = []

    # Prepare the search pattern
    if case_sensitive:
        pattern = r'\b' + re.escape(symbol_name) + r'\b'
        flags = re.MULTILINE
    else:
        pattern = r'\b' + re.escape(symbol_name) + r'\b'
        flags = re.MULTILINE | re.IGNORECASE

    # Common patterns for function and class definitions in various languages
    definition_patterns = {
        '.py': [
            rf'def\s+{re.escape(symbol_name)}\s*\(',
            rf'class\s+{re.escape(symbol_name)}\s*[:\(]',
            # Handle method definitions in classes (with self parameter)
            rf'def\s+{re.escape(symbol_name)}\s*\(\s*self',
            # Handle private/protected methods with underscores
            rf'def\s+_{re.escape(symbol_name)}\s*\(',
            rf'def\s+__{re.escape(symbol_name)}\s*\('
        ],
        '.js': [
            rf'function\s+{re.escape(symbol_name)}\s*\(',
            rf'class\s+{re.escape(symbol_name)}\s*[{{\(]',
            rf'const\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'let\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'var\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'{re.escape(symbol_name)}\s*:\s*function'
        ],
        '.java': [
            rf'(public|private|protected)?\s+(static)?\s+\w+\s+{re.escape(symbol_name)}\s*\(',
            rf'(public|private|protected)?\s+class\s+{re.escape(symbol_name)}\s*[{{\(]'
        ],
        '.cpp': [
            rf'\w+\s+{re.escape(symbol_name)}\s*\(',
            rf'class\s+{re.escape(symbol_name)}\s*[:{{\(]'
        ],
        '.c': [
            rf'\w+\s+{re.escape(symbol_name)}\s*\('
        ],
        '.cs': [
            rf'(public|private|protected|internal)?\s+(static)?\s+\w+\s+{re.escape(symbol_name)}\s*\(',
            rf'(public|private|protected|internal)?\s+class\s+{re.escape(symbol_name)}\s*[:{{\(]'
        ],
        '.php': [
            rf'function\s+{re.escape(symbol_name)}\s*\(',
            rf'class\s+{re.escape(symbol_name)}\s*[{{\(]'
        ],
        '.rb': [
            rf'def\s+{re.escape(symbol_name)}',
            rf'class\s+{re.escape(symbol_name)}\s*[<]?'
        ],
        '.go': [
            rf'func\s+{re.escape(symbol_name)}\s*\(',
            rf'type\s+{re.escape(symbol_name)}\s+struct'
        ],
        '.ts': [
            rf'function\s+{re.escape(symbol_name)}\s*\(',
            rf'class\s+{re.escape(symbol_name)}\s*[{{\(]',
            rf'const\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'let\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'var\s+{re.escape(symbol_name)}\s*=\s*function',
            rf'{re.escape(symbol_name)}\s*:\s*function'
        ]
    }

    # Walk through the repository
    for root, _, files in os.walk(root_dir):
        for file in files:
            # Skip binary files and hidden files
            if file.startswith('.') or any(file.endswith(ext) for ext in ['.exe', '.dll', '.so', '.pyc', '.pyo']):
                continue

            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, root_dir)

            try:
                # Try to read the file as text
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Get file extension
                _, ext = os.path.splitext(file)

                # Check for definition patterns based on file extension
                is_definition = False
                if ext in definition_patterns:
                    for def_pattern in definition_patterns[ext]:
                        if re.search(def_pattern, content, flags):
                            is_definition = True
                            break

                # If we found a definition or we're just looking for any mention
                if is_definition or re.search(pattern, content, flags):
                    # Find all occurrences with line numbers
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if re.search(pattern, line, flags):
                            match_type = "definition" if is_definition else "reference"
                            results.append({
                                'file': rel_path,
                                'line': i + 1,
                                'content': line.strip(),
                                'type': match_type
                            })
            except (UnicodeDecodeError, IOError):
                # Skip files that can't be read as text
                continue

    # Sort results to prioritize definitions over references
    results.sort(key=lambda x: 0 if x['type'] == 'definition' else 1)

    return results


def format_search_results(results: List[Dict[str, str]]) -> str:
    """
    Format search results into a readable string.

    Args:
        results: List of search result dictionaries

    Returns:
        Formatted string with search results
    """
    if not results:
        return "No matches found."

    output = f"Found {len(results)} matches:\n\n"

    # Group results by file
    files = {}
    for result in results:
        file_path = result['file']
        if file_path not in files:
            files[file_path] = []
        files[file_path].append(result)

    # Format results by file
    for file_path, file_results in files.items():
        output += f"{file_path}:\n"
        for result in file_results:
            match_type = "[DEFINITION]" if result['type'] == 'definition' else "[REFERENCE]"
            output += f"  Line {result['line']} {match_type}: {result['content']}\n"
        output += "\n"

    return output


def extract_code_elements(text: str) -> List[str]:
    """
    Extract potential code elements (functions, classes, methods, variables) from text.

    Args:
        text: Text to extract code elements from

    Returns:
        List of potential code element names
    """
    # Common patterns for code elements
    patterns = [
        # Functions and methods
        r'\b(\w+)\s*\(',                      # function_name(
        r'\b_(\w+)\s*\(',                     # _private_function_name(
        r'\b__(\w+)\s*\(',                    # __private_function_name(
        r'function\s+[\'"]?(\w+)[\'"]?',      # function 'function_name'
        r'method\s+[\'"]?(\w+)[\'"]?',        # method 'method_name'
        r'function\s+[\'"]?_(\w+)[\'"]?',     # function '_private_function_name'
        r'method\s+[\'"]?_(\w+)[\'"]?',       # method '_private_method_name'

        # Classes
        r'class\s+[\'"]?(\w+)[\'"]?',         # class 'ClassName'
        r'(\w+)\s+class',                     # ClassName class

        # Variables and properties
        r'variable\s+[\'"]?(\w+)[\'"]?',      # variable 'variable_name'
        r'property\s+[\'"]?(\w+)[\'"]?',      # property 'property_name'
        r'attribute\s+[\'"]?(\w+)[\'"]?',     # attribute 'attribute_name'

        # General code elements
        r'[\'"](\w+)[\'"]',                   # 'element_name'
        r'`(\w+)`',                           # `element_name`
    ]

    # Extract all potential code elements
    element_names = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple):
                element_names.extend([m for m in match if m])
            else:
                element_names.append(match)

    # Filter out common words and programming keywords
    common_words = {'if', 'else', 'for', 'while', 'return', 'import', 'from', 'class', 'def', 'print',
                   'int', 'str', 'list', 'dict', 'set', 'tuple', 'function', 'method', 'variable',
                   'property', 'attribute', 'the', 'and', 'or', 'not', 'in', 'is', 'as', 'true', 'false',
                   'none', 'this', 'self', 'super'}

    filtered_names = [name for name in element_names
                     if name and name.lower() not in common_words and len(name) > 2]

    # Remove duplicates while preserving order
    seen = set()
    unique_names = []
    for name in filtered_names:
        if name not in seen:
            seen.add(name)
            unique_names.append(name)

    return unique_names

def fuzzy_match_filename(query: str, file_path: str, threshold: float = 0.7) -> float:
    """
    Perform fuzzy matching between a query and a file path.

    Args:
        query: The search query or term
        file_path: The file path to match against
        threshold: The similarity threshold (0.0 to 1.0)

    Returns:
        Similarity score (0.0 to 1.0) or 0.0 if below threshold
    """
    from difflib import SequenceMatcher

    # Extract the filename and directory parts
    import os
    filename = os.path.basename(file_path)
    dirname = os.path.dirname(file_path)

    # Remove extension for better matching
    name_without_ext, _ = os.path.splitext(filename)

    # Calculate similarity scores for different parts
    # 1. Full path similarity
    full_path_sim = SequenceMatcher(None, query.lower(), file_path.lower()).ratio()

    # 2. Filename similarity (higher weight)
    filename_sim = SequenceMatcher(None, query.lower(), filename.lower()).ratio()

    # 3. Name without extension similarity (highest weight)
    name_sim = SequenceMatcher(None, query.lower(), name_without_ext.lower()).ratio()

    # 4. Directory path similarity (lower weight)
    dir_sim = 0.0
    if dirname:
        dir_sim = SequenceMatcher(None, query.lower(), dirname.lower()).ratio()

    # Calculate weighted score - prioritize filename matches
    weighted_score = (
        full_path_sim * 0.2 +  # 20% weight for full path
        filename_sim * 0.3 +   # 30% weight for filename with extension
        name_sim * 0.4 +       # 40% weight for name without extension
        dir_sim * 0.1          # 10% weight for directory path
    )

    # Return the score if it meets the threshold
    return weighted_score if weighted_score >= threshold else 0.0

def smart_search_fallback(root_dir: str, query: str, io=None) -> List[Dict[str, str]]:
    """
    Perform a smart search fallback when specific code elements aren't found.
    This now uses the Smart Map Request System for intelligent search.

    Args:
        root_dir: The root directory of the repository
        query: The search query (e.g., "login authentication function")
            Can be a descriptive query, comma-separated keywords, or a specific function name
        io: Optional IO object for logging

    Returns:
        List of suggested files with relevance scores
    """
    # Try to use the Smart Map Request System if available
    try:
        from .smart_map_request_handler import SmartMapRequestHandler
        from .repomap import RepoMap
        from .models import Model

        # Create a temporary repo map for the smart search
        model = Model("gpt-3.5-turbo")
        repo_map = RepoMap(
            map_tokens=8192,
            root=root_dir,
            main_model=model,
            io=io,
            verbose=False
        )

        # Create smart map request handler
        smart_handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=root_dir,
            io=io
        )

        # Extract keywords from query
        keywords = extract_keywords_from_query(query)

        # Create a map request
        map_request = {
            "keywords": keywords,
            "type": "implementation",
            "scope": "all",
            "max_results": 10
        }

        if io:
            io.tool_output(f"Using Smart Map Request System with keywords: {keywords}")

        # Process the request
        relevant_files = smart_handler._smart_hierarchical_search(
            keywords=keywords,
            request_type="implementation",
            scope="all",
            max_results=10
        )

        # Convert to the expected format
        results = []
        for file_info in relevant_files:
            results.append({
                'file': file_info['file'],
                'relevance_score': file_info['relevance_score'],
                'is_code_file': True  # Assume code files for now
            })

        if io and results:
            io.tool_output(f"Smart Map Request found {len(results)} relevant files")

        return results

    except ImportError:
        if io:
            io.tool_warning("Smart Map Request System not available, falling back to legacy search")
    except Exception as e:
        if io:
            io.tool_warning(f"Smart Map Request System error: {e}, falling back to legacy search")

    # Fallback to legacy search implementation
    potential_function_names = extract_function_names(query)
    # Define file types that are likely to contain code
    code_file_extensions = {
        '.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs', '.go', '.rb', '.php',
        '.html', '.css', '.jsx', '.tsx', '.vue', '.scala', '.kt', '.rs', '.swift',
        '.m', '.h', '.sh', '.bash', '.ps1', '.pl', '.pm', '.r'
    }

    # Define file types to exclude (binary, data, etc.)
    excluded_extensions = {
        '.exe', '.dll', '.so', '.pyc', '.pyo', '.jar', '.war', '.ear', '.zip', '.tar',
        '.gz', '.rar', '.7z', '.db', '.sqlite', '.sqlite3', '.mdb', '.accdb', '.csv',
        '.xls', '.xlsx', '.doc', '.docx', '.pdf', '.jpg', '.jpeg', '.png', '.gif',
        '.bmp', '.svg', '.ico', '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.log'
    }

    # IMPROVEMENT 1: More selective common terms filtering
    # We're now being more careful about what terms we consider "common"
    # in programming contexts
    # These terms are used to filter out common programming terms from search results
    filtered_terms = {
        # Only the most generic programming terms
        'function', 'method', 'class', 'import', 'export',
        'require', 'include', 'new', 'return',
        # Only the most generic variable names
        'data', 'result', 'input', 'output', 'value', 'key',
        'index', 'count', 'size', 'length'
    }

    # IMPROVEMENT 2: Look for potential function identifiers in the query first
    # This helps prioritize exact function name matches
    potential_function_patterns = [
        # Match snake_case patterns (common in Python, Ruby)
        r'\b[a-z][a-z0-9_]+(?:_[a-z0-9_]+)+\b',
        # Match private snake_case patterns with leading underscore (common in Python)
        r'\b_[a-z0-9_]+(?:_[a-z0-9_]+)*\b',
        # Match private snake_case patterns with double leading underscore (Python name mangling)
        r'\b__[a-z0-9_]+(?:_[a-z0-9_]+)*\b',
        # Match camelCase patterns (common in JS, Java)
        r'\b[a-z][a-z0-9]*(?:[A-Z][a-z0-9]*)+\b',
        # Match PascalCase patterns (common for classes)
        r'\b[A-Z][a-z0-9]*(?:[A-Z][a-z0-9]*)+\b',
        # Match hyphenated-names (common in CSS, HTML attributes)
        r'\b[a-z][a-z0-9]*(?:-[a-z0-9]+)+\b',
    ]

    potential_function_names = []
    for pattern in potential_function_patterns:
        potential_function_names.extend(re.findall(pattern, query))

    if potential_function_names and io:
        io.tool_output(f"Detected potential function/identifier names: {', '.join(potential_function_names)}")

    # Enhanced debugging for query processing
    if io:
        io.tool_output(f"Processing search query: '{query}'")
        # Check for any double quotes that might cause issues
        if '\"' in query:
            io.tool_warning(f"WARNING: Query contains double quotes which may cause search issues")
            # Remove double quotes for better processing
            query = query.replace('\"', '')
            io.tool_output(f"Cleaned query: '{query}'")

    # Check if the query is a comma-separated list of keywords
    # This would be the case when the AI provides keywords in the format:
    # "keyword1, keyword2, keyword3, ..."
    if "," in query and not query.startswith("{") and not query.endswith("}"):
        # First, remove any double quotes from individual keywords
        # This handles cases where the AI provides: "\"keyword1\", \"keyword2\", \"keyword3\""
        cleaned_query = query.replace('\"', '')
        if cleaned_query != query and io:
            io.tool_output(f"Removed double quotes from query: '{cleaned_query}'")

        # Split by commas and clean up each keyword
        raw_ai_keywords = [k.strip() for k in cleaned_query.split(",") if k.strip()]

        # IMPORTANT: Do NOT filter out AI-provided keywords, even if they're common terms
        # The AI model has explicitly chosen these keywords for a reason
        ai_keywords = raw_ai_keywords

        # Special handling for keywords that start with underscore (private methods)
        # Ensure they're preserved exactly as provided
        for i, keyword in enumerate(ai_keywords):
            if keyword.startswith('_'):
                # Log that we're preserving a private method name
                if io:
                    io.tool_output(f"Preserving private method name: '{keyword}'")
                # Ensure it's treated as a high-priority function name
                if keyword not in potential_function_names:
                    potential_function_names.append(keyword)

        if io:
            io.tool_output(f"Using AI-provided keywords: {', '.join(ai_keywords)}")
            # Log each keyword individually for debugging
            for i, keyword in enumerate(ai_keywords):
                io.tool_output(f"  Keyword {i+1}: '{keyword}'")

        # Use these as priority keywords
        priority_keywords = ai_keywords
        # No need for further keyword extraction
        quoted_terms = []
        clean_query = ""
    # Special case for exact function/class names that might not contain commas
    elif len(query.strip()) > 0 and not query.startswith("{") and not query.endswith("}"):
        # Check if the query looks like a function or class name (no spaces, contains underscores or camelCase)
        # Also explicitly check for leading underscores to catch private methods
        if "_" in query or query.startswith("_") or (any(c.isupper() for c in query) and not query.isupper()):
            if io:
                io.tool_output(f"Query appears to be a specific code element: '{query}'")
            # Treat it as a high-priority keyword
            priority_keywords = [query.strip()]
            quoted_terms = []
            clean_query = ""
    else:
        # Extract quoted terms (high priority keywords)
        quoted_terms = []
        # Match terms in double quotes
        quoted_terms.extend(re.findall(r'"([^"]+)"', query))
        # Match terms in single quotes
        quoted_terms.extend(re.findall(r"'([^']+)'", query))
        # Match terms in backticks
        quoted_terms.extend(re.findall(r"`([^`]+)`", query))

        # Clean up any remaining double quotes in quoted terms
        cleaned_quoted_terms = []
        for term in quoted_terms:
            if '\"' in term:
                cleaned_term = term.replace('\"', '')
                if io:
                    io.tool_output(f"Removed double quotes from quoted term: '{cleaned_term}'")
                cleaned_quoted_terms.append(cleaned_term)
            else:
                cleaned_quoted_terms.append(term)
        quoted_terms = cleaned_quoted_terms

        # IMPROVEMENT 3: Add potential function names to quoted terms
        # This gives them similar priority treatment
        for func_name in potential_function_names:
            if func_name not in quoted_terms:
                quoted_terms.append(func_name)

        # Remove the quoted terms from the query for separate processing
        clean_query = query
        for term in quoted_terms:
            clean_query = clean_query.replace(f'"{term}"', ' ')
            clean_query = clean_query.replace(f"'{term}'", ' ')
            clean_query = clean_query.replace(f"`{term}`", ' ')
            # Also try to remove the term without quotes (for potential_function_names)
            clean_query = re.sub(r'\b' + re.escape(term) + r'\b', ' ', clean_query)

    # Get a comprehensive set of stopwords including common programming terms
    basic_stopwords = {
        # Articles
        'the', 'a', 'an',
        # Prepositions
        'in', 'on', 'at', 'by', 'for', 'with', 'about', 'to', 'from', 'of',
        # Conjunctions
        'and', 'or', 'but', 'nor', 'so', 'yet',
        # Pronouns
        'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
        'this', 'that', 'these', 'those', 'who', 'whom', 'whose', 'which', 'what',
        # Common verbs
        'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
        'do', 'does', 'did', 'can', 'could', 'will', 'would', 'shall', 'should',
        'may', 'might', 'must'
    }

    # IMPROVEMENT 4: Be more selective about stopwords for code searching
    # Removed many programming terms from stopwords to prevent filtering out
    # potentially relevant keywords

    # Note: We've removed the dynamic repository common words identification
    # as it was causing issues with filtering out important domain-specific terms.
    # Instead, we're using a more conservative approach with just basic stopwords.

    # This commented section is kept for reference but is not used in the current implementation:
    #
    # try:
    #     # Count word frequency across a sample of files
    #     word_counts = {}
    #     file_count = 0
    #     max_files = 100  # Limit to avoid processing too many files
    #
    #     # Process files to identify common words
    #     # ...
    #
    #     # Words need to appear in more than 75% of files to be considered common
    #     if file_count > 0:
    #         threshold = 0.75 * file_count
    #         repo_common_words = {word for word, count in word_counts.items()
    #                           if count > threshold and len(word) > 2}
    # except Exception as e:
    #     if io:
    #         io.tool_warning(f"Error identifying common words: {str(e)}")

    # We're now using only basic_stopwords for filtering

    # IMPROVEMENT 6: Be more selective about filtering out common words
    # Only filter out basic stopwords, not all common programming terms
    # Note: We're using basic_stopwords directly instead of combining with repo_common_words
    # This helps prevent filtering out important domain-specific terms
    keywords = [word.lower() for word in re.findall(r'\b\w+\b', clean_query)
               if word.lower() not in basic_stopwords and len(word) > 2]

    # If we don't already have priority keywords from AI-provided keywords
    if not 'priority_keywords' in locals():
        # Add quoted terms as high-priority keywords
        priority_keywords = []
        for term in quoted_terms:
            # Split multi-word terms into individual words
            term_words = re.findall(r'\b\w+\b', term.lower())
            if len(term_words) > 1:
                # Keep the full term for phrase matching
                priority_keywords.append(term.lower())
                # Also add individual words if they're not stopwords
                for word in term_words:
                    if word not in basic_stopwords and len(word) > 2 and word not in priority_keywords:
                        priority_keywords.append(word)
            elif len(term_words) == 1 and term_words[0] not in priority_keywords:
                priority_keywords.append(term_words[0])

    # Combine priority keywords with regular keywords
    all_keywords = priority_keywords + [k for k in keywords if k not in priority_keywords]

    # If no keywords found, extract any words with length > 2
    if not all_keywords:
        all_keywords = [word.lower() for word in re.findall(r'\b\w+\b', query)
                      if len(word) > 2]

    if io:
        if priority_keywords:
            io.tool_output(f"Smart search using priority keywords: {', '.join(priority_keywords)}")
        if keywords:
            io.tool_output(f"Smart search using additional keywords: {', '.join(keywords)}")

    # If still no keywords, return empty results
    if not all_keywords:
        if io:
            io.tool_warning("No meaningful keywords found in the query.")
        return []

    # Search for each keyword in the repository
    file_scores = {}  # file path -> relevance score
    file_match_details = {}  # Keep track of what matched in each file
    filename_match_scores = {}  # Track filename match scores
    class_function_match_scores = {}  # Track class/function match scores
    content_relevance_scores = {}  # Track content relevance scores

    # IMPROVEMENT 7: Enhanced keyword weighting system
    # Assign weights to different keywords based on their priority and position
    keyword_weights = {}

    # Priority keywords (from quotes or potential function names) get higher weights
    for i, keyword in enumerate(priority_keywords):
        # Priority keywords start at 3.0 and decrease slightly, but stay above 2.0
        # Increased weight compared to original implementation
        keyword_weights[keyword] = max(3.0 - (i * 0.1), 2.0)

    # Regular keywords get lower weights
    for i, keyword in enumerate(keywords):
        if keyword not in keyword_weights:  # Skip if already assigned as priority
            # Regular keywords start at 1.0 and decrease slightly, but stay above 0.5
            keyword_weights[keyword] = max(1.0 - (i * 0.1), 0.5)

    # IMPROVEMENT 8: Special higher weight for potential function names
    for func_name in potential_function_names:
        if func_name in keyword_weights:
            # Boost function names even higher
            keyword_weights[func_name] *= 1.5

            # Give extra boost to private methods (starting with underscore)
            if func_name.startswith('_'):
                # Private methods are more specific and should be prioritized
                keyword_weights[func_name] *= 1.5

    # First pass: Check for filename matches using fuzzy matching
    # This prioritizes files whose names match the search query
    for root, _, files in os.walk(root_dir):
        for file in files:
            # Skip hidden files
            if file.startswith('.'):
                continue

            # Skip excluded file types
            _, ext = os.path.splitext(file)
            if ext.lower() in excluded_extensions:
                continue

            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, root_dir)

            # Check for fuzzy filename matches with each keyword
            for keyword in all_keywords:
                # Skip very short keywords for filename matching
                if len(keyword) < 3:
                    continue

                # Get fuzzy match score
                match_score = fuzzy_match_filename(keyword, rel_path)

                if match_score > 0:
                    # Apply keyword weight
                    weighted_score = match_score * keyword_weights.get(keyword, 1.0)

                    # Boost score for exact matches in filename
                    if keyword.lower() in os.path.basename(rel_path).lower():
                        weighted_score *= 2.0

                    # Initialize if not already present
                    if rel_path not in filename_match_scores:
                        filename_match_scores[rel_path] = 0

                    # Add to filename match scores
                    filename_match_scores[rel_path] += weighted_score

                    # Log high-scoring filename matches
                    if io and weighted_score > 0.8:
                        io.tool_output(f"High filename match: '{keyword}' matched '{rel_path}' (score: {weighted_score:.2f})")

    # Add filename match scores to file_scores
    for file_path, score in filename_match_scores.items():
        if file_path not in file_scores:
            file_scores[file_path] = 0
            file_match_details[file_path] = []

        # Filename matches get a high weight in the overall score
        file_scores[file_path] += score * 3.0  # Triple the impact of filename matches

        # Add match details
        file_match_details[file_path].append({
            'type': 'filename_match',
            'score': score,
            'details': 'Filename match'
        })

    # Second pass: Check for class/function name matches
    # This prioritizes files containing exact matches of requested class or function names
    class_function_match_scores = {}  # Track class/function match scores

    for keyword in potential_function_names:
        # Skip very short function names
        if len(keyword) < 3:
            continue

        # Search for class/function definitions
        for root, _, files in os.walk(root_dir):
            for file in files:
                # Skip hidden files
                if file.startswith('.'):
                    continue

                # Get file extension
                _, ext = os.path.splitext(file)

                # Skip excluded file types
                if ext.lower() in excluded_extensions:
                    continue

                # Prioritize code files
                is_code_file = ext.lower() in code_file_extensions

                # Skip non-code files for function name matching
                if not is_code_file:
                    continue

                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, root_dir)

                try:
                    # Try to read the file as text
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # Check for function/class definitions based on file extension
                    definition_found = False
                    definition_score = 0.0

                    # Get definition patterns for this file extension
                    patterns = []

                    # Check for Python-style definitions
                    if ext.lower() == '.py':
                        patterns = [
                            rf'def\s+{re.escape(keyword)}\s*\(',  # Function definition
                            rf'class\s+{re.escape(keyword)}\s*[:\(]',  # Class definition
                            rf'def\s+{re.escape(keyword)}\s*\(\s*self',  # Method definition
                        ]

                        # Check for snake_case variations
                        if '_' not in keyword:
                            # Try camelCase to snake_case conversion
                            snake_case = ''.join(['_' + c.lower() if c.isupper() else c for c in keyword]).lstrip('_')
                            if snake_case != keyword:
                                patterns.append(rf'def\s+{re.escape(snake_case)}\s*\(')
                                patterns.append(rf'def\s+{re.escape(snake_case)}\s*\(\s*self')

                    # Check for JavaScript/TypeScript definitions
                    elif ext.lower() in ['.js', '.ts', '.jsx', '.tsx']:
                        patterns = [
                            rf'function\s+{re.escape(keyword)}\s*\(',  # Function definition
                            rf'class\s+{re.escape(keyword)}\s*[{{\(]',  # Class definition
                            rf'const\s+{re.escape(keyword)}\s*=\s*function',  # Function expression
                            rf'let\s+{re.escape(keyword)}\s*=\s*function',  # Function expression
                            rf'var\s+{re.escape(keyword)}\s*=\s*function',  # Function expression
                            rf'{re.escape(keyword)}\s*:\s*function',  # Object method
                            rf'{re.escape(keyword)}\s*=\s*\(',  # Arrow function
                        ]

                        # Check for camelCase variations
                        if '_' in keyword:
                            # Try snake_case to camelCase conversion
                            camel_case = keyword.split('_')[0] + ''.join(x.title() for x in keyword.split('_')[1:])
                            if camel_case != keyword:
                                patterns.append(rf'function\s+{re.escape(camel_case)}\s*\(')
                                patterns.append(rf'const\s+{re.escape(camel_case)}\s*=\s*function')
                                patterns.append(rf'{re.escape(camel_case)}\s*:\s*function')

                    # Check for Java/C#/C++ definitions
                    elif ext.lower() in ['.java', '.cs', '.cpp', '.c']:
                        patterns = [
                            rf'\w+\s+{re.escape(keyword)}\s*\(',  # Method definition
                            rf'class\s+{re.escape(keyword)}\s*[:{{\(]',  # Class definition
                        ]

                    # Check for definition patterns
                    for pattern in patterns:
                        matches = re.findall(pattern, content, re.MULTILINE)
                        if matches:
                            definition_found = True
                            definition_score += len(matches) * 2.0  # Double score for definitions

                    # Check for references (not definitions)
                    if not definition_found:
                        # Look for references to the function/class name
                        reference_pattern = r'\b' + re.escape(keyword) + r'\b'
                        references = re.findall(reference_pattern, content, re.MULTILINE)
                        if references:
                            definition_score += len(references) * 0.5  # Half score for references

                    # If we found definitions or references
                    if definition_score > 0:
                        # Apply keyword weight
                        weighted_score = definition_score * keyword_weights.get(keyword, 1.0)

                        # Initialize if not already present
                        if rel_path not in class_function_match_scores:
                            class_function_match_scores[rel_path] = 0

                        # Add to class/function match scores
                        class_function_match_scores[rel_path] += weighted_score

                        # Log high-scoring function/class matches
                        if io and weighted_score > 1.0:
                            match_type = "definition" if definition_found else "reference"
                            io.tool_output(f"Function/class match: '{keyword}' {match_type} in '{rel_path}' (score: {weighted_score:.2f})")

                except (UnicodeDecodeError, IOError):
                    # Skip files that can't be read as text
                    continue

    # Add class/function match scores to file_scores
    for file_path, score in class_function_match_scores.items():
        if file_path not in file_scores:
            file_scores[file_path] = 0
            file_match_details[file_path] = []

        # Class/function matches get a high weight in the overall score
        file_scores[file_path] += score * 2.5  # 2.5x impact for class/function matches

        # Add match details
        file_match_details[file_path].append({
            'type': 'class_function_match',
            'score': score,
            'details': 'Class/function match'
        })

    for keyword in all_keywords:
        # Search for the keyword in the repository
        results = []

        # Check if this is a multi-word quoted term
        is_phrase = ' ' in keyword

        # Walk through the repository
        for root, _, files in os.walk(root_dir):
            for file in files:
                # Skip hidden files
                if file.startswith('.'):
                    continue

                # Get file extension
                _, ext = os.path.splitext(file)

                # Skip excluded file types
                if ext.lower() in excluded_extensions:
                    continue

                # Prioritize code files
                is_code_file = ext.lower() in code_file_extensions

                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, root_dir)

                try:
                    # Try to read the file as text
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # IMPROVEMENT 9: Enhanced pattern matching for function names
                    # Check if this keyword looks like a function name
                    is_potential_function = keyword in potential_function_names

                    occurrences = 0
                    matches = []

                    # Different search pattern for phrases vs. single words
                    if is_phrase:
                        # For phrases, search for the exact phrase
                        pattern = re.escape(keyword)
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        occurrences = len(matches)
                    elif is_potential_function:
                        # IMPROVEMENT 10: More comprehensive function name pattern matching
                        # Get all possible variations of function name declarations
                        patterns = [
                            # Standard function definition
                            r'def\s+' + re.escape(keyword) + r'\s*\(',
                            # Method definition
                            r'def\s+' + re.escape(keyword) + r'\s*\(self',
                            # Class method (Python)
                            r'def\s+' + re.escape(keyword) + r'\s*\(cls',
                            # JavaScript/TypeScript function
                            r'function\s+' + re.escape(keyword) + r'\s*\(',
                            # JavaScript/TypeScript arrow function
                            r'const\s+' + re.escape(keyword) + r'\s*=\s*(\([^)]*\)|[^=]*)\s*=>\s*[{(]',
                            # JavaScript/TypeScript method
                            r'[a-zA-Z0-9_$]*\s*' + re.escape(keyword) + r'\s*\([^)]*\)\s*[{]',
                            # Variable assignment that might be a function
                            r'[a-zA-Z0-9_$]*\s*' + re.escape(keyword) + r'\s*=\s*',
                            # The plain function name with word boundaries
                            r'\b' + re.escape(keyword) + r'\b',
                        ]

                        # Match all patterns
                        all_matches = []
                        for pattern in patterns:
                            pattern_matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                            all_matches.extend(pattern_matches)

                        # Definition matches get higher weight
                        definition_patterns = patterns[:-1]  # All except the plain name match
                        definition_matches = []
                        for pattern in definition_patterns:
                            def_matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                            definition_matches.extend(def_matches)

                        # Count plain occurrences once, definitions three times
                        occurrences = len(all_matches) + len(definition_matches) * 2
                        matches = all_matches
                    else:
                        # For single words, search for word boundaries
                        # Handle special case for function names with underscores
                        if '_' in keyword:
                            # For function names with underscores, we need to be more flexible
                            # to match both the full name and parts of the name
                            pattern = re.escape(keyword)
                            matches = re.findall(pattern, content, re.IGNORECASE)

                            # Also check for function definitions with this name
                            def_pattern = r'def\s+' + re.escape(keyword) + r'\s*\('
                            def_matches = re.findall(def_pattern, content, re.IGNORECASE)

                            # Handle functions that already start with underscore
                            if keyword.startswith('_'):
                                # For keywords already starting with underscore, search for exact match
                                # This handles private methods like _analyze_trading_conditions
                                exact_pattern = r'def\s+' + re.escape(keyword) + r'\s*\('
                                exact_matches = re.findall(exact_pattern, content, re.IGNORECASE)
                                # Add with higher weight for exact matches of private methods
                                matches.extend(exact_matches * 3)  # Count exact private method matches with higher weight
                            else:
                                # For non-underscore keywords, also check for private method variants
                                private_pattern = r'def\s+_' + re.escape(keyword) + r'\s*\('
                                private_matches = re.findall(private_pattern, content, re.IGNORECASE)
                                matches.extend(private_matches)

                            # Add definition matches with higher weight
                            matches.extend(def_matches * 2)  # Count definitions twice

                            occurrences = len(matches)
                        else:
                            # Regular word boundary search for normal keywords
                            pattern = r'\b' + re.escape(keyword) + r'\b'
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            occurrences = len(matches)

                    if occurrences > 0:
                        # IMPROVEMENT 11: Enhanced relevance scoring
                        # Apply file type multiplier
                        file_type_multiplier = 2.0 if is_code_file else 0.5

                        # IMPROVEMENT 12: Python files get higher weight for Python-like patterns
                        if ext.lower() == '.py' and ('def ' in keyword or '_' in keyword):
                            file_type_multiplier *= 1.5

                        # IMPROVEMENT 13: JS/TS files get higher weight for camelCase
                        if ext.lower() in {'.js', '.ts', '.jsx', '.tsx'} and re.search(r'[a-z][A-Z]', keyword):
                            file_type_multiplier *= 1.5

                        # Apply keyword weight
                        keyword_multiplier = keyword_weights.get(keyword, 1.0)

                        # Apply phrase multiplier (phrases are more specific)
                        phrase_multiplier = 3.0 if is_phrase else 1.0

                        # IMPROVEMENT 14: Function names get higher weight
                        # Give even higher weight to private methods (starting with underscore)
                        function_multiplier = 3.0 if is_potential_function and keyword.startswith('_') else (2.0 if is_potential_function else 1.0)

                        # Calculate weighted score
                        weighted_score = occurrences * file_type_multiplier * keyword_multiplier * phrase_multiplier * function_multiplier

                        # Add to results
                        results.append({
                            'file': rel_path,
                            'occurrences': occurrences,
                            'weighted_score': weighted_score,
                            'keyword': keyword,
                            'is_function': is_potential_function
                        })
                except (UnicodeDecodeError, IOError):
                    # Skip files that can't be read as text
                    continue

        # Update file scores
        for result in results:
            file_path = result['file']
            weighted_score = result['weighted_score']

            if file_path not in file_scores:
                file_scores[file_path] = 0
                file_match_details[file_path] = []

            # Increase score based on weighted score
            file_scores[file_path] += weighted_score

            # Save match details for logging
            file_match_details[file_path].append({
                'keyword': result['keyword'],
                'occurrences': result['occurrences'],
                'score': weighted_score,
                'is_function': result.get('is_function', False)
            })

            # Log high-scoring matches for debugging
            if io and weighted_score > 5.0:
                io.tool_output(f"High relevance match: '{result['keyword']}' found in {file_path} (score: {weighted_score:.2f})")

    # IMPROVEMENT 15: Enhanced proximity matching
    # Check for multi-keyword matches (higher relevance)
    for file_path in list(file_scores.keys()):
        # Skip files that don't exist anymore
        if not os.path.exists(os.path.join(root_dir, file_path)):
            continue

        try:
            with open(os.path.join(root_dir, file_path), 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # First check proximity between priority keywords (highest bonus)
            for i in range(len(priority_keywords)):
                for j in range(i+1, len(priority_keywords)):
                    # Skip if either keyword is a phrase (already handled)
                    if ' ' in priority_keywords[i] or ' ' in priority_keywords[j]:
                        continue

                    # IMPROVEMENT 16: Tighter proximity window (50 chars instead of 100)
                    # for more relevant matches
                    pattern = r'(?:\b' + re.escape(priority_keywords[i]) + r'\b.{0,50}\b' + re.escape(priority_keywords[j]) + r'\b)|(?:\b' + re.escape(priority_keywords[j]) + r'\b.{0,50}\b' + re.escape(priority_keywords[i]) + r'\b)'
                    proximity_matches = len(re.findall(pattern, content, re.IGNORECASE | re.DOTALL))

                    if proximity_matches > 0:
                        # IMPROVEMENT 17: Higher proximity bonus for function names
                        # Check if either keyword is a potential function name
                        is_function_proximity = (priority_keywords[i] in potential_function_names or
                                                priority_keywords[j] in potential_function_names)

                        # Higher bonus for priority keywords appearing close to each other
                        proximity_bonus = proximity_matches * (15.0 if is_function_proximity else 10.0)
                        file_scores[file_path] += proximity_bonus
                        if io and proximity_bonus > 10.0:
                            io.tool_output(f"High proximity bonus: '{priority_keywords[i]}' near '{priority_keywords[j]}' in {file_path} (bonus: {proximity_bonus:.2f})")

            # Then check proximity between all keywords
            single_word_keywords = [k for k in all_keywords if ' ' not in k]
            for i in range(len(single_word_keywords)):
                for j in range(i+1, len(single_word_keywords)):
                    # Skip if we already checked this pair (both priority keywords)
                    if (single_word_keywords[i] in priority_keywords and
                        single_word_keywords[j] in priority_keywords):
                        continue

                    pattern = r'(?:\b' + re.escape(single_word_keywords[i]) + r'\b.{0,50}\b' + re.escape(single_word_keywords[j]) + r'\b)|(?:\b' + re.escape(single_word_keywords[j]) + r'\b.{0,50}\b' + re.escape(single_word_keywords[i]) + r'\b)'
                    proximity_matches = len(re.findall(pattern, content, re.IGNORECASE | re.DOTALL))

                    if proximity_matches > 0:
                        # Regular bonus for keywords appearing close to each other
                        proximity_bonus = proximity_matches * 5.0
                        file_scores[file_path] += proximity_bonus
                        if io and proximity_bonus > 10.0:
                            io.tool_output(f"Proximity bonus: '{single_word_keywords[i]}' near '{single_word_keywords[j]}' in {file_path} (bonus: {proximity_bonus:.2f})")
        except (UnicodeDecodeError, IOError):
            continue

    # IMPROVEMENT 18: Apply special bonus for files that match function patterns
    # This gives an extra boost to files that contain actual function definitions
    for file_path in list(file_scores.keys()):
        match_details = file_match_details.get(file_path, [])
        function_matches = [m for m in match_details if m.get('is_function', False)]

        if function_matches:
            # Apply a multiplier based on number of function matches
            # Safely access the 'score' key with a default value of 0
            function_bonus = sum(m.get('score', 0) for m in function_matches) * 0.5
            file_scores[file_path] += function_bonus

            if io and function_bonus > 5.0:
                io.tool_output(f"Function match bonus: {file_path} (bonus: {function_bonus:.2f})")

    # Sort files by relevance score
    sorted_files = sorted(file_scores.items(), key=lambda x: x[1], reverse=True)

    # Enhanced debugging for threshold calculation
    if io:
        io.tool_output(f"Total files with matches: {len(sorted_files)}")
        if sorted_files:
            io.tool_output(f"Top 5 files by score:")
            for i, (file, score) in enumerate(sorted_files[:5]):
                io.tool_output(f"  {i+1}. {file}: {score:.2f}")
        else:
            io.tool_output("No files matched any keywords")

    # IMPROVEMENT 19: Improved threshold calculation
    # Define minimum relevance score threshold
    # Use a dynamic threshold based on the highest score
    if sorted_files:
        highest_score = sorted_files[0][1]

        # IMPROVEMENT 20: More nuanced thresholding logic
        if highest_score > 50.0:
            # For very high scores, use a percentage of the highest
            min_score_threshold = max(highest_score * 0.05, 0.5)  # Lower percentage (5% instead of 10%)
        elif highest_score > 20.0:
            # For moderately high scores
            min_score_threshold = max(highest_score * 0.1, 0.5)
        else:
            # For lower scores, use an even lower threshold
            min_score_threshold = 0.3  # Lowered from 0.5 to catch more results
    else:
        min_score_threshold = 0.3  # Lower default threshold

    # If we have AI-provided keywords or potential function names, use an even lower threshold
    if (('priority_keywords' in locals() and priority_keywords and len(priority_keywords) > 0) or
        potential_function_names):
        # Lower threshold for explicit AI-provided keywords or function names
        min_score_threshold = min(min_score_threshold, 0.3)  # Lowered from 0.5

    if io:
        io.tool_output(f"Using minimum score threshold: {min_score_threshold:.2f}")

    # Return top results that meet the minimum threshold
    top_results = []
    filtered_files = []

    # Enhanced debugging for file filtering
    if io:
        io.tool_output(f"Filtering files based on threshold {min_score_threshold:.2f}:")

    for file_path, score in sorted_files[:20]:  # Consider top 20 results (increased from 15)
        # Get file extension
        _, ext = os.path.splitext(file_path)

        # Log file being considered
        if io:
            io.tool_output(f"  Considering file: {file_path} (score: {score:.2f})")

        if score < min_score_threshold:
            if io:
                io.tool_output(f"    FILTERED: Score {score:.2f} below threshold {min_score_threshold:.2f}")
            filtered_files.append((file_path, f"Score {score:.2f} below threshold"))
            continue

        # Skip database files and other non-code files
        if ext.lower() in {'.db', '.sqlite', '.sqlite3', '.log', '.txt'}:
            if io:
                io.tool_output(f"    FILTERED: File type {ext} excluded")
            filtered_files.append((file_path, f"File type {ext} excluded"))
            continue

        # Prioritize code files
        is_code_file = ext.lower() in code_file_extensions
        if io and is_code_file:
            io.tool_output(f"    INCLUDED: Is a code file with extension {ext}")
        elif io:
            io.tool_output(f"    INCLUDED: Non-code file with extension {ext}")

        # IMPROVEMENT 21: Include match details for better result output
        matches_info = file_match_details.get(file_path, [])
        # Safely extract keywords, ensuring the 'keyword' key exists
        function_matches = [m.get('keyword', '') for m in matches_info if m.get('is_function', False) and 'keyword' in m]
        other_matches = [m.get('keyword', '') for m in matches_info if not m.get('is_function', False)
                        and 'keyword' in m and m.get('keyword', '') not in function_matches]

        top_results.append({
            'file': file_path,
            'relevance_score': score,
            'is_code_file': is_code_file,
            'function_matches': function_matches,
            'other_matches': other_matches
        })

    # IMPROVEMENT 22: Sort results to prioritize code files and function matches
    top_results.sort(key=lambda x: (
        not x['is_code_file'],  # Code files first
        not bool(x['function_matches']),  # Files with function matches first
        -x['relevance_score']  # Then by score
    ))

    # Limit to top 10 results
    top_results = top_results[:10]

    if io:
        if top_results:
            io.tool_output(f"Found {len(top_results)} relevant files with scores above threshold {min_score_threshold:.2f}:")
            for result in top_results:
                file_type = "code file" if result['is_code_file'] else "non-code file"
                match_info = ""
                if result['function_matches']:
                    match_info += f" | Function matches: {', '.join(result['function_matches'])}"
                if result['other_matches']:
                    other_matches_str = ', '.join(result['other_matches'][:3])
                    if len(result['other_matches']) > 3:
                        other_matches_str += f" + {len(result['other_matches']) - 3} more"
                    match_info += f" | Other matches: {other_matches_str}"

                io.tool_output(f"  - {result['file']} (score: {result['relevance_score']:.2f}, {file_type}{match_info})")
        else:
            io.tool_warning(f"No files found with relevance score above threshold {min_score_threshold:.2f}")

            # Show information about filtered files to help diagnose issues
            if filtered_files:
                io.tool_output("\nFiles that were filtered out:")
                for file_path, reason in filtered_files[:5]:  # Show up to 5 filtered files
                    io.tool_output(f"  - {file_path} - {reason}")

            # Provide suggestions for improving search
            io.tool_output("\nSuggestions for improving search:")
            io.tool_output("1. Check if keywords contain exact function/class names")
            io.tool_output("2. Try using more specific technical terms")
            io.tool_output("3. Avoid using double quotes around individual keywords")
            io.tool_output("4. Include domain-specific terminology")
            io.tool_output("5. Try both formats: comma-separated string or array of strings")

        # Explain the search process
        io.tool_output("\nSearch process:")
        if potential_function_names:
            io.tool_output(f"- Potential function names: {', '.join(potential_function_names)}")
        if 'priority_keywords' in locals() and priority_keywords:
            io.tool_output(f"- Priority keywords: {', '.join(priority_keywords)}")
        if 'keywords' in locals() and keywords:
            io.tool_output(f"- Regular keywords: {', '.join(keywords)}")
        io.tool_output(f"- Dynamic threshold: {min_score_threshold:.2f}")
        if top_results:
            io.tool_output("- Files were ranked based on keyword matches, proximity, and file type")
        else:
            io.tool_output("- No files met the relevance criteria")

    # IMPROVEMENT 23: Clean up result objects before returning
    # Remove the extra fields we added for internal use
    for result in top_results:
        result.pop('function_matches', None)
        result.pop('other_matches', None)

    return top_results

def extract_function_names(text: str) -> List[str]:
    """
    Extract potential function names from text.

    Args:
        text: Text to extract function names from

    Returns:
        List of potential function names
    """
    # Common patterns for function names
    patterns = [
        r'\b(\w+)\s*\(',                      # function_name(
        r'\b(_\w+)\s*\(',                     # _private_function_name(
        r'\b(__\w+)\s*\(',                    # __private_function_name(
        r'function\s+[\'"]?(\w+)[\'"]?',      # function 'function_name'
        r'method\s+[\'"]?(\w+)[\'"]?',        # method 'method_name'
        r'function\s+[\'"]?(_\w+)[\'"]?',     # function '_private_function_name'
        r'method\s+[\'"]?(_\w+)[\'"]?',       # method '_private_method_name'
        r'function\s+called\s+[\'"]?(\w+)[\'"]?',  # function called 'function_name'
        r'function\s+called\s+[\'"]?(_\w+)[\'"]?', # function called '_private_function_name'
        r'(\w+)\s+function',                  # function_name function
        r'(_\w+)\s+function',                 # _private_function_name function
        r'(\w+)\s+method',                    # method_name method
        r'(_\w+)\s+method',                   # _private_method_name method
    ]

    # Extract all potential function names
    function_names = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        function_names.extend([m for m in matches if m])

    # Filter out common words and programming keywords
    common_words = {'if', 'else', 'for', 'while', 'return', 'import', 'from', 'class', 'def', 'print',
                   'int', 'str', 'list', 'dict', 'set', 'tuple', 'function', 'method', 'called'}

    filtered_names = []
    for name in function_names:
        # Handle tuple results from regex groups
        if isinstance(name, tuple):
            for n in name:
                if n and n.lower() not in common_words and len(n) > 2:
                    filtered_names.append(n)
        elif name and name.lower() not in common_words and len(name) > 2:
            filtered_names.append(name)

    # Remove duplicates while preserving order
    seen = set()
    unique_names = []
    for name in filtered_names:
        if name not in seen:
            seen.add(name)
            unique_names.append(name)

    return unique_names


def extract_keywords_from_query(query: str) -> List[str]:
    """
    Extract keywords from a search query for use with Smart Map Request System.

    Args:
        query: The search query

    Returns:
        List of keywords extracted from the query
    """
    # Check if the query is comma-separated keywords
    if "," in query and not query.startswith("{") and not query.endswith("}"):
        # Split by commas and clean up
        keywords = [k.strip().replace('"', '').replace("'", '') for k in query.split(",") if k.strip()]
        return keywords

    # Extract potential function names
    function_names = extract_function_names(query)

    # Extract general keywords
    words = re.findall(r'\b\w+\b', query.lower())

    # Filter out common stopwords
    stopwords = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
        'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
        'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
    }

    keywords = []

    # Add function names first (highest priority)
    keywords.extend(function_names)

    # Add other meaningful words
    for word in words:
        if (word not in stopwords and
            len(word) > 2 and
            word not in keywords):
            keywords.append(word)

    # If no keywords found, return the original query as a single keyword
    if not keywords:
        keywords = [query.strip()]

    return keywords