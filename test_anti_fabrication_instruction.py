#!/usr/bin/env python3
"""
Test the anti-fabrication instruction in Smart Map Request responses.
"""

import sys
import os

def test_smart_map_response_contains_instruction():
    """Test that Smart Map Request responses contain anti-fabrication instructions."""
    print("🧪 Testing Anti-Fabrication Instruction in Smart Map Response")
    print("=" * 80)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )

        # Create handler
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )

        print("✅ SmartMapRequestHandler created successfully")

        # Test map request for position calculator
        test_request = {
            "keywords": ["position", "quantity", "calculator"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        print(f"\n🔍 Testing MAP_REQUEST: {test_request}")

        # Get the response
        response = handler.handle_map_request(test_request)

        print(f"\n📋 Response length: {len(response)} characters")

        # Check for anti-fabrication instruction (different phrases for different response types)
        critical_phrases = [
            "CRITICAL INSTRUCTION",
            # For normal responses:
            "DO NOT analyze or explain functionality based on this map alone",
            "You MUST use CONTEXT_REQUEST",
            "get the actual code implementation",
            "before providing any analysis",
            # For "No Results Found" responses:
            "DO NOT fabricate or guess about code functionality",
            "use CONTEXT_REQUEST with different keywords"
        ]

        print(f"\n🔍 Checking for anti-fabrication instructions:")

        found_phrases = []
        for phrase in critical_phrases:
            if phrase in response:
                print(f"✅ Found: '{phrase}'")
                found_phrases.append(phrase)
            else:
                print(f"❌ Missing: '{phrase}'")

        # Success if we found the critical instruction and at least one specific anti-fabrication phrase
        has_critical = "CRITICAL INSTRUCTION" in found_phrases
        has_anti_fabrication = any(phrase for phrase in found_phrases if "DO NOT" in phrase)

        if has_critical and has_anti_fabrication:
            print(f"\n🎉 SUCCESS: Found critical anti-fabrication instructions!")
            print(f"   Found {len(found_phrases)} out of {len(critical_phrases)} phrases")

            # Show a snippet of the response
            print(f"\n📄 Response Preview:")
            print("=" * 60)
            lines = response.split('\n')
            for i, line in enumerate(lines[:15]):  # Show first 15 lines
                print(f"{i+1:2d}: {line}")
            if len(lines) > 15:
                print(f"... and {len(lines) - 15} more lines")
            print("=" * 60)

            return True
        else:
            print(f"\n❌ FAILURE: Missing essential anti-fabrication instructions!")
            print(f"   Has CRITICAL INSTRUCTION: {has_critical}")
            print(f"   Has anti-fabrication phrase: {has_anti_fabrication}")
            return False

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_response_contains_instruction():
    """Test that fallback responses also contain anti-fabrication instructions."""
    print("\n🧪 Testing Anti-Fabrication Instruction in Fallback Response")
    print("=" * 80)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )

        # Create handler
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir="aider-main",
            io=io
        )

        # Test with keywords that should return results but might trigger fallback
        test_request = {
            "keywords": ["nonexistent", "fake", "imaginary"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        print(f"🔍 Testing MAP_REQUEST with unlikely keywords: {test_request}")

        # Get the response
        response = handler.handle_map_request(test_request)

        print(f"\n📋 Response length: {len(response)} characters")

        # Check if it's a "no results" response
        if "No Results Found" in response or "No files found" in response:
            print("✅ Got 'No Results Found' response as expected")
            return True

        # If we got results, check for anti-fabrication instruction
        if "CRITICAL INSTRUCTION" in response:
            print("✅ Found anti-fabrication instruction in response")
            return True
        else:
            print("❌ Missing anti-fabrication instruction")
            print(f"\nResponse preview:\n{response[:500]}...")
            return False

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run all anti-fabrication instruction tests."""
    print("🚀 Testing Anti-Fabrication Instructions in Smart Map Responses")
    print("=" * 100)

    tests = [
        ("Smart Map Response Contains Instruction", test_smart_map_response_contains_instruction),
        ("Fallback Response Contains Instruction", test_fallback_response_contains_instruction),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 100)
    print("📊 ANTI-FABRICATION INSTRUCTION TESTING SUMMARY")
    print("=" * 100)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n📋 Anti-Fabrication Instructions Status:")
        print("  ✅ Smart Map Request responses contain critical instructions")
        print("  ✅ LLM will be forced to request actual code content")
        print("  ✅ No more analysis based on repository map alone")
        print("  ✅ CONTEXT_REQUEST will be required before analysis")
        print("\n🎯 Expected LLM Behavior:")
        print("  1. ✅ Receive MAP_REQUEST response with warning")
        print("  2. ✅ Read the critical instruction")
        print("  3. ✅ Identify specific symbols from the map")
        print("  4. ✅ Make CONTEXT_REQUEST for actual code")
        print("  5. ✅ Analyze based on real implementation")
    else:
        print("❌ SOME TESTS FAILED!")
        print("   The anti-fabrication instructions may not be working properly")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
