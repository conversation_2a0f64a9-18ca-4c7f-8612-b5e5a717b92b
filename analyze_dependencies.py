#!/usr/bin/env python

import os
import sys
from collections import defaultdict, Counter

def load_dependencies(file_path):
    """Load the dependencies from the generated file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    dependencies = {}
    current_file = None

    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue

        if line.startswith('##'):
            # New file section
            current_file = line[3:].split(' depends on:')[0].strip()
            dependencies[current_file] = []
            print(f"Found file: {current_file}")
        elif line.startswith('- '):
            # Dependency line
            if current_file:
                # Extract the file and reference count
                parts = line[2:].split(' (')
                if len(parts) == 2:
                    dep_file = parts[0].strip()
                    ref_count_str = parts[1].split(' references')[0].strip()
                    try:
                        ref_count = int(ref_count_str)
                        dependencies[current_file].append((dep_file, ref_count))
                    except ValueError:
                        print(f"Error parsing reference count: {ref_count_str}")

    print(f"Parsed {len(dependencies)} files with dependencies")
    return dependencies

def identify_strong_dependencies(dependencies, min_references=3):
    """Identify files with strong dependencies (multiple references)."""
    strong_deps = {}

    for file, deps in dependencies.items():
        strong_deps[file] = [dep for dep in deps if dep[1] >= min_references]

    return strong_deps

def identify_central_files(dependencies):
    """Identify central files that are referenced by many other files."""
    # Count how many times each file is referenced
    reference_counts = Counter()

    for file, deps in dependencies.items():
        for dep_file, _ in deps:
            reference_counts[dep_file] += 1

    # Sort by reference count
    central_files = sorted(reference_counts.items(), key=lambda x: x[1], reverse=True)

    return central_files

def analyze_class_dependencies(dependencies):
    """Analyze dependencies specifically for classes and their related files."""
    class_dependencies = {}

    # Look for files that might contain class definitions
    for file, deps in dependencies.items():
        # Simple heuristic: files with "coder" in the name likely contain classes
        if "coder" in file.lower() or "prompts" in file.lower():
            class_dependencies[file] = deps

    return class_dependencies

def main():
    # Load dependencies
    deps_file = "aider_repo_map_output/dependencies.txt"
    if not os.path.exists(deps_file):
        print(f"Error: {deps_file} not found. Run generate_repo_map.py first.")
        return

    print("Loading dependencies...")
    dependencies = load_dependencies(deps_file)
    print(f"Loaded {len(dependencies)} files with dependencies")

    print("Identifying strong dependencies...")
    strong_deps = identify_strong_dependencies(dependencies, min_references=3)
    print(f"Found {sum(1 for deps in strong_deps.values() if deps)} files with strong dependencies")

    print("Identifying central files...")
    central_files = identify_central_files(dependencies)
    print(f"Found {len(central_files)} central files")

    print("Analyzing class dependencies...")
    class_deps = analyze_class_dependencies(dependencies)
    print(f"Found {len(class_deps)} class-related files")

    # Create output directory
    output_dir = "aider_repo_map_output"
    os.makedirs(output_dir, exist_ok=True)

    print("Saving analysis results...")
    # Save the analysis results
    with open(os.path.join(output_dir, "dependency_analysis.txt"), "w", encoding="utf-8") as f:
        f.write("# Dependency Analysis\n\n")

        f.write("## Top Central Files\n")
        f.write("These files are referenced by many other files and are central to the codebase:\n\n")
        for file, count in central_files[:20]:  # Top 20
            f.write(f"- {file}: referenced by {count} files\n")

        f.write("\n## Strong Dependencies\n")
        f.write("These are files with strong dependencies (3+ references) to other files:\n\n")
        for file, deps in strong_deps.items():
            if deps:
                f.write(f"### {file} strongly depends on:\n")
                for dep_file, ref_count in sorted(deps, key=lambda x: x[1], reverse=True):
                    f.write(f"- {dep_file} ({ref_count} references)\n")
                f.write("\n")

        f.write("\n## Class Dependencies\n")
        f.write("These are dependencies specifically for files likely containing classes:\n\n")
        for file, deps in class_deps.items():
            if deps:
                f.write(f"### {file} depends on:\n")
                for dep_file, ref_count in sorted(deps, key=lambda x: x[1], reverse=True)[:10]:  # Limit to top 10
                    f.write(f"- {dep_file} ({ref_count} references)\n")
                f.write("\n")

    print(f"Analysis complete. Results saved to {os.path.join(output_dir, 'dependency_analysis.txt')}")

if __name__ == "__main__":
    main()
