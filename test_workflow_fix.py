#!/usr/bin/env python3
"""
Test the workflow fix to ensure LLM follows proper sequence.
"""

import os
import sys
import subprocess
import tempfile

def test_workflow_enforcement():
    """Test that the LLM follows proper workflow sequence."""
    print("🔍 Testing Workflow Enforcement Fix")
    print("=" * 80)
    
    # Create a temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Test with a simple query about position calculation
        cmd = [
            sys.executable, "-m", "aider.main",
            "--model", "ollama_chat/qwen3:1.7b",
            "--message", "How does the _calculate_position_quantity function work?",
            r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        ]
        
        print(f"🚀 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        print(f"📊 Command exit code: {result.returncode}")
        print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        
        # Check for proper workflow in the output
        output = result.stdout + result.stderr
        
        # Look for signs of proper workflow
        has_map_request = "MAP_REQUEST" in output
        has_context_request = "CONTEXT_REQUEST" in output
        has_simultaneous_requests = "{MAP_REQUEST:" in output and "{CONTEXT_REQUEST:" in output and output.find("{MAP_REQUEST:") > output.find("{CONTEXT_REQUEST:")
        
        print(f"\n📊 Workflow Analysis:")
        print(f"   Has MAP_REQUEST: {has_map_request}")
        print(f"   Has CONTEXT_REQUEST: {has_context_request}")
        print(f"   Simultaneous requests detected: {has_simultaneous_requests}")
        
        # Check for anti-fabrication compliance
        fabrication_keywords = [
            "the function works by",
            "the function calculates",
            "it takes parameters",
            "it returns"
        ]
        
        has_fabrication = any(keyword.lower() in output.lower() for keyword in fabrication_keywords)
        has_proper_response = "I need to retrieve the actual implementation" in output or "I need to see the actual code" in output
        
        print(f"   Fabrication detected: {has_fabrication}")
        print(f"   Proper response: {has_proper_response}")
        
        # Determine success
        workflow_success = has_map_request and not has_simultaneous_requests
        anti_fabrication_success = not has_fabrication or has_proper_response
        
        overall_success = workflow_success and anti_fabrication_success
        
        print(f"\n📊 Test Results:")
        print(f"   Workflow compliance: {'✅ PASS' if workflow_success else '❌ FAIL'}")
        print(f"   Anti-fabrication compliance: {'✅ PASS' if anti_fabrication_success else '❌ FAIL'}")
        print(f"   Overall: {'✅ PASS' if overall_success else '❌ FAIL'}")
        
        return overall_success
        
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 2 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)
        
        # Clean up temporary file
        try:
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
        except Exception as e:
            print(f"⚠️  Could not clean up temporary file: {e}")

def main():
    """Run workflow enforcement tests."""
    print("🚀 Testing Workflow Enforcement Fix")
    print("=" * 100)
    
    success = test_workflow_enforcement()
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 WORKFLOW FIX TESTING SUMMARY")
    print("=" * 100)
    
    if success:
        print("🎉 WORKFLOW FIX SUCCESSFUL!")
        print("   ✅ LLM follows proper sequential workflow")
        print("   ✅ No simultaneous requests detected")
        print("   ✅ Anti-fabrication rules enforced")
    else:
        print("❌ WORKFLOW FIX FAILED!")
        print("   The LLM is still violating workflow rules")
        print("   Additional enforcement may be needed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
