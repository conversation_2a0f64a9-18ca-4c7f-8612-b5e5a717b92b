graph TB
    %% System Overview - Top Level Architecture

    subgraph scripts[scripts]
        homepage["homepage<br/>(115 entities)"]
        style homepage fill:#ff6b6b,stroke:#d63031,color:#fff
        issues["issues<br/>(107 entities)"]
        style issues fill:#ff6b6b,stroke:#d63031,color:#fff
        recording_audio["recording_audio<br/>(61 entities)"]
        style recording_audio fill:#ff6b6b,stroke:#d63031,color:#fff
        blame["blame<br/>(60 entities)"]
        style blame fill:#ff6b6b,stroke:#d63031,color:#fff
        M_30k-image["30k-image<br/>(49 entities)"]
        style M_30k-image fill:#feca57,stroke:#ff9ff3,color:#000
    end

    subgraph aider[aider]
        base_coder["base_coder<br/>(673 entities)"]
        style base_coder fill:#ff6b6b,stroke:#d63031,color:#fff
        base_coder_old["base_coder_old<br/>(607 entities)"]
        style base_coder_old fill:#ff6b6b,stroke:#d63031,color:#fff
        commands["commands<br/>(344 entities)"]
        style commands fill:#ff6b6b,stroke:#d63031,color:#fff
        repomap["repomap<br/>(247 entities)"]
        style repomap fill:#ff6b6b,stroke:#d63031,color:#fff
        io["io<br/>(210 entities)"]
        style io fill:#ff6b6b,stroke:#d63031,color:#fff
    end

    subgraph root[root]
        aider_integration_service["aider_integration_service<br/>(337 entities)"]
        style aider_integration_service fill:#ff6b6b,stroke:#d63031,color:#fff
        surgical_context_extractor["surgical_context_extractor<br/>(232 entities)"]
        style surgical_context_extractor fill:#ff6b6b,stroke:#d63031,color:#fff
        test_llm_message_inspection["test_llm_message_inspection<br/>(61 entities)"]
        style test_llm_message_inspection fill:#ff6b6b,stroke:#d63031,color:#fff
        simulate_smart_map_request["simulate_smart_map_request<br/>(54 entities)"]
        style simulate_smart_map_request fill:#ff6b6b,stroke:#d63031,color:#fff
        context_request_handler["context_request_handler<br/>(49 entities)"]
        style context_request_handler fill:#feca57,stroke:#ff9ff3,color:#000
    end

    subgraph tests[tests]
        test_commands["test_commands<br/>(465 entities)"]
        style test_commands fill:#ff6b6b,stroke:#d63031,color:#fff
        test_coder["test_coder<br/>(263 entities)"]
        style test_coder fill:#ff6b6b,stroke:#d63031,color:#fff
        test_main["test_main<br/>(206 entities)"]
        style test_main fill:#ff6b6b,stroke:#d63031,color:#fff
        test_repo["test_repo<br/>(143 entities)"]
        style test_repo fill:#ff6b6b,stroke:#d63031,color:#fff
        test_editblock["test_editblock<br/>(129 entities)"]
        style test_editblock fill:#ff6b6b,stroke:#d63031,color:#fff
    end

    subgraph benchmark[benchmark]
        benchmark["benchmark<br/>(195 entities)"]
        style benchmark fill:#ff6b6b,stroke:#d63031,color:#fff
        problem_stats["problem_stats<br/>(79 entities)"]
        style problem_stats fill:#ff6b6b,stroke:#d63031,color:#fff
        plots["plots<br/>(73 entities)"]
        style plots fill:#ff6b6b,stroke:#d63031,color:#fff
        refactor_tools["refactor_tools<br/>(38 entities)"]
        style refactor_tools fill:#feca57,stroke:#ff9ff3,color:#000
        swe_bench["swe_bench<br/>(33 entities)"]
        style swe_bench fill:#feca57,stroke:#ff9ff3,color:#000
    end

    subgraph mid_level_ir[mid_level_ir]
        entity_extractor["entity_extractor<br/>(48 entities)"]
        style entity_extractor fill:#feca57,stroke:#ff9ff3,color:#000
        criticality_scorer["criticality_scorer<br/>(42 entities)"]
        style criticality_scorer fill:#feca57,stroke:#ff9ff3,color:#000
        ir_builder["ir_builder<br/>(42 entities)"]
        style ir_builder fill:#feca57,stroke:#ff9ff3,color:#000
        ir_context["ir_context<br/>(31 entities)"]
        style ir_context fill:#feca57,stroke:#ff9ff3,color:#000
        main["main<br/>(31 entities)"]
        style main fill:#feca57,stroke:#ff9ff3,color:#000
    end

    %% Group Dependencies
    scripts --> aider
    aider --> root
    aider --> mid_level_ir
    root --> mid_level_ir
    root --> aider
    benchmark --> aider
    tests --> aider