│        self.symbol = symbol
│        self.price_key = price_key
│        self.intervals = intervals
│        self.data_by_interval = {}
│
│        # Use the appropriate database based on mode
│        mode_manager = ModeManager()
│        if mode_manager.is_backtest_mode():
│            db_path = mode_manager.get_backtest_db_path()
⋮
│    def calculate_trend_and_indicators(self, data: pd.DataFrame) -> dict:
⋮

models\__init__.py:
⋮
│Base = declarative_base()
│engine = create_engine('sqlite:///database/manage_positions.db')
│Session = sessionmaker(bind=engine)
│
⋮

----------------------------------------
│        self.target_cache = {}
│
│        class MinimalPriceManager:
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
⋮
│    async def initialize_symbols(self, symbols_config: Dict):
⋮
│    async def process_symbol(self, symbol: str, symbols_config: Dict, current_time: Optional[dateti
⋮
│    async def get_targets(self, symbol: str, force_recalculate: bool = False) -> Optional[Dict[str,
⋮

services\technical_analysis_service.py:
⋮
│class TechnicalAnalysisService:
│    """
│    Service for calculating technical indicators that combine data from
│    both candle data and target data sources.
⋮
│    async def initialize_symbol(self, symbol: str) -> None:
----------------------------------------
⋮
│    def log_trade_exit(self,
│                       trade_id: str,
│                       exit_price: float,
│                       pnl: Optional[float] = None,
│                       pnl_percentage: Optional[float] = None,
│                       pnl_pips: Optional[float] = None,
⋮
│    def get_trade_by_id(self, trade_id: str) -> Optional[TradeLog]:
⋮
│    def calculate_pnl(self, trade_id: str, exit_price: float) -> Dict:
⋮
│    def get_performance_stats(self, symbol: Optional[str] = None,
│                             start_date: Optional[datetime] = None,
⋮

strategy\base.py:
⋮
│@dataclass
│class TradeDetails:
⋮
----------------------------------------
│def update_scheduling():
⋮

utils\backtest_results copy.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮

utils\backtest_results.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
----------------------------------------
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮

utils\backtest_results.py:
⋮
│class BacktestResultsCollector:
│    """
│    Collects and analyzes backtest results.
│    Provides performance metrics and visualization.
⋮
│    def calculate_metrics(self):
⋮
│    def generate_report(self, output_dir: str = 'backtest_results'):
⋮

utils\clock.py:
⋮
│class TradingClock:
│    """
│    Provides a consistent time interface for both live and backtest modes.
│    In live mode, returns the actual system time.
----------------------------------------
⋮
│    async def notify_failure(self, app, message):
⋮

utils\performance_analyzer.py:
⋮
│class PerformanceAnalyzer:
│    """
│    Advanced performance metrics calculator for trading strategies
⋮
│    def calculate_metrics_from_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Union[str, f
⋮
│    def calculate_metrics_from_db(self, start_date: datetime, end_date: datetime) -> Dict[str, Unio
⋮

utils\progress.py:
⋮
│class ProgressIndicator:
⋮

utils\terminal_logger.py:
----------------------------------------
⋮

utils\performance_analyzer.py:
⋮
│class PerformanceAnalyzer:
│    """
│    Advanced performance metrics calculator for trading strategies
⋮
│    def calculate_metrics_from_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Union[str, f
⋮
│    def calculate_metrics_from_db(self, start_date: datetime, end_date: datetime) -> Dict[str, Unio
⋮

utils\progress.py:
⋮
│class ProgressIndicator:
⋮

utils\terminal_logger.py:
⋮
│class TerminalLogger:
----------------------------------------