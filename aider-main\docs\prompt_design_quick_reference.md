# Aider Prompt Design Quick Reference

## Key Prompt Components

| Component | Description | Location |
|-----------|-------------|----------|
| `main_system` | Primary system prompt | Each coder class |
| `system_reminder` | Additional instructions at end of system prompt | Each coder class |
| `file_access_reminder` | Critical instructions about file access | `base_prompts.py` |
| `file_request_instructions` | Detailed instructions on requesting files | `base_prompts.py` |
| `repo_content_prefix` | Instructions shown with repository info | `base_prompts.py` |
| `example_messages` | Example conversations | Each coder class |

## File Request Format

```
{REQUEST_FILE: path/to/file.py}
```

```
{REQUEST_FILES:
- path/to/file1.py
- path/to/file2.py
}
```

## Critical Instructions Checklist

- [ ] Zero files at start: "YOU DO NOT HAVE ANY FILES LOADED AT THE START OF A CONVERSATION"
- [ ] Request before answering: "ALWAYS request files BEFORE answering questions"
- [ ] No assumptions: "NEVER assume you have access to any file unless requested"
- [ ] Clear request format: "Request files using {REQUEST_FILE: path}"
- [ ] Error on the side of requesting: "ALWAYS err on the side of requesting more context"

## Prompt Formatting Best Practices

1. **Use ALL CAPS for critical instructions**
2. **Number sequential instructions**
3. **Use short, direct sentences**
4. **Place most important instructions first**
5. **Use consistent terminology**
6. **Include explicit examples**

## Common Prompt Patterns

### 1. Critical Workflow Instructions

```
CRITICAL WORKFLOW INSTRUCTIONS:
1. YOU DO NOT HAVE ANY FILES LOADED AT THE START OF A CONVERSATION. You must explicitly request them.
2. ALWAYS START by requesting relevant files BEFORE attempting to answer ANY code-related question.
3. NEVER assume you already have access to any file unless you've explicitly requested it and seen its contents.
```

### 2. Request Format Instructions

```
When you need to see the complete content of a file, request it using:

{REQUEST_FILE: exact_file_path}

For example:
{REQUEST_FILE: src/utils/calculator.py}
```

### 3. Error Prevention Instructions

```
IMPORTANT: This is the ONLY way to add files to the conversation. Simply mentioning file names in your response will NOT add them to the conversation.

REMEMBER: At the beginning of each conversation, you have ZERO files loaded. You must explicitly request any file you need to see.
```

### 4. Acknowledgment Response

```
I understand that I have ZERO files loaded at the start of this conversation. I must explicitly request any file I need using the {REQUEST_FILE: path} format. I will ALWAYS request relevant files BEFORE attempting to answer any code-related questions.
```

## Modifying Prompts: Quick Guide

### Adding to All System Prompts

Modify `file_access_reminder` in `base_prompts.py`:

```python
file_access_reminder = """
CRITICAL: You start with ZERO files loaded. You must explicitly request any file you need using {REQUEST_FILE: path}.
NEVER assume you have access to any file unless you've explicitly requested it and seen its contents.
NEVER provide information about file contents you haven't seen.
YOUR NEW INSTRUCTION HERE.
"""
```

### Modifying File Request Instructions

Update `file_request_instructions` in `base_prompts.py`:

```python
file_request_instructions = """
You have access to a repository map that shows all files in the codebase and their structure.

CRITICAL WORKFLOW INSTRUCTIONS:
1. YOUR MODIFIED INSTRUCTION HERE.
2. ALWAYS START by requesting relevant files BEFORE attempting to answer ANY code-related question.
...
"""
```

### Creating a New Coder Class with Custom Prompts

```python
from .base_prompts import CoderPrompts

class MyCustomPrompts(CoderPrompts):
    main_system = """Act as an expert software developer with YOUR CUSTOM FOCUS.
    {final_reminders}
    YOUR CUSTOM INSTRUCTIONS HERE.
    ...
    """
    
    system_reminder = """
    YOUR CUSTOM REMINDERS HERE.
    """
    
    # Override other prompt components as needed
```

## Testing Your Prompts

1. **Start with a simple question** about a file not yet in the conversation
2. **Verify the AI requests the file** before attempting to answer
3. **Check the request format** is correct
4. **Confirm the AI doesn't make assumptions** about files it hasn't seen
5. **Test with ambiguous questions** to see if the AI identifies and requests relevant files
6. **Test with follow-up questions** about previously requested files

## Common Issues and Quick Fixes

| Issue | Quick Fix |
|-------|-----------|
| AI makes assumptions | Add "NEVER provide ANY information about file contents you haven't seen" |
| AI doesn't request files first | Add "WORKFLOW ORDER: 1) Identify needed files, 2) Request ALL needed files, 3) ONLY THEN analyze and respond" |
| AI requests unnecessary files | Add "Request ONLY the files you need to answer the specific question" |
| AI forgets it has requested files | Add "Keep track of which files you've already requested and received" |

## Remember

- The prompt system has multiple layers - changes in base classes affect all derived classes
- Test your prompts thoroughly with a variety of inputs
- Balance detail and brevity - focus on critical instructions
- Use consistent terminology throughout your prompts
- Place the most important instructions at the beginning
