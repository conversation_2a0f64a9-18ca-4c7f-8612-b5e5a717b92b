#!/usr/bin/env python

import os
import json
from pprint import pprint

from aider_integration_service import AiderIntegrationService

def format_snippet(snippet, max_lines=10):
    """Format a code snippet for display with line numbers."""
    lines = snippet.split('\n')
    if len(lines) > max_lines:
        # Show first few lines and last few lines
        first_half = lines[:max_lines//2]
        last_half = lines[-(max_lines//2):]
        formatted_lines = [f"{i+1}: {line}" for i, line in enumerate(first_half)]
        formatted_lines.append("...")
        formatted_lines.extend([f"{len(lines)-(len(last_half)-i)+1}: {line}" for i, line in enumerate(last_half)])
    else:
        formatted_lines = [f"{i+1}: {line}" for i, line in enumerate(lines)]

    return '\n'.join(formatted_lines)

def demo_query(service, project_path, query):
    """Demonstrate surgical context extraction for a query."""
    print(f"\n=== SURGICAL CONTEXT EXTRACTION DEMO ===")
    print(f"Query: '{query}'\n")

    # Determine relevant files based on the query
    relevant_files = []

    # Simple keyword matching for demo purposes
    if "surgical" in query.lower() or "context" in query.lower() or "extract" in query.lower():
        relevant_files.append("surgical_context_extractor.py")
    if "aider" in query.lower() or "integration" in query.lower() or "service" in query.lower():
        relevant_files.append("aider_integration_service.py")
    if "snippet" in query.lower() or "code" in query.lower():
        relevant_files.append("surgical_context_extractor.py")

    if not relevant_files:
        # Default to a few interesting files
        relevant_files = [
            "surgical_context_extractor.py",
            "aider_integration_service.py"
        ]

    print(f"Identified relevant files: {relevant_files}\n")

    # Extract contextual dependencies for each file
    for file_path in relevant_files:
        print(f"\n--- Surgical Context Extraction for {file_path} ---")

        try:
            # Get contextual dependencies
            context_map = service.get_contextual_dependencies(project_path, file_path, max_snippets=4)

            print(f"Primary file: {context_map['primary_file']}")
            print(f"Related files: {', '.join(context_map['related_files'][:3]) if context_map['related_files'] else 'None'}")

            # Show usage contexts
            if context_map['usage_contexts']:
                print("\nUsage Contexts:")
                for i, usage_ctx in enumerate(context_map['usage_contexts'][:2]):
                    print(f"\n  {i+1}. {usage_ctx['symbol_name']} ({usage_ctx['usage_type']})")
                    print(f"     File: {usage_ctx['file_path']}")
                    print(f"     Lines: {usage_ctx['start_line']}-{usage_ctx['end_line']}")
                    print(f"     Surrounding function: {usage_ctx['surrounding_function'] or 'N/A'}")
                    print(f"     Snippet:\n{format_snippet(usage_ctx['content'])}")
            else:
                print("\nNo usage contexts found.")

            # Show definition contexts
            if context_map['definition_contexts']:
                print("\nDefinition Contexts:")
                for i, def_ctx in enumerate(context_map['definition_contexts'][:2]):
                    print(f"\n  {i+1}. {def_ctx['symbol_name']} ({def_ctx['definition_type']})")
                    print(f"     File: {def_ctx['file_path']}")
                    print(f"     Lines: {def_ctx['start_line']}-{def_ctx['end_line']}")
                    if def_ctx['signature']:
                        print(f"     Signature: {def_ctx['signature'][:100]}...")
                    print(f"     Snippet:\n{format_snippet(def_ctx['content'])}")
            else:
                print("\nNo definition contexts found.")

        except Exception as e:
            print(f"Error extracting contexts: {e}")

    # If query mentions inheritance, demonstrate inheritance context extraction
    if "inheritance" in query.lower() or "class" in query.lower():
        class_name = "AiderIntegrationService" if "aider" in query.lower() or "service" in query.lower() else "SurgicalContextExtractor"
        file_path = "aider_integration_service.py" if class_name == "AiderIntegrationService" else "surgical_context_extractor.py"

        print(f"\n--- Inheritance Context for {class_name} ---")

        try:
            inheritance_ctx = service.get_focused_inheritance_context(project_path, class_name, file_path)

            # Show base classes
            if inheritance_ctx['base_classes']:
                print("\nBase Classes:")
                for i, base_ctx in enumerate(inheritance_ctx['base_classes'][:2]):
                    print(f"\n  {i+1}. {base_ctx['symbol_name']}")
                    print(f"     File: {base_ctx['file_path']}")
                    print(f"     Lines: {base_ctx['start_line']}-{base_ctx['end_line']}")
                    print(f"     Snippet:\n{format_snippet(base_ctx['content'])}")
            else:
                print("\nNo base classes found.")

            # Show derived classes
            if inheritance_ctx['derived_classes']:
                print("\nDerived Classes:")
                for i, derived_ctx in enumerate(inheritance_ctx['derived_classes'][:2]):
                    print(f"\n  {i+1}. {derived_ctx['symbol_name']}")
                    print(f"     File: {derived_ctx['file_path']}")
                    print(f"     Lines: {derived_ctx['start_line']}-{derived_ctx['end_line']}")
                    print(f"     Snippet:\n{format_snippet(derived_ctx['content'])}")
            else:
                print("\nNo derived classes found.")

        except Exception as e:
            print(f"Error extracting inheritance context: {e}")

if __name__ == "__main__":
    service = AiderIntegrationService()
    project_path = "."  # Use the current directory

    # Test with different queries
    queries = [
        "How does the SurgicalContextExtractor class work?",
        "Show me the inheritance hierarchy of the AiderIntegrationService class",
        "How are code snippets extracted in this codebase?"
    ]

    for query in queries:
        demo_query(service, project_path, query)
        print("\n" + "="*80)
