#!/usr/bin/env python3
"""
Debug the repo map generation directly.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_repo_map_direct():
    """Test repo map generation directly."""
    print("🔍 Testing repo map generation directly")
    print("=" * 80)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components
        model = Model("gpt-3.5-turbo")  # Use a simple model
        io = InputOutput()
        
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=".",
            main_model=model,
            io=io,
            verbose=True
        )
        
        print("✅ RepoMap created successfully")
        
        # Test 1: Get repo map with no files
        print("\n🧪 Test 1: Get repo map with no files")
        print("-" * 60)
        
        map_result_1 = repo_map.get_repo_map(
            chat_fnames=[],
            other_fnames=[],
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        print(f"Result 1 type: {type(map_result_1)}")
        print(f"Result 1 length: {len(map_result_1) if map_result_1 else 0}")
        if map_result_1:
            print(f"Result 1 preview: {map_result_1[:200]}...")
        else:
            print("Result 1 is None or empty")
        
        # Test 2: Get repo map with specific files
        print("\n🧪 Test 2: Get repo map with specific files")
        print("-" * 60)
        
        test_files = ["aider/main.py", "aider/repomap.py"]
        
        map_result_2 = repo_map.get_repo_map(
            chat_fnames=test_files,
            other_fnames=[],
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        print(f"Result 2 type: {type(map_result_2)}")
        print(f"Result 2 length: {len(map_result_2) if map_result_2 else 0}")
        if map_result_2:
            print(f"Result 2 preview: {map_result_2[:200]}...")
        else:
            print("Result 2 is None or empty")
        
        # Test 3: Get repo map with other_fnames
        print("\n🧪 Test 3: Get repo map with other_fnames")
        print("-" * 60)
        
        map_result_3 = repo_map.get_repo_map(
            chat_fnames=[],
            other_fnames=test_files,
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        print(f"Result 3 type: {type(map_result_3)}")
        print(f"Result 3 length: {len(map_result_3) if map_result_3 else 0}")
        if map_result_3:
            print(f"Result 3 preview: {map_result_3[:200]}...")
        else:
            print("Result 3 is None or empty")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in direct repo map test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def test_coder_get_repo_map():
    """Test coder.get_repo_map() method directly."""
    print("\n🔍 Testing coder.get_repo_map() method")
    print("=" * 80)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        print("✅ Modules imported successfully")
        
        # Change to aider-main directory
        original_cwd = os.getcwd()
        os.chdir("aider-main")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Create a git repo
        repo = GitRepo(io, [], ".")
        
        # Test 1: Coder with no files
        print("\n🧪 Test 1: Coder with no files")
        print("-" * 60)
        
        coder1 = Coder.create(
            main_model=model,
            io=io,
            repo=repo,
            fnames=[],
            map_tokens=8192
        )
        
        repo_map_1 = coder1.get_repo_map()
        print(f"Coder 1 repo map type: {type(repo_map_1)}")
        print(f"Coder 1 repo map length: {len(repo_map_1) if repo_map_1 else 0}")
        if repo_map_1:
            print(f"Coder 1 repo map preview: {repo_map_1[:200]}...")
        else:
            print("Coder 1 repo map is None or empty")
        
        # Test 2: Coder with specific files
        print("\n🧪 Test 2: Coder with specific files")
        print("-" * 60)
        
        test_files = ["aider/main.py", "aider/repomap.py"]
        
        coder2 = Coder.create(
            main_model=model,
            io=io,
            repo=repo,
            fnames=test_files,
            map_tokens=8192
        )
        
        repo_map_2 = coder2.get_repo_map()
        print(f"Coder 2 repo map type: {type(repo_map_2)}")
        print(f"Coder 2 repo map length: {len(repo_map_2) if repo_map_2 else 0}")
        if repo_map_2:
            print(f"Coder 2 repo map preview: {repo_map_2[:200]}...")
        else:
            print("Coder 2 repo map is None or empty")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in coder repo map test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Restore original directory
        os.chdir(original_cwd)

def main():
    """Run direct repo map debugging tests."""
    print("🚀 Direct Repository Map Debugging")
    print("=" * 100)
    
    tests = [
        ("Direct RepoMap Test", test_repo_map_direct),
        ("Coder get_repo_map Test", test_coder_get_repo_map),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 DIRECT DEBUGGING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
