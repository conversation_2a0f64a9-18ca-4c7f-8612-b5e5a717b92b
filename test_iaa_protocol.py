#!/usr/bin/env python3
"""
Test script for the Iterative Analysis Accumulation (IAA) Protocol

This script validates the Multi-Turn Reasoning Loop implementation and demonstrates
its capabilities for complex code analysis workflows.
"""

import os
import sys
import json
import time
from pathlib import Path

def test_iaa_protocol():
    """Test the complete IAA Protocol implementation."""
    print("🧠 Testing Iterative Analysis Accumulation (IAA) Protocol")
    print("=" * 60)
    
    # Test scenarios for different types of analysis tasks
    test_scenarios = [
        {
            "name": "Bug Investigation",
            "task": "Investigate potential memory leaks in the file processing pipeline",
            "task_type": "debugging",
            "focus_entities": ["file", "memory", "process"],
            "max_iterations": 4
        },
        {
            "name": "Feature Development",
            "task": "Analyze the architecture for adding real-time collaboration features",
            "task_type": "feature_development", 
            "focus_entities": ["collaboration", "real_time", "sync"],
            "max_iterations": 3
        },
        {
            "name": "Code Refactoring",
            "task": "Identify opportunities to refactor the context selection logic for better modularity",
            "task_type": "refactoring",
            "focus_entities": ["context", "selection", "modular"],
            "max_iterations": 3
        },
        {
            "name": "Documentation Analysis",
            "task": "Assess documentation coverage and identify areas needing better documentation",
            "task_type": "documentation",
            "focus_entities": ["doc", "coverage", "documentation"],
            "max_iterations": 2
        }
    ]
    
    try:
        # Import the service
        from aider_integration_service import AiderIntegrationService
        
        # Initialize the service
        service = AiderIntegrationService()
        project_path = os.getcwd()
        
        print(f"📁 Project path: {project_path}")
        print(f"🔧 Service initialized successfully")
        
        # Test each scenario
        all_results = {}
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{'='*60}")
            print(f"🎯 Test Scenario {i}: {scenario['name']}")
            print(f"{'='*60}")
            
            start_time = time.time()
            
            # Run multi-turn reasoning analysis
            result = service.analyze_with_multi_turn_reasoning(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['task_type'],
                focus_entities=scenario['focus_entities'],
                max_iterations=scenario['max_iterations'],
                max_tokens=4000
            )
            
            end_time = time.time()
            analysis_time = end_time - start_time
            
            # Store results
            all_results[scenario['name']] = result
            
            # Display results summary
            if 'error' in result:
                print(f"❌ Error: {result['error']}")
                print(f"💡 Fallback: {result['fallback']}")
                continue
            
            print(f"✅ Analysis completed in {analysis_time:.2f} seconds")
            print(f"📊 Results Summary:")
            print(f"   • Total iterations: {result['total_iterations']}")
            print(f"   • Overall confidence: {result['overall_confidence']:.2f}")
            print(f"   • Entities analyzed: {len(result['entity_summaries'])}")
            print(f"   • Global insights: {len(result['global_insights'])}")
            
            # Show iteration progression
            print(f"\n🔄 Iteration Progression:")
            for iter_info in result['iteration_history']:
                print(f"   Iteration {iter_info['iteration']}: "
                      f"{iter_info['entities_count']} entities, "
                      f"confidence {iter_info['confidence']:.2f}, "
                      f"status: {iter_info['status']}")
            
            # Show confidence analysis
            if 'confidence_summary' in result:
                conf_summary = result['confidence_summary']
                if 'average_confidence' in conf_summary:
                    print(f"\n📈 Confidence Analysis:")
                    print(f"   • Average confidence: {conf_summary['average_confidence']:.2f}")
                    print(f"   • Min confidence: {conf_summary['min_confidence']:.2f}")
                    print(f"   • Max confidence: {conf_summary['max_confidence']:.2f}")
                    print(f"   • Low confidence entities: {conf_summary['low_confidence_count']}")
            
            # Show sample insights
            if result['global_insights']:
                print(f"\n💡 Sample Insights:")
                for insight in result['global_insights'][:3]:
                    print(f"   • {insight}")
                if len(result['global_insights']) > 3:
                    print(f"   • ... and {len(result['global_insights']) - 3} more insights")
        
        # Generate comprehensive test report
        print(f"\n{'='*60}")
        print(f"📋 COMPREHENSIVE TEST REPORT")
        print(f"{'='*60}")
        
        successful_tests = sum(1 for result in all_results.values() if 'error' not in result)
        total_tests = len(test_scenarios)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        
        if successful_tests > 0:
            # Calculate aggregate statistics
            total_iterations = sum(result['total_iterations'] for result in all_results.values() if 'error' not in result)
            avg_confidence = sum(result['overall_confidence'] for result in all_results.values() if 'error' not in result) / successful_tests
            total_entities = sum(len(result['entity_summaries']) for result in all_results.values() if 'error' not in result)
            
            print(f"📊 Aggregate Statistics:")
            print(f"   • Total iterations across all tests: {total_iterations}")
            print(f"   • Average confidence across tests: {avg_confidence:.2f}")
            print(f"   • Total unique entities analyzed: {total_entities}")
            
            # Test quality metrics
            print(f"\n🎯 Quality Metrics:")
            
            # Check if confidence improves over iterations
            confidence_improvements = 0
            for result in all_results.values():
                if 'error' not in result and len(result['iteration_history']) > 1:
                    first_conf = result['iteration_history'][0]['confidence']
                    last_conf = result['iteration_history'][-1]['confidence']
                    if last_conf > first_conf:
                        confidence_improvements += 1
            
            print(f"   • Tests showing confidence improvement: {confidence_improvements}/{successful_tests}")
            
            # Check completion status distribution
            completion_statuses = {}
            for result in all_results.values():
                if 'error' not in result:
                    final_status = result['iteration_history'][-1]['status']
                    completion_statuses[final_status] = completion_statuses.get(final_status, 0) + 1
            
            print(f"   • Completion status distribution:")
            for status, count in completion_statuses.items():
                print(f"     - {status}: {count} tests")
        
        # Save detailed results
        output_file = "iaa_protocol_test_results.json"
        with open(output_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        # Final assessment
        if successful_tests == total_tests:
            print(f"\n🎉 ALL TESTS PASSED! IAA Protocol is working correctly.")
            return True
        elif successful_tests > 0:
            print(f"\n⚠️ PARTIAL SUCCESS: {successful_tests}/{total_tests} tests passed.")
            return True
        else:
            print(f"\n❌ ALL TESTS FAILED: IAA Protocol needs debugging.")
            return False
            
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False


def test_individual_components():
    """Test individual IAA Protocol components."""
    print(f"\n{'='*60}")
    print(f"🔧 Testing Individual IAA Components")
    print(f"{'='*60}")
    
    try:
        # Test AnalysisMemory
        print("🧠 Testing AnalysisMemory...")
        from iterative_analysis_engine import AnalysisMemory, ConfidenceTracker
        
        memory = AnalysisMemory()
        
        # Test entity memory operations
        memory.update_entity_analysis("test_entity", 0.8, ["insight1", "insight2"], ["issue1"])
        memory.mark_entity_usage("test_entity", True)
        
        entity_memory = memory.get_entity_memory("test_entity")
        assert entity_memory.last_confidence == 0.8
        assert len(entity_memory.insights) == 2
        assert entity_memory.was_useful == True
        
        print("✅ AnalysisMemory tests passed")
        
        # Test ConfidenceTracker
        print("🎯 Testing ConfidenceTracker...")
        
        tracker = ConfidenceTracker()
        
        # Test confidence calculation
        analysis_data = {
            'doc_coverage': 0.8,
            'complexity': 3,
            'dependencies': ['dep1', 'dep2'],
            'insights': ['insight1', 'insight2', 'insight3'],
            'open_issues': []
        }
        
        confidence = tracker.calculate_entity_confidence("test_entity", analysis_data)
        print(f"   Calculated confidence: {confidence}")
        assert 0.0 <= confidence <= 1.0

        print("✅ ConfidenceTracker tests passed")

        return True

    except Exception as e:
        print(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Starting IAA Protocol Test Suite")
    print("=" * 60)
    
    # Test individual components first
    components_ok = test_individual_components()
    
    if components_ok:
        # Test the full protocol
        protocol_ok = test_iaa_protocol()
        
        if protocol_ok:
            print(f"\n🎉 IAA Protocol Test Suite: ALL TESTS PASSED!")
            sys.exit(0)
        else:
            print(f"\n❌ IAA Protocol Test Suite: SOME TESTS FAILED!")
            sys.exit(1)
    else:
        print(f"\n❌ IAA Protocol Test Suite: COMPONENT TESTS FAILED!")
        sys.exit(1)
