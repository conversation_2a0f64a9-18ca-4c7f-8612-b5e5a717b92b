#!/usr/bin/env python3

"""
Test to verify that the Aider Coder context request path fix works correctly.
This test simulates the actual Aider Coder initialization and context request processing.
"""

import os
import sys
import tempfile
from pathlib import Path

def test_aider_coder_context_request_path():
    """Test that Aider Coder uses the correct project path for context requests."""
    
    print("🧪 Testing Aider Coder Context Request Path Fix")
    print("=" * 50)
    
    # Create a temporary trading project structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary project at: {temp_dir}")
        
        # Create the trading project structure
        trade_management_dir = os.path.join(temp_dir, "trade_management")
        services_dir = os.path.join(temp_dir, "services")
        os.makedirs(trade_management_dir)
        os.makedirs(services_dir)
        
        # Create position_exit_manager.py
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    async def close_position_based_on_conditions(self, app):
        """Close positions based on predefined conditions."""
        return True
'''
        
        position_exit_manager_path = os.path.join(trade_management_dir, "position_exit_manager.py")
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        print(f"✅ Created: {position_exit_manager_path}")
        
        # Change to the temporary directory to simulate user running aider from project root
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            print(f"📁 Changed working directory to: {temp_dir}")
            
            # Test the Aider Coder context request path resolution
            try:
                # Import the required modules
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.coders.base_coder import Coder
                from aider.models import Model
                from aider.io import InputOutput
                
                print("✅ Successfully imported Aider modules")
                
                # Create a mock Coder instance
                model = Model("gpt-3.5-turbo")
                io = InputOutput()
                
                # Create a minimal coder instance with a file from the project
                coder = Coder.create(
                    main_model=model,
                    io=io,
                    fnames=[position_exit_manager_path],  # Add the file to the chat
                    use_git=False,
                    map_tokens=1000
                )
                
                print(f"📁 Coder root: {coder.root}")
                print(f"📁 Current working directory: {os.getcwd()}")
                
                # Simulate a context request
                context_request_content = '''
{CONTEXT_REQUEST: {"original_user_query_context": "how does the close_position_based_on_conditions function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}]}}
'''
                
                # Process the context request
                cleaned_content, augmented_prompt = coder.process_context_requests(
                    context_request_content, 
                    "how does the close_position_based_on_conditions function work?"
                )
                
                print(f"🔍 Cleaned content: {cleaned_content.strip()}")
                print(f"🔍 Augmented prompt length: {len(augmented_prompt) if augmented_prompt else 0}")
                
                # Check if the context request was processed successfully
                if augmented_prompt and "could not find" not in augmented_prompt.lower():
                    print("✅ SUCCESS: Context request processed successfully!")
                    print("✅ The fix correctly resolves project paths in Aider Coder!")
                    return True
                else:
                    print("❌ FAILED: Context request failed to find symbols")
                    if augmented_prompt:
                        print(f"   Augmented prompt: {augmented_prompt[:200]}...")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import Aider modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

def main():
    """Main test function."""
    try:
        success = test_aider_coder_context_request_path()
        
        if success:
            print("\n🎉 AIDER CODER CONTEXT REQUEST PATH FIX: PASSED")
            print("The fix correctly resolves project paths in the Aider Coder integration.")
        else:
            print("\n❌ AIDER CODER CONTEXT REQUEST PATH FIX: FAILED")
            print("The fix needs further investigation.")
        
        return success
    except Exception as e:
        print(f"\n❌ TEST EXECUTION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
