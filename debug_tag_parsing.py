#!/usr/bin/env python3
"""
Debug the tag parsing system to find why no function/method tags are being generated.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_tag_parsing():
    """Debug why tag parsing is returning 0 function/method tags."""
    print("🔍 Debugging Tag Parsing System")
    print("=" * 80)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ RepoMap modules imported successfully")
        
        # Create a RepoMap instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=8192,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=True
        )
        
        print("✅ RepoMap instance created successfully")
        
        # Test 1: Check if tree-sitter is working
        print("\n🧪 Test 1: Tree-sitter availability")
        print("-" * 60)
        
        try:
            from tree_sitter import Language, Parser
            print("✅ tree-sitter imported successfully")
        except ImportError as e:
            print(f"❌ tree-sitter import failed: {e}")
            return False
        
        # Test 2: Check specific file tag parsing
        print("\n🧪 Test 2: Direct file tag parsing")
        print("-" * 60)
        
        # Test with a known Python file
        test_file = "aider-main/aider/repomap.py"
        if os.path.exists(test_file):
            print(f"Testing tag parsing for: {test_file}")
            
            try:
                rel_fname = os.path.relpath(test_file, "aider-main")
                tags = list(repo_map.get_tags_raw(test_file, rel_fname))
                
                print(f"   Total tags found: {len(tags)}")
                
                # Categorize tags
                def_tags = [tag for tag in tags if hasattr(tag, 'kind') and tag.kind == 'def']
                ref_tags = [tag for tag in tags if hasattr(tag, 'kind') and tag.kind == 'ref']
                
                print(f"   Definition tags: {len(def_tags)}")
                print(f"   Reference tags: {len(ref_tags)}")
                
                # Show some examples
                if def_tags:
                    print("   Example definitions:")
                    for tag in def_tags[:5]:
                        print(f"     - {tag.name} (line {tag.line})")
                else:
                    print("   ❌ No definition tags found!")
                
                if ref_tags:
                    print("   Example references:")
                    for tag in ref_tags[:5]:
                        print(f"     - {tag.name}")
                else:
                    print("   ❌ No reference tags found!")
                    
            except Exception as e:
                print(f"   ❌ Error parsing tags: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"❌ Test file not found: {test_file}")
            return False
        
        # Test 3: Check get_ranked_tags method
        print("\n🧪 Test 3: get_ranked_tags method")
        print("-" * 60)
        
        try:
            # Test with a small set of files
            test_files = ["aider/repomap.py", "aider/models.py"]
            
            ranked_tags = repo_map.get_ranked_tags(
                chat_fnames=[],
                other_fnames=test_files,
                mentioned_fnames=set(),
                mentioned_idents=set()
            )
            
            print(f"   Ranked tags returned: {len(ranked_tags)}")
            
            if ranked_tags:
                print("   Example ranked tags:")
                for i, tag in enumerate(ranked_tags[:10]):
                    if hasattr(tag, 'name') and hasattr(tag, 'kind'):
                        print(f"     {i+1}. {tag.name} ({tag.kind}) in {tag.rel_fname}")
                    else:
                        print(f"     {i+1}. {tag} (type: {type(tag)})")
            else:
                print("   ❌ No ranked tags returned!")
                
        except Exception as e:
            print(f"   ❌ Error getting ranked tags: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 4: Check tree-sitter language support
        print("\n🧪 Test 4: Tree-sitter language support")
        print("-" * 60)
        
        try:
            from aider.repomap import filename_to_lang, get_language, get_parser
            
            test_file = "test.py"
            lang = filename_to_lang(test_file)
            print(f"   Language for {test_file}: {lang}")
            
            if lang:
                try:
                    language = get_language(lang)
                    parser = get_parser(lang)
                    print(f"   ✅ Language and parser loaded for {lang}")
                except Exception as e:
                    print(f"   ❌ Error loading language/parser for {lang}: {e}")
                    return False
            else:
                print(f"   ❌ No language detected for {test_file}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error testing language support: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 5: Check query files
        print("\n🧪 Test 5: Tree-sitter query files")
        print("-" * 60)
        
        try:
            from aider.repomap import get_scm_fname
            
            lang = "python"
            query_scm = get_scm_fname(lang)
            
            if query_scm and query_scm.exists():
                print(f"   ✅ Query file found: {query_scm}")
                
                query_content = query_scm.read_text()
                print(f"   Query file size: {len(query_content)} characters")
                print(f"   Query preview: {query_content[:200]}...")
                
                # Check for function/method definitions
                if "function" in query_content.lower() or "method" in query_content.lower():
                    print("   ✅ Query file contains function/method patterns")
                else:
                    print("   ⚠️  Query file may not contain function/method patterns")
                    
            else:
                print(f"   ❌ Query file not found for {lang}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error checking query files: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in tag parsing debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_tag_extraction():
    """Test manual tag extraction to see if we can get function names."""
    print("\n🔍 Manual Tag Extraction Test")
    print("=" * 80)
    
    try:
        # Test with a simple Python file
        test_code = '''
def test_function():
    pass

class TestClass:
    def method_one(self):
        pass
    
    def method_two(self):
        return True

def another_function(param):
    return param * 2
'''
        
        print("🧪 Testing with sample Python code:")
        print(test_code)
        
        # Try to parse with tree-sitter
        try:
            from tree_sitter import Language, Parser
            from aider.repomap import get_language, get_parser, get_scm_fname
            
            lang = "python"
            language = get_language(lang)
            parser = get_parser(lang)
            
            print(f"✅ Language and parser loaded for {lang}")
            
            # Parse the code
            tree = parser.parse(test_code.encode('utf-8'))
            print(f"✅ Code parsed successfully")
            
            # Get the query
            query_scm = get_scm_fname(lang)
            if query_scm and query_scm.exists():
                query_content = query_scm.read_text()
                query = language.query(query_content)
                
                print(f"✅ Query loaded successfully")
                
                # Run the query
                captures = query.captures(tree.root_node)
                print(f"Query captures: {len(captures)} items")
                
                # Process captures
                from aider.repomap import USING_TSL_PACK
                
                if USING_TSL_PACK:
                    all_nodes = []
                    for tag, nodes in captures.items():
                        all_nodes += [(node, tag) for node in nodes]
                else:
                    all_nodes = list(captures)
                
                print(f"All nodes: {len(all_nodes)} items")
                
                # Extract function/method names
                functions_found = []
                for node, tag in all_nodes:
                    if tag.startswith("name.definition."):
                        name = node.text.decode("utf-8")
                        functions_found.append(name)
                        print(f"   ✅ Found definition: {name} (tag: {tag})")
                
                if functions_found:
                    print(f"✅ Successfully extracted {len(functions_found)} function/method names")
                    return True
                else:
                    print("❌ No function/method names extracted")
                    return False
                    
            else:
                print(f"❌ Query file not found for {lang}")
                return False
                
        except Exception as e:
            print(f"❌ Error in manual tag extraction: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Error in manual tag extraction test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tag parsing debugging tests."""
    print("🚀 Tag Parsing System Debugging")
    print("=" * 100)
    
    tests = [
        ("Tag Parsing Debug", debug_tag_parsing),
        ("Manual Tag Extraction", test_manual_tag_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 TAG PARSING DEBUGGING SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 TAG PARSING SYSTEM IS WORKING!")
        print("   The issue may be elsewhere in the search system")
    else:
        print("⚠️  TAG PARSING SYSTEM HAS ISSUES!")
        print("   This explains why function search is failing")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
