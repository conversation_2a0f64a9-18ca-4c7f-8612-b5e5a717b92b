#!/usr/bin/env python3
"""
Test script to verify that the workflow enforcement fix works correctly.
This tests that the LLM receives strong mandatory messages about using MAP_REQUEST first.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_repo_messages_enforcement():
    """Test that the repository messages enforce MAP_REQUEST workflow."""
    print("🧪 Testing Repository Messages Enforcement")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create a coder instance
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            edit_format="informative"
        )

        # Get the repo messages
        repo_messages = coder.get_repo_messages()

        print("=== REPO MESSAGES ANALYSIS ===")
        print(f"Number of messages: {len(repo_messages)}")

        enforcement_checks = [
            ("CRITICAL WORKFLOW RULE", "Strong enforcement language"),
            ("ZERO repository context", "Clear state indication"),
            ("MUST start with MAP_REQUEST", "Mandatory requirement"),
            ("CANNOT use CONTEXT_REQUEST", "Clear prohibition"),
            ("mandatory, not optional", "Emphasis on requirement"),
        ]

        passed = 0
        total = len(enforcement_checks)

        for i, msg in enumerate(repo_messages):
            print(f"\nMessage {i+1} ({msg['role']}):")
            content = msg['content']
            print(f"  Content: {content}")

            # Check for enforcement keywords in user messages
            if msg['role'] == 'user':
                for check_phrase, description in enforcement_checks:
                    if check_phrase in content:
                        print(f"  ✅ Found: {check_phrase} ({description})")
                        passed += 1
                    else:
                        print(f"  ❌ Missing: {check_phrase} ({description})")

        print(f"\n📊 Enforcement strength: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing repo messages enforcement: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_files_no_full_files_reply():
    """Test that the files_no_full_files_with_repo_map_reply is consistent."""
    print("\n🧪 Testing Files No Full Files Reply")
    print("=" * 60)

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()
        reply = prompts.files_no_full_files_with_repo_map_reply

        print(f"Reply content: {reply}")

        # Check that it doesn't contain a direct MAP_REQUEST
        if "{{MAP_REQUEST" in reply:
            print("❌ Reply contains direct MAP_REQUEST (should be removed)")
            return False
        else:
            print("✅ Reply does not contain direct MAP_REQUEST")

        # Check that it contains understanding language
        understanding_phrases = [
            "I understand",
            "must start with MAP_REQUEST",
            "explore the repository structure",
        ]

        passed = 0
        total = len(understanding_phrases)

        for phrase in understanding_phrases:
            if phrase in reply:
                print(f"✅ Found: {phrase}")
                passed += 1
            else:
                print(f"❌ Missing: {phrase}")

        print(f"\n📊 Reply consistency: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing files reply: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_flow_consistency():
    """Test that the message flow is consistent and doesn't contradict itself."""
    print("\n🧪 Testing Message Flow Consistency")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create a coder instance with no files
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],  # No files in chat
            use_git=False,
            map_tokens=1000,
            edit_format="informative"
        )

        # Get all message types
        repo_messages = coder.get_repo_messages()
        chat_files_messages = coder.get_chat_files_messages()

        print("=== MESSAGE FLOW ANALYSIS ===")
        print(f"Repo messages: {len(repo_messages)}")
        print(f"Chat files messages: {len(chat_files_messages)}")

        # Check for contradictions
        contradictions = []

        # Look for any message that suggests the LLM can directly use CONTEXT_REQUEST
        all_messages = repo_messages + chat_files_messages

        for i, msg in enumerate(all_messages):
            content = msg['content']

            # Check for problematic patterns
            if "{{MAP_REQUEST" in content and msg['role'] == 'assistant':
                contradictions.append(f"Message {i+1}: Assistant provides direct MAP_REQUEST example")

            if "when needed" in content.lower() and "MAP_REQUEST" in content:
                contradictions.append(f"Message {i+1}: Uses permissive 'when needed' language")

        if contradictions:
            print("❌ Found contradictions:")
            for contradiction in contradictions:
                print(f"  - {contradiction}")
            return False
        else:
            print("✅ No contradictions found in message flow")
            return True

    except Exception as e:
        print(f"❌ Error testing message flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all workflow enforcement tests."""
    print("🚀 Testing Workflow Enforcement Fix")
    print("=" * 80)

    tests = [
        test_repo_messages_enforcement,
        test_files_no_full_files_reply,
        test_message_flow_consistency,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 Workflow enforcement fix is working correctly!")
        print("\n📋 The LLM now receives:")
        print("  1. ✅ Strong mandatory language about MAP_REQUEST")
        print("  2. ✅ Clear prohibition against CONTEXT_REQUEST without MAP_REQUEST")
        print("  3. ✅ Consistent message flow without contradictions")
        print("  4. ✅ No direct MAP_REQUEST examples that bypass the workflow")
        print("  5. ✅ Emphasis that the requirement is mandatory, not optional")
    else:
        print("⚠️  Some workflow enforcement fixes need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
