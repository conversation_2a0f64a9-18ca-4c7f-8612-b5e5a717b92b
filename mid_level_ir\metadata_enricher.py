"""
Metadata Enricher - Calculates various code quality and complexity metrics.

This module enriches the IR with additional metadata including complexity
metrics, documentation coverage, and code quality indicators.
"""

import ast
from typing import Dict, List, Any

from .ir_context import IRContext, ModuleMetadata


class MetadataEnricher:
    """
    Enriches modules and entities with additional metadata.
    
    This analyzer calculates:
    - Documentation coverage
    - Code complexity metrics
    - Comment density
    - Code quality indicators
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the metadata enricher with configuration.
        
        Args:
            config: Configuration dictionary for metadata options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.calculate_complexity = config.get('calculate_complexity', True)
        self.calculate_doc_coverage = config.get('calculate_doc_coverage', True)
    
    def enrich(self, context: IRContext) -> IRContext:
        """
        Enrich all modules in the context with metadata.
        
        Args:
            context: The IR context containing modules to enrich
            
        Returns:
            Updated context with enriched metadata
        """
        if self.verbose:
            print(f"   Enriching metadata for {len(context.modules)} modules")
        
        for module_info in context.modules.values():
            # Calculate module-level metadata
            metadata = self._calculate_module_metadata(module_info)
            module_info.metadata = metadata
            
            # Enrich individual entities
            for entity in module_info.entities:
                self._enrich_entity(entity, module_info.source_code)
        
        if self.verbose:
            print(f"   Metadata enrichment completed")
        
        return context
    
    def _calculate_module_metadata(self, module_info) -> ModuleMetadata:
        """
        Calculate metadata for a single module.
        
        Args:
            module_info: ModuleInfo object to analyze
            
        Returns:
            ModuleMetadata object with calculated metrics
        """
        metadata = ModuleMetadata()
        
        # Count entities by type
        for entity in module_info.entities:
            if entity.type == 'function' or entity.type == 'async_function':
                metadata.function_count += 1
            elif entity.type == 'class':
                metadata.class_count += 1
            elif entity.type in ('variable', 'constant'):
                metadata.variable_count += 1
        
        # Calculate documentation coverage
        if self.calculate_doc_coverage:
            metadata.doc_coverage = self._calculate_doc_coverage(module_info.entities)
        
        # Calculate comment density
        if module_info.source_code:
            metadata.comment_density = self._calculate_comment_density(module_info.source_code)
        
        # Calculate average complexity
        if self.calculate_complexity:
            metadata.avg_complexity = self._calculate_average_complexity(module_info.entities)
        
        # Find most critical entity
        metadata.most_critical_entity = self._find_most_critical_entity(module_info.entities)
        
        return metadata
    
    def _calculate_doc_coverage(self, entities: List) -> float:
        """Calculate the percentage of entities with documentation."""
        if not entities:
            return 0.0
        
        documented_count = 0
        for entity in entities:
            if entity.doc and entity.doc.strip() and not entity.doc.startswith(entity.type.title()):
                documented_count += 1
        
        return documented_count / len(entities)
    
    def _calculate_comment_density(self, source_code: str) -> float:
        """Calculate the ratio of comment lines to total lines."""
        if not source_code:
            return 0.0
        
        lines = source_code.split('\n')
        comment_lines = 0
        code_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('#'):
                comment_lines += 1
            elif stripped:  # Non-empty, non-comment line
                code_lines += 1
        
        if code_lines == 0:
            return 0.0
        
        return comment_lines / code_lines
    
    def _calculate_average_complexity(self, entities: List) -> float:
        """Calculate the average cyclomatic complexity of functions."""
        complexities = []
        
        for entity in entities:
            if entity.type in ('function', 'async_function') and entity.ast_node:
                complexity = self._calculate_cyclomatic_complexity(entity.ast_node)
                complexities.append(complexity)
                entity.complexity = complexity
        
        if not complexities:
            return 0.0
        
        return sum(complexities) / len(complexities)
    
    def _calculate_cyclomatic_complexity(self, func_node: ast.FunctionDef) -> float:
        """
        Calculate cyclomatic complexity for a function.
        
        This is a simplified version that counts decision points.
        """
        complexity = 1  # Base complexity
        
        for node in ast.walk(func_node):
            # Count decision points
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, ast.comprehension):
                complexity += 1
        
        return float(complexity)
    
    def _find_most_critical_entity(self, entities: List) -> str:
        """Find the entity with the highest criticality."""
        critical_entities = [e for e in entities if e.criticality == "high"]
        
        if critical_entities:
            # Return the first high-criticality entity
            return critical_entities[0].name
        
        # If no high-criticality entities, look for medium
        medium_entities = [e for e in entities if e.criticality == "medium"]
        if medium_entities:
            return medium_entities[0].name
        
        # Otherwise, return the first entity if any exist
        if entities:
            return entities[0].name
        
        return None
    
    def _enrich_entity(self, entity, source_code: str) -> None:
        """
        Enrich a single entity with additional metadata.
        
        Args:
            entity: EntityInfo object to enrich
            source_code: Source code for analysis
        """
        # Calculate complexity if not already done
        if (entity.complexity is None and 
            entity.type in ('function', 'async_function') and 
            entity.ast_node and 
            self.calculate_complexity):
            entity.complexity = self._calculate_cyclomatic_complexity(entity.ast_node)
