[{"role": "user", "content": "Here are summaries of files in the repository:\n\nanalyze_data_flow.py:\n⋮\n│def parse_log_line(line):\n⋮\n│def extract_data_from_message(message):\n⋮\n│def analyze_log_file(log_file, output_format='text', output_file=None):\n⋮\n\nbacktest\\backtest_runner.py:\n⋮\n│class BacktestRunner:\n│    \"\"\"\n│    Main class for running backtests.\n│    Coordinates all components and manages the backtest execution.\n⋮\n│    async def run(self) -> bool:\n⋮\n│    def generate_report(self, output_dir: str = 'backtest_results') -> str:\n⋮\n│def parse_arguments():\n⋮\n\ncheck_backtest_data.py:\n⋮\n│def check_database(db_path):\n⋮\n\ncheck_database.py:\n⋮\n│db_path = os.path.join('database', 'indicator_data.db')\n⋮\n│conn = sqlite3.connect(db_path)\n│cursor = conn.cursor()\n│\n⋮\n│schema = cursor.fetchall()\n⋮\n│rows = cursor.fetchall()\n⋮\n\ncheck_database_again.py:\n⋮\n│db_path = os.path.join('database', 'indicator_data.db')\n⋮\n│conn = sqlite3.connect(db_path)\n│cursor = conn.cursor()\n│\n⋮\n│rows = cursor.fetchall()\n⋮\n│schema = cursor.fetchall()\n⋮\n\ncheck_date_range.py:\n⋮\n│def check_date_range(db_path, table_name, start_date, end_date):\n⋮\n\ncheck_datetime_format.py:\n⋮\n│def check_datetime_format(db_path, table_name):\n⋮\n\ncheck_db.py:\n⋮\n│engine = create_engine('sqlite:///database/live_trade_logs.db')\n│\n⋮\n│inspector = inspect(engine)\n│tables = inspector.get_table_names()\n│\n⋮\n\ncheck_indicator_db.py:\n⋮\n│conn = sqlite3.connect('database/indicator_data.db')\n│cursor = conn.cursor()\n│\n⋮\n│tables = cursor.fetchall()\n⋮\n│rows = cursor.fetchall()\n⋮\n\ncheck_live_db.py:\n⋮\n│def check_live_database():\n⋮\n\ncheck_market_regime.py:\n⋮\n│cursor = conn.cursor()\n│\n⋮\n\ncheck_scheduling.py:\n⋮\n│def check_scheduling():\n⋮\n\ncheck_web_dashboard.py:\n⋮\n│def get_dashboard_data():\n⋮\n│def simulate_web_data_processing():\n⋮\n│def check_database_directly():\n⋮\n\nconfig\\backtest_config.py:\n⋮\n│class BacktestConfig:\n│    # Transaction costs\n│    DEFAULT_SLIPPAGE_PIPS = 2\n⋮\n│    @staticmethod\n│    def get_config():\n⋮\n\nconfig\\base_config.py:\n⋮\n│BASE_SYMBOLS_CONFIG = {\n│    # 'EURUSD': {\n│    #     'mt5_symbol': 'EURUSD',\n│    #     'price_key': 'EURUSD',\n│    #     'symbol_allowed': True,\n│    #     'exchange': 'OANDA',\n│    #     'prefix': 'eur',\n│    #     'cache_intervals': [\n│    #         Interval.in_1_hour\n│    #     ],\n⋮\n\nconfig\\environment.py:\n│TELEGRAM_BOT_TOKEN = '**********************************************'\n│TELEGRAM_CHAT_ID = '5185654136'\n│\n│TV_USERNAME = '<EMAIL>'\n│TV_PASSWORD = '!#Uvv&Y9UM5t7F8'\n│\n⋮\n│LIVE_DB_URI = 'sqlite:///database/live_data.db'\n│BACKTEST_DB_URI = 'sqlite:///database/backtest_data.db'\n│\n⋮\n│LIVE_TRADE_LOGS_DB_URI = 'sqlite:///database/live_trade_logs.db'\n│BACKTEST_TRADE_LOGS_DB_URI = 'sqlite:///database/backtest_trade_logs.db'\n│\n⋮\n\nconfig\\trading_pairs_config.py:\n⋮\n│def configure_symbol_strategies():\n⋮\n│def init_config_store(config):\n⋮\n\ncreate_new_database.py:\n⋮\n│def create_new_database():\n⋮\n\ndatabase\\database_manager.py:\n⋮\n│class DatabaseManager:\n│    \"\"\"\n│    Manages database connections for both live and backtest modes.\n│    Ensures that backtest data doesn't contaminate live databases.\n⋮\n│    def __new__(cls):\n⋮\n│    def _initialize(self):\n⋮\n│    def get_engine(self) -> Engine:\n⋮\n\ndatabase\\read_db_head.py:\n⋮\n│def print_head_of_table(table_name: str, db_path: str = \"database/live_data.db\"):\n⋮\n\ndebug_data_access.py:\n⋮\n│def debug_data_access(db_path, table_name, target_date):\n⋮\n\ndebug_historical_provider.py:\n⋮\n│def debug_historical_provider(db_path, symbol, interval, current_time):\n⋮\n\nfind_continuous_data.py:\n⋮\n│def find_continuous_periods(db_path, table_name, min_period_hours=24):\n⋮\n\nfix_database_directly.py:\n⋮\n│def fix_database():\n⋮\n\nfix_database_with_pragma.py:\n⋮\n│def fix_database():\n⋮\n\nfix_market_direction.py:\n⋮\n│db_path = os.path.join('database', 'indicator_data.db')\n⋮\n│conn = sqlite3.connect(db_path)\n│cursor = conn.cursor()\n│\n⋮\n│rows = cursor.fetchall()\n⋮\n│schema = cursor.fetchall()\n│\n⋮\n\nfix_market_direction_again.py:\n⋮\n│db_path = os.path.join('database', 'indicator_data.db')\n⋮\n│conn = sqlite3.connect(db_path)\n│cursor = conn.cursor()\n│\n⋮\n│schema = cursor.fetchall()\n⋮\n│rows = cursor.fetchall()\n⋮\n│rows = cursor.fetchall()\n⋮\n│market_direction = cursor.fetchone()[0]\n⋮\n\nfix_market_direction_field.py:\n⋮\n│def fix_market_direction():\n⋮\n\nfix_market_direction_field_again.py:\n⋮\n│def fix_market_direction():\n⋮\n\nget_date_range.py:\n⋮\n│def get_date_range(db_path, table_name):\n⋮\n\nmain.py:\n⋮\n│async def initialize_components(is_backtest=False):\n⋮\n│def parse_arguments():\n⋮\n\nmarket_data\\data_provider.py:\n⋮\n│class DataProvider:\n│    \"\"\"\n│    Abstraction layer for data access that works in both live and backtest modes.\n│    Delegates to the appropriate data source based on the current mode.\n⋮\n│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op\n⋮\n\nmarket_data\\historical_data_provider.py:\n⋮\n│class HistoricalDataProvider:\n│    \"\"\"\n│    Provides historical data for backtesting.\n│    Loads data from source files/databases and prepares it for backtesting.\n⋮\n│    def get_candles(self, symbol: str, interval: Any, count: Optional[int] = None, current_time: Op\n⋮\n\nmarket_data\\market_data_repository.py:\n⋮\n│class DatabaseManager:\n│    \"\"\"Handles robust data operations from old version\"\"\"\n⋮\n│    def get_and_save_data(self, symbol: str, interval, timestamp: datetime, max_retries: int = 20, \n⋮\n\nmarket_data\\tick.py:\n⋮\n│symbol_info = mt5.symbol_info(symbol)\n⋮\n\nmarket_data\\timeframe_data.py:\n⋮\n│def analyze_volume(data: pd.DataFrame, lookback: int = 20) -> pd.DataFrame:\n⋮\n│def get_volume_signals(data: pd.DataFrame, thresh_strong: float = 75, thresh_weak: float = 25) -> p\n│\n⋮\n│class CandleData:\n│    def __init__(self, symbol: str, intervals: list, price_key: str, engine=None):\n│        self.symbol = symbol\n│        self.price_key = price_key\n│        self.intervals = intervals\n│        self.data_by_interval = {}\n│\n│        # Use the appropriate database based on mode\n│        mode_manager = ModeManager()\n│        if mode_manager.is_backtest_mode():\n│            db_path = mode_manager.get_backtest_db_path()\n⋮\n│    def calculate_trend_and_indicators(self, data: pd.DataFrame) -> dict:\n⋮\n\nmodels\\__init__.py:\n⋮\n│Base = declarative_base()\n│engine = create_engine('sqlite:///database/manage_positions.db')\n│Session = sessionmaker(bind=engine)\n│\n⋮\n\nmodels\\backtest_indicator_data.py:\n⋮\n│class BacktestIndicatorData(Base):\n│    \"\"\"\n│    Model for storing indicator data during backtest iterations.\n│    Stores values per symbol/interval for each candle date.\n⋮\n│    @classmethod\n│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt) -> 'BacktestIndicat\n⋮\n│def get_backtest_indicator_session():\n⋮\n\nmodels\\live_indicator_data.py:\n⋮\n│class LiveIndicatorData(Base):\n│    \"\"\"\n│    Model for storing indicator data during live trading.\n│    Stores values per symbol/interval for each candle date.\n⋮\n│    @classmethod\n│    def from_dict(cls, data: Dict, symbol: str, interval: str, candle_date: dt, is_complete_candle:\n│        \"\"\"Create LiveIndicatorData instance from data dictionary\"\"\"\n⋮\n│        try:\n│            print(f\"DEBUG: from_dict called for {symbol} {interval} {candle_date}\")\n⋮\n│            def get_value(key_prefix, default=0):\n⋮\n│def get_live_indicator_session():\n⋮\n\nmodels\\position_repository.py:\n⋮\n│Session = sessionmaker(bind=engine)\n│\n│class Position(Base):\n⋮\n│def verify_position_records():\n⋮\n\nmodels\\target_data.py:\n⋮\n│@dataclass\n│class TargetData:\n│    \"\"\"Encapsulates all target-related data with dynamic attributes\"\"\"\n⋮\n│    @classmethod\n│    def from_dict(cls, data: Dict, symbol: str) -> 'TargetData':\n⋮\n│    def to_dict(self) -> Dict:\n⋮\n│    def get_timeframe_data(self, tf: str) -> Dict:\n⋮\n│class TargetDataService:\n│    \"\"\"Manages target data caching and access\"\"\"\n⋮\n│    def get_target(self, symbol: str) -> Optional[TargetData]:\n⋮\n\nmodels\\target_history.py:\n⋮\n│class TargetHistory(Base):\n│    __tablename__ = 'target_history'\n│    \n⋮\n│    @classmethod\n│    def from_dict(cls, data: Dict, symbol: str, timeframe: str, timestamp: dt) -> 'TargetHistory':\n⋮\n\nmodels\\trade_log.py:\n⋮\n│def get_trade_log_engine():\n⋮\n│class TradeLog(TradeLogBase):\n│    \"\"\"Model for logging trade information for each symbol\"\"\"\n⋮\n│    def to_dict(self) -> Dict:\n⋮\n│class TradeExecution(TradeLogBase):\n│    \"\"\"Model for tracking individual trade executions\"\"\"\n⋮\n│    def to_dict(self) -> Dict:\n⋮\n│def ensure_trade_log_tables_exist():\n⋮\n│def get_trade_log_session():\n⋮\n\nmodels\\trigger_repository.py:\n⋮\n│class TriggerTimeframe(Base):\n⋮\n│class Trigger(Base):\n⋮\n│class TriggerRepository:\n│    def __init__(self, session: SQLAlchemySession, trigger_strategy=None):  # Add trigger_strategy \n│        self.session = session\n│        self.logger = logging.getLogger(__name__)\n│        self.trigger_strategy = trigger_strategy  # Store the strategy\n⋮\n│    def get_latest_triggers(self, symbol: str, interval_name: str = None, limit: int = 2) -> List[T\n⋮\n│    def _convert_to_datetime(self, date_value):\n⋮\n\nmonitor_data_flow.py:\n⋮\n│def parse_log_line(line):\n⋮\n│def get_color_for_message(message):\n⋮\n│def format_json_in_message(message):\n⋮\n│def monitor_log_file(log_file, follow=True, filter_symbol=None, filter_interval=None, max_lines=Non\n⋮\n\nreset_scheduling.py:\n⋮\n│def reset_scheduling_table():\n⋮\n\nrun_backtest.py:\n⋮\n│def parse_arguments():\n⋮\n\nscripts\\check_indicator_data.py:\n⋮\n│def check_indicator_data():\n⋮\n\nscripts\\check_live_indicators_detailed.py:\n⋮\n│def check_live_indicators_detailed():\n⋮\n\nscripts\\debug_live_indicator_service.py:\n⋮\n│class DebugLiveIndicatorService(LiveIndicatorService):\n│    \"\"\"Enhanced version of LiveIndicatorService with more detailed logging\"\"\"\n│\n⋮\n│    async def store_indicator_data(self, symbol: str, interval: str, data: dict, is_complete_candle\n⋮\n\nscripts\\debug_live_indicators.py:\n⋮\n│async def test_direct_db_write():\n⋮\n\nscripts\\debug_try_store_indicator_data.py:\n⋮\n│class MockCandleDataService:\n│    \"\"\"Mock candle data service\"\"\"\n│    def get_candle_data(self, symbol, interval):\n⋮\n│class MockTechnicalAnalysisService:\n⋮\n│class MockTargetService:\n⋮\n│class MockMarketRegimeService:\n│    \"\"\"Mock market regime service\"\"\"\n│    def get_market_regime(self, symbol):\n⋮\n│class DebugLiveIndicatorService(LiveIndicatorService):\n⋮\n\nscripts\\initialize_components.py:\n⋮\n│class MockCandleDataService:\n│    \"\"\"Mock candle data service that returns predefined data\"\"\"\n⋮\n│    async def get_candle_data(self, symbol):\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│class MockTechnicalAnalysisService:\n│    \"\"\"Mock technical analysis service that returns predefined data\"\"\"\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│    async def process_symbol(self, symbol):\n⋮\n│class MockTargetService:\n│    \"\"\"Mock target service that returns predefined data\"\"\"\n⋮\n│    async def get_targets(self, symbol):\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│    async def process_symbol(self, symbol, symbols_config=None):\n⋮\n│class MockMarketRegimeService:\n│    \"\"\"Mock market regime service that returns predefined data\"\"\"\n⋮\n│    def get_market_regime(self, symbol):\n⋮\n│async def initialize_components():\n⋮\n\nscripts\\insert_test_record.py:\n⋮\n│def insert_test_record():\n⋮\n\nscripts\\monitor_events.py:\n⋮\n│async def event_callback(*args, **kwargs):\n⋮\n│async def monitor_events():\n⋮\n\nscripts\\monitor_live_indicator_events.py:\n⋮\n│async def monitor_events():\n⋮\n\nscripts\\print_indicator_tables.py:\n⋮\n│def get_table_info(db_path, table_name):\n⋮\n│def print_table_data(db_path, table_name, limit=10, where_clause=None):\n⋮\n\nscripts\\recreate_backtest_indicator_table.py:\n⋮\n│def parse_arguments():\n⋮\n\nscripts\\register_mock_components.py:\n⋮\n│class MockCandleDataService:\n│    \"\"\"Mock candle data service\"\"\"\n│    def get_candle_data(self, symbol, interval):\n⋮\n│class MockTechnicalAnalysisService:\n⋮\n│class MockTargetService:\n⋮\n│class MockMarketRegimeService:\n│    \"\"\"Mock market regime service\"\"\"\n│    def get_market_regime(self, symbol):\n⋮\n\nscripts\\test_database.py:\n⋮\n│def test_database_connection():\n⋮\n\nscripts\\test_fixes.py:\n⋮\n│async def test_event_system():\n⋮\n\nscripts\\test_live_indicators_with_mock_data.py:\n⋮\n│class MockCandleDataService:\n│    \"\"\"Mock candle data service that returns predefined data\"\"\"\n⋮\n│    async def get_candle_data(self, symbol):\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│class MockTechnicalAnalysisService:\n│    \"\"\"Mock technical analysis service that returns predefined data\"\"\"\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│    async def process_symbol(self, symbol):\n⋮\n│class MockTargetService:\n│    \"\"\"Mock target service that returns predefined data\"\"\"\n⋮\n│    async def get_targets(self, symbol):\n⋮\n│    async def initialize_symbol(self, symbol):\n⋮\n│    async def process_symbol(self, symbol, symbols_config=None):\n⋮\n│class MockMarketRegimeService:\n│    \"\"\"Mock market regime service that returns predefined data\"\"\"\n⋮\n│    def get_market_regime(self, symbol):\n⋮\n\nscripts\\view_backtest_indicators.py:\n⋮\n│def parse_arguments():\n⋮\n\nservices\\backtest_indicator_service.py:\n⋮\n│class BacktestIndicatorService:\n│    \"\"\"\n│    Service for storing and retrieving indicator data during backtests.\n⋮\n│    def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any]) -> bool:\n⋮\n│    def get_indicator_data(self, symbol: str, interval: str,\n│                          from_date: datetime, to_date: Optional[datetime] = None,\n⋮\n\nservices\\candle_data_service.py:\n⋮\n│class CandleDataService:\n│    def __init__(self, price_manager, event_system=None):\n│        self.price_manager = price_manager\n│        self.historical_data = {}\n⋮\n│    async def initialize_symbol(self, symbol: str) -> bool:\n⋮\n│    async def get_candle_data(self, symbol: str, price_key: str = None) -> dict:\n⋮\n\nservices\\event_system.py:\n⋮\n│class DataEventSystem:\n│    _instance = None\n│    def __new__(cls):\n⋮\n│    def subscribe(self, event_or_symbol: str, callback: Callable) -> None:\n⋮\n│    def publish(self, event_name: str, *args, **kwargs) -> None:\n⋮\n\nservices\\live_indicator_service.py:\n⋮\n│class LiveIndicatorService:\n│    \"\"\"\n│    Service for storing and retrieving indicator data during live trading.\n⋮\n│    async def store_indicator_data(self, symbol: str, interval: str, data: Dict[str, Any], is_compl\n│        \"\"\"\n│        Store indicator data for a symbol and interval\n│\n│        Args:\n│            symbol: Trading symbol (e.g., 'GBPUSD')\n│            interval: Timeframe interval (e.g., '1h')\n│            data: Dictionary containing all indicator values\n│            is_complete_candle: Whether this is a complete candle or a partial update\n│\n│        Returns:\n⋮\n│        try:\n│            print(f\"DEBUG: store_indicator_data called for {symbol} {interval}\")\n⋮\n│            def get_value(key_prefix, default=0.0):\n⋮\n│    def get_indicator_data(self, symbol: str, interval: str,\n│                          from_date: datetime, to_date: Optional[datetime] = None,\n⋮\n│    def get_storage_statistics(self) -> Dict[str, Any]:\n⋮\n\nservices\\market_regime_service.py:\n⋮\n│class MarketRegimeService:\n│    def __init__(self, candle_data_service: CandleDataService, event_system: DataEventSystem):\n│        self.candle_data_service = candle_data_service\n│        self.event_system = event_system\n│        self.market_regimes = {}\n│        self.version = \"1.0.0\"\n│\n│        # Default parameters\n│        self.volatility_window = 14\n│        self.linear_reg_period = 20\n│        self.ranging_sensitivity = 4\n│\n⋮\n│    def get_market_regime(self, symbol: str, interval: str) -> Dict[str, Any]:\n⋮\n│    def analyze_market_regime(self, symbol: str, interval: str, config_symbol: str = None, current_\n⋮\n\nservices\\mock_technical_analysis_service.py:\n⋮\n│class MockTechnicalAnalysisService:\n│    \"\"\"\n│    Mock version of TechnicalAnalysisService that returns predefined values\n│    without doing any actual calculations.\n⋮\n│    async def initialize_symbols(self, symbols_config):\n⋮\n│    async def process_symbol(self, symbol, interval=None, current_time=None):\n⋮\n│    def _get_default_indicators(self):\n⋮\n\nservices\\target_service.py:\n⋮\n│class TargetService:\n│    def __init__(self, session: Session):\n│        self.trigger_repository = TriggerRepository(session)\n│        self.trigger_strategy = TriggerStrategy(self.trigger_repository)\n│        self.target_data_service = TargetDataService()\n│        self.event_system = DataEventSystem()\n│\n│        # Cache for target calculations - no expiration, purely event-driven\n│        self.target_cache = {}\n│\n│        class MinimalPriceManager:\n⋮\n│    async def initialize_symbol(self, symbol: str) -> None:\n⋮\n│    async def initialize_symbols(self, symbols_config: Dict):\n⋮\n│    async def process_symbol(self, symbol: str, symbols_config: Dict, current_time: Optional[dateti\n⋮\n│    async def get_targets(self, symbol: str, force_recalculate: bool = False) -> Optional[Dict[str,\n⋮\n\nservices\\technical_analysis_service.py:\n⋮\n│class TechnicalAnalysisService:\n│    \"\"\"\n│    Service for calculating technical indicators that combine data from\n│    both candle data and target data sources.\n⋮\n│    async def initialize_symbol(self, symbol: str) -> None:\n⋮\n│    async def initialize_symbols(self, symbols_config: Dict):\n⋮\n│    async def process_symbol(self, symbol: str, targets_data: Optional[Dict] = None, current_time: \n⋮\n\nservices\\trade_logger.py:\n⋮\n│class TradeLogger:\n│    \"\"\"Service for logging trade information\"\"\"\n│\n⋮\n│    def log_trade_exit(self,\n│                       trade_id: str,\n│                       exit_price: float,\n│                       pnl: Optional[float] = None,\n│                       pnl_percentage: Optional[float] = None,\n│                       pnl_pips: Optional[float] = None,\n⋮\n│    def get_trade_by_id(self, trade_id: str) -> Optional[TradeLog]:\n⋮\n│    def calculate_pnl(self, trade_id: str, exit_price: float) -> Dict:\n⋮\n│    def get_performance_stats(self, symbol: Optional[str] = None,\n│                             start_date: Optional[datetime] = None,\n⋮\n\nstrategy\\base.py:\n⋮\n│@dataclass\n│class TradeDetails:\n⋮\n\nstrategy\\eur_strategy.py:\n⋮\n│def get_default_strategy_parameters() -> dict:\n⋮\n│class EURConditionStrategy(BaseStrategyWithRetry):\n│    def __init__(self):\n│        super().__init__()\n│        self.logger = logging.getLogger(__name__)\n│        self.trading_symbol = \"EURUSD\"\n│        self.timeframes = [\"1h\"]\n│        self.tf = \"1h\"\n⋮\n│    def print_data(self, data, title=\"\"):\n⋮\n\nstrategy\\gbp_strategy.py:\n⋮\n│def get_default_strategy_parameters() -> dict:\n⋮\n│class GBPConditionStrategy(BaseStrategyWithRetry):\n│    def __init__(self):\n│        super().__init__()\n│        self.logger = logging.getLogger(__name__)\n│        self.trading_symbol = \"GBPUSD\"\n│        self.timeframes = [\"1h\"]\n│        self.tf = \"1h\"\n⋮\n│    def print_data(self, data, title=\"\"):\n⋮\n\nstrategy\\price_target_calculator.py:\n⋮\n│@dataclass\n│class TargetPrice:\n⋮\n│@dataclass\n│class CombinedTarget:\n⋮\n│class TargetResult(NamedTuple):\n⋮\n\nstrategy\\trigger_strategy.py:\n⋮\n│class TriggerStrategy:\n│    def __init__(self, trigger_repository: TriggerRepository):\n│        self.trigger_repository = trigger_repository\n│        self.trigger_repository.trigger_strategy = self  # Set self as the strategy\n⋮\n│    def _timeframe_obj_to_dict(self, tf_obj) -> Dict:\n⋮\n│    def initialize_symbols(self, symbols_config: Dict):\n⋮\n\nstrategy\\xau_strategy.py:\n⋮\n│class XAUConditionStrategy(BaseStrategyWithRetry):\n│    def __init__(self):\n│        super().__init__()\n│        self.logger = logging.getLogger(__name__)\n│        self.trading_symbol = \"XAUUSD\"\n│        self.timeframes = [\"1h\"]\n│        self.tf = \"1h\"\n│        self.prefix = 'gold'\n⋮\n│    def print_data(self, data, title=\"\"):\n⋮\n\ntest_trade_logs.py:\n⋮\n│def test_trade_logs():\n⋮\n\ntest_tv_connection.py:\n⋮\n│def test_tv_connection():\n⋮\n\ntests\\test_event_system_coroutines.py:\n⋮\n│async def test_event_system():\n⋮\n\nupdate_scheduling.py:\n⋮\n│def update_scheduling():\n⋮\n\nutils\\backtest_results copy.py:\n⋮\n│class BacktestResultsCollector:\n│    \"\"\"\n│    Collects and analyzes backtest results.\n│    Provides performance metrics and visualization.\n⋮\n│    def calculate_metrics(self):\n⋮\n│    def generate_report(self, output_dir: str = 'backtest_results'):\n⋮\n\nutils\\backtest_results.py:\n⋮\n│class BacktestResultsCollector:\n│    \"\"\"\n│    Collects and analyzes backtest results.\n│    Provides performance metrics and visualization.\n⋮\n│    def calculate_metrics(self):\n⋮\n│    def generate_report(self, output_dir: str = 'backtest_results'):\n⋮\n\nutils\\clock.py:\n⋮\n│class TradingClock:\n│    \"\"\"\n│    Provides a consistent time interface for both live and backtest modes.\n│    In live mode, returns the actual system time.\n│    In backtest mode, returns a simulated time that can be advanced programmatically.\n⋮\n│    def now(self) -> datetime:\n⋮\n\nutils\\components_registry.py:\n⋮\n│def register_component(name: str, component: Any) -> None:\n⋮\n│def get_component(name: str) -> Optional[Any]:\n⋮\n\nutils\\config_manager.py:\n⋮\n│class ConfigManager:\n│    \"\"\"\n│    A class to manage configuration settings for the trading system\n⋮\n│    def load_config(self) -> Dict[str, Any]:\n⋮\n│    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:\n⋮\n│    def get(self, key: str, default: Any = None) -> Any:\n⋮\n│    def set(self, key: str, value: Any) -> bool:\n⋮\n│    def reset(self) -> bool:\n⋮\n│    def _deep_update(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:\n⋮\n│config_manager = ConfigManager()\n\nutils\\config_utils.py:\n⋮\n│class ConfigStore:\n│    \"\"\"Singleton class to store and manage symbol configurations\"\"\"\n⋮\n│    def __new__(cls):\n⋮\n│    @classmethod\n│    def set_config(cls, config: Dict) -> None:\n⋮\n│    @classmethod\n│    def resolve_symbols(cls, symbol: str) -> Tuple[str, str, str]:\n⋮\n│def init_config_store(symbols_config: Dict) -> None:\n⋮\n\nutils\\dashboard.py:\n⋮\n│class TradingDashboard:\n│    \"\"\"\n│    A real-time dashboard for displaying critical trading information\n⋮\n│    def start(self):\n⋮\n│    def update_trades(self, trades):\n⋮\n\nutils\\dashboard_manager.py:\n⋮\n│class DashboardManager:\n│    \"\"\"\n│    Manager class to handle dashboard operations and data collection\n⋮\n│    def start(self, symbols=None, mode=None, debug=False):\n⋮\n│    def is_enabled(self) -> bool:\n⋮\n\nutils\\data_flow_logger.py:\n⋮\n│class DataFlowLogger:\n│    \"\"\"\n│    A specialized logger for tracking data flow through the system.\n│    Provides clean, structured logging of market data updates, processing steps,\n│    and indicator calculations with enhanced traceability.\n⋮\n│    def _format_value(self, value):\n⋮\n│    def _format_dict(self, data_dict):\n⋮\n│    def generate_correlation_id(self, symbol, interval):\n⋮\n│    def log_candle_data(self, symbol, interval, candle_data, source=\"Unknown\", is_latest=False, ser\n⋮\n│    def log_event_published(self, event_name, symbol, interval, data=None):\n⋮\n│    def log_calculation_start(self, service, symbol, interval):\n⋮\n│    def log_error(self, symbol, interval, error_message, context=None):\n⋮\n│    def log_database_connection(self, db_path, connection_type, status, details=None):\n⋮\n\nutils\\debug_utils.py:\n⋮\n│class DebugManager:\n│    \"\"\"\n│    Manager class to handle debugging operations when dashboard is disabled\n⋮\n│    def start(self):\n⋮\n│    def is_enabled(self) -> bool:\n⋮\n\nutils\\logger.py:\n│def log(message):\n⋮\n\nutils\\mode_manager.py:\n⋮\n│class ModeManager:\n│    \"\"\"\n│    Controls whether the system runs in live or backtest mode.\n│    Implemented as a singleton to ensure consistent mode across all components.\n⋮\n│    def __new__(cls):\n⋮\n│    def _initialize(self):\n⋮\n│    def set_backtest_mode(self, start_date: datetime, end_date: datetime, db_path: Optional[str] = \n⋮\n│    def set_live_mode(self):\n⋮\n│    def is_backtest_mode(self) -> bool:\n⋮\n│    def get_backtest_dates(self) -> Tuple[Optional[datetime], Optional[datetime]]:\n⋮\n│    def get_backtest_db_path(self) -> str:\n⋮\n│    def set_backtest_speed(self, speed_multiplier: float):\n⋮\n│    def get_backtest_speed(self) -> float:\n⋮\n\nutils\\mt5_utils.py:\n⋮\n│class MT5Manager:\n│    def __init__(self):\n│        self._monitor_task = None\n│        self._is_running = False\n⋮\n│    def initialize_mt5(self) -> bool:\n⋮\n│    def ensure_connection(self) -> bool:\n⋮\n│    async def stop_monitoring(self):\n⋮\n│    def get_account_balance(self):\n⋮\n│    def get_symbol_prices(self, symbols_config):\n⋮\n│def initialize_mt5():\n⋮\n│def get_account_balance():\n⋮\n│def get_symbol_prices():\n⋮\n│async def stop_monitoring():\n⋮\n\nutils\\notification_service.py:\n⋮\n│def log(message):\n⋮\n│class TelegramManager:\n│    def __init__(self, bot_token, chat_id):\n│        self.bot_token = bot_token\n│        self.chat_id = chat_id\n│        self.bot = telegram.Bot(token=bot_token) \n│        self.message_sent_w = False\n│        self.message_sent = False\n│        self.message_sent_positions = False\n⋮\n│    async def send_message(self, app, message, max_retries=3):\n⋮\n│    async def notify_success(self, app, message):\n⋮\n│    async def notify_failure(self, app, message):\n⋮\n\nutils\\performance_analyzer.py:\n⋮\n│class PerformanceAnalyzer:\n│    \"\"\"\n│    Advanced performance metrics calculator for trading strategies\n⋮\n│    def calculate_metrics_from_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Union[str, f\n⋮\n│    def calculate_metrics_from_db(self, start_date: datetime, end_date: datetime) -> Dict[str, Unio\n⋮\n\nutils\\progress.py:\n⋮\n│class ProgressIndicator:\n⋮\n\nutils\\terminal_logger.py:\n⋮\n│class TerminalLogger:\n│    \"\"\"\n│    Enhanced terminal logger with improved formatting and organization.\n│\n│    Features:\n│    - Different log levels with color coding\n│    - Structured output for complex data\n│    - Section headers and separators\n│    - Configurable verbosity\n⋮\n│    def _format_message(self, level: LogLevel, message: str) -> str:\n⋮\n│    def _format_data(self, data: Union[Dict[str, Any], List], indent: int = 0) -> str:\n⋮\n│    def log(self, level: LogLevel, message: str):\n⋮\n│    def error(self, message: str):\n⋮\n│    def warning(self, message: str):\n⋮\n│    def info(self, message: str):\n⋮\n│    def debug(self, message: str):\n⋮\n│def set_log_level(level: LogLevel):\n⋮\n\nutils\\theme_manager.py:\n⋮\n│class ThemeManager:\n│    \"\"\"\n│    A class to manage color themes for the terminal output\n⋮\n│    def get_color(self, category: str) -> Tuple[str, str]:\n⋮\n│    def get_reset(self) -> str:\n⋮\n│    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:\n⋮\n│theme_manager = ThemeManager()\n\nutils\\trade_log_utils.py:\n⋮\n│def get_trade_logs(symbol: Optional[str] = None,\n│                  days: Optional[int] = None,\n⋮\n│def print_trade_summary(symbol: Optional[str] = None,\n⋮\n│def plot_trade_performance(symbol: Optional[str] = None,\n│                          days: Optional[int] = None,\n⋮\n│def print_trade_logs(symbol: Optional[str] = None,\n│                    days: Optional[int] = None,\n│                    status: Optional[str] = None,\n⋮\n\nutils\\trades_dashboard.py:\n⋮\n│def format_number(num: float) -> str:\n⋮\n\nutils\\uptime_tracker.py:\n⋮\n│class UptimeTracker:\n│    \"\"\"\n│    Tracks system uptime since initialization\n⋮\n│    def __new__(cls):\n⋮\n│    def _initialize(self):\n⋮\n│    def start(self):\n⋮\n\nutils\\web_dashboard_manager.py:\n⋮\n│class WebDashboardManager:\n│    \"\"\"\n│    Manager class to handle web dashboard operations (FastAPI version)\n⋮\n│    def initialize(self, event_system):\n⋮\n│    def start(self, host=None, port=None, update_interval=None, **kwargs): # Removed unused args fo\n⋮\n\nweb\\server.py:\n⋮\n│def get_indicator_data_from_db():\n⋮\n│def update_clients():\n⋮\n\nweb\\server_fastapi.py:\n⋮\n│async def get_db_connection(db_path: str) -> aiosqlite.Connection:\n⋮\n│async def get_indicator_data_from_db_async(limit: int = 10) -> List[Dict[str, Any]]:\n⋮\n│async def get_trades_from_db_async(days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:\n⋮\n│def serialize_value(value: Any) -> Any:\n⋮\n│async def get_dashboard_data() -> Dict[str, Any]:\n⋮\n│async def get_current_user(request: Request) -> Optional[str]:\n⋮\n│async def dashboard_update_task():\n⋮\n│async def start_server_async(host: str = SERVER_HOST, port: int = SERVER_PORT, update_interval: flo\n⋮\n│async def stop_server_async():\n⋮\n\nweb\\simple_server.py:\n⋮\n│def get_indicator_data_from_db():\n⋮\n\nweb\\static\\js\\dashboard.js:\n⋮\n│function startPolling() {\n│    if (!pollingInterval) {\n│        pollingInterval = setInterval(fetchDashboardData, 1000);\n│        fetchDashboardData(); // Initial fetch\n│    }\n⋮\n│function formatCurrency(value) {\n│    return new Intl.NumberFormat('en-US', {\n│        style: 'currency',\n│        currency: 'USD'\n│    }).format(value);\n⋮\n│function formatNumber(value, decimals = 5) {\n│    return parseFloat(value).toFixed(decimals);\n⋮\n│function updateDashboard(data) {\n│    console.log('updateDashboard called with data:', data);\n│\n│    // Check if data contains trades\n│    if (data.trades) {\n│        console.log('Data contains trades array with length:', data.trades.length);\n│        if (data.trades.length > 0) {\n│            console.log('First trade in data:', data.trades[0]);\n│        }\n│    } else {\n⋮\n"}, {"role": "assistant", "content": "Ok, I won't try and edit those files without asking first."}, {"role": "user", "content": "What does the position function/class do?"}, {"role": "user", "content": "What does the quantity function/class do?"}, {"role": "user", "content": "What does the calculate function/class do?"}]