{"Debugging Task": {"task": "Debug memory leak in test function", "task_type": "debugging", "context_bundle": [{"id": "test_module.TestClass", "type": "class", "file": "test_module.py", "code": "# Code for TestClass", "score_breakdown": {"criticality": 1.0, "change_risk": 1.0, "task_relevance": 0.08333333333333333, "confidence_gap": 0.5, "dependency_proximity": 1.0, "complexity": 0.1, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.6616666666666667}, {"id": "test_module.test_function", "type": "function", "file": "test_module.py", "code": "# Code for test_function", "score_breakdown": {"criticality": 1.0, "change_risk": 0.6, "task_relevance": 0.2833333333333333, "confidence_gap": 0.5, "dependency_proximity": 1.0, "complexity": 0.2, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.6466666666666666}, {"id": "test_module.helper_function", "type": "function", "file": "test_module.py", "code": "# Code for helper_function", "score_breakdown": {"criticality": 0.6, "change_risk": 0.2, "task_relevance": 0.13333333333333333, "confidence_gap": 0.5, "dependency_proximity": 1.0, "complexity": 0.1, "doc_gap": 1.0, "historical_relevance": 0.0}, "total_score": 0.4666666666666667}], "selected_by": "ContextBundleBuilder(v2.0.1)", "token_estimate": 256, "selection_timestamp": "2025-05-28T22:56:08.545838+00:00", "selection_rationale": "Enhanced Context Selection for debugging:\n- Selected 3 entities based on multi-factor scoring\n- 0 high-relevance entities (score >= 6.0)\n- 2 critical entities included\n- Memory-aware selection with confidence gap analysis\n- Task-specific weighting optimized for debugging", "score_distribution": {"0-2": 3, "2-4": 0, "4-6": 0, "6-8": 0, "8-10": 0}, "confidence_analysis": {"average_confidence": 0.5, "min_confidence": 0.5, "max_confidence": 0.5, "entities_analyzed": 3}, "memory_insights": []}, "Feature Development": {"task": "Add new helper functionality", "task_type": "feature_development", "context_bundle": [{"id": "test_module.test_function", "type": "function", "file": "test_module.py", "code": "# Code for test_function", "score_breakdown": {"criticality": 1.0, "change_risk": 0.6, "task_relevance": 0.0, "confidence_gap": 0.5, "dependency_proximity": 1.0, "complexity": 0.2, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.545}, {"id": "test_module.TestClass", "type": "class", "file": "test_module.py", "code": "# Code for TestClass", "score_breakdown": {"criticality": 1.0, "change_risk": 1.0, "task_relevance": 0.0, "confidence_gap": 0.5, "dependency_proximity": 0.5, "complexity": 0.1, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.545}, {"id": "test_module.helper_function", "type": "function", "file": "test_module.py", "code": "# Code for helper_function", "score_breakdown": {"criticality": 0.6, "change_risk": 0.2, "task_relevance": 0.125, "confidence_gap": 0.5, "dependency_proximity": 1.0, "complexity": 0.1, "doc_gap": 1.0, "historical_relevance": 0.0}, "total_score": 0.42625}], "selected_by": "ContextBundleBuilder(v2.0.1)", "token_estimate": 256, "selection_timestamp": "2025-05-28T22:56:08.568849+00:00", "selection_rationale": "Enhanced Context Selection for feature_development:\n- Selected 3 entities based on multi-factor scoring\n- 0 high-relevance entities (score >= 6.0)\n- 2 critical entities included\n- Memory-aware selection with confidence gap analysis\n- Task-specific weighting optimized for feature_development", "score_distribution": {"0-2": 3, "2-4": 0, "4-6": 0, "6-8": 0, "8-10": 0}, "confidence_analysis": {"average_confidence": 0.5, "min_confidence": 0.5, "max_confidence": 0.5, "entities_analyzed": 3}, "memory_insights": []}, "Documentation Task": {"task": "Improve documentation coverage", "task_type": "documentation", "context_bundle": [{"id": "test_module.TestClass", "type": "class", "file": "test_module.py", "code": "# Code for TestClass", "score_breakdown": {"criticality": 1.0, "change_risk": 1.0, "task_relevance": 0.06666666666666667, "confidence_gap": 0.5, "dependency_proximity": 0.0, "complexity": 0.1, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.42166666666666663}, {"id": "test_module.test_function", "type": "function", "file": "test_module.py", "code": "# Code for test_function", "score_breakdown": {"criticality": 1.0, "change_risk": 0.6, "task_relevance": 0.0, "confidence_gap": 0.5, "dependency_proximity": 0.0, "complexity": 0.2, "doc_gap": 0.5, "historical_relevance": 0.0}, "total_score": 0.37}, {"id": "test_module.helper_function", "type": "function", "file": "test_module.py", "code": "# Code for helper_function", "score_breakdown": {"criticality": 0.6, "change_risk": 0.2, "task_relevance": 0.0, "confidence_gap": 0.5, "dependency_proximity": 0.0, "complexity": 0.1, "doc_gap": 1.0, "historical_relevance": 0.0}, "total_score": 0.365}], "selected_by": "ContextBundleBuilder(v2.0.1)", "token_estimate": 256, "selection_timestamp": "2025-05-28T22:56:08.585838+00:00", "selection_rationale": "Enhanced Context Selection for documentation:\n- Selected 3 entities based on multi-factor scoring\n- 0 high-relevance entities (score >= 6.0)\n- 2 critical entities included\n- Memory-aware selection with confidence gap analysis\n- Task-specific weighting optimized for documentation", "score_distribution": {"0-2": 3, "2-4": 0, "4-6": 0, "6-8": 0, "8-10": 0}, "confidence_analysis": {"average_confidence": 0.5, "min_confidence": 0.5, "max_confidence": 0.5, "entities_analyzed": 3}, "memory_insights": []}}