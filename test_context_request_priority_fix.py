#!/usr/bin/env python3

"""
Test script to verify that the CONTEXT_REQUEST protocol is now properly prioritized
over REQUEST_FILE in the LLM instructions.
"""

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

def test_repo_messages_structure():
    """Test that get_repo_messages() no longer contains conflicting instructions."""

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repomap import RepoMap

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create a coder instance
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000
        )

        # Get the repo messages
        repo_messages = coder.get_repo_messages()

        print("=== REPO MESSAGES STRUCTURE ===")
        print(f"Number of messages: {len(repo_messages)}")

        for i, msg in enumerate(repo_messages):
            print(f"\nMessage {i+1} ({msg['role']}):")
            content = msg['content']
            if len(content) > 200:
                print(f"  Content: {content[:200]}...")
            else:
                print(f"  Content: {content}")

        # If there are no repo messages, that might be because there's no repo map
        if len(repo_messages) == 0:
            print("\n⚠️  No repo messages found - this might be because there's no repository map available")
            print("   This is expected in a test environment without a proper git repository")
            return True

        # Check that the assistant response is now minimal and doesn't contain conflicting instructions
        if len(repo_messages) >= 2:
            assistant_msg = repo_messages[1]
            if assistant_msg['role'] == 'assistant':
                content = assistant_msg['content']

                # The new assistant response should be short and focused
                if len(content) < 200 and "CONTEXT_REQUEST" in content and "primary method" in content:
                    print("\n✅ SUCCESS: Assistant response is now concise and emphasizes CONTEXT_REQUEST")
                    return True
                else:
                    print(f"\n❌ ISSUE: Assistant response is still too detailed or doesn't emphasize CONTEXT_REQUEST properly")
                    print(f"Content: {content}")
                    return False

        print("\n❌ ISSUE: Expected structure not found")
        return False

    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repo_content_prefix_structure():
    """Test that repo_content_prefix still has the correct CONTEXT_REQUEST prioritization."""

    try:
        from aider.coders.base_prompts import CoderPrompts

        prompts = CoderPrompts()
        repo_content_prefix = prompts.repo_content_prefix

        print("\n=== REPO CONTENT PREFIX ANALYSIS ===")

        # Check that CONTEXT_REQUEST is mentioned as PREFERRED
        if "PREFERRED" in repo_content_prefix and "CONTEXT_REQUEST" in repo_content_prefix:
            print("✅ CONTEXT_REQUEST is marked as PREFERRED")
        else:
            print("❌ CONTEXT_REQUEST is not marked as PREFERRED")
            return False

        # Check that REQUEST_FILE is mentioned as SECONDARY
        if "SECONDARY" in repo_content_prefix and "REQUEST_FILE" in repo_content_prefix:
            print("✅ REQUEST_FILE is marked as SECONDARY")
        else:
            print("❌ REQUEST_FILE is not marked as SECONDARY")
            return False

        # Check the order - CONTEXT_REQUEST should come before REQUEST_FILE
        context_pos = repo_content_prefix.find("CONTEXT_REQUEST")
        request_file_pos = repo_content_prefix.find("REQUEST_FILE")

        if context_pos < request_file_pos and context_pos != -1 and request_file_pos != -1:
            print("✅ CONTEXT_REQUEST appears before REQUEST_FILE in instructions")
        else:
            print("❌ CONTEXT_REQUEST does not appear before REQUEST_FILE")
            return False

        return True

    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all tests to verify the fix."""

    print("Testing CONTEXT_REQUEST Priority Fix")
    print("=" * 50)

    test1_passed = test_repo_content_prefix_structure()
    test2_passed = test_repo_messages_structure()

    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"  Repo Content Prefix Structure: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"  Repo Messages Structure: {'✅ PASS' if test2_passed else '❌ FAIL'}")

    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! The fix should resolve the CONTEXT_REQUEST priority issue.")
        return True
    else:
        print("\n❌ Some tests failed. The issue may not be fully resolved.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
