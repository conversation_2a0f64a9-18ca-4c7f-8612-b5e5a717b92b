#!/usr/bin/env python3
"""
Test script to verify that repo_content_prefix is now included in Smart Map Request responses.
"""

import os
import sys
import json

def test_smart_map_with_repo_content_prefix():
    """Test that Smart Map Request responses include repo_content_prefix."""
    print("🧪 Testing Smart Map Request with repo_content_prefix")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created")
        
        # Test MAP_REQUEST processing
        test_map_request = '''I need to understand how the coder works.
        
        {MAP_REQUEST: {"keywords": ["coder", "base"], "type": "implementation", "scope": "all", "max_results": 5}}
        '''
        
        print(f"\n🔍 Testing MAP_REQUEST processing...")
        print(f"Request: {test_map_request}")
        
        # Process the map request
        cleaned_content, augmented_prompt = coder.process_map_requests(test_map_request, test_map_request)
        
        if augmented_prompt:
            print(f"\n✅ MAP_REQUEST was processed successfully!")
            print(f"Augmented prompt length: {len(augmented_prompt)} characters")
            
            # Check if repo_content_prefix is included
            if "🎮 Repository Map:" in augmented_prompt:
                print("✅ repo_content_prefix IS included in Smart Map response!")
                print("   → The fix is working correctly")
                
                # Show the beginning of the response to verify
                lines = augmented_prompt.split('\n')[:10]
                print(f"\n📋 Response preview (first 10 lines):")
                for i, line in enumerate(lines, 1):
                    print(f"  {i:2d}: {line}")
                
                return True
            else:
                print("❌ repo_content_prefix is NOT included in Smart Map response")
                print("   → The fix may not be working")
                
                # Show the beginning of the response for debugging
                lines = augmented_prompt.split('\n')[:10]
                print(f"\n📋 Response preview (first 10 lines):")
                for i, line in enumerate(lines, 1):
                    print(f"  {i:2d}: {line}")
                
                return False
        else:
            print("❌ MAP_REQUEST was not processed (no augmented prompt returned)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Smart Map Request: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repo_content_prefix_content():
    """Test the current repo_content_prefix content."""
    print("\n🔍 Checking current repo_content_prefix content...")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        repo_content_prefix = prompts.repo_content_prefix
        
        print(f"repo_content_prefix content:")
        print("-" * 40)
        print(repr(repo_content_prefix))  # Use repr to show exact content including newlines
        print("-" * 40)
        
        # Check if it contains the key content we expect
        if "🎮 Repository Map:" in repo_content_prefix:
            print("✅ Contains expected game-style header")
        else:
            print("❌ Missing expected game-style header")
            
        if "LEVEL 2" in repo_content_prefix:
            print("✅ Contains LEVEL 2 reference")
        else:
            print("❌ Missing LEVEL 2 reference")
        
        return repo_content_prefix
        
    except Exception as e:
        print(f"❌ Error checking repo_content_prefix: {e}")
        return None

def main():
    """Main test function."""
    print("🧪 REPO_CONTENT_PREFIX IN SMART MODE TEST")
    print("=" * 60)
    
    # Test 1: Check repo_content_prefix content
    repo_content_prefix = test_repo_content_prefix_content()
    
    # Test 2: Test Smart Map Request with repo_content_prefix
    smart_map_success = test_smart_map_with_repo_content_prefix()
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 60)
    
    if smart_map_success:
        print("🎯 SUCCESS: repo_content_prefix is now included in Smart Map responses!")
        print("   → The LLM will receive the game instructions with repository maps")
        print("   → This should help fix the 'I can only use the code I have' issue")
    else:
        print("❌ FAILURE: repo_content_prefix is not being included in Smart Map responses")
        print("   → The fix may need additional work")
    
    if repo_content_prefix:
        print(f"   → repo_content_prefix is {len(repo_content_prefix)} characters long")

if __name__ == "__main__":
    main()
