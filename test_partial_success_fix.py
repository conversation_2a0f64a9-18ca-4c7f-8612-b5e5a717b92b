#!/usr/bin/env python3

"""
Test to verify that the partial success fix works correctly.
This test verifies that when some symbols are found and some are not,
the system provides partial success rather than complete failure.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_partial_success_context_request():
    """Test that context request system provides partial success when some symbols are found."""
    
    print("🧪 Testing Partial Success Context Request Fix")
    print("=" * 60)
    
    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create the trading project structure
        trading_dir = os.path.join(temp_dir, "live_backtest_dashboard")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)
        
        # Initialize a git repository in the trading project
        import subprocess
        subprocess.run(["git", "init"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=trading_dir, capture_output=True)
        
        # Create the actual trading files
        # File 1: position_observer.py - WITHOUT close_position_based_on_conditions
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    def observe_position_open(self, app):
        """Observe when a position is opened."""
        return True
        
    def observe_position_close(self, app):
        """Observe when a position is closed."""
        return True
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        # File 2: position_exit_manager.py - WITH close_position_based_on_conditions
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")
        
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        # Add files to git
        subprocess.run(["git", "add", "."], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=trading_dir, capture_output=True)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path} (WITHOUT close_position_based_on_conditions)")
        print(f"   📄 {position_exit_manager_path} (WITH close_position_based_on_conditions)")
        
        # Test the context request system
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
            from aider.context_request.aider_context_request_integration import AiderContextRequestIntegration
            
            print("✅ Successfully imported context request modules")
            
            # Create the context request integration
            integration = AiderContextRequestIntegration(trading_dir)
            
            # Create a context request that asks for the function in BOTH files
            # (simulating the real scenario where LLM incorrectly assumes it exists in both)
            symbol1 = SymbolRequest(
                type="method_definition",
                name="close_position_based_on_conditions",
                file_hint="services/position_observer.py"  # This will NOT be found
            )
            
            symbol2 = SymbolRequest(
                type="method_definition", 
                name="close_position_based_on_conditions",
                file_hint="trade_management/position_exit_manager.py"  # This WILL be found
            )
            
            context_request = ContextRequest(
                original_user_query_context="how does the close_position_based_on_conditions function work?",
                symbols_of_interest=[symbol1, symbol2],
                reason_for_request="User wants to understand how position closing works"
            )
            
            print("🔍 Testing context request with partial success scenario:")
            print(f"   ❌ Requesting from: services/position_observer.py (should NOT be found)")
            print(f"   ✅ Requesting from: trade_management/position_exit_manager.py (should be found)")
            
            # Process the context request
            augmented_prompt = integration.process_context_request(
                context_request=context_request,
                original_user_query="how does the close_position_based_on_conditions function work?",
                repo_overview="Trading system repository"
            )
            
            print(f"\n📝 Augmented prompt length: {len(augmented_prompt)} characters")
            
            # Check if the response contains partial success indicators
            success_indicators = [
                "### EXTRACTED SYMBOLS",  # Should contain found symbols
                "### ⚠️ SYMBOLS NOT FOUND ⚠️",  # Should contain not found symbols
                "close_position_based_on_conditions",  # Should contain the function
                "position_exit_manager.py"  # Should reference the file where it was found
            ]
            
            failure_indicators = [
                "❌ **Context Request Failed**",  # Should NOT show complete failure
                "All requested symbols could not be found"  # Should NOT show complete failure
            ]
            
            found_success = []
            found_failure = []
            
            for indicator in success_indicators:
                if indicator in augmented_prompt:
                    found_success.append(indicator)
                    
            for indicator in failure_indicators:
                if indicator in augmented_prompt:
                    found_failure.append(indicator)
            
            print(f"\n✅ Success indicators found: {len(found_success)}/{len(success_indicators)}")
            for indicator in found_success:
                print(f"   ✅ Found: {indicator}")
                
            print(f"\n❌ Failure indicators found: {len(found_failure)}/{len(failure_indicators)}")
            for indicator in found_failure:
                print(f"   ❌ Found: {indicator}")
            
            # Test result
            if len(found_success) >= 3 and len(found_failure) == 0:
                print("\n🎉 SUCCESS: Partial success working correctly!")
                print("   ✅ System provides found symbols")
                print("   ✅ System notes missing symbols") 
                print("   ✅ System does NOT show complete failure")
                return True
            else:
                print("\n❌ FAILED: Partial success not working correctly")
                if len(found_failure) > 0:
                    print("   ❌ System still shows complete failure")
                if len(found_success) < 3:
                    print("   ❌ System doesn't provide adequate partial success information")
                
                # Show a snippet of the response for debugging
                print(f"\n🔍 Response snippet (first 500 chars):")
                print(augmented_prompt[:500])
                print("...")
                return False
                
        except ImportError as e:
            print(f"❌ FAILED: Could not import context request modules: {e}")
            return False
        except Exception as e:
            print(f"❌ FAILED: Error during testing: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main test function."""
    success = test_partial_success_context_request()
    
    if success:
        print("\n🎉 PARTIAL SUCCESS FIX: PASSED")
        print("The fix correctly provides partial success instead of complete failure.")
        print("When some symbols are found and some are not, the system now:")
        print("  ✅ Shows the found symbols with their implementations")
        print("  ⚠️ Notes which symbols were not found")
        print("  ✅ Provides guidance for the missing symbols")
        print("  ❌ Does NOT show complete failure message")
    else:
        print("\n❌ PARTIAL SUCCESS FIX: FAILED")
        print("The fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
