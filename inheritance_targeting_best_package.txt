# USER QUERY
How are AI models configured and used?

# INTELLIGENT CONTEXT ANALYSIS
## Task: general_analysis
## Focus: Target model classes that might have inheritance

## CRITICAL ENTITIES (12 most important)

### 1. send_completion (function)
- File: aider-main\aider\models.py


- Criticality: high | Risk: high
- **Calls**: ["get", "sanity_check_messages", "is_deepseek_r1", "ensure_alternating_roles", "update", "..."] (total: 12)
- **Used by**: ["test_sendchat", "base_coder_old", "base_coder", "models", "test_models"] (total: 5)
- **Side Effects**: modifies_file, modifies_state, modifies_container

### 2. simple_send_with_retries (function)
- File: aider-main\aider\models.py


- Criticality: high | Risk: high
- **Calls**: ["LiteLLMExceptions", "ensure_alternating_roles", "dump", "send_completion", "remove_reasoning_content", "..."] (total: 8)
- **Used by**: ["test_sendchat", "repo", "history", "test_reasoning"] (total: 4)
- **Side Effects**: modifies_file, network_io, modifies_state

### 3. token_count (function)
- File: aider-main\aider\models.py


- Criticality: high | Risk: medium
- **Calls**: ["token_counter", "dumps", "tokenizer"] (total: 3)
- **Used by**: ["repomap_analysis", "diagnose_map_slicing", "history", "corrected_token_analysis", "repomap", "..."] (total: 10)
- **Side Effects**: writes_log, modifies_state

### 4. main (function)
- File: aider-main\aider\models.py


- Criticality: high | Risk: medium
- **Calls**: ["exit", "get_model_settings_as_yaml", "fuzzy_match_models"] (total: 3)
- **Used by**: ["test_browser", "test_deprecated", "test_ssl_verification", "test_main"] (total: 4)
- **Side Effects**: writes_log, network_io

### 5. try_to_select_default_model (function)
- File: aider-main\aider\onboarding.py


- Criticality: medium | Risk: medium
- **Calls**: ["get", "check_openrouter_tier"] (total: 2)
- **Used by**: ["test_onboarding", "onboarding"] (total: 2)
- **Side Effects**: modifies_file, network_io

### 6. Model (class)
- File: aider-main\aider\models.py

- Inherits From: ['ModelSettings']
- Criticality: medium | Risk: medium

#### 🔁 Class Inheritance
- Inheritance chain: ModelSettings
- **Calls**: []
- **Used by**: ["optimize_repomap", "test_keyword_extraction_fix", "test_repo_content_prefix_in_smart_mode", "check_system_mode", "test_external_project", "..."] (total: 10)
- **Side Effects**: none

### 7. get_model_from_cached_json_db (function)
- File: aider-main\aider\models.py


- Criticality: medium | Risk: medium
- **Calls**: ["get", "_load_cache", "_update_cache", "split"] (total: 4)
- **Used by**: ["analytics", "models", "test_model_info_manager"] (total: 3)
- **Side Effects**: network_io, modifies_state, database_io

### 8. get_repo_map_tokens (function)
- File: aider-main\aider\models.py


- Criticality: medium | Risk: medium
- **Calls**: ["get"] (total: 1)
- **Used by**: ["test_warning_fix", "corrected_token_analysis", "base_coder_old", "base_coder", "test_models"] (total: 5)
- **Side Effects**: network_io, modifies_state

### 9. select_default_model (function)
- File: aider-main\aider\onboarding.py


- Criticality: medium | Risk: medium
- **Calls**: ["try_to_select_default_model", "tool_warning", "event", "offer_openrouter_oauth", "offer_url"] (total: 5)
- **Used by**: ["test_onboarding"] (total: 1)
- **Side Effects**: writes_log, modifies_file

### 10. sanity_check_model (function)
- File: aider-main\aider\models.py


- Criticality: medium | Risk: medium
- **Calls**: ["tool_warning", "get", "tool_output", "system", "check_for_dependencies", "..."] (total: 6)
- **Used by**: ["models", "test_models"] (total: 2)
- **Side Effects**: writes_log, network_io

### 11. configure_model_settings (function)
- File: aider-main\aider\models.py


- Criticality: medium | Risk: medium
- **Calls**: ["_copy_fields", "lower", "apply_generic_model_settings", "items", "get", "..."] (total: 7)
- **Used by**: ["models"] (total: 1)
- **Side Effects**: network_io, modifies_state, modifies_container

### 12. validate_environment (function)
- File: aider-main\aider\models.py


- Criticality: medium | Risk: medium
- **Calls**: ["fast_validate_environment", "validate_environment", "startswith", "get", "lower", "..."] (total: 6)
- **Used by**: ["models"] (total: 1)
- **Side Effects**: network_io, modifies_state, modifies_container

## KEY IMPLEMENTATIONS (12 functions)

### 1. send_completion
```python
    def send_completion(self, messages, functions, stream, temperature=None):
        if os.environ.get("AIDER_SANITY_CHECK_TURNS"):
            sanity_check_messages(messages)

        if self.is_deepseek_r1():
            messages = ensure_alternating_roles(messages)

        kwargs = dict(
            model=self.name,
            stream=stream,
        )

        if self.use_temperature is not False:
            if temperature is None:
                if isinstance(self.use_temperature, bool):
                    temperature = 0
                else:
                    temperature = float(self.use_temperature)

            kwargs["temperature"] = temperature

        if functions is not None:
            function = functions[0]
            kwargs["tools"] = [dict(type="function", function=function)]
    # ... (implementation continues)
```

### 2. simple_send_with_retries
```python
    def simple_send_with_retries(self, messages):
        from aider.exceptions import LiteLLMExceptions

        litellm_ex = LiteLLMExceptions()
        if "deepseek-reasoner" in self.name:
            messages = ensure_alternating_roles(messages)
        retry_delay = 0.125

        if self.verbose:
            dump(messages)

        while True:
            try:
                kwargs = {
                    "messages": messages,
                    "functions": None,
                    "stream": False,
                }

                _hash, response = self.send_completion(**kwargs)
                if not response or not hasattr(response, "choices") or not response.choices:
                    return None
                res = response.choices[0].message.content
    # ... (implementation continues)
```

### 3. try_to_select_default_model
```python
def try_to_select_default_model():
    """
    Attempts to select a default model based on available API keys.
    Checks OpenRouter tier status to select appropriate model.

    Returns:
        The name of the selected model, or None if no suitable default is found.
    """
    # Special handling for OpenRouter
    openrouter_key = os.environ.get("OPENROUTER_API_KEY")
    if openrouter_key:
        # Check if the user is on a free tier
        is_free_tier = check_openrouter_tier(openrouter_key)
        if is_free_tier:
            return "openrouter/google/gemini-2.5-pro-exp-03-25:free"
        else:
            return "openrouter/anthropic/claude-3.7-sonnet"

    # Select model based on other available API keys
    model_key_pairs = [
        ("ANTHROPIC_API_KEY", "sonnet"),
    # ... (implementation continues)
```

### 4. Model
```python
class Model(ModelSettings):
```

### 5. get_model_from_cached_json_db
```python
    def get_model_from_cached_json_db(self, model):
        data = self.local_model_metadata.get(model)
        if data:
            return data

        # Ensure cache is loaded before checking content
        self._load_cache()

        if not self.content:
            self._update_cache()

        if not self.content:
            return dict()

        info = self.content.get(model, dict())
        if info:
            return info

        pieces = model.split("/")
        if len(pieces) == 2:
            info = self.content.get(pieces[1])
            if info and info.get("litellm_provider") == pieces[0]:
                return info

        return dict()

```

### 6. get_repo_map_tokens
```python
    def get_repo_map_tokens(self):
        map_tokens = 20000  # Increased from 1024 to 4096 for better code coverage
        max_inp_tokens = self.info.get("max_input_tokens")
        if max_inp_tokens:
            map_tokens = max_inp_tokens / 2  # Changed from 1/8 to 1/6 for more tokens
            map_tokens = min(map_tokens, 20202)  # Increased max to support optimized repositories
            map_tokens = max(map_tokens, 4096)  # Increased min from 1024 to 4096
        return map_tokens

```

### 7. select_default_model
```python
def select_default_model(args, io, analytics):
    """
    Selects a default model based on available API keys if no model is specified.
    Offers OAuth flow for OpenRouter if no keys are found.

    Args:
        args: The command line arguments object.
        io: The InputOutput object for user interaction.
        analytics: The Analytics object for tracking events.

    Returns:
        The name of the selected model, or None if no suitable default is found.
    """
    if args.model:
        return args.model  # Model already specified

    model = try_to_select_default_model()
    if model:
        io.tool_warning(f"Using {model} model with API key from environment.")
        analytics.event("auto_model_selection", model=model)
        return model

    # ... (implementation continues)
```

### 8. token_count
```python
    def token_count(self, messages):
        if type(messages) is list:
            try:
                return litellm.token_counter(model=self.name, messages=messages)
            except Exception as err:
                print(f"Unable to count tokens: {err}")
                return 0

        if not self.tokenizer:
            return

        if type(messages) is str:
            msgs = messages
        else:
            msgs = json.dumps(messages)

        try:
            return len(self.tokenizer(msgs))
        except Exception as err:
            print(f"Unable to count tokens: {err}")
            return 0

```

### 9. main
```python
def main():
    if len(sys.argv) < 2:
        print("Usage: python models.py <model_name> or python models.py --yaml")
        sys.exit(1)

    if sys.argv[1] == "--yaml":
        yaml_string = get_model_settings_as_yaml()
        print(yaml_string)
    else:
        model_name = sys.argv[1]
        matching_models = fuzzy_match_models(model_name)

        if matching_models:
            print(f"Matching models for '{model_name}':")
            for model in matching_models:
                print(model)
        else:
            print(f"No matching models found for '{model_name}'.")


if __name__ == "__main__":
    main()
```

### 10. sanity_check_model
```python
def sanity_check_model(io, model):
    show = False

    if model.missing_keys:
        show = True
        io.tool_warning(f"Warning: {model} expects these environment variables")
        for key in model.missing_keys:
            value = os.environ.get(key, "")
            status = "Set" if value else "Not set"
            io.tool_output(f"- {key}: {status}")

        if platform.system() == "Windows":
            io.tool_output(
                "Note: You may need to restart your terminal or command prompt for `setx` to take"
                " effect."
            )

    elif not model.keys_in_environment:
        show = True
        io.tool_warning(f"Warning for {model}: Unknown which environment variables are required.")

    # Check for model-specific dependencies
    # ... (implementation continues)
```

### 11. configure_model_settings
```python
    def configure_model_settings(self, model):
        # Look for exact model match
        exact_match = False
        for ms in MODEL_SETTINGS:
            # direct match, or match "provider/<model>"
            if model == ms.name:
                self._copy_fields(ms)
                exact_match = True
                break  # Continue to apply overrides

        # Initialize accepts_settings if it's None
        if self.accepts_settings is None:
            self.accepts_settings = []

        model = model.lower()

        # If no exact match, try generic settings
        if not exact_match:
            self.apply_generic_model_settings(model)

        # Apply override settings last if they exist
        if (
            self.extra_model_settings
            and self.extra_model_settings.extra_params
    # ... (implementation continues)
```

### 12. validate_environment
```python
    def validate_environment(self):
        res = self.fast_validate_environment()
        if res:
            return res

        # https://github.com/BerriAI/litellm/issues/3190

        model = self.name
        res = litellm.validate_environment(model)

        # If missing AWS credential keys but AWS_PROFILE is set, consider AWS credentials valid
        if res["missing_keys"] and any(
            key in ["AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"] for key in res["missing_keys"]
        ):
            if model.startswith("bedrock/") or model.startswith("us.anthropic."):
                if os.environ.get("AWS_PROFILE"):
                    res["missing_keys"] = [
                        k
                        for k in res["missing_keys"]
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 12 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: How are AI models configured and used?

Provide specific, actionable insights based on this focused context.
