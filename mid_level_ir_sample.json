{"modules": [{"name": "aider_context_request_integration", "file": "aider_context_request_integration.py", "loc": 156, "dependencies": [], "entities": [{"type": "function", "name": "__init__", "doc": "Initialize the integration.", "params": ["project_path", "aider_service"], "returns": "None", "calls": ["AiderIntegrationService", "ContextRequestHandler", "Aider<PERSON>emplate<PERSON><PERSON><PERSON>", "get_llm_instructions", "need", "detect_context_request", "response", "parse_context_request", "join", "process_context_request"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "process_context_request", "doc": "Process a context request and generate an augmented prompt.", "params": ["context_request", "original_user_query", "repo_overview"], "returns": "str", "calls": ["process_context_request", "get", "render_augmented_prompt", "update_conversation_history", "message", "Content", "enumerate", "append", "reset_iteration_counter", "has_reached_max_iterations"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "detect_context_request", "doc": "Detect if the LLM response contains a context request.", "params": ["llm_response"], "returns": "Optional[ContextRequest]", "calls": ["response", "parse_context_request", "join", "process_context_request", "get", "render_augmented_prompt", "update_conversation_history", "message", "Content", "enumerate"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "get_llm_instructions", "doc": "Get the instructions for the LLM to use the CONTEXT_REQUEST protocol.", "params": [], "returns": "str", "calls": ["need", "detect_context_request", "response", "parse_context_request", "join", "process_context_request", "get", "render_augmented_prompt", "update_conversation_history", "message"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "update_conversation_history", "doc": "Update the conversation history.", "params": ["role", "content"], "returns": "None", "calls": ["message", "Content", "enumerate", "get", "append", "reset_iteration_counter", "has_reached_max_iterations", "get_context_request_summary", "join"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "reset_iteration_counter", "doc": "Reset the iteration counter.", "params": [], "returns": "None", "calls": ["has_reached_max_iterations", "get_context_request_summary", "join"], "used_by": [], "side_effects": ["modifies_state"], "errors": ["IndexError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "get_context_request_summary", "doc": "Get a summary of the context request for logging purposes.", "params": ["context_request"], "returns": "str", "calls": ["join"], "used_by": [], "side_effects": ["none"], "errors": ["IndexError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "has_reached_max_iterations", "doc": "Check if the maximum number of context request iterations has been reached.", "params": [], "returns": "bool", "calls": ["get_context_request_summary", "join"], "used_by": [], "side_effects": ["modifies_state"], "errors": ["IndexError"], "criticality": "low", "change_risk": "low"}]}, {"name": "aider_integration_service", "file": "aider_integration_service.py", "loc": 1568, "dependencies": [], "entities": [{"type": "function", "name": "get_symbols_defined_in_file", "doc": "Get all symbols (functions, classes, variables) defined in a file.", "params": ["project_path", "model_name", "file_path"], "returns": "Dict[str, List[str]]", "calls": ["symbols", "_is_cache_valid", "_get_repo_map", "_normalize_path", "relpath", "get_tags", "startswith", "split", "append", "abspath"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_count_lines_of_code", "doc": "Count lines of code in a file (excluding comments and blank lines).", "params": ["file_path"], "returns": "int", "calls": ["file", "open", "readlines", "strip", "startswith", "_analyze_entities", "entities", "read", "get", "_analyze_function"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_extract_docstring", "doc": "Extract docstring for a function.", "params": ["file_content", "func_name"], "returns": "Optional[str]", "calls": ["escape", "search", "group", "strip", "split", "_extract_class_docstring", "_analyze_function_calls", "finditer", "append", "_extract_class_methods"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_analyze_potential_errors", "doc": "Analyze potential errors a function might raise.", "params": ["file_content", "func_name"], "returns": "List[str]", "calls": ["escape", "search", "group", "finditer", "append", "open", "_calculate_criticality", "get", "lower", "_calculate_change_risk"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "generate_mid_level_ir", "doc": "Generate the complete Mid-Level IR for a project.", "params": ["project_path", "model_name"], "returns": "Dict", "calls": ["_get_python_files", "_analyze_module", "append", "_calculate_module_dependencies", "time", "walk", "any", "endswith", "startswith", "join"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "find_file_defining_symbol", "doc": "Find the file that defines a specific symbol.", "params": ["project_path", "symbol_name", "model_name"], "returns": "Optional[str]", "calls": ["_find_file_defining_symbol", "clear_cache", "clear", "set_cache_ttl", "_get_context_extractor", "SurgicalContextExtractor", "get_contextual_dependencies", "append", "get_focused_inheritance_context", "get_symbol_references_between_files"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_get_context_extractor", "doc": "Get the surgical context extractor, initializing it if necessary.", "params": [], "returns": "None", "calls": ["SurgicalContextExtractor", "get_contextual_dependencies", "_get_context_extractor", "append", "get_focused_inheritance_context", "get_symbol_references_between_files", "get_symbols_defined_in_file", "_get_repo_map", "_normalize_path", "relpath"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_get_module_path", "doc": "Convert a file path to a module path.", "params": ["file_path"], "returns": "str", "calls": ["path", "endswith", "replace"], "used_by": [], "side_effects": ["none"], "errors": ["IndexError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_parse_function_parameters", "doc": "Parse function parameters from parameter string.", "params": ["params_str"], "returns": "List[str]", "calls": ["strip", "split", "name", "append", "_extract_docstring", "escape", "search", "group", "_extract_class_docstring", "_analyze_function_calls"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_calculate_change_risk", "doc": "Calculate the change risk level of a symbol.", "params": ["symbol_name", "dependencies", "project_path", "model_name"], "returns": "str", "calls": ["get", "lower", "any", "_calculate_module_dependencies", "join", "get_files_imported_by", "relpath", "isabs", "_calculate_dependency_strength", "append"], "used_by": [], "side_effects": ["writes_log", "modifies_state", "dynamic_import"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "get_symbol_references_between_files", "doc": "Get detailed information about symbols referenced between two files.", "params": ["project_path", "source_file", "target_file", "model_name"], "returns": "Dict[str, List[str]]", "calls": ["get_symbols_defined_in_file", "_get_repo_map", "_normalize_path", "relpath", "get_tags", "_get_module_path", "append", "abspath", "exists", "open"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "get_contextual_dependencies", "doc": "Get a comprehensive map of contextual dependencies for a file.", "params": ["project_path", "primary_file", "max_snippets"], "returns": "Dict", "calls": ["_get_context_extractor", "get_contextual_dependencies", "append", "get_focused_inheritance_context", "get_symbol_references_between_files", "get_symbols_defined_in_file", "_get_repo_map", "_normalize_path", "relpath", "get_tags"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_is_cache_valid", "doc": "Check if a cache entry is still valid based on TTL.", "params": ["cache_key"], "returns": "bool", "calls": ["return", "time", "_update_cache_timestamp", "_extract_class_info_from_repomap", "_get_repo_map", "walk", "endswith", "append", "join", "relpath"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "get_top_central_files", "doc": "Get the top central files in the project based on reference count.", "params": ["project_path", "model_name", "count"], "returns": "List[Dict]", "calls": ["get_files_that_import", "import", "_get_repo_map", "_normalize_path", "relpath", "walk", "endswith", "append", "join", "get_tags"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_calculate_module_dependencies", "doc": "Calculate dependencies between modules.", "params": ["modules", "project_path", "model_name"], "returns": "None", "calls": ["join", "get_files_imported_by", "relpath", "isabs", "_calculate_dependency_strength", "append", "_update_entity_usage", "get_symbol_references_between_files", "sum", "values"], "used_by": [], "side_effects": ["writes_log", "modifies_state", "dynamic_import"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "get_files_that_import", "doc": "Get a list of files that import (reference) the target file.", "params": ["project_path", "model_name", "target_file_path"], "returns": "List[str]", "calls": ["import", "_get_repo_map", "_normalize_path", "relpath", "walk", "endswith", "append", "join", "get_tags", "basename"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "set_cache_ttl", "doc": "Set the time-to-live for cache entries.", "params": ["ttl"], "returns": "None", "calls": ["_get_context_extractor", "SurgicalContextExtractor", "get_contextual_dependencies", "append", "get_focused_inheritance_context", "get_symbol_references_between_files", "get_symbols_defined_in_file", "_get_repo_map", "_normalize_path", "relpath"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_extract_class_info_from_repomap", "doc": "Extract class inheritance information from RepoMap.", "params": ["project_path", "model_name"], "returns": "Dict[str, Dict]", "calls": ["_get_repo_map", "walk", "endswith", "append", "join", "relpath", "abspath", "open", "read", "get_tags"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_find_file_defining_symbol", "doc": "Find the file that defines a specific symbol.", "params": ["project_path", "model_name", "symbol_name"], "returns": "Optional[str]", "calls": ["_is_cache_valid", "_get_repo_map", "walk", "endswith", "append", "join", "relpath", "get_tags", "_update_cache_timestamp", "get_symbols_defined_in_file"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_normalize_path", "doc": "Normalize a file path for cross-platform consistency.", "params": ["path"], "returns": "str", "calls": ["isabs", "exists", "join", "abspath", "Path", "resolve", "replace", "_is_cache_valid", "return", "time"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_extract_class_docstring", "doc": "Extract docstring for a class.", "params": ["file_content", "class_name"], "returns": "Optional[str]", "calls": ["escape", "search", "group", "strip", "split", "_analyze_function_calls", "finditer", "append", "_extract_class_methods", "ndef"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_update_entity_usage", "doc": "Update the 'used_by' field for entities in a module.", "params": ["module", "all_modules", "project_path", "model_name"], "returns": "None", "calls": ["join", "get_files_that_import", "relpath", "isabs", "append", "get"], "used_by": [], "side_effects": ["writes_log", "modifies_state", "dynamic_import"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "clear_cache", "doc": "Clear all cached data.", "params": [], "returns": "None", "calls": ["clear", "set_cache_ttl", "_get_context_extractor", "SurgicalContextExtractor", "get_contextual_dependencies", "append", "get_focused_inheritance_context", "get_symbol_references_between_files", "get_symbols_defined_in_file", "_get_repo_map"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "get_focused_inheritance_context", "doc": "Get focused context around class inheritance relationships.", "params": ["project_path", "class_name", "file_path"], "returns": "Dict", "calls": ["_get_context_extractor", "get_focused_inheritance_context", "append", "get_symbol_references_between_files", "get_symbols_defined_in_file", "_get_repo_map", "_normalize_path", "relpath", "get_tags", "_get_module_path"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_update_cache_timestamp", "doc": "Update the timestamp for a cache entry.", "params": ["cache_key"], "returns": "None", "calls": ["time", "_extract_class_info_from_repomap", "_get_repo_map", "walk", "endswith", "append", "join", "relpath", "abspath", "open"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "get_files_imported_by", "doc": "Get a list of files that are imported by the target file.", "params": ["project_path", "model_name", "target_file_path"], "returns": "List[str]", "calls": ["_get_repo_map", "_normalize_path", "relpath", "get_tags", "_find_file_defining_symbol", "append", "basename", "endswith", "_is_cache_valid", "walk"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_get_python_files", "doc": "Get all Python files in the project.", "params": ["project_path"], "returns": "List[str]", "calls": ["walk", "any", "endswith", "startswith", "append", "join", "_analyze_module", "relpath", "_get_module_name", "_count_lines_of_code"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_analyze_function", "doc": "Analyze a function and extract detailed metadata.", "params": ["file_content", "func_name", "project_path", "file_path", "model_name"], "returns": "Optional[Dict]", "calls": ["escape", "search", "group", "strip", "_parse_function_parameters", "_extract_docstring", "_analyze_function_calls", "_analyze_side_effects", "_analyze_potential_errors", "_calculate_criticality"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_calculate_dependency_strength", "doc": "Calculate the strength of dependency between two files.", "params": ["source_file", "target_file", "project_path", "model_name"], "returns": "str", "calls": ["join", "get_symbol_references_between_files", "sum", "values", "_update_entity_usage", "get_files_that_import", "relpath", "isabs", "append", "get"], "used_by": [], "side_effects": ["writes_log", "modifies_state", "dynamic_import"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "get_base_classes_of", "doc": "Get the base classes of the specified class.", "params": ["project_path", "model_name", "class_name", "file_path"], "returns": "List[Dict]", "calls": ["class", "_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "_update_cache_timestamp", "get_derived_classes_of", "path", "endswith"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "get_derived_classes_of", "doc": "Get the derived classes of the specified class.", "params": ["project_path", "model_name", "class_name", "file_path"], "returns": "List[Dict]", "calls": ["class", "_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "_update_cache_timestamp", "path", "endswith", "replace"], "used_by": [], "side_effects": ["writes_log", "modifies_state"], "errors": ["IndexError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_extract_class_methods", "doc": "Extract methods from a class.", "params": ["file_content", "class_name"], "returns": "List[str]", "calls": ["escape", "ndef", "search", "group", "finditer", "append", "_analyze_side_effects", "open", "write", "_analyze_potential_errors"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_analyze_entities", "doc": "Analyze entities (functions, classes) in a file.", "params": ["project_path", "file_path", "symbols", "List[str]]", "model_name"], "returns": "List[Dict]", "calls": ["entities", "open", "read", "get", "_analyze_function", "append", "_analyze_class", "escape", "search", "group"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_analyze_function_calls", "doc": "Analyze function calls within a function.", "params": ["file_content", "func_name"], "returns": "List[str]", "calls": ["escape", "search", "group", "finditer", "append", "_extract_class_methods", "ndef", "_analyze_side_effects", "open", "write"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_calculate_criticality", "doc": "Calculate the criticality level of a symbol.", "params": ["symbol_name", "project_path", "model_name"], "returns": "str", "calls": ["get", "lower", "_calculate_change_risk", "any", "_calculate_module_dependencies", "join", "get_files_imported_by", "relpath", "isabs", "_calculate_dependency_strength"], "used_by": [], "side_effects": ["writes_log", "modifies_state", "dynamic_import"], "errors": ["IndexError", "ValueError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_analyze_class", "doc": "Analyze a class and extract detailed metadata.", "params": ["file_content", "class_name", "project_path", "file_path", "model_name"], "returns": "Optional[Dict]", "calls": ["escape", "search", "group", "strip", "split", "_extract_class_docstring", "_extract_class_methods", "_calculate_criticality", "_calculate_change_risk", "_parse_function_parameters"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "__init__", "doc": "Initialize the project manager.", "params": ["cache_ttl"], "returns": "None", "calls": ["seconds", "getcwd", "_load_dependency_data", "exists", "open", "read", "split", "strip", "startswith", "append"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_get_repo_map", "doc": "Get or create a RepoMap instance for the given project.", "params": ["project_path", "model_name"], "returns": "Optional[Any]", "calls": ["_normalize_path", "Model", "InputOutput", "RepoMap", "isabs", "exists", "join", "abspath", "Path", "resolve"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_load_dependency_data", "doc": "Load dependency data from the analysis file.", "params": [], "returns": "None", "calls": ["exists", "open", "read", "split", "strip", "startswith", "append", "_get_repo_map", "_normalize_path", "Model"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_get_module_name", "doc": "Convert a file path to a module name.", "params": ["rel_path"], "returns": "str", "calls": ["replace", "endswith", "split", "_count_lines_of_code", "file", "open", "readlines", "strip", "startswith", "_analyze_entities"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_analyze_side_effects", "doc": "Analyze potential side effects of a function.", "params": ["file_content", "func_name"], "returns": "List[str]", "calls": ["escape", "search", "group", "append", "open", "write", "_analyze_potential_errors", "finditer", "_calculate_criticality", "get"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "_get_ir_generator", "doc": "Get the Mid-Level IR generator, initializing it if necessary.", "params": [], "returns": "None", "calls": ["MidLevelIRGenerator", "generate_mid_level_ir", "_get_ir_generator", "get_files_that_import", "import", "get_files_imported_by", "get_base_classes_of", "class", "get_derived_classes_of", "get_top_central_files"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}, {"type": "function", "name": "_analyze_module", "doc": "Analyze a single file as a module.", "params": ["project_path", "file_path", "model_name"], "returns": "Optional[Dict]", "calls": ["relpath", "_get_module_name", "_count_lines_of_code", "get_symbols_defined_in_file", "entities", "_analyze_entities", "replace", "endswith", "split", "file"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "modifies_global", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError", "ZeroDivisionError"], "criticality": "low", "change_risk": "medium"}]}, {"name": "aider_template_renderer", "file": "aider_template_renderer.py", "loc": 237, "dependencies": [], "entities": [{"type": "function", "name": "__init__", "doc": "Initialize the template renderer.", "params": [], "returns": "None", "calls": ["format_repository_overview", "OVERVIEW", "format_conversation_history", "enumerate", "get", "Content", "format_extracted_symbols", "DEFINITIONS", "format_dependency_snippets", "SNIPPETS"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_repository_overview", "doc": "Format the repository overview section.", "params": ["repo_overview"], "returns": "str", "calls": ["OVERVIEW", "format_conversation_history", "enumerate", "get", "Content", "format_extracted_symbols", "DEFINITIONS", "format_dependency_snippets", "SNIPPETS", "format_not_found_symbols"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_instructions", "doc": "Format the instructions section.", "params": ["original_query", "reason_for_request"], "returns": "str", "calls": ["render_augmented_prompt", "enumerate", "get", "Content", "format_repository_overview", "format_extracted_symbols", "format_dependency_snippets", "format_instructions", "HISTORY", "format_conversation_history"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_dependency_snippets", "doc": "Format the dependency snippets section.", "params": ["dependency_snippets", "Any]]"], "returns": "str", "calls": ["SNIPPETS", "enumerate", "get", "format_not_found_symbols", "format_instructions", "render_augmented_prompt", "Content", "format_repository_overview", "format_extracted_symbols", "format_dependency_snippets"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_not_found_symbols", "doc": "Format the not found symbols section.", "params": ["not_found_symbols", "Any]]"], "returns": "str", "calls": ["get", "format_instructions", "render_augmented_prompt", "enumerate", "Content", "format_repository_overview", "format_extracted_symbols", "format_dependency_snippets", "HISTORY", "format_conversation_history"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_conversation_history", "doc": "Format the conversation history section.", "params": ["conversation_history", "str]]"], "returns": "str", "calls": ["enumerate", "get", "Content", "format_extracted_symbols", "DEFINITIONS", "format_dependency_snippets", "SNIPPETS", "format_not_found_symbols", "format_instructions", "render_augmented_prompt"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "format_extracted_symbols", "doc": "Format the extracted symbols section.", "params": ["extracted_symbols", "Any]]"], "returns": "str", "calls": ["DEFINITIONS", "get", "format_dependency_snippets", "SNIPPETS", "enumerate", "format_not_found_symbols", "format_instructions", "render_augmented_prompt", "Content", "format_repository_overview"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}, {"type": "function", "name": "render_augmented_prompt", "doc": "Render the augmented prompt with the extracted context.", "params": ["original_query", "repo_overview", "extracted_context", "Any]", "conversation_history", "str]]]"], "returns": "str", "calls": ["enumerate", "get", "Content", "format_repository_overview", "format_extracted_symbols", "format_dependency_snippets", "format_instructions", "HISTORY", "format_conversation_history", "join"], "used_by": [], "side_effects": ["writes_log", "modifies_file", "modifies_state", "dynamic_import"], "errors": ["IndexError", "FileNotFoundError", "ValueError"], "criticality": "low", "change_risk": "low"}]}], "metadata": {"generated_at": 1748462235.9456358, "project_path": "C:\\Users\\<USER>\\Documents\\aider_project\\aider__500", "total_modules": 3, "generator_version": "1.0.0", "test_mode": true}}