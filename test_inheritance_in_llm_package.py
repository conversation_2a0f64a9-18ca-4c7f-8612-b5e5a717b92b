#!/usr/bin/env python3
"""
Test script to check inheritance data in the actual LLM package.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_inheritance_in_llm_package():
    """Test inheritance data in the LLM package generation."""
    
    print("🔍 TESTING INHERITANCE IN LLM PACKAGE")
    print("=" * 60)
    
    try:
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        # Create handler
        handler = ContextRequestHandler(external_project)
        
        # Test 1: Position management query
        print("🎯 TEST 1: POSITION MANAGEMENT QUERY")
        print("-" * 40)
        
        request1 = IRContextRequest(
            user_query="How does position management work in the trading system?",
            task_description="Analyze position management in trading system",
            task_type="general_analysis",
            focus_entities=["position", "management", "trading", "system"],
            max_tokens=2000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=8
        )
        
        result1 = handler.process_ir_context_request(request1)
        
        if "llm_friendly_package" in result1:
            package1 = result1["llm_friendly_package"]
            print(f"✅ Generated LLM package ({len(package1)} chars)")
            
            # Check for inheritance indicators
            inheritance_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Class Context",
                "🧩 Method Details"
            ]
            
            found_indicators = {}
            for indicator in inheritance_indicators:
                count = package1.count(indicator)
                found_indicators[indicator] = count
                if count > 0:
                    print(f"   Found '{indicator}': {count} times")
            
            # Show a sample of inheritance context
            lines = package1.split('\n')
            inheritance_lines = []
            for i, line in enumerate(lines):
                if any(indicator in line for indicator in inheritance_indicators):
                    # Include context around the inheritance line
                    start = max(0, i-1)
                    end = min(len(lines), i+3)
                    inheritance_lines.extend(lines[start:end])
                    inheritance_lines.append("---")
            
            if inheritance_lines:
                print(f"\n📄 INHERITANCE CONTEXT SAMPLE:")
                for line in inheritance_lines[:20]:  # Show first 20 lines
                    print(f"   {line}")
                if len(inheritance_lines) > 20:
                    print(f"   ... and {len(inheritance_lines) - 20} more lines")
        
        # Test 2: Strategy inheritance query
        print(f"\n🎯 TEST 2: STRATEGY INHERITANCE QUERY")
        print("-" * 40)
        
        request2 = IRContextRequest(
            user_query="How do the strategy classes inherit from base classes?",
            task_description="Analyze strategy class inheritance patterns",
            task_type="general_analysis",
            focus_entities=["strategy", "base", "inherit", "class"],
            max_tokens=2000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_entities=8
        )
        
        result2 = handler.process_ir_context_request(request2)
        
        if "llm_friendly_package" in result2:
            package2 = result2["llm_friendly_package"]
            print(f"✅ Generated LLM package ({len(package2)} chars)")
            
            # Look for actual class inheritance
            lines = package2.split('\n')
            class_inheritance_lines = []
            for i, line in enumerate(lines):
                if "inherits from:" in line.lower() and "no inheritance" not in line.lower():
                    # Include context around the inheritance line
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    class_inheritance_lines.extend(lines[start:end])
                    class_inheritance_lines.append("---")
            
            if class_inheritance_lines:
                print(f"\n📄 ACTUAL CLASS INHERITANCE FOUND:")
                for line in class_inheritance_lines[:15]:  # Show first 15 lines
                    print(f"   {line}")
            else:
                print(f"\n❌ No actual class inheritance found in package")
                
                # Check what we do have
                inheritance_count = package2.count("Inherits From:")
                class_count = package2.count("Belongs to Class:")
                print(f"   'Inherits From:' mentions: {inheritance_count}")
                print(f"   'Belongs to Class:' mentions: {class_count}")
        
        # Test 3: Check IR slices directly
        print(f"\n🎯 TEST 3: CHECK IR SLICES DIRECTLY")
        print("-" * 40)
        
        if "ir_slices" in result2:
            ir_slices = result2["ir_slices"]
            print(f"✅ Found {len(ir_slices)} IR slices")
            
            slices_with_inheritance = []
            for slice_data in ir_slices:
                if (slice_data.get("inherits_from") or 
                    slice_data.get("method_overrides") or 
                    slice_data.get("calls_super") or
                    slice_data.get("overridden_by") or
                    slice_data.get("class_name")):
                    slices_with_inheritance.append(slice_data)
            
            print(f"📊 IR slices with inheritance data: {len(slices_with_inheritance)}")
            
            if slices_with_inheritance:
                print(f"\n📄 SAMPLE IR SLICES WITH INHERITANCE:")
                for i, slice_data in enumerate(slices_with_inheritance[:3], 1):
                    print(f"   {i}. {slice_data.get('entity_name')} ({slice_data.get('entity_type')})")
                    if slice_data.get("inherits_from"):
                        print(f"      inherits_from: {slice_data.get('inherits_from')}")
                    if slice_data.get("class_name"):
                        print(f"      class_name: {slice_data.get('class_name')}")
                    if slice_data.get("method_overrides"):
                        print(f"      method_overrides: {slice_data.get('method_overrides')}")
        
        # Summary
        print(f"\n📊 INHERITANCE IN LLM PACKAGE SUMMARY")
        print("=" * 60)
        
        if "llm_friendly_package" in result1 and "llm_friendly_package" in result2:
            package1_has_inheritance = "Belongs to Class:" in result1["llm_friendly_package"]
            package2_has_inheritance = "inherits from:" in result2["llm_friendly_package"].lower()
            
            print(f"Position query package has class context: {'✅' if package1_has_inheritance else '❌'}")
            print(f"Strategy query package has inheritance: {'✅' if package2_has_inheritance else '❌'}")
            
            if package1_has_inheritance or package2_has_inheritance:
                print("\n✅ CONCLUSION: Inheritance data IS present in LLM packages!")
                print("The inheritance system is working correctly.")
                return True
            else:
                print("\n❌ CONCLUSION: Inheritance data is missing from LLM packages.")
                return False
        else:
            print("\n❌ Failed to generate LLM packages for testing.")
            return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_inheritance_in_llm_package()
    
    if success:
        print("\n✅ Inheritance in LLM package test completed successfully!")
    else:
        print("\n❌ Inheritance in LLM package test failed.")
