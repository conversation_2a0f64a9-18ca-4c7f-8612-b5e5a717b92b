#!/usr/bin/env python

import os
import sys
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported context_request module")
    CONTEXT_REQUEST_AVAILABLE = True
except ImportError as e:
    print(f"❌ Failed to import context_request module: {e}")
    CONTEXT_REQUEST_AVAILABLE = False

def main():
    print("\n=== Testing CONTEXT_REQUEST Availability ===")
    
    if not CONTEXT_REQUEST_AVAILABLE:
        print("❌ CONTEXT_REQUEST functionality is not available")
        return
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the context request integration
    try:
        integration = AiderContextRequestIntegration(project_path)
        print("✅ Successfully initialized AiderContextRequestIntegration")
    except Exception as e:
        print(f"❌ Failed to initialize AiderContextRequestIntegration: {e}")
        return
    
    # Get the LLM instructions
    try:
        instructions = integration.get_llm_instructions()
        print("✅ Successfully got LLM instructions")
        print("\nLLM Instructions:")
        print(instructions)
    except Exception as e:
        print(f"❌ Failed to get LLM instructions: {e}")
    
    # Create a sample context request
    try:
        context_request = ContextRequest(
            original_user_query_context="User is asking about the surgical file extractor",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="SurgicalFileExtractor.extract_symbol_content",
                    file_hint="surgical_file_extractor.py"
                )
            ],
            reason_for_request="To understand how the surgical file extractor works"
        )
        print("✅ Successfully created ContextRequest")
    except Exception as e:
        print(f"❌ Failed to create ContextRequest: {e}")
        return
    
    # Get the context request summary
    try:
        summary = integration.get_context_request_summary(context_request)
        print("✅ Successfully got context request summary")
        print(f"Summary: {summary}")
    except Exception as e:
        print(f"❌ Failed to get context request summary: {e}")
    
    print("\n=== Test completed! ===")

if __name__ == "__main__":
    main()
