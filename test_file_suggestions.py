"""
Test script for the file not found suggestion feature.
"""

import os
import sys
from pathlib import Path

# Create test files
test_dir = Path("test_files")
test_dir.mkdir(exist_ok=True)

# Create some test files
(test_dir / "file1.py").write_text("# This is file1.py")
(test_dir / "file2.py").write_text("# This is file2.py")
(test_dir / "similar_file.py").write_text("# This is similar_file.py")
(test_dir / "another_file.txt").write_text("This is another_file.txt")

# Create a subdirectory
sub_dir = test_dir / "subdir"
sub_dir.mkdir(exist_ok=True)

# Create files in the subdirectory
(sub_dir / "subfile.py").write_text("# This is subfile.py")

# Define a function to find similar files
def find_similar_files(requested_file, all_files):
    """
    Find files with similar names to the requested file.

    Args:
        requested_file: The file that was requested but not found
        all_files: List of all available files

    Returns:
        List of files with similar names
    """
    # Extract the base filename without path or extension
    import os

    # Get the basename without extension
    basename = os.path.basename(requested_file)
    name_without_ext, _ = os.path.splitext(basename)

    # Look for files with similar names
    similar_files = []

    # First, look for exact basename matches with different paths
    for file_path in all_files:
        if os.path.basename(file_path) == basename:
            similar_files.append(file_path)

    # If no exact basename matches, look for partial matches
    if not similar_files:
        for file_path in all_files:
            file_basename = os.path.basename(file_path)
            file_name, _ = os.path.splitext(file_basename)

            # Use Levenshtein distance to find similar names
            from difflib import SequenceMatcher
            similarity = SequenceMatcher(None, name_without_ext.lower(), file_name.lower()).ratio()

            # If similarity is above threshold, consider it a match
            if similarity > 0.7:  # 70% similarity threshold
                similar_files.append(file_path)

    # Limit to top 5 most relevant matches
    return similar_files[:5]

# Get all files in the test directory
all_files = []
for root, _, files in os.walk(test_dir):
    for file in files:
        rel_path = os.path.relpath(os.path.join(root, file), test_dir)
        all_files.append(rel_path)

print("All files:", all_files)

# Test with a file that doesn't exist but has similar names
similar_files = find_similar_files("simlar_file.py", all_files)
print("\nTest 1: File not found but similar files exist")
print("Requested file: simlar_file.py")
print("Similar files:", similar_files)

# Let's debug why similar_file.py isn't being found
print("\nDebugging similar file detection:")
requested_file = "simlar_file.py"
basename = os.path.basename(requested_file)
name_without_ext, _ = os.path.splitext(basename)
print(f"Basename: {basename}")
print(f"Name without extension: {name_without_ext}")

for file_path in all_files:
    file_basename = os.path.basename(file_path)
    file_name, _ = os.path.splitext(file_basename)
    print(f"Checking {file_path}: basename={file_basename}, name={file_name}")
    print(f"  {name_without_ext.lower()} in {file_name.lower()}? {name_without_ext.lower() in file_name.lower()}")
    print(f"  {file_name.lower()} in {name_without_ext.lower()}? {file_name.lower() in name_without_ext.lower()}")

# Test with a file that doesn't exist and has no similar names
similar_files = find_similar_files("nonexistent_file.py", all_files)
print("\nTest 2: File not found and no similar files")
print("Requested file: nonexistent_file.py")
print("Similar files:", similar_files)

# Create a message for the AI model
def create_file_not_found_message(requested_file, similar_files, reason=""):
    """
    Create a message to send back to the AI model when a file is not found.

    Args:
        requested_file: The file that was requested but not found
        similar_files: List of similar files to suggest
        reason: The reason provided by the AI for requesting the file

    Returns:
        Message to send back to the AI model
    """
    message = f"System Note to AI:\nI could not find the file at the path you requested: \"{requested_file}\"."

    if similar_files:
        message += "\nHowever, I found these similar paths in the project:\n"
        for file in similar_files:
            message += f"- \"{file}\"\n"
        message += "Please review your request. If one of these is correct, issue a new request with the corrected path."
    else:
        message += "\nI couldn't find any similar files in the project. Please check the file path or try a different approach (e.g., a SEARCH_REQUEST)."

    return message

# Test the message creation
similar_files = find_similar_files("simlar_file.py", all_files)
message = create_file_not_found_message("simlar_file.py", similar_files, "I need to understand the implementation")
print("\nTest 3: Message for file with similar names")
print(message)

similar_files = find_similar_files("nonexistent_file.py", all_files)
message = create_file_not_found_message("nonexistent_file.py", similar_files, "I need to understand the implementation")
print("\nTest 4: Message for file with no similar names")
print(message)

# Clean up
import shutil
shutil.rmtree(test_dir)
