#!/usr/bin/env python3

"""
Simple verification script to confirm the CONTEXT_REQUEST priority fix is applied to the codebase.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

def verify_fix_applied():
    """Verify that the fix has been applied to the actual codebase."""
    
    print("=== VERIFYING CONTEXT_REQUEST PRIORITY FIX ===")
    
    # Read the actual file content
    file_path = "aider-main/aider/coders/base_coder.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the get_repo_messages method
        method_start = content.find("def get_repo_messages(self):")
        if method_start == -1:
            print("❌ Could not find get_repo_messages method")
            return False
        
        # Extract the method (find the next method definition)
        method_end = content.find("\n    def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        print("Found get_repo_messages method:")
        print("-" * 50)
        
        # Show just the assistant response part
        lines = method_content.split('\n')
        for i, line in enumerate(lines):
            if 'role="assistant"' in line:
                print(f"Line {i+1}: {line.strip()}")
                # Show the next few lines to see the content
                for j in range(i+1, min(i+4, len(lines))):
                    if lines[j].strip():
                        print(f"Line {j+1}: {lines[j].strip()}")
                break
        
        print("-" * 50)
        
        # Check for the fix indicators
        fix_indicators = [
            "I understand the repository structure",
            "CONTEXT_REQUEST as my primary method",
            "requesting specific code context when needed"
        ]
        
        fix_applied = all(indicator in method_content for indicator in fix_indicators)
        
        # Check that the old problematic content is NOT there
        problematic_indicators = [
            "I will ALWAYS:",
            "1. Request relevant files BEFORE attempting",
            "CAREFULLY EXAMINE THE REPOSITORY MAP",
            "SEARCH_REQUEST:"
        ]
        
        old_content_removed = not any(indicator in method_content for indicator in problematic_indicators)
        
        if fix_applied and old_content_removed:
            print("✅ SUCCESS: Fix has been applied to the codebase!")
            print("   - New concise assistant response is present")
            print("   - Old verbose conflicting instructions have been removed")
            print("   - Assistant response emphasizes CONTEXT_REQUEST as primary")
            return True
        elif fix_applied and not old_content_removed:
            print("⚠️  PARTIAL: New content is there but old content still exists")
            return False
        elif not fix_applied and old_content_removed:
            print("⚠️  PARTIAL: Old content removed but new content not found")
            return False
        else:
            print("❌ FAIL: Fix has not been applied")
            print("   - Old verbose instructions may still be present")
            print("   - New concise response not found")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: Could not verify fix: {e}")
        return False

def verify_repo_content_prefix():
    """Verify that the repo_content_prefix still has correct CONTEXT_REQUEST prioritization."""
    
    try:
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        repo_content_prefix = prompts.repo_content_prefix
        
        print("\n=== VERIFYING REPO_CONTENT_PREFIX STRUCTURE ===")
        
        # Check key elements
        has_preferred = "PREFERRED" in repo_content_prefix
        has_secondary = "SECONDARY" in repo_content_prefix
        has_context_request = "CONTEXT_REQUEST" in repo_content_prefix
        has_request_file = "REQUEST_FILE" in repo_content_prefix
        
        # Check order
        context_pos = repo_content_prefix.find("CONTEXT_REQUEST")
        request_file_pos = repo_content_prefix.find("REQUEST_FILE")
        correct_order = context_pos < request_file_pos and context_pos != -1
        
        print(f"✅ CONTEXT_REQUEST marked as PREFERRED: {has_preferred}")
        print(f"✅ REQUEST_FILE marked as SECONDARY: {has_secondary}")
        print(f"✅ CONTEXT_REQUEST appears before REQUEST_FILE: {correct_order}")
        
        return has_preferred and has_secondary and correct_order
        
    except Exception as e:
        print(f"❌ ERROR verifying repo_content_prefix: {e}")
        return False

def main():
    """Run verification."""
    
    print("CONTEXT_REQUEST Priority Fix Verification")
    print("=" * 50)
    
    fix_applied = verify_fix_applied()
    prefix_correct = verify_repo_content_prefix()
    
    print("\n" + "=" * 50)
    print("VERIFICATION SUMMARY:")
    print(f"  Fix Applied to get_repo_messages(): {'✅ YES' if fix_applied else '❌ NO'}")
    print(f"  repo_content_prefix Structure: {'✅ CORRECT' if prefix_correct else '❌ INCORRECT'}")
    
    if fix_applied and prefix_correct:
        print("\n🎉 VERIFICATION COMPLETE: The CONTEXT_REQUEST priority fix is successfully applied!")
        print("\nThe LLM should now:")
        print("  - Receive clear, non-conflicting instructions")
        print("  - Naturally prefer CONTEXT_REQUEST over REQUEST_FILE")
        print("  - Experience reduced cognitive load when choosing protocols")
        return True
    else:
        print("\n❌ VERIFICATION FAILED: The fix may not be properly applied")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
