"""
Dependency Analyzer - Analyzes import statements and module dependencies.

This module examines import statements to build comprehensive dependency
maps between modules, including import types and dependency strength.
"""

import ast
from typing import Dict, List, Any

from .ir_context import IRContext, DependencyInfo


class DependencyAnalyzer:
    """
    Analyzes module dependencies through import statements.
    
    This analyzer examines:
    - Import statements and their types
    - Module aliasing
    - Relative vs absolute imports
    - Dependency strength based on usage patterns
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the dependency analyzer with configuration.
        
        Args:
            config: Configuration dictionary for dependency analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.include_stdlib = config.get('include_stdlib', False)
        self.include_external = config.get('include_external', True)
        
        # Common standard library modules to potentially exclude
        self.stdlib_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'collections', 'itertools',
            'functools', 'operator', 're', 'math', 'random', 'pathlib', 'typing',
            'dataclasses', 'abc', 'contextlib', 'copy', 'pickle', 'logging',
            'unittest', 'pytest', 'argparse', 'configparser', 'io', 'tempfile'
        }
    
    def analyze(self, context: IRContext) -> IRContext:
        """
        Analyze dependencies for all modules in the context.
        
        Args:
            context: The IR context containing modules to analyze
            
        Returns:
            Updated context with dependency information
        """
        if self.verbose:
            print(f"   Analyzing dependencies in {len(context.modules)} modules")
        
        # First pass: extract import statements
        for module_name, module_info in context.modules.items():
            dependencies = self._analyze_module_dependencies(module_info, context)
            module_info.dependencies = dependencies
        
        # Second pass: calculate dependency strengths
        self._calculate_dependency_strengths(context)
        
        if self.verbose:
            total_deps = sum(len(module.dependencies) for module in context.modules.values())
            print(f"   Found {total_deps} dependencies")
        
        return context
    
    def _analyze_module_dependencies(self, module_info, context: IRContext) -> List[DependencyInfo]:
        """
        Analyze dependencies for a single module.
        
        Args:
            module_info: ModuleInfo object to analyze
            context: IR context for cross-module analysis
            
        Returns:
            List of DependencyInfo objects
        """
        if not module_info.ast_tree:
            return []
        
        dependencies = []
        
        for node in ast.walk(module_info.ast_tree):
            if isinstance(node, ast.Import):
                deps = self._process_import(node)
                dependencies.extend(deps)
            elif isinstance(node, ast.ImportFrom):
                deps = self._process_from_import(node)
                dependencies.extend(deps)
        
        # Filter dependencies based on configuration
        filtered_deps = []
        for dep in dependencies:
            if self._should_include_dependency(dep):
                filtered_deps.append(dep)
        
        return filtered_deps
    
    def _process_import(self, node: ast.Import) -> List[DependencyInfo]:
        """Process a regular import statement."""
        dependencies = []
        
        for alias in node.names:
            module_name = alias.name.split('.')[0]  # Get top-level module
            
            dep = DependencyInfo(
                module=module_name,
                import_type="import",
                alias=alias.asname
            )
            dependencies.append(dep)
        
        return dependencies
    
    def _process_from_import(self, node: ast.ImportFrom) -> List[DependencyInfo]:
        """Process a from...import statement."""
        if not node.module:
            return []  # Skip relative imports without module
        
        module_name = node.module.split('.')[0]  # Get top-level module
        imported_names = [alias.name for alias in node.names]
        
        dep = DependencyInfo(
            module=module_name,
            import_type="from_import",
            imported_names=imported_names
        )
        
        if node.level > 0:
            dep.import_type = "relative"
        
        return [dep]
    
    def _should_include_dependency(self, dep: DependencyInfo) -> bool:
        """Determine if a dependency should be included based on configuration."""
        # Check if it's a standard library module
        if dep.module in self.stdlib_modules:
            return self.include_stdlib
        
        # For now, include all other dependencies
        # In the future, we could add logic to detect external vs internal modules
        return self.include_external
    
    def _calculate_dependency_strengths(self, context: IRContext) -> None:
        """
        Calculate dependency strengths based on usage patterns.
        
        Args:
            context: IR context with modules and their dependencies
        """
        # Simple heuristic for now: base strength on number of imports
        for module_info in context.modules.values():
            import_counts = {}
            
            # Count imports per module
            for dep in module_info.dependencies:
                import_counts[dep.module] = import_counts.get(dep.module, 0) + 1
            
            # Update strengths
            for dep in module_info.dependencies:
                count = import_counts[dep.module]
                if count >= 5:
                    dep.strength = "strong"
                elif count >= 2:
                    dep.strength = "medium"
                else:
                    dep.strength = "weak"
