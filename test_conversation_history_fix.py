#!/usr/bin/env python3
"""
Test script to verify that the conversation history update fix works correctly.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_conversation_history_update():
    """Test that conversation history update works without errors."""
    print("🧪 Testing Conversation History Update Fix")
    print("=" * 60)
    
    try:
        from aider.context_request.aider_context_request_integration import AiderContextRequestIntegration
        
        # Create an integration instance
        integration = AiderContextRequestIntegration("aider-main")
        
        print("✅ AiderContextRequestIntegration created successfully")
        
        # Test the update_conversation_history method with correct signature
        print("\n🔍 Testing update_conversation_history method...")
        
        # This should work without errors
        integration.update_conversation_history("user", "How does authentication work?")
        integration.update_conversation_history("assistant", "Authentication works by...")
        
        print("✅ update_conversation_history called successfully")
        
        # Check that the conversation history was updated
        if len(integration.conversation_history) == 2:
            print("✅ Conversation history updated correctly")
            print(f"   - Message 1: {integration.conversation_history[0]['role']} - {integration.conversation_history[0]['content'][:50]}...")
            print(f"   - Message 2: {integration.conversation_history[1]['role']} - {integration.conversation_history[1]['content'][:50]}...")
        else:
            print(f"❌ Conversation history length incorrect: {len(integration.conversation_history)}")
            return False
        
        # Test that the method signature is correct (no unexpected keyword arguments)
        print("\n🔍 Testing method signature compatibility...")
        
        try:
            # This should work - correct signature
            integration.update_conversation_history("user", "Another test message")
            print("✅ Correct method signature works")
        except TypeError as e:
            print(f"❌ Method signature error: {e}")
            return False
        
        # Test that old incorrect calls would fail (as expected)
        print("\n🔍 Testing that incorrect calls fail as expected...")
        
        try:
            # This should fail - incorrect signature with keyword arguments
            integration.update_conversation_history(user_query="test", llm_response="test")
            print("❌ Incorrect signature should have failed but didn't")
            return False
        except TypeError:
            print("✅ Incorrect signature correctly fails")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversation history update: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the conversation history fix test."""
    print("🚀 Conversation History Fix Test")
    print("=" * 80)
    
    success = test_conversation_history_update()
    
    print("\n" + "=" * 80)
    print("📊 CONVERSATION HISTORY FIX TEST SUMMARY")
    print("=" * 80)
    
    if success:
        print("✅ PASS - Conversation history update fix works correctly")
        print("\n📋 The fix ensures:")
        print("  1. ✅ update_conversation_history() uses correct signature: (role, content)")
        print("  2. ✅ No more 'unexpected keyword argument' errors")
        print("  3. ✅ Conversation history updates work properly")
        print("  4. ✅ Method calls use positional arguments instead of keyword arguments")
    else:
        print("❌ FAIL - Conversation history update fix has issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
