#!/usr/bin/env python3
"""
Simulate the intelligent map request system with semantic search
"""

import json
import os
import sys
from pathlib import Path
import re
from collections import defaultdict

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

# Try to use real sentence transformers, fall back to mock if not available
try:
    from sentence_transformers import SentenceTransformer
    from sentence_transformers.util import cos_sim
    REAL_EMBEDDINGS = True
    print("✅ Using real sentence transformers for semantic search")
except ImportError:
    print("⚠️  sentence-transformers not available, using mock embeddings")
    REAL_EMBEDDINGS = False

    class MockSentenceTransformer:
        def encode(self, text, convert_to_numpy=True):
            # Simple mock: return hash-based "embedding"
            return [hash(word) % 1000 for word in text.lower().split()]

    def cos_sim(embedding1, embedding2):
        # Simple mock cosine similarity
        if len(embedding1) != len(embedding2):
            return [[0.0]]

        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
        norm1 = sum(a * a for a in embedding1) ** 0.5
        norm2 = sum(b * b for b in embedding2) ** 0.5

        if norm1 == 0 or norm2 == 0:
            return [[0.0]]

        return [[dot_product / (norm1 * norm2)]]

def expand_term(term, code_terms, code_embeddings, model, top_k=5, similarity_threshold=0.3):
    """Expand search terms using semantic similarity"""
    query_embedding = model.encode(term, convert_to_numpy=True)

    expanded_terms = [term]  # Always include original term

    for i, code_term in enumerate(code_terms):
        if code_term.lower() == term.lower():
            continue

        similarity = cos_sim(query_embedding, code_embeddings[i])[0][0]
        if similarity >= similarity_threshold:
            expanded_terms.append(code_term)

    return expanded_terms[:top_k]


class SmartMapRequestHandler:
    def __init__(self):
        if REAL_EMBEDDINGS:
            self.model = SentenceTransformer('all-MiniLM-L6-v2')  # Fast, good quality model
        else:
            self.model = MockSentenceTransformer()

        self.repository_files = self._scan_repository()
        self.code_terms = self._extract_code_terms()
        self.code_embeddings = self._generate_embeddings()

    def _scan_repository(self):
        """Scan repository for files and their metadata"""
        files = []

        try:
            from aider.repomap import find_src_files
            all_files = find_src_files("aider-main")

            for file_path in all_files:
                if file_path.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c')):
                    files.append({
                        'path': file_path,
                        'name': Path(file_path).name,
                        'dir': str(Path(file_path).parent),
                        'content_preview': self._get_file_preview(file_path)
                    })
        except Exception as e:
            print(f"Error scanning repository: {e}")
            # Fallback to mock data
            files = self._get_mock_files()

        return files

    def _get_file_preview(self, file_path):
        """Get a preview of file content for analysis"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(1000)  # First 1000 chars
                return content
        except:
            return ""

    def _get_mock_files(self):
        """Mock repository files for simulation"""
        return [
            {'path': 'aider/auth/login.py', 'name': 'login.py', 'dir': 'aider/auth', 'content_preview': 'def authenticate_user(username, password):\n    """Handle user authentication"""'},
            {'path': 'aider/auth/validators.py', 'name': 'validators.py', 'dir': 'aider/auth', 'content_preview': 'def validate_credentials(username, password):\n    """Validate user credentials"""'},
            {'path': 'aider/models/user.py', 'name': 'user.py', 'dir': 'aider/models', 'content_preview': 'class User:\n    def login(self, password):\n        """User login method"""'},
            {'path': 'aider/api/auth_endpoints.py', 'name': 'auth_endpoints.py', 'dir': 'aider/api', 'content_preview': '@app.route("/login")\ndef login_endpoint():'},
            {'path': 'aider/security/encryption.py', 'name': 'encryption.py', 'dir': 'aider/security', 'content_preview': 'def hash_password(password):\n    """Hash user password"""'},
            {'path': 'aider/database/queries.py', 'name': 'queries.py', 'dir': 'aider/database', 'content_preview': 'def get_user_by_username(username):'},
            {'path': 'aider/utils/helpers.py', 'name': 'helpers.py', 'dir': 'aider/utils', 'content_preview': 'def format_response(data):'},
            {'path': 'aider/config/settings.py', 'name': 'settings.py', 'dir': 'aider/config', 'content_preview': 'AUTH_TIMEOUT = 3600'},
        ]

    def _extract_code_terms(self):
        """Extract relevant terms from code for semantic matching"""
        terms = set()

        for file_info in self.repository_files:
            # Extract from file path
            path_parts = file_info['path'].replace('/', ' ').replace('\\', ' ').replace('_', ' ').replace('.', ' ')
            terms.update(path_parts.split())

            # Extract from content preview
            content = file_info['content_preview']
            # Extract function/class names
            functions = re.findall(r'def\s+(\w+)', content)
            classes = re.findall(r'class\s+(\w+)', content)
            terms.update(functions)
            terms.update(classes)

            # Extract common keywords
            keywords = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]{2,}\b', content)
            terms.update(keywords)

        return list(terms)

    def _generate_embeddings(self):
        """Generate embeddings for all code terms"""
        return [self.model.encode(term) for term in self.code_terms]

    def handle_map_request(self, request_json):
        """Handle intelligent map request from LLM"""

        print("🔍 Processing Map Request...")
        print(f"📝 Request: {json.dumps(request_json, indent=2)}")

        keywords = request_json.get("keywords", [])
        request_type = request_json.get("type", "general")
        scope = request_json.get("scope", "all")

        # Expand keywords using semantic similarity
        expanded_keywords = []
        for keyword in keywords:
            expanded = expand_term(keyword, self.code_terms, self.code_embeddings, self.model)
            expanded_keywords.extend(expanded)
            print(f"🔍 Expanded '{keyword}' → {expanded}")

        # Remove duplicates while preserving order
        expanded_keywords = list(dict.fromkeys(expanded_keywords))

        # Search for relevant files
        relevant_files = self._search_files(expanded_keywords, request_type, scope)

        # Generate focused map
        focused_map = self._generate_focused_map(relevant_files, expanded_keywords)

        return focused_map

    def _search_files(self, keywords, request_type, scope):
        """Search for files relevant to the keywords"""

        scored_files = []

        for file_info in self.repository_files:
            score = self._calculate_relevance_score(file_info, keywords, request_type, scope)
            if score > 0:
                scored_files.append((score, file_info))

        # Sort by relevance score (highest first)
        scored_files.sort(reverse=True, key=lambda x: x[0])

        # Return top relevant files
        return [file_info for score, file_info in scored_files[:10]]

    def _calculate_relevance_score(self, file_info, keywords, request_type, scope):
        """Calculate how relevant a file is to the search"""

        score = 0.0

        # Check file path
        path_lower = file_info['path'].lower()
        for keyword in keywords:
            if keyword.lower() in path_lower:
                score += 2.0  # High weight for path matches

        # Check file name
        name_lower = file_info['name'].lower()
        for keyword in keywords:
            if keyword.lower() in name_lower:
                score += 3.0  # Very high weight for filename matches

        # Check content preview
        content_lower = file_info['content_preview'].lower()
        for keyword in keywords:
            if keyword.lower() in content_lower:
                score += 1.0  # Medium weight for content matches

        # Boost score based on request type
        if request_type == "implementation":
            if any(pattern in path_lower for pattern in ['auth', 'login', 'security']):
                score *= 1.5
        elif request_type == "api":
            if any(pattern in path_lower for pattern in ['api', 'endpoint', 'route']):
                score *= 1.5
        elif request_type == "model":
            if any(pattern in path_lower for pattern in ['model', 'schema', 'entity']):
                score *= 1.5

        return score

    def _generate_focused_map(self, relevant_files, keywords):
        """Generate a focused repository map"""

        if not relevant_files:
            return "No relevant files found for the given keywords."

        map_content = f"# Focused Repository Map\n"
        map_content += f"## Search Keywords: {', '.join(keywords)}\n"
        map_content += f"## Found {len(relevant_files)} relevant files:\n\n"

        # Group files by directory
        files_by_dir = defaultdict(list)
        for file_info in relevant_files:
            files_by_dir[file_info['dir']].append(file_info)

        for directory, files in files_by_dir.items():
            map_content += f"### {directory}/\n"
            for file_info in files:
                map_content += f"- **{file_info['name']}**: {file_info['path']}\n"
                if file_info['content_preview']:
                    preview = file_info['content_preview'][:100].replace('\n', ' ')
                    map_content += f"  Preview: {preview}...\n"
            map_content += "\n"

        return map_content


def simulate_llm_queries():
    """Simulate various LLM queries and system responses"""

    handler = SmartMapRequestHandler()

    # Test queries
    test_queries = [
        {
            "description": "User asks about login authentication",
            "llm_query": "Show me where login authentication is implemented",
            "map_request": {
                "keywords": ["login", "authentication", "auth", "user", "password"],
                "type": "implementation",
                "scope": "functions_and_classes"
            }
        },
        {
            "description": "User asks about repository mapping",
            "llm_query": "How does the repository mapping work?",
            "map_request": {
                "keywords": ["repository", "repo", "map", "mapping", "tags"],
                "type": "implementation",
                "scope": "all"
            }
        },
        {
            "description": "User asks about code analysis",
            "llm_query": "How does aider analyze code structure?",
            "map_request": {
                "keywords": ["analysis", "parse", "tree", "ast", "structure"],
                "type": "implementation",
                "scope": "functions"
            }
        },
        {
            "description": "User asks about LLM integration",
            "llm_query": "How does aider integrate with language models?",
            "map_request": {
                "keywords": ["llm", "model", "openai", "anthropic", "api"],
                "type": "integration",
                "scope": "all"
            }
        }
    ]

    print("🚀 Simulating Smart Map Request System")
    print("=" * 60)

    for i, test in enumerate(test_queries, 1):
        print(f"\n🎯 Test Case {i}: {test['description']}")
        print(f"👤 User Query: \"{test['llm_query']}\"")
        print(f"🤖 LLM Map Request:")

        # Simulate LLM sending map request
        focused_map = handler.handle_map_request(test['map_request'])

        print(f"\n📋 System Response (Focused Map):")
        print("-" * 40)
        print(focused_map)
        print("-" * 40)

        print(f"\n✅ Result: LLM now has focused context for perfect answer!")
        print("=" * 60)


if __name__ == "__main__":
    simulate_llm_queries()
