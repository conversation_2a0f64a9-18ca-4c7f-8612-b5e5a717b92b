#!/usr/bin/env python3
"""
Simple test to verify the compute_next_boundary bug.
"""

import os
import sys

def test_function_exists():
    """Test if compute_next_boundary function exists in the target file."""
    print("🔍 Testing if compute_next_boundary function exists")
    print("=" * 60)
    
    # Check if the target file exists
    target_file = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____\market_data\market_data_repository.py"
    
    if not os.path.exists(target_file):
        print(f"❌ File does not exist: {target_file}")
        return False
    
    print(f"✅ File exists: {target_file}")
    
    # Check if function exists in file
    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        function_exists = "compute_next_boundary" in content
        print(f"Function exists in file: {function_exists}")
        
        if function_exists:
            # Show the function definition
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "compute_next_boundary" in line and "def " in line:
                    print(f"✅ Function definition found at line {i+1}:")
                    print(f"   {line.strip()}")
                    # Show a few lines of context
                    for j in range(max(0, i-1), min(len(lines), i+4)):
                        if j != i:
                            print(f"   {lines[j].strip()}")
                    break
            return True
        else:
            print("❌ Function not found in file")
            return False
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def test_smart_map_request():
    """Test if Smart Map Request can find the function."""
    print("\n🔍 Testing Smart Map Request System")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        print("✅ Modules imported successfully")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        dashboard_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        
        if not os.path.exists(dashboard_path):
            print(f"❌ Dashboard path does not exist: {dashboard_path}")
            return False
        
        print(f"✅ Dashboard path exists")
        
        repo_map = RepoMap(
            map_tokens=8192,
            root=dashboard_path,
            main_model=model,
            io=io,
            verbose=False
        )
        
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=dashboard_path,
            io=io
        )
        
        print("✅ Smart Map Request Handler created")
        
        # Test MAP_REQUEST
        map_request = {
            "keywords": ["compute_next_boundary"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }
        
        print(f"Testing MAP_REQUEST with keywords: {map_request['keywords']}")
        
        result = handler.handle_map_request(map_request)
        
        print(f"Result length: {len(result)} characters")
        
        # Check if the function is in the result
        function_in_result = "compute_next_boundary" in result
        print(f"Function found in result: {function_in_result}")
        
        if not function_in_result:
            print("❌ BUG CONFIRMED: Function exists in file but NOT found by Smart Map Request!")
            print(f"Result preview: {result[:300]}...")
        else:
            print("✅ Function found by Smart Map Request")
        
        return function_in_result
        
    except Exception as e:
        print(f"❌ Error in Smart Map Request test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the bug verification tests."""
    print("🚀 compute_next_boundary Bug Verification")
    print("=" * 80)
    
    # Test 1: Check if function exists in file
    function_exists = test_function_exists()
    
    # Test 2: Check if Smart Map Request can find it
    smart_map_works = test_smart_map_request()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 BUG VERIFICATION SUMMARY")
    print("=" * 80)
    
    print(f"Function exists in file: {'✅ YES' if function_exists else '❌ NO'}")
    print(f"Smart Map Request finds it: {'✅ YES' if smart_map_works else '❌ NO'}")
    
    if function_exists and not smart_map_works:
        print("\n🚨 BUG CONFIRMED!")
        print("   The function exists in the file but Smart Map Request cannot find it.")
        print("   This proves the search algorithm is broken.")
    elif function_exists and smart_map_works:
        print("\n✅ NO BUG!")
        print("   Smart Map Request is working correctly.")
    elif not function_exists:
        print("\n⚠️  FUNCTION DOES NOT EXIST!")
        print("   The function is not in the target file.")
    
    return function_exists and smart_map_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
