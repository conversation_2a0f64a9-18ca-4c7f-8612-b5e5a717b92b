#!/usr/bin/env python3
"""
Simple script to check IR data structure.
"""

import sys
import os
import json

# Add aider to path
sys.path.insert(0, "aider-main")

def check_ir_structure():
    """Check the IR data structure."""
    
    print("🔍 CHECKING IR DATA STRUCTURE")
    print("=" * 60)
    
    try:
        from aider_integration_service import AiderIntegrationService
        
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        service = AiderIntegrationService()
        ir_data = service.generate_mid_level_ir(external_project)
        
        print(f"📊 IR data type: {type(ir_data)}")
        print(f"📊 IR data keys: {list(ir_data.keys()) if ir_data else 'None'}")
        
        if ir_data and "modules" in ir_data:
            modules = ir_data["modules"]
            print(f"📊 Number of modules: {len(modules)}")
            
            if modules:
                # Check first module structure
                first_module = modules[0]
                print(f"📊 First module keys: {list(first_module.keys())}")
                
                if "entities" in first_module:
                    entities = first_module["entities"]
                    print(f"📊 First module has {len(entities)} entities")
                    
                    if entities:
                        # Check first entity structure
                        first_entity = entities[0]
                        print(f"📊 First entity keys: {list(first_entity.keys())}")
                        print(f"📊 First entity sample:")
                        for key, value in first_entity.items():
                            if isinstance(value, (str, int, bool, type(None))):
                                print(f"   {key}: {value}")
                            elif isinstance(value, list):
                                print(f"   {key}: {value[:3]}{'...' if len(value) > 3 else ''} (len: {len(value)})")
                            else:
                                print(f"   {key}: {type(value)}")
                        
                        # Look for inheritance data in entities
                        entities_with_inheritance = 0
                        for entity in entities[:10]:  # Check first 10
                            if (entity.get("inherits_from") or 
                                entity.get("method_overrides") or 
                                entity.get("calls_super") or 
                                entity.get("overridden_by") or
                                entity.get("class_name")):
                                entities_with_inheritance += 1
                        
                        print(f"📊 Entities with inheritance data (first 10): {entities_with_inheritance}")
        
        # Save a small sample for inspection
        if ir_data:
            sample_data = {
                "structure": {
                    "keys": list(ir_data.keys()),
                    "modules_count": len(ir_data.get("modules", [])),
                    "metadata": ir_data.get("metadata", {})
                }
            }
            
            if ir_data.get("modules"):
                sample_module = ir_data["modules"][0]
                sample_data["sample_module"] = {
                    "keys": list(sample_module.keys()),
                    "entities_count": len(sample_module.get("entities", [])),
                    "module_name": sample_module.get("module_name", "unknown")
                }
                
                if sample_module.get("entities"):
                    sample_entity = sample_module["entities"][0]
                    sample_data["sample_entity"] = sample_entity
            
            with open("ir_structure_sample.json", "w", encoding="utf-8") as f:
                json.dump(sample_data, f, indent=2)
            print(f"💾 Saved IR structure sample to: ir_structure_sample.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_ir_structure()
