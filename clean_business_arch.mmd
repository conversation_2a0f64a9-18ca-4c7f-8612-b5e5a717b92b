graph TB
    %% Dynamic Business Architecture from IR Data
    
    Users[👥 Users]
    External[🌐 External Systems]
    
    subgraph System[🏢 Aider System]
        direction TB
        
        subgraph UI[User Interfaces]
            GUI[📱 GUI<br/>102 components]
            CLI[🖥️ CLI Interface<br/>42 components]
            Web[🌐 Web Interface<br/>29 components]
        end
        
        subgraph Core[Core Business Services]
            Coder[✏️ Code Editor<br/>673 components]
            Context[🔍 Context Engine<br/>232 components]
            Mapper[🗺️ Repository Mapper<br/>247 components]
        end
        
        subgraph Integration[Integration Services]
            Service[🔗 Integration Service<br/>337 components]
            Handler[🔌 Request Handler<br/>164 components]
        end
        
        subgraph Data[Data Services]
            Models[📊 Data Models<br/>195 components]
            Metadata[🗃️ Metadata Service<br/>44 components]
        end
    end
    
    %% Workflows
    Users --> UI
    UI --> Core
    Core --> Integration
    Integration --> External
    Core --> Data
    
    %% Styling
    style Users fill:#e1f5fe,stroke:#0277bd
    style External fill:#fff3e0,stroke:#f57c00
    style GUI fill:#e8f5e8,stroke:#2e7d32
    style CLI fill:#e8f5e8,stroke:#2e7d32
    style Web fill:#e8f5e8,stroke:#2e7d32
    style Coder fill:#ffebee,stroke:#d32f2f
    style Context fill:#fff8e1,stroke:#f9a825
    style Mapper fill:#fff8e1,stroke:#f9a825
    style Service fill:#fce4ec,stroke:#c2185b
    style Handler fill:#fce4ec,stroke:#c2185b
    style Models fill:#e0f2f1,stroke:#00695c
    style Metadata fill:#e0f2f1,stroke:#00695c
