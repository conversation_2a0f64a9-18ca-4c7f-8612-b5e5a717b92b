#!/usr/bin/env python3
"""
Test script for the new direct IR context flow.
This tests "Step 1" of the new required flow:
User query → system provides LLM-Friendly IR Context Package directly.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def test_direct_ir_context_flow():
    """Test the new direct IR context generation flow."""
    
    print("🧪 Testing New Direct IR Context Flow")
    print("=" * 50)
    
    try:
        # Import required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        from aider.coders.base_coder import Coder
        from aider.io import InputOutput
        from aider.models import Model
        
        print("✅ Successfully imported required modules")
        
        # Test 1: Test focus entity extraction
        print("\n📝 Test 1: Focus Entity Extraction")
        
        # Create a mock coder instance to test the method
        class MockCoder:
            def _extract_focus_entities_from_query(self, user_message):
                """Copy of the method from base_coder.py for testing"""
                import re
                
                stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their'}
                
                words = re.findall(r'\b\w+\b', user_message.lower())
                focus_entities = [word for word in words if word not in stop_words and len(word) > 2]
                return focus_entities[:8]
        
        mock_coder = MockCoder()
        
        test_queries = [
            "Why is my context selection taking so long?",
            "How does authentication work in this system?",
            "What are the main classes and their inheritance relationships?",
            "How do I fix performance issues in the code?"
        ]
        
        for query in test_queries:
            entities = mock_coder._extract_focus_entities_from_query(query)
            print(f"Query: '{query}'")
            print(f"Focus entities: {entities}")
            print()
        
        # Test 2: Test IR Context Request Creation
        print("📝 Test 2: IR Context Request Creation")
        
        test_query = "Why is my context selection taking so long?"
        focus_entities = mock_coder._extract_focus_entities_from_query(test_query)
        
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=focus_entities,
            max_tokens=2000,
            llm_friendly=True,
            include_code_context=True,
            max_entities=8
        )
        
        print(f"✅ Created IR Context Request:")
        print(f"   User Query: {request.user_query}")
        print(f"   Task Type: {request.task_type}")
        print(f"   Focus Entities: {request.focus_entities}")
        print(f"   Max Tokens: {request.max_tokens}")
        print(f"   LLM Friendly: {request.llm_friendly}")
        
        # Test 3: Test IR Context Generation
        print("\n📝 Test 3: IR Context Generation")
        
        # Create context request handler
        project_path = "."
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Process the IR context request
        print("🔄 Processing IR context request...")
        result = handler.process_ir_context_request(request)
        
        if result and "llm_friendly_package" in result:
            llm_package = result["llm_friendly_package"]
            print(f"✅ Successfully generated IR context package!")
            print(f"   Package size: {len(llm_package)} characters")
            print(f"   Contains 'CRITICAL ENTITIES': {'CRITICAL ENTITIES' in llm_package}")
            print(f"   Contains 'KEY IMPLEMENTATIONS': {'KEY IMPLEMENTATIONS' in llm_package}")
            
            # Show a preview of the package
            print("\n📄 Package Preview (first 500 chars):")
            print("-" * 50)
            print(llm_package[:500] + "..." if len(llm_package) > 500 else llm_package)
            print("-" * 50)
            
            # Test 4: Test Context Message Creation
            print("\n📝 Test 4: Context Message Creation")
            
            context_message = f"""# Intelligent Context for Your Query

{llm_package}

---

**Original User Query**: {test_query}

Please analyze the above context and provide a comprehensive answer to the user's query."""
            
            print(f"✅ Created context message for LLM injection")
            print(f"   Total message size: {len(context_message)} characters")
            print(f"   Ready for injection into conversation")
            
            # Save the full context message for inspection
            with open("test_direct_ir_context_message.txt", "w", encoding="utf-8") as f:
                f.write(context_message)
            print(f"💾 Saved full context message to: test_direct_ir_context_message.txt")
            
            return True
            
        else:
            print("❌ Failed to generate IR context package")
            print(f"   Result: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flow_comparison():
    """Compare old vs new flow characteristics."""
    
    print("\n🔄 Flow Comparison")
    print("=" * 50)
    
    print("📊 OLD FLOW:")
    print("   User Query → LLM → MAP_REQUEST → System → Repository Map → LLM → CONTEXT_REQUEST → System → Code Context")
    print("   Steps: 7")
    print("   LLM Calls: 2")
    print("   User Interaction: Manual request generation")
    
    print("\n📊 NEW FLOW:")
    print("   User Query → System → LLM-Friendly IR Context Package → LLM")
    print("   Steps: 3")
    print("   LLM Calls: 1")
    print("   User Interaction: Direct, automatic")
    
    print("\n✅ NEW FLOW BENEFITS:")
    print("   • 57% fewer steps (3 vs 7)")
    print("   • 50% fewer LLM calls (1 vs 2)")
    print("   • Automatic context generation")
    print("   • No manual request formatting needed")
    print("   • Immediate intelligent context")

if __name__ == "__main__":
    print("🚀 Starting New Direct IR Context Flow Test")
    print("Testing implementation of 'Step 1' of the new required flow\n")
    
    success = test_direct_ir_context_flow()
    
    if success:
        test_flow_comparison()
        print("\n🎉 All tests passed! New direct IR context flow is working.")
        print("\n📋 NEXT STEPS:")
        print("   1. Test with real aider session")
        print("   2. Verify context injection works with LLM")
        print("   3. Compare response quality vs old flow")
        print("   4. Add configuration options")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
    
    print(f"\n📁 Test completed. Check 'test_direct_ir_context_message.txt' for generated context.")
