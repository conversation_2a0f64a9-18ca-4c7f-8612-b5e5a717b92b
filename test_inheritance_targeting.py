#!/usr/bin/env python3
"""
Test different queries to find entities that actually have inheritance relationships.
This will target specific classes and methods that we know have inheritance.
"""

import os
import sys

def test_inheritance_targeting():
    """Test queries specifically targeting classes with inheritance."""
    print("🎯 Testing Inheritance-Targeting Queries")
    print("=" * 50)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)

        # Import the real working modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported working IR context request modules")
        
        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Test different queries that should find inheritance
        test_queries = [
            {
                "name": "Test Classes Query",
                "query": "How do test classes work in this codebase?",
                "description": "Target test classes that inherit from unittest.TestCase",
                "focus": ["test", "unittest", "TestCase", "setUp", "tearDown"]
            },
            {
                "name": "Exception Classes Query", 
                "query": "How are custom exceptions implemented?",
                "description": "Target exception classes that inherit from Exception/ValueError",
                "focus": ["exception", "error", "ValueError", "Exception", "raise"]
            },
            {
                "name": "Formatter Classes Query",
                "query": "How do formatters work in this project?",
                "description": "Target formatter classes that inherit from argparse.HelpFormatter",
                "focus": ["formatter", "format", "argparse", "HelpFormatter", "help"]
            },
            {
                "name": "Model Classes Query",
                "query": "How are AI models configured and used?",
                "description": "Target model classes that might have inheritance",
                "focus": ["model", "config", "settings", "ModelSettings", "AI"]
            }
        ]
        
        best_result = None
        best_inheritance_count = 0
        
        for test_case in test_queries:
            print(f"\n🔍 Testing: {test_case['name']}")
            print(f"   Query: {test_case['query']}")
            print(f"   Target: {test_case['description']}")
            
            # Create targeted request
            request = IRContextRequest(
                user_query=test_case['query'],
                task_description=test_case['description'],
                task_type="general_analysis",
                focus_entities=test_case['focus'],
                max_tokens=2500,
                include_ir_slices=True,
                include_code_context=True,
                llm_friendly=True,
                max_output_chars=35000,
                max_entities=12
            )
            
            result = handler.process_ir_context_request(request)
            
            if "ir_slices" in result:
                inheritance_count = 0
                real_inheritance_count = 0
                
                for entity in result['ir_slices']:
                    inherits_from = entity.get('inherits_from', [])
                    class_name = entity.get('class_name')
                    
                    if inherits_from:
                        inheritance_count += 1
                        # Check if it's real inheritance (not just empty list)
                        if len(inherits_from) > 0 and inherits_from != ['']:
                            real_inheritance_count += 1
                            print(f"      ✅ {entity.get('entity_name', 'unknown')} inherits from: {inherits_from}")
                    elif class_name:
                        print(f"      📝 {entity.get('entity_name', 'unknown')} (method of {class_name})")
                
                print(f"   📊 Results: {real_inheritance_count} entities with real inheritance")
                
                # Track the best result
                if real_inheritance_count > best_inheritance_count:
                    best_inheritance_count = real_inheritance_count
                    best_result = {
                        'test_case': test_case,
                        'result': result,
                        'inheritance_count': real_inheritance_count
                    }
            else:
                print("   ❌ No IR slices found")
        
        # Generate package with the best result
        if best_result and best_inheritance_count > 0:
            print(f"\n🏆 Best Result: {best_result['test_case']['name']}")
            print(f"   Found {best_inheritance_count} entities with real inheritance!")
            
            # Save the best package
            if "llm_friendly_package" in best_result['result']:
                output_file = "inheritance_targeting_best_package.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(best_result['result']["llm_friendly_package"])
                print(f"   📄 Best package saved to: {output_file}")
                
                # Verify inheritance data in the package
                package_content = best_result['result']["llm_friendly_package"]
                
                # Count inheritance instances in the package
                inherits_from_count = package_content.count("- Inherits From:")
                no_inheritance_count = package_content.count("No inheritance detected")
                real_inheritance_in_package = inherits_from_count - no_inheritance_count
                
                print(f"   📈 Package Analysis:")
                print(f"      Total 'Inherits From' lines: {inherits_from_count}")
                print(f"      'No inheritance detected': {no_inheritance_count}")
                print(f"      Real inheritance shown: {real_inheritance_in_package}")
                
                if real_inheritance_in_package > 0:
                    print("   🎉 SUCCESS: Found entities with real inheritance in package!")
                    return True, output_file
                else:
                    print("   ⚠️  Package still shows 'No inheritance detected' for all entities")
                    return False, output_file
            else:
                print("   ❌ No LLM package generated")
                return False, None
        else:
            print(f"\n❌ No queries found entities with real inheritance")
            print("   This suggests the inheritance data might not be flowing through properly")
            return False, None
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def analyze_inheritance_in_package(package_file):
    """Analyze the inheritance data in the generated package."""
    if not os.path.exists(package_file):
        print(f"❌ Package file {package_file} not found")
        return
    
    print(f"\n🔍 Analyzing inheritance data in {package_file}")
    print("=" * 60)
    
    with open(package_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all inheritance lines
    lines = content.split('\n')
    inheritance_lines = []
    
    for i, line in enumerate(lines):
        if "- Inherits From:" in line:
            # Get the entity name from previous lines
            entity_name = "Unknown"
            for j in range(i-5, i):
                if j >= 0 and lines[j].startswith("### "):
                    entity_name = lines[j].replace("### ", "").split(" ")[1]
                    break
            
            inheritance_lines.append({
                'entity': entity_name,
                'line': line.strip(),
                'line_number': i + 1
            })
    
    print(f"Found {len(inheritance_lines)} inheritance declarations:")
    
    real_inheritance = []
    no_inheritance = []
    
    for item in inheritance_lines:
        if "No inheritance detected" in item['line']:
            no_inheritance.append(item)
        else:
            real_inheritance.append(item)
            print(f"   ✅ {item['entity']}: {item['line']}")
    
    print(f"\n📊 Summary:")
    print(f"   Real inheritance: {len(real_inheritance)}")
    print(f"   No inheritance: {len(no_inheritance)}")
    
    if len(real_inheritance) > 0:
        print(f"\n🎉 SUCCESS: Found {len(real_inheritance)} entities with real inheritance!")
    else:
        print(f"\n❌ ISSUE: All entities show 'No inheritance detected'")


def main():
    """Main function."""
    print("🧪 Testing Inheritance-Targeting Queries")
    print("=" * 55)
    
    success, output_file = test_inheritance_targeting()
    
    if success and output_file:
        print(f"\n✅ INHERITANCE TARGETING: SUCCESS")
        print(f"📄 Best package: {output_file}")
        
        # Analyze the package content
        analyze_inheritance_in_package(output_file)
        
    elif output_file:
        print(f"\n⚠️  INHERITANCE TARGETING: PARTIAL SUCCESS")
        print(f"📄 Package generated: {output_file}")
        print("❌ But inheritance data not showing properly in package")
        
        # Still analyze what we got
        analyze_inheritance_in_package(output_file)
        
    else:
        print(f"\n❌ INHERITANCE TARGETING: FAILED")
        print("❌ No entities with inheritance found")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
