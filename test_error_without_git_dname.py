#!/usr/bin/env python3

"""
Test to demonstrate the error when no git_dname is provided.
This test shows what happens when aider is run without a project directory argument.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def test_error_without_git_dname():
    """Test that shows the error when no project directory is provided."""
    
    print("🧪 Testing Error Without git_dname")
    print("=" * 50)
    
    # Create a temporary directory structure with a git repository
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create a simple git repository
        git_dir = os.path.join(temp_dir, "simple_repo")
        os.makedirs(git_dir)
        
        # Initialize a git repository
        subprocess.run(["git", "init"], cwd=git_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=git_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=git_dir, capture_output=True)
        
        # Create a simple file
        test_file = os.path.join(git_dir, "test.py")
        with open(test_file, 'w') as f:
            f.write('print("Hello, World!")\n')
        
        # Add and commit the file
        subprocess.run(["git", "add", "."], cwd=git_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=git_dir, capture_output=True)
        
        print(f"✅ Created git repository: {git_dir}")
        print(f"   📄 {test_file}")
        
        # Test GitRepo and Coder initialization WITHOUT git_dname
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.repo import GitRepo
            from aider.coders.base_coder import Coder
            from aider.models import Model
            from aider.io import InputOutput
            
            print("✅ Successfully imported aider modules")
            
            # Create a GitRepo WITHOUT git_dname (simulating running aider without project directory)
            io = InputOutput()
            repo = GitRepo(
                io=io,
                fnames=[test_file],  # Just provide a file, no git_dname
                git_dname=None,      # This is the key - no project directory specified
                aider_ignore_file=None
            )
            
            print(f"📁 GitRepo root: {repo.root}")
            print(f"📁 GitRepo original git_dname: {getattr(repo, '_original_git_dname', 'NOT SET')}")
            
            # Try to create a Coder instance - this should trigger the error
            model = Model("gpt-3.5-turbo")
            print("🔥 Attempting to create Coder without project directory...")
            
            coder = Coder.create(
                main_model=model,
                edit_format="informative",
                io=io,
                repo=repo,
                fnames=[test_file],
                use_git=True,
                map_tokens=1000
            )
            
            # If we get here, the error wasn't triggered
            print("❌ UNEXPECTED: Coder creation succeeded without project directory")
            return False
            
        except ValueError as e:
            # This is the expected error
            print("✅ SUCCESS: Got expected ValueError!")
            print(f"🔥 Error message: {e}")
            
            # Check if the error message contains the expected information
            error_str = str(e)
            if ("Context request system requires a project directory argument" in error_str and
                "Please run aider with a project directory argument" in error_str):
                print("✅ SUCCESS: Error message contains correct guidance!")
                return True
            else:
                print("❌ FAILED: Error message doesn't contain expected guidance")
                print(f"   Got: {error_str}")
                return False
                
        except ImportError as e:
            print(f"❌ FAILED: Could not import aider modules: {e}")
            return False
        except Exception as e:
            print(f"❌ FAILED: Got unexpected error: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_success_with_git_dname():
    """Test that shows success when project directory is provided."""
    
    print("\n🧪 Testing Success With git_dname")
    print("=" * 50)
    
    # Create a temporary directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create a trading project structure
        trading_dir = os.path.join(temp_dir, "trading_project")
        os.makedirs(trading_dir)
        
        # Initialize a git repository
        subprocess.run(["git", "init"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=trading_dir, capture_output=True)
        
        # Create a simple file
        test_file = os.path.join(trading_dir, "trading.py")
        with open(test_file, 'w') as f:
            f.write('def close_position_based_on_conditions(): pass\n')
        
        # Add and commit the file
        subprocess.run(["git", "add", "."], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=trading_dir, capture_output=True)
        
        print(f"✅ Created trading project: {trading_dir}")
        
        # Test GitRepo and Coder initialization WITH git_dname
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.repo import GitRepo
            from aider.coders.base_coder import Coder
            from aider.models import Model
            from aider.io import InputOutput
            
            # Create a GitRepo WITH git_dname (simulating proper usage)
            io = InputOutput()
            repo = GitRepo(
                io=io,
                fnames=[],
                git_dname=trading_dir,  # This is the key - project directory specified
                aider_ignore_file=None
            )
            
            print(f"📁 GitRepo root: {repo.root}")
            print(f"📁 GitRepo original git_dname: {getattr(repo, '_original_git_dname', 'NOT SET')}")
            
            # Create a Coder instance - this should work
            model = Model("gpt-3.5-turbo")
            print("✅ Attempting to create Coder with project directory...")
            
            coder = Coder.create(
                main_model=model,
                edit_format="informative",
                io=io,
                repo=repo,
                fnames=[],
                use_git=True,
                map_tokens=1000
            )
            
            print("✅ SUCCESS: Coder creation succeeded with project directory!")
            print(f"📁 Context root: {getattr(coder.context_request_integration, 'project_path', 'NOT SET')}")
            return True
            
        except Exception as e:
            print(f"❌ FAILED: Unexpected error with project directory: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main test function."""
    print("Testing Context Request System Error Handling")
    print("=" * 60)
    
    # Test 1: Show error without git_dname
    success1 = test_error_without_git_dname()
    
    # Test 2: Show success with git_dname
    success2 = test_success_with_git_dname()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED")
        print("✅ Error correctly shown when no project directory provided")
        print("✅ Success when project directory is provided")
    else:
        print("\n❌ SOME TESTS FAILED")
        print(f"   Error test: {'PASSED' if success1 else 'FAILED'}")
        print(f"   Success test: {'PASSED' if success2 else 'FAILED'}")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
