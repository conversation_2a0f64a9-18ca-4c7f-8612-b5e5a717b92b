graph LR
    %% Module Dependency Graph

    aider["aider"]
    style aider fill:#ff6b6b,stroke:#d63031,color:#fff
    base_coder["base_coder"]
    style base_coder fill:#ff6b6b,stroke:#d63031,color:#fff
    test_commands["test_commands"]
    style test_commands fill:#ff6b6b,stroke:#d63031,color:#fff
    base_coder_old["base_coder_old"]
    style base_coder_old fill:#ff6b6b,stroke:#d63031,color:#fff
    io["io"]
    style io fill:#ff6b6b,stroke:#d63031,color:#fff
    aider_integration_service["aider_integration..."]
    style aider_integration_service fill:#ff6b6b,stroke:#d63031,color:#fff
    commands["commands"]
    style commands fill:#ff6b6b,stroke:#d63031,color:#fff
    test_llm_message_inspection["test_llm_message_..."]
    style test_llm_message_inspection fill:#ff6b6b,stroke:#d63031,color:#fff
    repomap["repomap"]
    style repomap fill:#ff6b6b,stroke:#d63031,color:#fff
    benchmark["benchmark"]
    style benchmark fill:#ff6b6b,stroke:#d63031,color:#fff
    models["models"]
    style models fill:#ff6b6b,stroke:#d63031,color:#fff
    test_complete_repo_map_elimination["test_complete_rep..."]
    style test_complete_repo_map_elimination fill:#ff6b6b,stroke:#d63031,color:#fff
    test_llm_workflow_understanding["test_llm_workflow..."]
    style test_llm_workflow_understanding fill:#ff6b6b,stroke:#d63031,color:#fff
    debug_tag_parsing["debug_tag_parsing"]
    style debug_tag_parsing fill:#ff6b6b,stroke:#d63031,color:#fff
    test_repo_map_leak_detection["test_repo_map_lea..."]
    style test_repo_map_leak_detection fill:#ff6b6b,stroke:#d63031,color:#fff
    test_repository_map_slicing_termination["test_repository_m..."]
    style test_repository_map_slicing_termination fill:#ff6b6b,stroke:#d63031,color:#fff
    test_simplified_system["test_simplified_s..."]
    style test_simplified_system fill:#ff6b6b,stroke:#d63031,color:#fff
    test_smart_map_request_implementation["test_smart_map_re..."]
    style test_smart_map_request_implementation fill:#ff6b6b,stroke:#d63031,color:#fff
    debug_repo_messages["debug_repo_messages"]
    style debug_repo_messages fill:#ff6b6b,stroke:#d63031,color:#fff
    context_request_handler["context_request_h..."]
    style context_request_handler fill:#ff6b6b,stroke:#d63031,color:#fff
    ir_context["ir_context"]
    style ir_context fill:#ff6b6b,stroke:#d63031,color:#fff
    main["main"]
    style main fill:#ff6b6b,stroke:#d63031,color:#fff
    test_coder["test_coder"]
    style test_coder fill:#ff6b6b,stroke:#d63031,color:#fff
    test_main["test_main"]
    style test_main fill:#ff6b6b,stroke:#d63031,color:#fff
    debug_search_issue["debug_search_issue"]
    style debug_search_issue fill:#ff6b6b,stroke:#d63031,color:#fff
    test_error_without_git_dname["test_error_withou..."]
    style test_error_without_git_dname fill:#ff6b6b,stroke:#d63031,color:#fff
    aider_context_request_integration["aider_context_req..."]
    style aider_context_request_integration fill:#feca57,stroke:#ff9ff3,color:#000
    dump["dump"]
    style dump fill:#feca57,stroke:#ff9ff3,color:#000
    help["help"]
    style help fill:#feca57,stroke:#ff9ff3,color:#000
    onboarding["onboarding"]
    style onboarding fill:#feca57,stroke:#ff9ff3,color:#000

    %% Dependencies
    aider --> aider
    aider_context_request_integration --> context_request_handler
    aider_context_request_integration --> aider_integration_service
    aider_integration_service --> aider
    aider_integration_service --> aider
    aider_integration_service --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> aider
    base_coder --> dump
    base_coder --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> aider
    base_coder_old --> dump
    base_coder_old --> aider
    benchmark --> aider
    benchmark --> aider
    benchmark --> aider
    benchmark --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> dump
    commands --> aider
    commands --> aider
    commands --> aider
    commands --> aider
    context_request_handler --> aider_integration_service
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_repo_messages --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_search_issue --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    debug_tag_parsing --> aider
    help --> aider
    help --> aider
    help --> aider
    io --> aider
    io --> dump
    main --> ir_context
    models --> aider
    models --> aider
    models --> aider
    models --> aider
    models --> aider
    models --> aider
    models --> aider
    onboarding --> aider
    repomap --> aider
    repomap --> aider
    repomap --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_coder --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_commands --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_complete_repo_map_elimination --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_error_without_git_dname --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_message_inspection --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_llm_workflow_understanding --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_main --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repo_map_leak_detection --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_repository_map_slicing_termination --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_simplified_system --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider
    test_smart_map_request_implementation --> aider