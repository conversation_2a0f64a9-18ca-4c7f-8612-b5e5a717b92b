#!/usr/bin/env python3
"""
Demo script showing different output modes for Intelligent Code Discovery.

This demonstrates how the same analysis can be presented in different formats
optimized for different use cases: summary, concise, LLM-friendly, and detailed.
"""

import os
import sys
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath("."))

from aider_integration_service import AiderIntegrationService


def demo_output_modes():
    """Demonstrate all output modes with the same feature request."""
    
    print("🎯 OUTPUT MODES DEMONSTRATION")
    print("=" * 60)
    print("Same analysis, different formats for different use cases")
    print()
    
    service = AiderIntegrationService()
    
    feature_description = "Add user authentication with JWT tokens"
    focus_areas = ["user", "auth", "token", "security"]
    
    modes = [
        ("summary", "Ultra-compact for quick overview"),
        ("concise", "Essential info only (default)"),
        ("llm_friendly", "Optimized for LLM consumption"),
        ("detailed", "Full analysis results")
    ]
    
    for mode, description in modes:
        print(f"\n📋 {mode.upper()} MODE")
        print(f"Purpose: {description}")
        print("-" * 50)
        
        try:
            # Suppress verbose output for cleaner demo
            import contextlib
            import io
            
            # Capture stdout to hide processing messages
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                results = service.find_relevant_code_for_feature(
                    project_path=".",
                    feature_description=feature_description,
                    focus_areas=focus_areas,
                    output_mode=mode
                )
            
            if 'error' not in results:
                print_mode_results(results, mode)
            else:
                print(f"❌ Error: {results['error']}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")


def print_mode_results(results, mode):
    """Print results formatted for each mode."""
    
    if mode == "summary":
        print(f"Task: {results['task']}")
        print(f"Entities: {results['entities_found']} found")
        print(f"Confidence: {results['confidence']}")
        print(f"Critical: {results['critical_count']}, Safe: {results['safe_count']}, Related: {results['related_count']}")
        if results['top_critical']:
            print(f"Top Critical: {results['top_critical']}")
        if results['top_safe']:
            print(f"Top Safe: {results['top_safe']}")
        print(f"Recommendation: {results['quick_recommendation']}")
        
    elif mode == "concise":
        summary = results['analysis_summary']
        print(f"Task: {summary['task']}")
        print(f"Entities: {summary['entities_selected']}, Confidence: {summary['confidence']}")
        
        print(f"\nCritical Entities ({len(results['critical_entities'])}):")
        for i, entity in enumerate(results['critical_entities'][:3], 1):
            print(f"  {i}. {entity['entity_name']} ({entity['entity_type']})")
            
        print(f"\nSafe Entities ({len(results['safe_entities'])}):")
        for i, entity in enumerate(results['safe_entities'][:3], 1):
            print(f"  {i}. {entity['entity_name']} ({entity['entity_type']})")
            
        print(f"\nGuidance: {results['implementation_guidance']}")
        
    elif mode == "llm_friendly":
        print(f"Task: {results['task']}")
        print(f"Analysis: {results['analysis']['entities_analyzed']} entities, confidence {results['analysis']['confidence']}")
        
        print(f"\nCritical ({len(results['critical_entities'])}):")
        for entity in results['critical_entities']:
            print(f"  - {entity['name']} ({entity['type']}) in {entity['file']}")
            print(f"    Risk: {entity['risk']}")
            
        print(f"\nSafe ({len(results['safe_entities'])}):")
        for entity in results['safe_entities'][:3]:
            print(f"  - {entity['name']} ({entity['type']}) in {entity['file']}")
            print(f"    Suggestion: {entity['suggestion']}")
            
        print(f"\nGuidance: {results['guidance']}")
        print(f"Next Steps: {', '.join(results['next_steps'])}")
        
    elif mode == "detailed":
        summary = results['analysis_summary']
        print(f"Task: {summary['task_description']}")
        print(f"Entities: {summary['entities_selected']} selected from {summary['total_entities_analyzed']} analyzed")
        print(f"Confidence: {summary['selection_confidence']:.2f}")
        
        print(f"\nCritical Entities: {len(results['critical_entities'])}")
        print(f"Safe Entities: {len(results['safe_entities'])}")
        print(f"Related Entities: {len(results['related_entities'])}")
        print(f"Dependencies Mapped: {len(results['dependency_map'])}")
        print(f"Recommendations: {len(results['recommendations'])}")
        
        print(f"\nImplementation Guidance:")
        for line in results['implementation_guidance'].split('\n')[:3]:
            if line.strip():
                print(f"  {line.strip()}")


def compare_output_sizes():
    """Compare the output sizes of different modes."""
    
    print("\n\n📊 OUTPUT SIZE COMPARISON")
    print("=" * 40)
    
    service = AiderIntegrationService()
    
    feature_description = "Add file upload processing"
    focus_areas = ["file", "upload", "process"]
    
    modes = ["summary", "concise", "llm_friendly", "detailed"]
    
    for mode in modes:
        try:
            # Suppress output
            import contextlib
            import io
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                results = service.find_relevant_code_for_feature(
                    project_path=".",
                    feature_description=feature_description,
                    focus_areas=focus_areas,
                    output_mode=mode
                )
            
            if 'error' not in results:
                # Calculate approximate size
                json_str = json.dumps(results, indent=2)
                size_kb = len(json_str) / 1024
                
                print(f"{mode:12} | {size_kb:6.1f} KB | {len(json_str):,} chars")
            else:
                print(f"{mode:12} | ERROR")
                
        except Exception as e:
            print(f"{mode:12} | EXCEPTION: {e}")


if __name__ == "__main__":
    print("🔍 INTELLIGENT CODE DISCOVERY - OUTPUT MODES DEMO")
    print("=" * 60)
    print("Demonstrating different output formats for different use cases:")
    print("• Summary: Quick overview for dashboards")
    print("• Concise: Essential info for developers (default)")
    print("• LLM-friendly: Optimized for AI consumption")
    print("• Detailed: Full analysis for deep investigation")
    print()
    
    demo_output_modes()
    compare_output_sizes()
    
    print("\n🎉 Demo Complete!")
    print("\nKey Benefits of Output Modes:")
    print("• 📱 Summary: Perfect for mobile/dashboard views")
    print("• 🎯 Concise: Ideal for daily development workflow")
    print("• 🤖 LLM-friendly: Optimized for AI agent consumption")
    print("• 🔍 Detailed: Complete analysis for complex features")
