#!/usr/bin/env python3
"""
Test script to verify that the directory_name/file_name format works correctly.
This tests that both CONTEXT_REQUEST and REQUEST_FILE can handle the new format.
"""

import os
import sys
import json

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_context_request_new_format():
    """Test that CONTEXT_REQUEST handles the new directory_name/file_name format."""
    print("🧪 Testing CONTEXT_REQUEST New Format")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler

        handler = ContextRequestHandler(".")

        # Test the new format
        new_format_request = '''
        {CONTEXT_REQUEST: {"original_user_query_context": "How does the function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}], "reason_for_request": "analyze the implementation"}}
        '''

        print("🔍 Testing new directory_name/file_name format...")
        print(f"Request: {new_format_request.strip()}")

        context_request = handler.parse_context_request(new_format_request)

        if context_request:
            print("✅ Successfully parsed CONTEXT_REQUEST with new format!")
            print(f"   Original query context: {context_request.original_user_query_context}")
            print(f"   Number of symbols: {len(context_request.symbols_of_interest)}")
            
            if context_request.symbols_of_interest:
                symbol = context_request.symbols_of_interest[0]
                print(f"   Symbol name: {symbol.name}")
                print(f"   Symbol type: {symbol.type}")
                print(f"   File hint (constructed): {symbol.file_hint}")
                
                # Check that file_hint was constructed correctly
                expected_path = "trade_management/position_exit_manager.py"
                if symbol.file_hint == expected_path:
                    print(f"✅ File hint correctly constructed: {symbol.file_hint}")
                    return True
                else:
                    print(f"❌ File hint incorrect. Expected: {expected_path}, Got: {symbol.file_hint}")
                    return False
            else:
                print("❌ No symbols found in parsed request")
                return False
        else:
            print("❌ Failed to parse CONTEXT_REQUEST")
            return False

    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST new format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_request_file_new_format():
    """Test that REQUEST_FILE handles the new directory_name/file_name format."""
    print("\n🧪 Testing REQUEST_FILE New Format")
    print("=" * 60)

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000,
            edit_format="informative"
        )

        # Test the new format
        new_format_content = '''I need to see the file.

{REQUEST_FILE: "directory_name": "trade_management", "file_name": "position_exit_manager.py", "reason": "analyze the implementation"}'''

        print("🔍 Testing new directory_name/file_name format...")
        print(f"Content: {new_format_content}")

        # Process the file request
        cleaned_content, message = coder.process_file_requests(new_format_content)

        print(f"Cleaned content: {cleaned_content}")
        print(f"Message: {message}")

        # Check if the file path was constructed correctly
        # We expect the system to try to add "trade_management/position_exit_manager.py"
        if message and "trade_management/position_exit_manager.py" in message:
            print("✅ REQUEST_FILE correctly handled new format!")
            return True
        else:
            print("❌ REQUEST_FILE did not handle new format correctly")
            return False

    except Exception as e:
        print(f"❌ Error testing REQUEST_FILE new format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test that the old file_hint format still works."""
    print("\n🧪 Testing Backward Compatibility")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler

        handler = ContextRequestHandler(".")

        # Test the old format
        old_format_request = '''
        {CONTEXT_REQUEST: {"original_user_query_context": "How does the function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "file_hint": "trade_management/position_exit_manager.py"}], "reason_for_request": "analyze the implementation"}}
        '''

        print("🔍 Testing old file_hint format...")
        print(f"Request: {old_format_request.strip()}")

        context_request = handler.parse_context_request(old_format_request)

        if context_request:
            print("✅ Successfully parsed CONTEXT_REQUEST with old format!")
            
            if context_request.symbols_of_interest:
                symbol = context_request.symbols_of_interest[0]
                print(f"   File hint: {symbol.file_hint}")
                
                # Check that file_hint is preserved
                expected_path = "trade_management/position_exit_manager.py"
                if symbol.file_hint == expected_path:
                    print(f"✅ Old format still works: {symbol.file_hint}")
                    return True
                else:
                    print(f"❌ Old format broken. Expected: {expected_path}, Got: {symbol.file_hint}")
                    return False
            else:
                print("❌ No symbols found in parsed request")
                return False
        else:
            print("❌ Failed to parse old format CONTEXT_REQUEST")
            return False

    except Exception as e:
        print(f"❌ Error testing backward compatibility: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_safety():
    """Test that the new format prevents JSON parsing errors."""
    print("\n🧪 Testing JSON Safety")
    print("=" * 60)

    # Test various problematic path formats
    test_cases = [
        {
            "name": "Windows backslashes (old format)",
            "request": '{"directory_name": "trade_management", "file_name": "position_exit_manager.py"}',
            "should_work": True
        },
        {
            "name": "Mixed slashes (old format)",
            "request": '{"file_hint": "trade_management\\position_exit_manager.py"}',
            "should_work": False  # This should cause JSON parsing errors
        },
        {
            "name": "New format with special characters",
            "request": '{"directory_name": "trade-management_v2", "file_name": "position.exit.manager.py"}',
            "should_work": True
        }
    ]

    passed = 0
    total = len(test_cases)

    for test_case in test_cases:
        print(f"\n🔍 Testing {test_case['name']}...")
        
        try:
            # Try to parse as JSON
            json.loads(test_case['request'])
            
            if test_case['should_work']:
                print(f"✅ {test_case['name']}: JSON parsed successfully")
                passed += 1
            else:
                print(f"⚠️  {test_case['name']}: JSON parsed but was expected to fail")
        except json.JSONDecodeError as e:
            if not test_case['should_work']:
                print(f"✅ {test_case['name']}: JSON failed as expected ({e})")
                passed += 1
            else:
                print(f"❌ {test_case['name']}: JSON failed unexpectedly ({e})")

    print(f"\n📊 JSON safety: {passed}/{total} tests passed")
    return passed == total

def main():
    """Run all directory/file format tests."""
    print("🚀 Testing Directory/File Format Solution")
    print("=" * 80)

    tests = [
        test_context_request_new_format,
        test_request_file_new_format,
        test_backward_compatibility,
        test_json_safety,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 Directory/File format solution is working correctly!")
        print("\n📋 The system now supports:")
        print("  1. ✅ New directory_name/file_name format for CONTEXT_REQUEST")
        print("  2. ✅ New directory_name/file_name format for REQUEST_FILE")
        print("  3. ✅ Backward compatibility with old file_hint format")
        print("  4. ✅ JSON safety - no more backslash parsing errors")
        print("  5. ✅ Automatic path construction from components")
    else:
        print("⚠️  Some directory/file format features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
