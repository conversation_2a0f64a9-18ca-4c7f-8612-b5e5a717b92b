#!/usr/bin/env python

import os
import sys
import time
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.coders.base_coder import Coder
    from aider.io import InputOutput
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO(InputOutput):
    """Mock IO class for testing."""

    def __init__(self):
        super().__init__()
        self.outputs = []
        self.warnings = []
        self.errors = []
        self.inputs = []
        self.current_input_index = 0

    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")

    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")

    def set_inputs(self, inputs):
        self.inputs = inputs
        self.current_input_index = 0

    def get_input(self, *args, **kwargs):
        if self.current_input_index < len(self.inputs):
            result = self.inputs[self.current_input_index]
            self.current_input_index += 1
            return result
        return ""

    def user_input(self, message):
        print(f"[USER] {message}")

    def ai_output(self, message):
        print(f"[AI] {message}")

    def read_text(self, path):
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return None


class MockCoder(Coder):
    """Mock Coder class for testing."""

    def __init__(self, project_path):
        self.io = MockIO()
        self.root = project_path
        self.root_path = project_path
        self.abs_fnames = set()
        self.abs_read_only_fnames = set()
        self.verbose = True
        self.current_query_context_requests = 0

    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Initialize the context request integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            from aider.context_request import AiderContextRequestIntegration
            self.context_request_integration = AiderContextRequestIntegration(self.root, coder=self)

        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Process the context request
        try:
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=user_message,
                repo_overview="This is a mock repository overview."
            )
        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            return content, None

        # Clean up the content by removing the context request
        import re
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        return cleaned_content, augmented_prompt

    def get_repo_overview(self):
        """Get a summary of the repository."""
        return "This is a mock repository overview."


def main():
    print("\n=== Testing CONTEXT_REQUEST in Aider Session ===")

    # Get the project path
    project_path = os.getcwd()

    # Initialize the mock coder
    mock_coder = MockCoder(project_path)

    # Create a sample context request
    context_request_content = """
    I need more information about the process_context_request method.

    {CONTEXT_REQUEST:
      "symbols_of_interest": [
        {
          "type": "method_definition",
          "name": "process_context_request",
          "file_hint": "base_coder.py"
        }
      ],
      "reason_for_request": "To analyze the implementation of the process_context_request method"
    }
    """

    # Process the context request
    print("\nProcessing context request...")
    cleaned_content, augmented_prompt = mock_coder.process_context_requests(
        content=context_request_content,
        user_message="How does the process_context_request method work?"
    )

    # Print the augmented prompt
    if augmented_prompt:
        print("\n=== Augmented Prompt Content ===")
        print(augmented_prompt)

        # Extract the code context from the augmented prompt
        code_context_start = augmented_prompt.find("### REQUESTED SYMBOL DEFINITIONS")
        code_context_end = augmented_prompt.find("INSTRUCTIONS FOR THIS TURN")

        if code_context_start != -1 and code_context_end != -1:
            code_context = augmented_prompt[code_context_start:code_context_end].strip()
            print("\n=== Code Context ===")
            print(code_context)

            # Check if the process_context_request method is in the code context
            if "process_context_request" in code_context:
                print("\n✅ process_context_request method found in code context")

                # Check if the method implementation is complete
                if "def process_context_request" in code_context and "return augmented_prompt" in code_context:
                    print("✅ Complete method implementation found")
                else:
                    print("❌ Method implementation is incomplete")
            else:
                print("\n❌ process_context_request method not found in code context")
        else:
            print("\n❌ No code context found in the augmented prompt")
    else:
        print("\n❌ No augmented prompt generated")

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
