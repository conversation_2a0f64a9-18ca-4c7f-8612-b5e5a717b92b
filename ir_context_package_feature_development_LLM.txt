# USER QUERY
I want to add a new code analysis feature. How should I integrate it with the existing system?

# INTELLIGENT CONTEXT ANALYSIS
## Task: feature_development
## Focus: I want to add a new code analysis feature. How should I integrate it with the existing system?

## CRITICAL ENTITIES (2 most important)

### 1. find_relevant_code_for_feature (function)
- File: aider_integration_service.py
- Criticality: high | Risk: medium
- Calls: 4 | Used by: 5
- Side Effects: network_io, writes_log, modifies_file

### 2. get_symbol_references_between_files (function)
- File: aider_integration_service.py
- Criticality: high | Risk: high
- Calls: 15 | Used by: 2
- Side Effects: network_io, writes_log, modifies_file

## KEY IMPLEMENTATIONS (2 functions)

### 1. find_relevant_code_for_feature
```python
    def find_relevant_code_for_feature(self, project_path: str,
                                     feature_description: str,
                                     focus_areas: list = None,
                                     output_mode: str = "concise") -> dict:
        """
        Find relevant code for a new feature with user-friendly output.

        This is the main entry point for the Intelligent Code Discovery feature.
        It combines intelligent context selection and multi-turn reasoning to provide
        organized, actionable results for feature development.

        Args:
            project_path: Path to the project
            feature_description: Natural language description of the feature
            focus_areas: Key areas/keywords to focus on (optional)
    # ... (implementation continues)
```

### 2. get_symbol_references_between_files
```python
    def get_symbol_references_between_files(self, project_path: str, source_file: str, target_file: str, model_name: str = "gpt-3.5-turbo") -> Dict[str, List[str]]:
        """
        Get detailed information about symbols referenced between two files.

        Args:
            project_path: Path to the project root
            source_file: Path to the source file that references symbols
            target_file: Path to the target file that defines symbols
            model_name: Name of the model to use

        Returns:
            Dictionary with symbol types as keys and lists of referenced symbol names as values
        """
        # Get symbols defined in the target file
        target_symbols = self.get_symbols_defined_in_file(project_path, target_file, model_name)

    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 2 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: I want to add a new code analysis feature. How should I integrate it with the existing system?

Provide specific, actionable insights based on this focused context.
