#!/usr/bin/env python3
"""
End-to-end test for CONTEXT_REQUEST with the trading project scenario.
This simulates the exact scenario where the user requests context for
close_position_based_on_conditions from trade_management/position_exit_manager.py
"""

import os
import sys
import tempfile
import shutil

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def create_mock_trading_project():
    """Create a mock trading project structure for testing."""
    temp_dir = tempfile.mkdtemp(prefix="mock_trading_project_")

    # Create directory structure
    trade_management_dir = os.path.join(temp_dir, "trade_management")
    services_dir = os.path.join(temp_dir, "services")
    os.makedirs(trade_management_dir)
    os.makedirs(services_dir)

    # Create position_exit_manager.py with the method
    position_exit_manager_content = '''
"""Position exit management module."""

class PositionExitManager:
    """Manages position exits based on various conditions."""

    def __init__(self, config):
        self.config = config
        self.exit_conditions = []

    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.

        Args:
            app: The application context containing market data and position info

        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True

        # Check take profit conditions
        if self._check_take_profit(app):
            await self._execute_close(app, "take_profit")
            return True

        # Check time-based conditions
        if self._check_time_exit(app):
            await self._execute_close(app, "time_exit")
            return True

        return False

    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder

    def _check_take_profit(self, app):
        """Check if take profit conditions are met."""
        return False  # Placeholder

    def _check_time_exit(self, app):
        """Check if time-based exit conditions are met."""
        return False  # Placeholder

    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''

    position_exit_manager_path = os.path.join(trade_management_dir, "position_exit_manager.py")
    with open(position_exit_manager_path, 'w') as f:
        f.write(position_exit_manager_content)

    # Create position_observer.py (without the method - to test partial failure)
    position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""

    def __init__(self):
        self.observers = []

    def add_observer(self, observer):
        """Add a position observer."""
        self.observers.append(observer)

    def notify_position_change(self, position):
        """Notify all observers of position changes."""
        for observer in self.observers:
            observer.on_position_change(position)

    # Note: close_position_based_on_conditions is NOT in this file
    # This tests the partial failure scenario
'''

    position_observer_path = os.path.join(services_dir, "position_observer.py")
    with open(position_observer_path, 'w') as f:
        f.write(position_observer_content)

    return temp_dir

def test_context_request_end_to_end():
    """Test the complete CONTEXT_REQUEST workflow."""
    print("🧪 Testing CONTEXT_REQUEST End-to-End")
    print("=" * 60)

    mock_project = None
    try:
        # Create mock trading project
        mock_project = create_mock_trading_project()
        print(f"📁 Created mock project: {mock_project}")

        from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
        from aider.context_request.aider_integration_service import AiderIntegrationService

        # Create context request handler
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(mock_project, aider_service)

        print(f"✅ Handler created successfully")

        # Create the exact CONTEXT_REQUEST from the user scenario
        context_request = ContextRequest(
            original_user_query_context="how does the close_position_based_on_conditions function work?",
            reason_for_request="User wants to understand the implementation of position closing logic",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"
                ),
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"
                )
            ]
        )

        print(f"🎯 Processing CONTEXT_REQUEST:")
        print(f"   Query: {context_request.original_user_query_context}")
        print(f"   Symbols: {len(context_request.symbols_of_interest)}")

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"\n📊 Results:")
        print(f"   Extracted symbols: {len(result['extracted_symbols'])}")
        print(f"   Not found symbols: {len(result['not_found_symbols'])}")

        # Verify results
        success_criteria = [
            (len(result['extracted_symbols']) > 0, "At least one symbol extracted"),
            (len(result['not_found_symbols']) > 0, "Some symbols not found (expected)"),
            (result['original_user_query_context'] == context_request.original_user_query_context, "Query context preserved"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            status = "✅" if condition else "❌"
            print(f"   {status} {description}")
            if condition:
                passed += 1

        # Show detailed results
        if result['extracted_symbols']:
            print(f"\n📋 Successfully extracted symbols:")
            for symbol in result['extracted_symbols']:
                print(f"   ✅ {symbol['symbol_name']} from {symbol['file_path']}")
                print(f"      Content length: {len(symbol['content'])} characters")
                print(f"      Preview: {symbol['content'][:100]}...")

        if result['not_found_symbols']:
            print(f"\n⚠️  Symbols not found (expected):")
            for symbol in result['not_found_symbols']:
                print(f"   ❌ {symbol['symbol_name']}: {symbol['reason']}")

        print(f"\n📊 End-to-end test: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error in end-to-end test: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # Clean up mock project
        if mock_project and os.path.exists(mock_project):
            shutil.rmtree(mock_project)
            print(f"🧹 Cleaned up mock project")

def test_template_rendering():
    """Test that the template rendering works with the results."""
    print("\n🧪 Testing Template Rendering")
    print("=" * 60)

    try:
        from aider.context_request.aider_template_renderer import AiderTemplateRenderer

        # Mock result data
        mock_result = {
            "original_user_query_context": "how does the close_position_based_on_conditions function work?",
            "extracted_symbols": [
                {
                    "symbol_name": "close_position_based_on_conditions",
                    "file_path": "trade_management/position_exit_manager.py",
                    "content": "async def close_position_based_on_conditions(self, app):\n    # Implementation here\n    pass",
                    "essential_imports": "import asyncio",
                    "containing_class": "class PositionExitManager:"
                }
            ],
            "not_found_symbols": [
                {
                    "symbol_name": "close_position_based_on_conditions",
                    "reason": "Could not find or extract the symbol content from services/position_observer.py"
                }
            ]
        }

        renderer = AiderTemplateRenderer()
        augmented_prompt = renderer.render_augmented_prompt(
            original_query=mock_result["original_user_query_context"],
            repo_overview="Mock repository overview",
            extracted_context=mock_result
        )

        print(f"✅ Template rendered successfully")
        print(f"📝 Augmented prompt length: {len(augmented_prompt)} characters")

        # Check that key elements are present
        success_criteria = [
            ("REQUESTED SYMBOL DEFINITIONS" in augmented_prompt or "extracted" in augmented_prompt.lower(), "Contains extracted context section"),
            ("close_position_based_on_conditions" in augmented_prompt, "Contains function name"),
            ("trade_management/position_exit_manager.py" in augmented_prompt, "Contains file path"),
            ("SYMBOLS NOT FOUND" in augmented_prompt or "not found" in augmented_prompt.lower(), "Contains not found section"),
            ("services/position_observer.py" in augmented_prompt, "Contains missing file info"),
            ("CURRENT USER QUERY" in augmented_prompt or "answer" in augmented_prompt.lower(), "Contains task instruction"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            status = "✅" if condition else "❌"
            print(f"   {status} {description}")
            if condition:
                passed += 1

        print(f"\n📊 Template rendering test: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error in template rendering test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all end-to-end tests."""
    print("🚀 Testing CONTEXT_REQUEST End-to-End Workflow")
    print("=" * 80)

    tests = [
        test_context_request_end_to_end,
        test_template_rendering,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 CONTEXT_REQUEST end-to-end workflow is working correctly!")
        print("\n📋 The complete solution ensures:")
        print("  1. ✅ RepoMap-style file discovery finds files correctly")
        print("  2. ✅ Partial failures are handled gracefully")
        print("  3. ✅ Template rendering creates proper augmented prompts")
        print("  4. ✅ Both successes and failures are reported to LLM")
        print("  5. ✅ User messages are sent as USER role, not SYSTEM role")
        print("\n🎯 Ready for real trading project testing!")
        print("   Navigate to your trading project directory and run aider")
        print("   The CONTEXT_REQUEST should now work correctly!")
    else:
        print("⚠️  Some end-to-end features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
