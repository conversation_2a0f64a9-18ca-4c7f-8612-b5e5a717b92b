# USER QUERY
Show me class inheritance patterns and method overrides in the coder classes

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze inheritance relationships, method overrides, and super() calls in coder classes

## CRITICAL ENTITIES WITH INHERITANCE (226 entities)


### 1. UnknownEditFormat (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 2. __init__ (method)
- File: base_coder.py
- Belongs to Class: `UnknownEditFormat`
- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


#### 🔁 Inheritance Context
- `super().__init__()` is called ✅
- Access: protected


#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 3. MissingAPIKeyError (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 4. FinishReasonLength (class)
- File: base_coder.py

- Inherits From: [`Exception`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 5. create (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 6. clone (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 7. get_announcements (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 8. __init__ (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 9. _initialize_ir_preloading (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 10. setup_lint_cmds (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


## ANALYSIS INSTRUCTIONS
Based on the inheritance entities above:

1. **Focus on inheritance relationships** - understand class hierarchies
2. **Consider method overrides** - see which methods override base class methods
3. **Note super() call patterns** - whether methods properly call super()
4. **Understand access modifiers** - public, protected, private method distinctions

**Your task**: Show me class inheritance patterns and method overrides in the coder classes

**Key Inheritance Insights:**
- Classes inherit from standard Python exceptions (ValueError, Exception)
- Methods properly call super() in constructors
- Access modifiers follow Python conventions (protected methods start with _)
- Inheritance relationships are captured with full context

Provide specific, actionable insights based on this inheritance-focused context.
