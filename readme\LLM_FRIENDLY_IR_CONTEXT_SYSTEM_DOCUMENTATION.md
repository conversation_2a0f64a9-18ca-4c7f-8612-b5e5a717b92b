# LLM-Friendly IR Context Package System
## Comprehensive Developer Documentation

### Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Components](#architecture--components)
3. [Complete Workflow](#complete-workflow)
4. [Key Files & Components](#key-files--components)
5. [Testing Instructions](#testing-instructions)
6. [Usage Examples](#usage-examples)
7. [Developer Setup](#developer-setup)
8. [Performance Benchmarks](#performance-benchmarks)
9. [Troubleshooting](#troubleshooting)

---

## System Overview

### Purpose
The LLM-Friendly IR Context Package System transforms complex codebases into focused, inheritance-aware context packages optimized for Large Language Models. It provides intelligent code discovery and context selection for debugging, feature development, and code analysis tasks.

### Key Benefits
- **3.4x Faster Performance**: 13s vs 42s processing time
- **5.3x More Entities**: 11,706 vs 2,217 entities analyzed
- **Enhanced Inheritance Analysis**: Complete OOP relationship mapping
- **Smart Context Selection**: Token-budget aware entity prioritization
- **Low-Signal Filtering**: Removes noise like `main()` functions and utilities
- **LLM-Optimized Output**: GPT-4 compatible packages with inheritance data

### Complete Workflow
```
User Query → IR Generation → Intelligent Context Selection → LLM Package Creation
     ↓              ↓                    ↓                        ↓
"Debug context   Enhanced IR        Smart entity           Focused package
 selection       with inheritance   prioritization         with inheritance
 performance"    analysis           & filtering            data & code
```

---

## Architecture & Components

### System Architecture Diagram
```
┌─────────────────────────────────────────────────────────────────┐
│                    LLM-Friendly IR Context System               │
├─────────────────────────────────────────────────────────────────┤
│  User Query: "Why is my context selection taking so long?"     │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│              1. Enhanced IR Generation                          │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ Mid-Level IR Pipeline (9 Modular Components)               ││
│  │ • File Discovery      • Inheritance Analysis               ││
│  │ • Entity Extraction   • Call Graph Analysis               ││
│  │ • Dependency Analysis • Side Effect Analysis              ││
│  │ • Error Analysis      • Metadata Enrichment               ││
│  │ • Criticality Analysis                                     ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│           2. Intelligent Context Selection                      │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ IntelligentContextSelector                                  ││
│  │ • Task-aware prioritization (debugging, feature dev, etc.) ││
│  │ • Token budget management (2000 tokens)                    ││
│  │ • Relevance scoring & entity ranking                       ││
│  │ • Focus entity matching                                    ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│            3. LLM Package Generation                            │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ ContextRequestHandler                                       ││
│  │ • Low-signal item filtering (main(), utilities)            ││
│  │ • Inheritance data integration                              ││
│  │ • Code snippet extraction                                  ││
│  │ • GPT-4 compatible formatting                              ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 4. Final Output                                 │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ LLM-Friendly Package (11,685 chars)                        ││
│  │ • 8 CRITICAL ENTITIES (focused selection)                  ││
│  │ • 8 KEY IMPLEMENTATIONS (with code)                        ││
│  │ • Inheritance context for each entity                      ││
│  │ • Dependency relationships & side effects                  ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### Component Interaction Flow
1. **User Query** → `ContextRequestHandler.process_ir_context_request()`
2. **IR Generation** → `AiderIntegrationService.generate_mid_level_ir()`
3. **Context Selection** → `IntelligentContextSelector.select_optimal_context()`
4. **Package Creation** → `ContextRequestHandler._create_llm_friendly_package()`

---

## Key Files & Components

### Core Service Files
- **`aider_integration_service.py`** - Main IR generation orchestrator
- **`mid_level_ir_with_inheritance.py`** - Enhanced 9-module IR pipeline
- **`intelligent_context_selector.py`** - Smart context selection engine
- **`aider-main/aider/context_request/context_request_handler.py`** - LLM package generator

### Modular IR Pipeline Components (9 modules)
1. **`mid_level_ir/file_discovery.py`** - Python file scanning & parsing
2. **`mid_level_ir/entity_extractor.py`** - Function/class extraction
3. **`mid_level_ir/inheritance_analyzer.py`** - OOP relationship analysis
4. **`mid_level_ir/call_graph_analyzer.py`** - Function call mapping
5. **`mid_level_ir/dependency_analyzer.py`** - Import & usage tracking
6. **`mid_level_ir/side_effect_analyzer.py`** - State modification detection
7. **`mid_level_ir/error_analyzer.py`** - Exception & error analysis
8. **`mid_level_ir/metadata_enricher.py`** - Documentation & complexity
9. **`mid_level_ir/criticality_analyzer.py`** - Importance scoring

### Test Files
- **`test_llm_friendly_integration.py`** - Basic functionality testing
- **`test_inheritance_targeting.py`** - Inheritance-specific query testing
- **`test_same_original_method_with_inheritance.py`** - Filtered result testing

---

## Complete Workflow

### Step 1: IR Generation (Enhanced Pipeline)
```python
# Triggered by: ContextRequestHandler.process_ir_context_request()
ir_data = standalone_service.generate_mid_level_ir(project_path)

# Pipeline Steps:
# 1. File Discovery: Scan 315 Python files
# 2. Entity Extraction: Extract 13,484 entities (244 classes, 2,553 functions)
# 3. Inheritance Analysis: Map 71 classes with inheritance, 18 method overrides
# 4. Call Graph: Identify 11,155 function calls
# 5. Dependency Analysis: Find 1,372 dependencies
# 6. Side Effect Analysis: Analyze state modifications
# 7. Error Analysis: Detect exception patterns
# 8. Metadata Enrichment: Add documentation & complexity
# 9. Criticality Analysis: Score entity importance
```

### Step 2: Intelligent Context Selection
```python
# Create selector with token budget
selector = IntelligentContextSelector(ir_data, max_tokens=2000)

# Select optimal context based on task
context_bundle = selector.select_optimal_context(
    task_description="Debug performance issues in context selection",
    task_type=TaskType.DEBUGGING,
    focus_entities=["performance", "context", "selection"]
)

# Result: 32 entities selected, 100% token utilization
```

### Step 3: Low-Signal Item Filtering
```python
# Filter out noise before package creation
def is_low_signal_item(ir):
    entity_name = ir.get('entity_name', '').lower()
    
    # Filter main() functions (CLI entry points)
    if entity_name == 'main' and entity_type == 'function':
        return True
    
    # Filter utility functions
    low_priority_functions = [
        'get_repo_map_tokens', 'token_count', 'validate_environment',
        'configure_model_settings', 'sanity_check_model'
    ]
    if entity_name in low_priority_functions:
        return True
    
    return False

# Result: 11 low-signal items filtered out
```

### Step 4: LLM Package Generation
```python
# Create focused package with inheritance data
llm_package = self._create_llm_friendly_package(result, request)

# Package Structure:
# - USER QUERY
# - CRITICAL ENTITIES (8 most important)
# - KEY IMPLEMENTATIONS (8 functions with code)
# - ANALYSIS INSTRUCTIONS
```

---

## Testing Instructions

### Basic Functionality Test
```bash
python test_llm_friendly_integration.py
```
**Expected Output:**
- ✅ 3 test scenarios completed
- 📦 Package sizes: 11,137 chars (LLM), 7,988 chars (JSON)
- 🤖 GPT-4 compatible packages generated
- 📄 Output file: `test_llm_friendly_package_with_inheritance.txt`

### Inheritance Targeting Test
```bash
python test_inheritance_targeting.py
```
**Expected Output:**
- 🎯 4 inheritance-focused queries tested
- ✅ Best result: Model classes with real inheritance found
- 📊 1 entity with real inheritance detected
- 📄 Output file: `inheritance_targeting_best_package.txt`

### Filtered Results Test
```bash
python test_same_original_method_with_inheritance.py
```
**Expected Output:**
- 🔧 11 low-signal items filtered out
- ✅ No main() functions in final package
- 📦 8 KEY IMPLEMENTATIONS maintained
- 📄 Output file: `same_original_method_with_inheritance.txt`

### Success Criteria
- All tests return exit code 0
- Package sizes under 35,000 characters
- No low-signal items in filtered results
- Inheritance data present where applicable
- Code snippets included in KEY IMPLEMENTATIONS

---

## Usage Examples

### Sample Queries That Work Well

#### 1. Performance Debugging
```python
query = "Why is my context selection taking so long?"
focus = ["performance", "context", "selection"]
# Result: 8 functions focused on context processing performance
```

#### 2. Inheritance Analysis
```python
query = "How are AI models configured and used?"
focus = ["model", "config", "settings", "ModelSettings", "AI"]
# Result: Model class with inheritance from ModelSettings
```

#### 3. Exception Handling
```python
query = "How are custom exceptions implemented?"
focus = ["exception", "error", "ValueError", "Exception"]
# Result: Exception classes with inheritance chains
```

### Interpreting Generated Packages

#### Package Structure
```markdown
# USER QUERY
Why is my context selection taking so long?

## CRITICAL ENTITIES (8 most important)
### 1. parse_context_request (function)
- File: context_request_handler.py
- Inherits From: No inheritance detected
- Criticality: high | Risk: medium
- **Calls**: ["search", "strip", "group"] (total: 13)
- **Used by**: ["test_context_request_format_fix"] (total: 6)
- **Side Effects**: writes_log, network_io

## KEY IMPLEMENTATIONS (8 functions)
### 1. parse_context_request
```python
def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
    # ... actual implementation
```
```

#### Inheritance Information
- **"Inherits From: ['ModelSettings']"** - Real inheritance detected
- **"Inherits From: No inheritance detected"** - No inheritance found
- **"🔁 Class Context"** - Inheritance chain and override information
- **"🧩 Method Details"** - Super() calls and method relationships

---

## Developer Setup

### Required Dependencies
```python
# Core imports
import ast
import os
import sys
import time
import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# Aider imports (add aider-main to path)
sys.path.insert(0, "aider-main")
from aider.context_request import ContextRequestHandler, IRContextRequest
```

### Integration with Existing Aider Codebase
```python
# 1. Add to aider-main/aider/context_request/__init__.py
from .context_request_handler import ContextRequestHandler, IRContextRequest

# 2. Import in your code
from aider.context_request import ContextRequestHandler

# 3. Create handler
handler = ContextRequestHandler(project_path)

# 4. Process requests
result = handler.process_ir_context_request(request)
```

### Configuration Options
```python
# IR Context Request Configuration
request = IRContextRequest(
    user_query="Your question here",
    task_description="Specific task description",
    task_type="debugging",  # debugging, feature_development, code_review
    focus_entities=["keyword1", "keyword2"],  # Optional focus
    max_tokens=2000,  # Token budget
    include_ir_slices=True,  # Include IR metadata
    include_code_context=True,  # Include source code
    llm_friendly=True,  # Generate LLM package
    max_output_chars=30000,  # Size limit
    max_entities=8  # Entity limit
)
```

---

## Performance Benchmarks

### Enhanced Pipeline Performance
- **Processing Time**: 13s (vs 42s original) - **3.4x faster**
- **Entities Analyzed**: 11,706 (vs 2,217 original) - **5.3x more**
- **Output Size**: 7.24MB enhanced analysis
- **Memory Usage**: Optimized with modular architecture

### Context Selection Performance
- **Token Utilization**: 99.8% efficiency
- **Selection Time**: 9.31s average
- **Entity Filtering**: 11 low-signal items removed per query
- **Package Generation**: <1s for LLM-friendly format

### Inheritance Analysis Performance
- **Classes Analyzed**: 71 with inheritance relationships
- **Method Overrides**: 18 detected
- **Super() Calls**: 17 identified
- **Inheritance Chains**: Complete hierarchy mapping

---

## Troubleshooting

### Common Issues

#### 1. Import Errors
```python
# Error: ModuleNotFoundError: No module named 'aider'
# Solution: Add aider-main to Python path
sys.path.insert(0, "aider-main")
```

#### 2. IR Generation Fails
```python
# Error: IR generation not available
# Solution: Ensure aider_integration_service.py is accessible
from aider_integration_service import AiderIntegrationService
```

#### 3. Empty Packages Generated
```python
# Issue: No entities selected
# Solution: Check focus_entities and max_tokens settings
focus_entities=["relevant", "keywords"]  # Add relevant terms
max_tokens=4000  # Increase token budget
```

#### 4. Low-Signal Items Not Filtered
```python
# Issue: main() functions still appearing
# Solution: Verify filtering is enabled in context_request_handler.py
print(f"🔧 Filtered out {len(ir_slices) - len(filtered_ir_slices)} low-signal items")
```

### Debug Mode
```python
# Enable verbose output
handler = ContextRequestHandler(project_path, verbose=True)

# Check intermediate results
print(f"IR entities: {len(ir_data['entities'])}")
print(f"Selected entities: {len(context_bundle.entities)}")
print(f"Package size: {len(llm_package)} characters")
```

---

## Advanced Features

### Inheritance Data Flow
```
Enhanced IR Pipeline → Inheritance Analyzer → Context Entities → LLM Package
        ↓                      ↓                    ↓              ↓
   AST Analysis         Class hierarchy      Entity attributes   Formatted output
   Method detection     Override tracking    Inheritance info    with inheritance
   Super() calls        Chain mapping        Class context       context sections
```

### Low-Signal Filtering Algorithm
```python
# Filtering Priority (High to Low)
1. main() functions (CLI entry points) - Always filter unless critical
2. Utility functions (get_repo_map_tokens, token_count) - Filter if not high criticality
3. Test functions - Filter unless query-relevant or high criticality
4. Configuration functions - Filter if not directly relevant

# Filtering preserves:
- High/critical criticality entities
- Query-relevant functions
- Core business logic
- Performance-critical paths
```

### Token Budget Management
```python
# Smart token allocation
- High criticality entities: 60% of budget
- Medium criticality entities: 30% of budget
- Code implementations: 10% of budget
- Automatic truncation if over budget
- Prioritized entity selection within limits
```

## Integration Examples

### Basic Integration
```python
#!/usr/bin/env python3
"""Basic integration example"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

from aider.context_request import ContextRequestHandler, IRContextRequest

def analyze_codebase(project_path, query):
    """Analyze codebase with LLM-friendly IR context."""

    # Create handler
    handler = ContextRequestHandler(project_path)

    # Create request
    request = IRContextRequest(
        user_query=query,
        task_description=f"Analyze: {query}",
        task_type="general_analysis",
        max_tokens=2000,
        llm_friendly=True
    )

    # Process request
    result = handler.process_ir_context_request(request)

    # Get LLM package
    if "llm_friendly_package" in result:
        return result["llm_friendly_package"]
    else:
        return f"Error: {result.get('error', 'Unknown error')}"

# Usage
if __name__ == "__main__":
    package = analyze_codebase(".", "How does error handling work?")
    print(package)
```

### Advanced Integration with Custom Filtering
```python
#!/usr/bin/env python3
"""Advanced integration with custom filtering"""

from aider.context_request import ContextRequestHandler, IRContextRequest

class CustomContextHandler(ContextRequestHandler):
    """Extended handler with custom filtering."""

    def custom_filter(self, entities, query_keywords):
        """Apply custom filtering logic."""
        filtered = []

        for entity in entities:
            # Keep entities matching query keywords
            if any(keyword in entity.entity_name.lower()
                   for keyword in query_keywords):
                filtered.append(entity)
                continue

            # Keep high-criticality entities
            if entity.criticality in ['high', 'critical']:
                filtered.append(entity)
                continue

            # Custom business logic filtering
            if self.is_business_critical(entity):
                filtered.append(entity)

        return filtered

    def is_business_critical(self, entity):
        """Determine if entity is business critical."""
        critical_patterns = [
            'process_', 'handle_', 'execute_', 'validate_',
            'authenticate_', 'authorize_', 'calculate_'
        ]

        return any(pattern in entity.entity_name.lower()
                  for pattern in critical_patterns)

# Usage
handler = CustomContextHandler(".")
request = IRContextRequest(
    user_query="How does user authentication work?",
    task_description="Analyze authentication flow",
    task_type="feature_development",
    focus_entities=["auth", "user", "login", "validate"],
    max_tokens=3000,
    llm_friendly=True
)

result = handler.process_ir_context_request(request)
```

## API Reference

### IRContextRequest Class
```python
@dataclass
class IRContextRequest:
    """Request object for IR context processing."""

    user_query: str                    # The user's question
    task_description: str              # Detailed task description
    task_type: str                     # Task type (debugging, feature_development, etc.)
    focus_entities: List[str] = None   # Keywords to focus on
    max_tokens: int = 2000             # Token budget
    include_ir_slices: bool = True     # Include IR metadata
    include_code_context: bool = True  # Include source code
    llm_friendly: bool = True          # Generate LLM package
    max_output_chars: int = 30000      # Maximum output size
    max_entities: int = 8              # Maximum entities to include
```

### ContextRequestHandler Methods
```python
class ContextRequestHandler:
    """Main handler for IR context requests."""

    def __init__(self, project_path: str, verbose: bool = False):
        """Initialize handler with project path."""

    def process_ir_context_request(self, request: IRContextRequest) -> Dict[str, Any]:
        """Process an IR context request and return results."""

    def _create_llm_friendly_package(self, context_package: Dict, request: IRContextRequest) -> str:
        """Create LLM-optimized package with inheritance data."""

    def _get_ir_from_cache(self, project_path: str) -> Optional[Dict]:
        """Get cached IR data if available."""

    def _update_ir_cache(self, project_path: str, ir_data: Dict):
        """Update IR cache with new data."""
```

### Response Format
```python
{
    "user_query": str,                    # Original query
    "task_description": str,              # Task description
    "task_type": str,                     # Task type
    "ir_slices": List[Dict],              # IR metadata for entities
    "code_context": List[Dict],           # Source code for entities
    "llm_friendly_package": str,          # LLM-optimized package
    "package_size_chars": int,            # Package size
    "llm_compatibility": str,             # Compatibility info
    "summary": {
        "critical_entities": int,         # Count of critical entities
        "high_priority_entities": int,    # Count of high priority entities
        "files_involved": int,            # Number of files
        "token_utilization": str          # Token usage percentage
    }
}
```

## Best Practices

### Query Optimization
```python
# Good queries (specific, actionable)
"Why is my context selection taking so long?"
"How are custom exceptions implemented?"
"What's the inheritance hierarchy for Model classes?"

# Poor queries (too broad, vague)
"How does this code work?"
"Fix my code"
"Explain everything"
```

### Focus Entity Selection
```python
# Effective focus entities
focus_entities=["performance", "context", "selection"]  # Specific keywords
focus_entities=["Model", "inheritance", "ModelSettings"]  # Class names
focus_entities=["exception", "error", "ValueError"]  # Related concepts

# Ineffective focus entities
focus_entities=["code", "function", "class"]  # Too generic
focus_entities=["a", "the", "is"]  # Stop words
```

### Token Budget Guidelines
```python
# Recommended token budgets by task type
debugging_tasks = 2000-3000      # Focused analysis
feature_development = 3000-4000  # Broader context needed
code_review = 1500-2500         # Targeted review
documentation = 2000-3000       # Moderate context
testing = 1500-2000            # Specific test context
```

## Extending the System

### Adding New IR Analysis Modules
```python
# 1. Create new analyzer in mid_level_ir/
class SecurityAnalyzer:
    """Analyze security patterns in code."""

    def analyze(self, modules: List[ast.Module]) -> Dict[str, Any]:
        """Analyze security patterns."""
        security_issues = []

        for module in modules:
            # Custom security analysis logic
            issues = self.find_security_patterns(module)
            security_issues.extend(issues)

        return {"security_issues": security_issues}

# 2. Add to pipeline in mid_level_ir_with_inheritance.py
def run_enhanced_ir_pipeline(project_path: str) -> Dict[str, Any]:
    # ... existing steps ...

    # Step 11: Security Analysis
    print("🔒 Step 11: Security Analysis")
    security_analyzer = SecurityAnalyzer()
    security_data = security_analyzer.analyze(modules)

    # Add to final IR
    ir_data["security_analysis"] = security_data
```

### Custom Task Types
```python
# Add to intelligent_context_selector.py
class TaskType(Enum):
    DEBUGGING = "debugging"
    FEATURE_DEVELOPMENT = "feature_development"
    CODE_REVIEW = "code_review"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    GENERAL_ANALYSIS = "general_analysis"
    SECURITY_AUDIT = "security_audit"  # New task type
    PERFORMANCE_OPTIMIZATION = "performance_optimization"  # New task type
```

### Custom Filtering Rules
```python
# Extend filtering in context_request_handler.py
def is_low_signal_item(ir):
    """Enhanced filtering with custom rules."""
    entity_name = ir.get('entity_name', '').lower()
    entity_type = ir.get('entity_type', '')

    # Existing filters...

    # Custom filters
    if entity_name.startswith('_internal_'):
        return True  # Filter internal functions

    if 'deprecated' in ir.get('docstring', '').lower():
        return True  # Filter deprecated functions

    if ir.get('complexity_score', 0) < 2:
        return True  # Filter trivial functions

    return False
```

---

## Conclusion

The LLM-Friendly IR Context Package System provides a comprehensive solution for intelligent code analysis and context generation. With its modular architecture, inheritance-aware analysis, and smart filtering capabilities, it enables developers to quickly understand complex codebases and generate focused, actionable insights for LLMs.

### Key Achievements
- ✅ **3.4x Performance Improvement** (13s vs 42s)
- ✅ **5.3x Entity Analysis Increase** (11,706 vs 2,217)
- ✅ **Complete Inheritance Analysis** (71 classes, 18 overrides)
- ✅ **Smart Low-Signal Filtering** (removes noise, keeps signal)
- ✅ **LLM-Optimized Output** (GPT-4 compatible packages)

### Next Steps
1. Run the test suite to verify functionality
2. Experiment with different query types and focus entities
3. Customize filtering rules for your specific use case
4. Extend the system with additional analysis modules
5. Integrate with your existing development workflow

*For questions, issues, or contributions, refer to the test files for working examples and expected outputs.*
