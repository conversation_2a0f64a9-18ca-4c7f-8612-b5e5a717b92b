(defun hello-world ()
  ;; ai this is a simple hello world function
  (format t "Hello, World!"))

(defun add (a b)
  ; ai! fix this function to handle nil values
  (+ a b))

(defun multiply (a b)
  ;;; ai? why is this function not working with large numbers?
  (* a b))

; ai this is a single semicolon comment

;; ai this is a double semicolon comment

;;; ai this is a triple semicolon comment

;;;; ai! this is a quadruple semicolon comment
