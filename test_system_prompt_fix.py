#!/usr/bin/env python3
"""
Test script to verify the system prompt no longer contradicts the Play Dumb rule
"""

import sys
import os

def test_system_prompt_fix():
    """Test that the system prompt doesn't contradict Play Dumb"""
    print("🧪 Testing System Prompt Fix")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test the main system prompt
        main_system = prompts.main_system
        print("📝 Main System Prompt:")
        print(f"   Length: {len(main_system)} characters")
        print(f"   Content: {main_system}")
        
        # Check for contradictory elements
        contradictory_checks = [
            ("Act as an expert software developer", "❌" if "Act as an expert software developer" in main_system else "✅"),
            ("expert software developer", "❌" if "expert software developer" in main_system else "✅"),
            ("Act as an expert", "❌" if "Act as an expert" in main_system else "✅")
        ]
        
        print("\n🚫 Contradictory Elements Check:")
        for check, status in contradictory_checks:
            print(f"   {status} Does NOT contain: '{check}'")
        
        # Check for positive elements
        positive_checks = [
            ("AI assistant", "✅" if "AI assistant" in main_system else "❌"),
            ("Follow the conversation history", "✅" if "Follow the conversation history" in main_system else "❌"),
            ("established workflow protocols", "✅" if "established workflow protocols" in main_system else "❌")
        ]
        
        print("\n✅ Positive Elements Check:")
        for check, status in positive_checks:
            print(f"   {status} Contains: '{check}'")
        
        # Overall assessment
        all_contradictory_removed = all(status == "✅" for _, status in contradictory_checks)
        all_positive_present = all(status == "✅" for _, status in positive_checks)
        
        if all_contradictory_removed and all_positive_present:
            print("\n🎉 SUCCESS: System prompt no longer contradicts Play Dumb rule!")
            return True
        else:
            print("\n❌ ISSUES: System prompt still has problems")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_message_hierarchy():
    """Test the message hierarchy and priority"""
    print("\n🔄 Testing Message Hierarchy")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Simulate the message flow
        system_prompt = prompts.main_system
        user_instruction = prompts.smart_map_request_user_prompt
        assistant_commitment = prompts.smart_map_request_assistant_reply
        
        print("📋 Message Flow Analysis:")
        print(f"\n1. SYSTEM PROMPT (Position 0):")
        print(f"   - Length: {len(system_prompt)} chars")
        print(f"   - Tone: {'Neutral/Helpful' if 'AI assistant' in system_prompt else 'Expert/Authoritative'}")
        print(f"   - Contradicts Play Dumb: {'No' if 'expert software developer' not in system_prompt else 'Yes'}")
        
        print(f"\n2. USER INSTRUCTION (Position 1):")
        print(f"   - Length: {len(user_instruction)} chars")
        print(f"   - Contains PLAY DUMB: {'Yes' if 'PLAY DUMB' in user_instruction else 'No'}")
        print(f"   - Contains ZERO knowledge: {'Yes' if 'ZERO knowledge' in user_instruction else 'No'}")
        
        print(f"\n3. ASSISTANT COMMITMENT (Position 2):")
        print(f"   - Length: {len(assistant_commitment)} chars")
        print(f"   - Commits to roadmap: {'Yes' if 'roadmap' in assistant_commitment else 'No'}")
        print(f"   - Commits to Play Dumb: {'Yes' if 'PLAY DUMB' in assistant_commitment else 'No'}")
        
        print(f"\n🎯 Expected LLM Behavior:")
        print(f"   1. System prompt sets neutral, helpful tone")
        print(f"   2. User instruction establishes Play Dumb rule")
        print(f"   3. Assistant commitment reinforces the rule")
        print(f"   4. LLM should follow the conversation pattern")
        
        # Check for consistency
        system_neutral = "expert software developer" not in system_prompt
        user_has_play_dumb = "PLAY DUMB" in user_instruction
        assistant_commits = "roadmap" in assistant_commitment
        
        if system_neutral and user_has_play_dumb and assistant_commits:
            print(f"\n✅ Message hierarchy is consistent!")
            return True
        else:
            print(f"\n❌ Message hierarchy has conflicts!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_behavior():
    """Test what the expected behavior should be now"""
    print("\n🤖 Expected Behavior Test")
    print("=" * 60)
    
    print("📝 With the fixed system prompt, when asked about a function, the LLM should:")
    print("   1. ✅ See neutral system prompt (no expert contradiction)")
    print("   2. ✅ See Play Dumb rule in conversation history")
    print("   3. ✅ See roadmap commitment in conversation history")
    print("   4. ✅ Follow the established pattern")
    print("   5. ✅ Provide roadmap first, then use MAP_REQUEST")
    
    print(f"\n📋 Expected response pattern:")
    expected_response = """I have ZERO knowledge about this codebase and need to gather information systematically. Here's my roadmap:

**Information I need to gather:**
1. Explore the repository structure to find files related to position management
2. Locate the close_position_based_on_conditions function
3. Understand the function's implementation and logic
4. Identify any dependencies or related functions

**Step 1: Repository exploration**
Let me start by exploring the codebase structure:

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""
    
    print(expected_response)
    
    print(f"\n🔍 Key improvements:")
    print(f"   ✅ No contradiction between system and conversation")
    print(f"   ✅ LLM follows established conversation pattern")
    print(f"   ✅ Roadmap-first approach maintained")
    print(f"   ✅ Play Dumb rule should be respected")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing System Prompt Fix")
    print("=" * 80)
    
    success1 = test_system_prompt_fix()
    success2 = test_message_hierarchy()
    success3 = test_expected_behavior()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: System prompt fix should resolve the issue!")
        print("\n📋 Summary of fix:")
        print("   ✅ Removed 'Act as an expert software developer'")
        print("   ✅ Changed to neutral 'AI assistant' tone")
        print("   ✅ Added 'Follow the conversation history'")
        print("   ✅ No more contradiction with Play Dumb rule")
        print("   ✅ Message hierarchy is now consistent")
        print("\n🎯 The LLM should now:")
        print("   1. Respect the Play Dumb rule from conversation history")
        print("   2. Provide roadmaps before taking action")
        print("   3. Use MAP_REQUEST systematically")
        print("   4. Not make assumptions about code structure")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
