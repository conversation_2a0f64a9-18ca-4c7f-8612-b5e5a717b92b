#!/usr/bin/env python3
"""
Test script to verify that CONTEXT_REQUEST works even when reason_for_request is missing.
This tests the exact scenario from the user's trading project.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_context_request_missing_reason():
    """Test CONTEXT_REQUEST parsing when reason_for_request is missing."""
    print("🧪 Testing CONTEXT_REQUEST with Missing reason_for_request")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import <PERSON>textRequestHandler
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a context request handler
        project_path = "."
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(project_path, aider_service)
        
        print(f"✅ Handler created successfully")
        
        # Test the EXACT CONTEXT_REQUEST from the user's scenario (missing reason_for_request)
        context_request_text = '''
{CONTEXT_REQUEST: {"original_user_query_context": "how does the close_position_based_on_conditions function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}, {"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "services", "file_name": "position_observer.py"}]}}
'''
        
        print(f"🔍 Testing CONTEXT_REQUEST parsing:")
        print(f"   Input: {context_request_text.strip()}")
        
        # Parse the context request
        parsed_request = handler.parse_context_request(context_request_text)
        
        if parsed_request:
            print(f"✅ CONTEXT_REQUEST parsed successfully!")
            print(f"   Original query: {parsed_request.original_user_query_context}")
            print(f"   Symbols count: {len(parsed_request.symbols_of_interest)}")
            print(f"   Reason for request: {parsed_request.reason_for_request}")
            
            # Verify the symbols were parsed correctly
            success_criteria = [
                (parsed_request.original_user_query_context == "how does the close_position_based_on_conditions function work?", "Original query preserved"),
                (len(parsed_request.symbols_of_interest) == 2, "Two symbols parsed"),
                (parsed_request.reason_for_request != "", "Reason for request has default value"),
                ("close_position_based_on_conditions" in parsed_request.reason_for_request, "Reason mentions the user query"),
            ]
            
            # Check individual symbols
            if len(parsed_request.symbols_of_interest) >= 2:
                symbol1 = parsed_request.symbols_of_interest[0]
                symbol2 = parsed_request.symbols_of_interest[1]
                
                success_criteria.extend([
                    (symbol1.name == "close_position_based_on_conditions", "First symbol name correct"),
                    (symbol1.type == "method_definition", "First symbol type correct"),
                    (symbol1.file_hint == "trade_management/position_exit_manager.py", "First symbol file hint correct"),
                    (symbol2.name == "close_position_based_on_conditions", "Second symbol name correct"),
                    (symbol2.type == "method_definition", "Second symbol type correct"),
                    (symbol2.file_hint == "services/position_observer.py", "Second symbol file hint correct"),
                ])
                
                print(f"\n📋 Symbol details:")
                print(f"   Symbol 1: {symbol1.type} '{symbol1.name}' in '{symbol1.file_hint}'")
                print(f"   Symbol 2: {symbol2.type} '{symbol2.name}' in '{symbol2.file_hint}'")
            
            passed = 0
            total = len(success_criteria)
            
            print(f"\n📊 Verification:")
            for condition, description in success_criteria:
                status = "✅" if condition else "❌"
                print(f"   {status} {description}")
                if condition:
                    passed += 1
            
            print(f"\n📊 CONTEXT_REQUEST parsing test: {passed}/{total} criteria passed")
            return passed == total
            
        else:
            print(f"❌ CONTEXT_REQUEST parsing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST parsing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_request_with_reason():
    """Test CONTEXT_REQUEST parsing when reason_for_request is present."""
    print("\n🧪 Testing CONTEXT_REQUEST with reason_for_request Present")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a context request handler
        project_path = "."
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(project_path, aider_service)
        
        # Test CONTEXT_REQUEST with reason_for_request included
        context_request_text = '''
{CONTEXT_REQUEST: {"original_user_query_context": "how does the close_position_based_on_conditions function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}], "reason_for_request": "To understand the position closing logic implementation"}}
'''
        
        print(f"🔍 Testing CONTEXT_REQUEST with explicit reason:")
        
        # Parse the context request
        parsed_request = handler.parse_context_request(context_request_text)
        
        if parsed_request:
            print(f"✅ CONTEXT_REQUEST parsed successfully!")
            print(f"   Reason for request: {parsed_request.reason_for_request}")
            
            # Verify the explicit reason was preserved
            success_criteria = [
                (parsed_request.reason_for_request == "To understand the position closing logic implementation", "Explicit reason preserved"),
                (len(parsed_request.symbols_of_interest) == 1, "One symbol parsed"),
            ]
            
            passed = 0
            total = len(success_criteria)
            
            print(f"\n📊 Verification:")
            for condition, description in success_criteria:
                status = "✅" if condition else "❌"
                print(f"   {status} {description}")
                if condition:
                    passed += 1
            
            print(f"\n📊 CONTEXT_REQUEST with reason test: {passed}/{total} criteria passed")
            return passed == total
            
        else:
            print(f"❌ CONTEXT_REQUEST parsing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST with reason: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all CONTEXT_REQUEST parsing tests."""
    print("🚀 Testing CONTEXT_REQUEST Parsing with Missing reason_for_request")
    print("=" * 80)

    tests = [
        test_context_request_missing_reason,
        test_context_request_with_reason,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 CONTEXT_REQUEST parsing is working correctly!")
        print("\n📋 The fix ensures:")
        print("  1. ✅ CONTEXT_REQUEST works even when reason_for_request is missing")
        print("  2. ✅ Default reason is generated from user query")
        print("  3. ✅ Explicit reason_for_request is preserved when present")
        print("  4. ✅ directory_name/file_name format is converted to file_hint")
        print("  5. ✅ Multiple symbols are parsed correctly")
        print("\n🎯 Ready for real trading project testing!")
        print("   The CONTEXT_REQUEST should now work without the missing field error!")
    else:
        print("⚠️  Some parsing features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
