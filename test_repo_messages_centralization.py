#!/usr/bin/env python3
"""
Test script to verify that repo_messages prompts are now centralized in base_prompts.py
"""

import sys
import os

def test_repo_messages_centralization():
    """Test that repo_messages now use prompts from base_prompts.py"""
    print("🧪 Testing Repo Messages Centralization")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        print("✅ Successfully imported required modules")
        
        # Test 1: Check that prompts exist in base_prompts.py
        prompts = CoderPrompts()
        
        required_prompts = [
            'smart_map_request_user_prompt',
            'smart_map_request_assistant_reply', 
            'legacy_repo_assistant_reply'
        ]
        
        for prompt_name in required_prompts:
            if hasattr(prompts, prompt_name):
                prompt_content = getattr(prompts, prompt_name)
                print(f"✅ Found {prompt_name}: {len(prompt_content)} characters")
                print(f"   Preview: {prompt_content[:80]}...")
            else:
                print(f"❌ Missing {prompt_name}")
                return False
        
        # Test 2: Check that get_repo_messages uses the centralized prompts
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
            
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test 3: Get repo messages and verify they use the centralized prompts
        repo_messages = coder.get_repo_messages()
        
        if not repo_messages:
            print("❌ No repo messages returned")
            return False
            
        print(f"✅ Got {len(repo_messages)} repo messages")
        
        # Check that the messages contain the expected content from base_prompts.py
        expected_user_content = prompts.smart_map_request_user_prompt
        expected_assistant_content = prompts.smart_map_request_assistant_reply
        
        found_user_match = False
        found_assistant_match = False
        
        for i, msg in enumerate(repo_messages):
            role = msg.get('role', '')
            content = msg.get('content', '')
            
            print(f"\nMessage {i+1} ({role}):")
            print(f"  Content length: {len(content)} characters")
            print(f"  Preview: {content[:100]}...")
            
            if role == 'user' and content == expected_user_content:
                found_user_match = True
                print("  ✅ Matches smart_map_request_user_prompt from base_prompts.py")
            elif role == 'assistant' and content == expected_assistant_content:
                found_assistant_match = True
                print("  ✅ Matches smart_map_request_assistant_reply from base_prompts.py")
        
        if found_user_match and found_assistant_match:
            print("\n✅ SUCCESS: repo_messages are using centralized prompts from base_prompts.py")
            return True
        else:
            print(f"\n❌ FAILURE: Expected prompts not found in repo_messages")
            print(f"   User match: {found_user_match}")
            print(f"   Assistant match: {found_assistant_match}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_content_verification():
    """Verify the actual content of the centralized prompts"""
    print("\n🔍 Testing Prompt Content Verification")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Check the smart map request user prompt
        user_prompt = prompts.smart_map_request_user_prompt
        expected_keywords = [
            "CRITICAL WORKFLOW RULE",
            "ZERO repository context", 
            "MUST start with MAP_REQUEST",
            "CANNOT use CONTEXT_REQUEST",
            "mandatory, not optional"
        ]
        
        print("📝 Smart Map Request User Prompt:")
        print(f"   Length: {len(user_prompt)} characters")
        print(f"   Content: {user_prompt}")
        
        for keyword in expected_keywords:
            if keyword in user_prompt:
                print(f"   ✅ Contains: '{keyword}'")
            else:
                print(f"   ❌ Missing: '{keyword}'")
                
        # Check the assistant reply
        assistant_reply = prompts.smart_map_request_assistant_reply
        expected_reply_keywords = [
            "I understand",
            "always start with MAP_REQUEST",
            "before using CONTEXT_REQUEST or REQUEST_FILE"
        ]
        
        print(f"\n📝 Smart Map Request Assistant Reply:")
        print(f"   Length: {len(assistant_reply)} characters")
        print(f"   Content: {assistant_reply}")
        
        for keyword in expected_reply_keywords:
            if keyword in assistant_reply:
                print(f"   ✅ Contains: '{keyword}'")
            else:
                print(f"   ❌ Missing: '{keyword}'")
                
        return True
        
    except Exception as e:
        print(f"❌ Error during prompt verification: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Repo Messages Centralization Tests")
    print("=" * 80)
    
    success1 = test_repo_messages_centralization()
    success2 = test_prompt_content_verification()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED: Repo messages are successfully centralized in base_prompts.py!")
    else:
        print("❌ SOME TESTS FAILED: Check the output above for details")
        
    print("=" * 80)
