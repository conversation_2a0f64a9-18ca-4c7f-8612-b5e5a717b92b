{
    "deepseek-reasoner": {
        "max_tokens": 8192,
        "max_input_tokens": 64000,
        "max_output_tokens": 8192,
        "input_cost_per_token": 0.00000055,
        "input_cost_per_token_cache_hit": 0.00000014,
        "cache_read_input_token_cost": 0.00000014,
        "cache_creation_input_token_cost": 0.0,
        "output_cost_per_token": 0.00000219,
        "litellm_provider": "deepseek",
        "mode": "chat",
        //"supports_function_calling": true, 
        "supports_assistant_prefill": true,
        //"supports_tool_choice": true,
        "supports_prompt_caching": true
    },
    "openrouter/deepseek/deepseek-r1:free": {
        "max_tokens": 8192,
        "max_input_tokens": 64000,
        "max_output_tokens": 8192,
        "input_cost_per_token": 0.0,
        "input_cost_per_token_cache_hit": 0.0,
        "cache_read_input_token_cost": 0.00,
        "cache_creation_input_token_cost": 0.0,
        "output_cost_per_token": 0.0,
        "litellm_provider": "openrouter",
        "mode": "chat",
        //"supports_function_calling": true, 
        "supports_assistant_prefill": true,
        //"supports_tool_choice": true,
        "supports_prompt_caching": true
    },
    "openrouter/deepseek/deepseek-chat:free": {
        "max_tokens": 8192,
        "max_input_tokens": 64000,
        "max_output_tokens": 8192,
        "input_cost_per_token": 0.0,
        "input_cost_per_token_cache_hit": 0.0,
        "cache_read_input_token_cost": 0.00,
        "cache_creation_input_token_cost": 0.0,
        "output_cost_per_token": 0.0,
        "litellm_provider": "openrouter",
        "mode": "chat",
        //"supports_function_calling": true, 
        "supports_assistant_prefill": true,
        //"supports_tool_choice": true,
        "supports_prompt_caching": true
    },
    "openrouter/deepseek/deepseek-chat-v3-0324": {
        "max_tokens": 8192,
        "max_input_tokens": 64000,
        "max_output_tokens": 8192,
        "input_cost_per_token": 0.00000055,
        "input_cost_per_token_cache_hit": 0.00000014,
        "cache_read_input_token_cost": 0.00000014,
        "cache_creation_input_token_cost": 0.0,
        "output_cost_per_token": 0.00000219,
        "litellm_provider": "openrouter",
        "mode": "chat",
        //"supports_function_calling": true, 
        "supports_assistant_prefill": true,
        //"supports_tool_choice": true,
        "supports_prompt_caching": true
    },
    "openrouter/deepseek/deepseek-chat-v3-0324:free": {
        "max_tokens": 131072,
        "max_input_tokens": 131072,
        "max_output_tokens": 131072,
        "input_cost_per_token": 0,
        "output_cost_per_token": 0,
        "litellm_provider": "openrouter",
        "supports_prompt_caching": true,
        "mode": "chat",
        "supports_tool_choice": true
    },
    "fireworks_ai/accounts/fireworks/models/deepseek-r1": {
        "max_tokens": 160000,
        "max_input_tokens": 128000,
        "max_output_tokens": 20480,
        "litellm_provider": "fireworks_ai",
        "input_cost_per_token": 0.000008,
        "output_cost_per_token": 0.000008,
        "mode": "chat",
    },
    "fireworks_ai/accounts/fireworks/models/deepseek-v3-0324": {
        "max_tokens": 160000,
        "max_input_tokens": 100000,
        "max_output_tokens": 8192,
        "litellm_provider": "fireworks_ai",
        "input_cost_per_token": 0.0000009,
        "output_cost_per_token": 0.0000009,
        "mode": "chat",
    },
    "openrouter/openrouter/quasar-alpha": {
        "max_input_tokens": 1000000,
        "max_output_tokens": 32000,
        "input_cost_per_token": 0.0,
        "output_cost_per_token": 0.0,
        "litellm_provider": "openrouter",
        "mode": "chat",
        "supports_vision": true,
        "supports_function_calling": true,
        "supports_system_messages": true,
        "supports_prompt_caching": true
    },
    "openrouter/openrouter/optimus-alpha": {
        "max_input_tokens": 1000000,
        "max_output_tokens": 32000,
        "input_cost_per_token": 0.0,
        "output_cost_per_token": 0.0,
        "litellm_provider": "openrouter",
        "mode": "chat"
    },
    "openrouter/openai/gpt-4o-mini": {
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "input_cost_per_token": 0.00000015,
        "output_cost_per_token": 0.00000060,
        "input_cost_per_token_batches": 0.000000075,
        "output_cost_per_token_batches": 0.00000030,
        "cache_read_input_token_cost": 0.000000075,
        "litellm_provider": "openrouter",
        "mode": "chat",
        "supports_function_calling": true,
        "supports_parallel_function_calling": true,
        "supports_response_schema": true,
        "supports_vision": true,
        "supports_prompt_caching": true,
        "supports_system_messages": true
    },
    "anthropic/claude-3-7-sonnet-20250219": {
        "max_tokens": 8192,
        "max_input_tokens": 200000,
        "max_output_tokens": 8192,
        "input_cost_per_token": 0.000003,
        "output_cost_per_token": 0.000015,
        "cache_creation_input_token_cost": 0.00000375,
        "cache_read_input_token_cost": 0.0000003,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": true,
        "supports_vision": true,
        "tool_use_system_prompt_tokens": 159,
        "supports_assistant_prefill": true,
        "supports_pdf_input": true,
        "supports_prompt_caching": true,
        "supports_response_schema": true,
        "deprecation_date": "2025-10-01",
        "supports_tool_choice": true
    },
    "openai/gpt-4.5-preview": {
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "input_cost_per_token": 0.000075,
        "output_cost_per_token": 0.00015,
        "cache_read_input_token_cost": 0.0000375,
        "litellm_provider": "openai",
        "mode": "chat",
        "supports_function_calling": true,
        "supports_parallel_function_calling": true,
        "supports_response_schema": true,
        "supports_vision": true,
        "supports_prompt_caching": true,
        "supports_system_messages": true,
        "supports_tool_choice": true
    },
    "gemini/gemini-2.5-pro-exp-03-25": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 64000,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_image": 0,
        "input_cost_per_video_per_second": 0,
        "input_cost_per_audio_per_second": 0,
        "input_cost_per_token": 0,
        "input_cost_per_character": 0, 
        "input_cost_per_token_above_128k_tokens": 0, 
        "input_cost_per_character_above_128k_tokens": 0, 
        "input_cost_per_image_above_128k_tokens": 0,
        "input_cost_per_video_per_second_above_128k_tokens": 0,
        "input_cost_per_audio_per_second_above_128k_tokens": 0,
        "output_cost_per_token": 0,
        "output_cost_per_character": 0,
        "output_cost_per_token_above_128k_tokens": 0,
        "output_cost_per_character_above_128k_tokens": 0,
      //"litellm_provider": "vertex_ai-language-models",
        "litellm_provider": "gemini",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true,
        "supports_video_input": true,
        "supports_pdf_input": true,
        "supports_response_schema": true,
        "supports_tool_choice": true,
        "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"
    },
    "vertex_ai/gemini-2.5-pro-exp-03-25": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 64000,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_image": 0,
        "input_cost_per_video_per_second": 0,
        "input_cost_per_audio_per_second": 0,
        "input_cost_per_token": 0,
        "input_cost_per_character": 0, 
        "input_cost_per_token_above_128k_tokens": 0, 
        "input_cost_per_character_above_128k_tokens": 0, 
        "input_cost_per_image_above_128k_tokens": 0,
        "input_cost_per_video_per_second_above_128k_tokens": 0,
        "input_cost_per_audio_per_second_above_128k_tokens": 0,
        "output_cost_per_token": 0,
        "output_cost_per_character": 0,
        "output_cost_per_token_above_128k_tokens": 0,
        "output_cost_per_character_above_128k_tokens": 0,
        "litellm_provider": "vertex_ai-language-models",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true,
        "supports_video_input": true,
        "supports_pdf_input": true,
        "supports_response_schema": true,
        "supports_tool_choice": true,
        "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"
    },
    "vertex_ai/gemini-2.5-pro-preview-03-25": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 64000,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_image": 0,
        "input_cost_per_video_per_second": 0,
        "input_cost_per_audio_per_second": 0,
        "input_cost_per_token": 0.00000125,
        "input_cost_per_character": 0,
        "input_cost_per_token_above_128k_tokens": 0,
        "input_cost_per_character_above_128k_tokens": 0,
        "input_cost_per_image_above_128k_tokens": 0,
        "input_cost_per_video_per_second_above_128k_tokens": 0,
        "input_cost_per_audio_per_second_above_128k_tokens": 0,
        "output_cost_per_token": 0.000010,
        "output_cost_per_character": 0,
        "output_cost_per_token_above_128k_tokens": 0,
        "output_cost_per_character_above_128k_tokens": 0,
        "litellm_provider": "vertex_ai-language-models",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true,
        "supports_video_input": true,
        "supports_pdf_input": true,
        "supports_response_schema": true,
        "supports_tool_choice": true,
        "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"
    },
    "openrouter/google/gemini-2.5-pro-preview-03-25": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 64000,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_image": 0,
        "input_cost_per_video_per_second": 0,
        "input_cost_per_audio_per_second": 0,
        "input_cost_per_token": 0.00000125,
        "input_cost_per_character": 0,
        "input_cost_per_token_above_128k_tokens": 0,
        "input_cost_per_character_above_128k_tokens": 0,
        "input_cost_per_image_above_128k_tokens": 0,
        "input_cost_per_video_per_second_above_128k_tokens": 0,
        "input_cost_per_audio_per_second_above_128k_tokens": 0,
        "output_cost_per_token": 0.000010,
        "output_cost_per_character": 0,
        "output_cost_per_token_above_128k_tokens": 0,
        "output_cost_per_character_above_128k_tokens": 0,
        "litellm_provider": "vertex_ai-language-models",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true,
        "supports_video_input": true,
        "supports_pdf_input": true,
        "supports_response_schema": true,
        "supports_tool_choice": true,
        "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"
    },
    "openrouter/google/gemini-2.5-pro-exp-03-25": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 64000,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_image": 0,
        "input_cost_per_video_per_second": 0,
        "input_cost_per_audio_per_second": 0,
        "input_cost_per_token": 0,
        "input_cost_per_character": 0,
        "input_cost_per_token_above_128k_tokens": 0,
        "input_cost_per_character_above_128k_tokens": 0,
        "input_cost_per_image_above_128k_tokens": 0,
        "input_cost_per_video_per_second_above_128k_tokens": 0,
        "input_cost_per_audio_per_second_above_128k_tokens": 0,
        "output_cost_per_token": 0,
        "output_cost_per_character": 0,
        "output_cost_per_token_above_128k_tokens": 0,
        "output_cost_per_character_above_128k_tokens": 0,
        "litellm_provider": "openrouter",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_audio_input": true,
        "supports_video_input": true,
        "supports_pdf_input": true,
        "supports_response_schema": true,
        "supports_tool_choice": true,
        "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"
    },
    "openrouter/x-ai/grok-3-beta": {
        "max_tokens": 131072,
        "max_input_tokens": 131072,
        "max_output_tokens": 131072,
        "input_cost_per_token": 0.000003,
        "output_cost_per_token": 0.000015,
        "litellm_provider": "openrouter",
        "mode": "chat"
    },
    "openrouter/x-ai/grok-3-mini-beta": {
        "max_tokens": 131072,
        "max_input_tokens": 131072,
        "max_output_tokens": 131072,
        "input_cost_per_token": 0.0000003,
        "output_cost_per_token": 0.0000005,
        "litellm_provider": "openrouter",
        "mode": "chat"
    },
    "openrouter/x-ai/grok-3-fast-beta": {
        "max_tokens": 131072,
        "max_input_tokens": 131072,
        "max_output_tokens": 131072,
        "input_cost_per_token": 0.000005,
        "output_cost_per_token": 0.000025,
        "litellm_provider": "openrouter",
        "mode": "chat"
    },
    "openrouter/x-ai/grok-3-mini-fast-beta": {
        "max_tokens": 131072,
        "max_input_tokens": 131072,
        "max_output_tokens": 131072,
        "input_cost_per_token": 0.0000006,
        "output_cost_per_token": 0.000004,
        "litellm_provider": "openrouter",
        "mode": "chat"
    },
    "openrouter/google/gemini-2.0-flash-exp:free": {
        "max_tokens": 8192,
        "max_input_tokens": 1048576,
        "max_output_tokens": 8192,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "litellm_provider": "openrouter",
        "mode": "chat",
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_response_schema": true,
        "supports_audio_output": true,
        "supports_tool_choice": true
    },
    "gemini-2.5-pro-preview-05-06": {
        "max_tokens": 65536,
        "max_input_tokens": 1048576,
        "max_output_tokens": 65536,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_audio_token": 0.00000125,
        "input_cost_per_token": 0.00000125,
        "input_cost_per_token_above_200k_tokens": 0.0000025,
        "output_cost_per_token": 0.00001,
        "output_cost_per_token_above_200k_tokens": 0.000015, 
        "litellm_provider": "vertex_ai-language-models",
        "mode": "chat",
        "supports_reasoning": true,
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_response_schema": true,
        "supports_audio_output": false,
        "supports_tool_choice": true,
        "supported_endpoints": ["/v1/chat/completions", "/v1/completions", "/v1/batch"],
        "supported_modalities": ["text", "image", "audio", "video"],
        "supported_output_modalities": ["text"],
        "source": "https://ai.google.dev/gemini-api/docs/models#gemini-2.5-flash-preview"
    },
    "gemini/gemini-2.5-pro-preview-05-06": {
        "max_tokens": 65536,
        "max_input_tokens": 1048576,
        "max_output_tokens": 65536,
        "max_images_per_prompt": 3000,
        "max_videos_per_prompt": 10,
        "max_video_length": 1,
        "max_audio_length_hours": 8.4,
        "max_audio_per_prompt": 1,
        "max_pdf_size_mb": 30,
        "input_cost_per_audio_token": 0.0000007,
        "input_cost_per_token": 0.00000125,
        "input_cost_per_token_above_200k_tokens": 0.0000025, 
        "output_cost_per_token": 0.00001,
        "output_cost_per_token_above_200k_tokens": 0.000015, 
        "litellm_provider": "gemini",
        "mode": "chat",
        "rpm": 10000,
        "tpm": 10000000,
        "supports_system_messages": true,
        "supports_function_calling": true,
        "supports_vision": true,
        "supports_response_schema": true,
        "supports_audio_output": false,
        "supports_tool_choice": true,
        "supported_modalities": ["text", "image", "audio", "video"],
        "supported_output_modalities": ["text"],
        "source": "https://ai.google.dev/gemini-api/docs/pricing#gemini-2.5-pro-preview"
    },
  "together_ai/Qwen/Qwen3-235B-A22B-fp8-tput": {
        "input_cost_per_token": 0.0000002,
        "output_cost_per_token": 0.0000006,
  }
}
