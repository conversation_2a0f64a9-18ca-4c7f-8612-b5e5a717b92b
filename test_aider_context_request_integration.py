#!/usr/bin/env python

import os
import sys
import argparse
from pathlib import Path
import json
import re

# Add the current directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from aider_context_request_integration import AiderContextRequestIntegration
    from context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
    from aider_template_renderer import AiderTemplateRenderer
    from aider_integration_service import AiderIntegrationService
except ImportError:
    print("Error: Could not import the required modules.")
    print("Make sure you have implemented the surgical extraction modules.")
    sys.exit(1)


def parse_args():
    parser = argparse.ArgumentParser(description="Test the CONTEXT_REQUEST integration with Aider")
    parser.add_argument(
        "--symbol",
        type=str,
        default="SurgicalFileExtractor.extract_symbol_content",
        help="Symbol to extract (default: SurgicalFileExtractor.extract_symbol_content)",
    )
    parser.add_argument(
        "--file-hint",
        type=str,
        default="surgical_file_extractor.py",
        help="File hint for the symbol (default: surgical_file_extractor.py)",
    )
    parser.add_argument(
        "--reason",
        type=str,
        default="To understand how the surgical file extractor works",
        help="Reason for the context request",
    )
    parser.add_argument(
        "--test-mode",
        type=str,
        choices=["basic", "parsing", "integration", "all"],
        default="all",
        help="Test mode (default: all)",
    )
    return parser.parse_args()


def test_basic_functionality(args):
    """Test basic functionality of the context request handler."""
    print("\n=== Testing Basic Functionality ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request handler
    handler = ContextRequestHandler(project_path, aider_service)
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name=args.symbol,
                file_hint=args.file_hint
            )
        ],
        reason_for_request=args.reason
    )
    
    # Process the context request
    print(f"Processing context request for symbol: {args.symbol}")
    result = handler.process_context_request(context_request)
    
    # Check if the result contains the expected keys
    expected_keys = ["original_user_query_context", "reason_for_request", "extracted_symbols", "dependency_snippets"]
    missing_keys = [key for key in expected_keys if key not in result]
    
    if missing_keys:
        print(f"Error: Missing keys in result: {missing_keys}")
        return False
    
    print("Basic functionality test passed!")
    return True


def test_context_request_parsing(args):
    """Test parsing of context requests from LLM responses."""
    print("\n=== Testing Context Request Parsing ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request handler
    handler = ContextRequestHandler(project_path, aider_service)
    
    # Test cases for parsing
    test_cases = [
        # Standard format
        f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {{"type": "method_definition", "name": "{args.symbol}", "file_hint": "{args.file_hint}"}}
  ],
  "reason_for_request": "{args.reason}"
}}}}
""",
        # Format with single quotes
        f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{
  'original_user_query_context': 'User is asking about the surgical file extractor',
  'symbols_of_interest': [
    {{'type': 'method_definition', 'name': '{args.symbol}', 'file_hint': '{args.file_hint}'}}
  ],
  'reason_for_request': '{args.reason}'
}}}}
""",
        # Format with unquoted keys
        f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{
  original_user_query_context: "User is asking about the surgical file extractor",
  symbols_of_interest: [
    {{type: "method_definition", name: "{args.symbol}", file_hint: "{args.file_hint}"}}
  ],
  reason_for_request: "{args.reason}"
}}}}
"""
    ]
    
    # Test each case
    for i, test_case in enumerate(test_cases):
        print(f"\nTesting case {i+1}:")
        context_request = handler.parse_context_request(test_case)
        
        if context_request is None:
            print(f"Error: Failed to parse test case {i+1}")
            return False
        
        print(f"Successfully parsed test case {i+1}")
        print(f"Symbol: {context_request.symbols_of_interest[0].name}")
        print(f"Reason: {context_request.reason_for_request}")
    
    print("\nContext request parsing test passed!")
    return True


def test_aider_integration(args):
    """Test integration with Aider."""
    print("\n=== Testing Aider Integration ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\nLLM Instructions:")
    instructions = integration.get_llm_instructions()
    print(instructions)
    
    # Check if the instructions contain the expected content
    if "CONTEXT_REQUEST" not in instructions:
        print("Error: LLM instructions do not contain CONTEXT_REQUEST")
        return False
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name=args.symbol,
                file_hint=args.file_hint
            )
        ],
        reason_for_request=args.reason
    )
    
    # Process the context request
    print("\nProcessing context request:")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Check if the augmented prompt contains the expected content
    if "REPOSITORY OVERVIEW" not in augmented_prompt:
        print("Error: Augmented prompt does not contain repository overview")
        return False
    
    if "INSTRUCTIONS FOR THIS TURN" not in augmented_prompt:
        print("Error: Augmented prompt does not contain instructions")
        return False
    
    # Test conversation history update
    integration.update_conversation_history("user", "How does the surgical file extractor work?")
    integration.update_conversation_history("assistant", "I'll explain how it works.")
    
    # Check if the conversation history was updated
    if len(integration.conversation_history) != 2:
        print("Error: Conversation history was not updated correctly")
        return False
    
    # Test iteration counter
    integration.reset_iteration_counter()
    if integration.current_iteration != 0:
        print("Error: Iteration counter was not reset correctly")
        return False
    
    print("\nAider integration test passed!")
    return True


def main():
    args = parse_args()
    
    # Run the selected tests
    if args.test_mode in ["basic", "all"]:
        test_basic_functionality(args)
    
    if args.test_mode in ["parsing", "all"]:
        test_context_request_parsing(args)
    
    if args.test_mode in ["integration", "all"]:
        test_aider_integration(args)
    
    print("\n=== All tests completed! ===")


if __name__ == "__main__":
    main()
