#!/usr/bin/env python3
"""
Test to verify that CONTEXT_REQUEST is properly prioritized over REQUEST_FILE
in the prompt structure.
"""

def test_file_access_reminder_priority():
    """Test that file_access_reminder properly prioritizes CONTEXT_REQUEST."""
    print("=== Testing file_access_reminder priority ===")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        file_access_reminder = prompts.file_access_reminder
        
        print("File access reminder content:")
        print(file_access_reminder)
        print()
        
        # Check for CONTEXT_REQUEST priority
        context_request_mentioned = "CONTEXT_REQUEST" in file_access_reminder
        preferred_method_mentioned = "PREFERRED METHOD" in file_access_reminder
        secondary_method_mentioned = "SECONDARY METHOD" in file_access_reminder
        request_file_mentioned = "REQUEST_FILE" in file_access_reminder
        
        print("Analysis:")
        print(f"✅ CONTEXT_REQUEST mentioned: {context_request_mentioned}")
        print(f"✅ PREFERRED METHOD mentioned: {preferred_method_mentioned}")
        print(f"✅ SECONDARY METHOD mentioned: {secondary_method_mentioned}")
        print(f"✅ REQUEST_FILE mentioned: {request_file_mentioned}")
        
        # Check order - CONTEXT_REQUEST should appear before REQUEST_FILE
        context_pos = file_access_reminder.find("CONTEXT_REQUEST")
        request_file_pos = file_access_reminder.find("REQUEST_FILE")
        
        if context_pos != -1 and request_file_pos != -1:
            context_first = context_pos < request_file_pos
            print(f"✅ CONTEXT_REQUEST appears before REQUEST_FILE: {context_first}")
        else:
            print("❌ Could not find both protocols in file_access_reminder")
            return False
        
        # Check that prioritization language is clear
        prioritize_mentioned = "prioritize CONTEXT_REQUEST" in file_access_reminder
        print(f"✅ Explicit prioritization mentioned: {prioritize_mentioned}")
        
        success = all([
            context_request_mentioned,
            preferred_method_mentioned,
            secondary_method_mentioned,
            request_file_mentioned,
            context_first,
            prioritize_mentioned
        ])
        
        print(f"\n🎯 Overall file_access_reminder test: {'PASS' if success else 'FAIL'}")
        return success
        
    except Exception as e:
        print(f"❌ Error testing file_access_reminder: {e}")
        return False


def test_repo_content_prefix_priority():
    """Test that repo_content_prefix properly prioritizes CONTEXT_REQUEST."""
    print("\n=== Testing repo_content_prefix priority ===")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        repo_content_prefix = prompts.repo_content_prefix
        
        print("Repo content prefix (first 800 chars):")
        print(repo_content_prefix[:800])
        print("...")
        print()
        
        # Check for CONTEXT_REQUEST priority
        context_request_mentioned = "CONTEXT_REQUEST" in repo_content_prefix
        preferred_mentioned = "PREFERRED" in repo_content_prefix
        secondary_mentioned = "SECONDARY" in repo_content_prefix
        request_file_mentioned = "REQUEST_FILE" in repo_content_prefix
        
        print("Analysis:")
        print(f"✅ CONTEXT_REQUEST mentioned: {context_request_mentioned}")
        print(f"✅ PREFERRED mentioned: {preferred_mentioned}")
        print(f"✅ SECONDARY mentioned: {secondary_mentioned}")
        print(f"✅ REQUEST_FILE mentioned: {request_file_mentioned}")
        
        # Check order - CONTEXT_REQUEST should appear before REQUEST_FILE
        context_pos = repo_content_prefix.find("CONTEXT_REQUEST")
        request_file_pos = repo_content_prefix.find("REQUEST_FILE")
        
        if context_pos != -1 and request_file_pos != -1:
            context_first = context_pos < request_file_pos
            print(f"✅ CONTEXT_REQUEST appears before REQUEST_FILE: {context_first}")
        else:
            print("❌ Could not find both protocols in repo_content_prefix")
            return False
        
        # Check that workflow prioritizes CONTEXT_REQUEST
        workflow_prioritizes = "Prioritize `CONTEXT_REQUEST`" in repo_content_prefix
        print(f"✅ Workflow explicitly prioritizes CONTEXT_REQUEST: {workflow_prioritizes}")
        
        success = all([
            context_request_mentioned,
            preferred_mentioned,
            secondary_mentioned,
            request_file_mentioned,
            context_first,
            workflow_prioritizes
        ])
        
        print(f"\n🎯 Overall repo_content_prefix test: {'PASS' if success else 'FAIL'}")
        return success
        
    except Exception as e:
        print(f"❌ Error testing repo_content_prefix: {e}")
        return False


def test_prompt_consistency():
    """Test that both prompts are consistent in their prioritization."""
    print("\n=== Testing prompt consistency ===")
    
    file_access_success = test_file_access_reminder_priority()
    repo_content_success = test_repo_content_prefix_priority()
    
    overall_success = file_access_success and repo_content_success
    
    print(f"\n🏆 OVERALL RESULT: {'SUCCESS' if overall_success else 'FAILURE'}")
    
    if overall_success:
        print("✅ CONTEXT_REQUEST is properly prioritized in both key prompt components")
        print("✅ The structural issues preventing LLM adoption of CONTEXT_REQUEST have been fixed")
    else:
        print("❌ There are still structural issues in the prompt prioritization")
    
    return overall_success


if __name__ == "__main__":
    test_prompt_consistency()
