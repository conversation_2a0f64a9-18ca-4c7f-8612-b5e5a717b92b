"""
Call Graph Builder - Analyzes function calls and builds call relationships.

This module analyzes ASTs to identify function calls and build a comprehensive
call graph showing which functions call which other functions.
"""

import ast
from typing import Dict, List, Any, Set

from .ir_context import IRContext, EntityInfo


class CallGraphBuilder:
    """
    Builds call graphs by analyzing function calls in ASTs.
    
    This analyzer identifies:
    - Function calls within each entity
    - Cross-module function calls
    - Method calls and attribute access
    - Call frequency and patterns
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the call graph builder with configuration.
        
        Args:
            config: Configuration dictionary for call analysis options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.max_calls_per_entity = config.get('max_calls_per_entity', 20)
        self.include_builtin_calls = config.get('include_builtin_calls', False)
        
        # Common built-in functions to potentially exclude
        self.builtin_functions = {
            'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple',
            'range', 'enumerate', 'zip', 'map', 'filter', 'sorted', 'reversed',
            'min', 'max', 'sum', 'any', 'all', 'isinstance', 'hasattr', 'getattr',
            'setattr', 'delattr', 'type', 'id', 'hash', 'repr', 'abs', 'round'
        }
    
    def build(self, context: IRContext) -> IRContext:
        """
        Build call graphs for all modules in the context.
        
        Args:
            context: The IR context containing modules with entities
            
        Returns:
            Updated context with call information populated
        """
        if self.verbose:
            print(f"   Analyzing calls in {len(context.modules)} modules")
        
        # First pass: analyze calls within each entity
        for module_name, module_info in context.modules.items():
            self._analyze_module_calls(module_info)
        
        # Second pass: build global call graph and usage relationships
        self._build_global_call_graph(context)
        self._update_used_by_relationships(context)
        
        if self.verbose:
            total_calls = sum(len(entity.calls) for module in context.modules.values() 
                            for entity in module.entities)
            print(f"   Identified {total_calls} function calls")
        
        return context
    
    def _analyze_module_calls(self, module_info) -> None:
        """
        Analyze function calls within a single module.
        
        Args:
            module_info: ModuleInfo object to analyze
        """
        for entity in module_info.entities:
            if entity.ast_node and entity.type in ('function', 'async_function'):
                calls = self._extract_calls_from_function(entity.ast_node)
                entity.calls = calls[:self.max_calls_per_entity]
    
    def _extract_calls_from_function(self, func_node: ast.FunctionDef) -> List[str]:
        """
        Extract function calls from a function AST node.
        
        Args:
            func_node: AST function definition node
            
        Returns:
            List of function names that are called
        """
        calls = []
        call_visitor = CallVisitor(self.include_builtin_calls, self.builtin_functions)
        call_visitor.visit(func_node)
        
        # Get unique calls while preserving order
        seen = set()
        for call in call_visitor.calls:
            if call not in seen:
                calls.append(call)
                seen.add(call)
        
        return calls
    
    def _build_global_call_graph(self, context: IRContext) -> None:
        """
        Build the global call graph across all modules.
        
        Args:
            context: IR context to update with global call graph
        """
        # Build mapping of entity names to modules
        entity_to_module = {}
        for module_name, module_info in context.modules.items():
            for entity in module_info.entities:
                entity_to_module[entity.name] = module_name
        
        # Build global call graph
        for module_name, module_info in context.modules.items():
            for entity in module_info.entities:
                if entity.calls:
                    context.global_call_graph[f"{module_name}.{entity.name}"] = set(entity.calls)
    
    def _update_used_by_relationships(self, context: IRContext) -> None:
        """
        Update the 'used_by' relationships based on the call graph.
        
        Args:
            context: IR context to update
        """
        # Build reverse mapping: what entities are used by what
        usage_map = {}
        
        for module_name, module_info in context.modules.items():
            for entity in module_info.entities:
                for called_func in entity.calls:
                    if called_func not in usage_map:
                        usage_map[called_func] = set()
                    usage_map[called_func].add(f"{module_name}.{entity.name}")
        
        # Update used_by for each entity
        for module_name, module_info in context.modules.items():
            for entity in module_info.entities:
                used_by = usage_map.get(entity.name, set())
                # Convert to module names and limit count
                used_by_modules = set()
                for user in used_by:
                    if '.' in user:
                        user_module = user.split('.')[0]
                        used_by_modules.add(user_module)
                
                entity.used_by = list(used_by_modules)[:10]  # Limit to 10 for brevity
        
        # Store global usage map
        context.global_usage_map = usage_map


class CallVisitor(ast.NodeVisitor):
    """AST visitor to extract function calls."""
    
    def __init__(self, include_builtin_calls: bool, builtin_functions: Set[str]):
        self.calls = []
        self.include_builtin_calls = include_builtin_calls
        self.builtin_functions = builtin_functions
    
    def visit_Call(self, node: ast.Call) -> None:
        """Visit a function call node."""
        call_name = self._get_call_name(node.func)
        
        if call_name:
            # Filter out built-in functions if configured
            if not self.include_builtin_calls and call_name in self.builtin_functions:
                pass  # Skip built-in functions
            else:
                self.calls.append(call_name)
        
        self.generic_visit(node)
    
    def _get_call_name(self, func_node) -> str:
        """Extract the function name from a call node."""
        if isinstance(func_node, ast.Name):
            return func_node.id
        elif isinstance(func_node, ast.Attribute):
            # For method calls like obj.method(), return just 'method'
            return func_node.attr
        elif isinstance(func_node, ast.Call):
            # For chained calls, get the outermost call
            return self._get_call_name(func_node.func)
        else:
            return None
