#!/usr/bin/env python3
"""
Debug script to understand where the slicing is happening.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def debug_repo_map_generation():
    """Debug repository map generation to find where slicing occurs."""
    print("🔍 Debugging Repository Map Generation")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,  # Small token limit
            root="aider-main",
            main_model=model,
            io=io,
            verbose=True
        )
        
        print("✅ RepoMap instance created")
        print(f"   max_map_tokens: {repo_map.max_map_tokens}")
        
        # Test get_ranked_tags_map_uncached directly
        print("\n🧪 Testing get_ranked_tags_map_uncached directly:")
        
        result = repo_map.get_ranked_tags_map_uncached(
            chat_fnames=[],
            other_fnames=["aider-main/aider/coders/base_coder.py"],
            max_map_tokens=None,  # No token limits
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        if result:
            print(f"   Result length: {len(result)} characters")
            print(f"   Contains '⋮': {'⋮' in result}")
            print(f"   First 200 chars: {result[:200]}...")
            
            if '⋮' in result:
                print("   ❌ SLICING DETECTED in get_ranked_tags_map_uncached!")
                # Find where the ⋮ symbols are
                lines = result.split('\n')
                for i, line in enumerate(lines[:20]):
                    if '⋮' in line:
                        print(f"   Line {i}: {line}")
            else:
                print("   ✅ No slicing detected in get_ranked_tags_map_uncached")
        else:
            print("   Result is None")
        
        # Test get_ranked_tags_map (with caching)
        print("\n🧪 Testing get_ranked_tags_map (with caching):")
        
        result2 = repo_map.get_ranked_tags_map(
            chat_fnames=[],
            other_fnames=["aider-main/aider/coders/base_coder.py"],
            max_map_tokens=None,  # No token limits
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        if result2:
            print(f"   Result length: {len(result2)} characters")
            print(f"   Contains '⋮': {'⋮' in result2}")
            print(f"   First 200 chars: {result2[:200]}...")
            
            if '⋮' in result2:
                print("   ❌ SLICING DETECTED in get_ranked_tags_map!")
            else:
                print("   ✅ No slicing detected in get_ranked_tags_map")
        else:
            print("   Result is None")
        
        # Test get_repo_map (full method)
        print("\n🧪 Testing get_repo_map (full method):")
        
        result3 = repo_map.get_repo_map(
            chat_files=set(),
            other_files=["aider-main/aider/coders/base_coder.py"],
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        if result3:
            print(f"   Result length: {len(result3)} characters")
            print(f"   Contains '⋮': {'⋮' in result3}")
            print(f"   First 200 chars: {result3[:200]}...")
            
            if '⋮' in result3:
                print("   ❌ SLICING DETECTED in get_repo_map!")
            else:
                print("   ✅ No slicing detected in get_repo_map")
        else:
            print("   Result is None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging repository map: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_tree_context():
    """Debug TreeContext to see if it's doing the slicing."""
    print("\n🔍 Debugging TreeContext")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        # Test render_tree directly
        print("🧪 Testing render_tree directly:")
        
        abs_fname = "aider-main/aider/coders/base_coder.py"
        rel_fname = "aider/coders/base_coder.py"
        lois = [1, 10, 20, 30]  # Some lines of interest
        
        tree_result = repo_map.render_tree(abs_fname, rel_fname, lois)
        
        if tree_result:
            print(f"   Tree result length: {len(tree_result)} characters")
            print(f"   Contains '⋮': {'⋮' in tree_result}")
            print(f"   First 200 chars: {tree_result[:200]}...")
            
            if '⋮' in tree_result:
                print("   ❌ SLICING DETECTED in render_tree!")
                # Find where the ⋮ symbols are
                lines = tree_result.split('\n')
                for i, line in enumerate(lines[:10]):
                    if '⋮' in line:
                        print(f"   Line {i}: {line}")
            else:
                print("   ✅ No slicing detected in render_tree")
        else:
            print("   Tree result is None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging TreeContext: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debugging tests."""
    print("🚀 Repository Map Slicing Debug")
    print("=" * 80)
    
    tests = [
        ("Repository Map Generation", debug_repo_map_generation),
        ("TreeContext", debug_tree_context),
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")

if __name__ == "__main__":
    main()
