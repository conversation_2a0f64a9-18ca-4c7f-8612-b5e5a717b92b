#!/usr/bin/env python

import os
import sys
from pathlib import Path

# Add the aider-main directory to the Python path
sys.path.insert(0, os.path.abspath("aider-main"))

from aider.repomap import RepoMap
from aider.io import InputOutput
from aider.models import Model

def main():
    # Set up the output file
    output_file = "aider_repo_map_output"
    os.makedirs(output_file, exist_ok=True)
    
    # Create a simple model instance for token counting
    model = Model("gpt-3.5-turbo")
    
    # Create an InputOutput instance
    io = InputOutput()
    
    # Initialize the RepoMap with a higher token limit for better coverage
    repo_map = RepoMap(
        map_tokens=8192,  # Increased token limit for better coverage
        root="aider-main",
        main_model=model,
        io=io,
        repo_content_prefix="# Repository Map\n\nThis map shows the structure and dependencies of the codebase:\n\n",
        verbose=True,
        refresh="always"  # Force refresh
    )
    
    # Get all files in the repository
    all_files = []
    for root, dirs, files in os.walk("aider-main"):
        # Skip __pycache__ directories and other non-source directories
        if "__pycache__" in root or ".git" in root:
            continue
        
        for file in files:
            # Only include Python files
            if file.endswith(".py"):
                all_files.append(os.path.join(root, file))
    
    # Generate the repository map
    print(f"Analyzing {len(all_files)} files...")
    
    # We'll use an empty list for chat_files since we want to analyze all files
    chat_files = []
    other_files = all_files
    
    # Generate the repository map
    repo_content = repo_map.get_repo_map(
        chat_files=chat_files,
        other_files=other_files,
        force_refresh=True
    )
    
    # Save the repository map to a file
    if repo_content:
        with open(os.path.join(output_file, "repository_map.txt"), "w", encoding="utf-8") as f:
            f.write(repo_content)
        print(f"Repository map saved to {os.path.join(output_file, 'repository_map.txt')}")
    else:
        print("Failed to generate repository map")
    
    # Now let's extract and save the dependency graph information
    # This requires accessing the internal graph structure
    try:
        # Create a file to store dependency information
        with open(os.path.join(output_file, "dependencies.txt"), "w", encoding="utf-8") as f:
            f.write("# File Dependencies\n\n")
            f.write("This file shows which files reference code defined in other files.\n\n")
            
            # We need to run get_ranked_tags to build the graph
            mentioned_fnames = set()
            mentioned_idents = set()
            
            # Get the graph data
            import networkx as nx
            
            # We'll manually build a simplified dependency graph
            # First get the tags for each file
            defines = {}  # file -> set of defined symbols
            references = {}  # file -> set of referenced symbols
            
            # Process each file to extract definitions and references
            for fname in all_files:
                rel_fname = os.path.relpath(fname, "aider-main")
                tags = list(repo_map.get_tags(fname, rel_fname))
                
                if not tags:
                    continue
                
                # Extract definitions and references
                file_defines = set()
                file_references = set()
                
                for tag in tags:
                    if tag.kind == "def":
                        file_defines.add(tag.name)
                    elif tag.kind == "ref":
                        file_references.add(tag.name)
                
                defines[rel_fname] = file_defines
                references[rel_fname] = file_references
            
            # Now identify dependencies between files
            dependencies = {}
            
            for file1, refs in references.items():
                dependencies[file1] = set()
                
                for file2, defs in defines.items():
                    if file1 == file2:
                        continue
                    
                    # Check if file1 references any symbols defined in file2
                    common_symbols = refs.intersection(defs)
                    if common_symbols:
                        dependencies[file1].add((file2, len(common_symbols)))
            
            # Write the dependencies to the file
            for file, deps in sorted(dependencies.items()):
                if deps:
                    f.write(f"\n## {file} depends on:\n")
                    for dep_file, count in sorted(deps, key=lambda x: x[1], reverse=True):
                        f.write(f"- {dep_file} ({count} references)\n")
            
            print(f"Dependency information saved to {os.path.join(output_file, 'dependencies.txt')}")
    
    except Exception as e:
        print(f"Error extracting dependency information: {e}")

if __name__ == "__main__":
    main()
