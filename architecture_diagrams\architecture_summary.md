# System Architecture Analysis Summary

Generated from: C:\Users\<USER>\Documents\aider_project\aider__500
Analysis date: 1748465789.725324

## 📊 Overall Statistics

- **Total Modules**: 271
- **Total Entities**: 11711
- **Total Functions**: 2243
- **Total Classes**: 209
- **Lines of Code**: 55093
- **Dependencies**: 1263

## 🗂️ Module Groups

### scripts
- **Modules**: 16
- **Total Entities**: 595
- **Key Modules**: 30k-image, blame, clean_metadata, dl_icons, history_prompts

### aider
- **Modules**: 51
- **Total Entities**: 3928
- **Key Modules**: __main__, aider, analytics, args, args_formatter

### root
- **Modules**: 142
- **Total Entities**: 4044
- **Key Modules**: aider_context_request_integration, aider_integration_service, aider_template_renderer, analyze_dependencies, analyze_slicing_intelligence

### tests
- **Modules**: 41
- **Total Entities**: 2341
- **Key Modules**: basic, sample, test, test_analytics, test_aws_credentials

### benchmark
- **Modules**: 9
- **Total Entities**: 462
- **Key Modules**: benchmark, over_time, plots, problem_stats, prompts

### mid_level_ir
- **Modules**: 12
- **Total Entities**: 341
- **Key Modules**: call_graph_builder, criticality_scorer, dependency_analyzer, entity_extractor, error_analyzer

## 🏗️ Architectural Layers

### Presentation Layer
- **Modules**: 7
- **Total Entities**: 287

### Service Layer
- **Modules**: 4
- **Total Entities**: 571

### Core Layer
- **Modules**: 93
- **Total Entities**: 5286

### Data Layer
- **Modules**: 7
- **Total Entities**: 438

### Utility Layer
- **Modules**: 2
- **Total Entities**: 102

### Test Layer
- **Modules**: 140
- **Total Entities**: 4443

### Script Layer
- **Modules**: 18
- **Total Entities**: 584

## ⚠️ Critical Modules

Top 10 most critical modules:

1. **aider**
   - Criticality Score: 68.70
   - High-Criticality Entities: 0
   - Incoming Dependencies: 687
   - Total Entities: 7

2. **aider_integration_service**
   - Criticality Score: 2.21
   - High-Criticality Entities: 4
   - Incoming Dependencies: 22
   - Total Entities: 337

3. **ir_context**
   - Criticality Score: 1.10
   - High-Criticality Entities: 0
   - Incoming Dependencies: 11
   - Total Entities: 31

4. **dump**
   - Criticality Score: 0.90
   - High-Criticality Entities: 0
   - Incoming Dependencies: 9
   - Total Entities: 8

5. **context_request_handler**
   - Criticality Score: 0.82
   - High-Criticality Entities: 2
   - Incoming Dependencies: 8
   - Total Entities: 49

6. **aider_template_renderer**
   - Criticality Score: 0.80
   - High-Criticality Entities: 0
   - Incoming Dependencies: 8
   - Total Entities: 43

7. **aider_context_request_integration**
   - Criticality Score: 0.70
   - High-Criticality Entities: 0
   - Incoming Dependencies: 7
   - Total Entities: 16

8. **surgical_file_extractor**
   - Criticality Score: 0.70
   - High-Criticality Entities: 0
   - Incoming Dependencies: 7
   - Total Entities: 38

9. **surgical_context_extractor**
   - Criticality Score: 0.50
   - High-Criticality Entities: 0
   - Incoming Dependencies: 5
   - Total Entities: 232

10. **context_request**
   - Criticality Score: 0.40
   - High-Criticality Entities: 0
   - Incoming Dependencies: 4
   - Total Entities: 1

## 📈 Generated Diagrams

### 1. System Overview (`system_overview.mmd`)
High-level view of module groups and their relationships. Shows the main architectural components and how they connect.

### 2. Dependency Graph (`dependency_graph.mmd`)
Detailed dependency relationships between the most connected modules. Color-coded by connectivity level.

### 3. Component Architecture (`component_architecture.mmd`)
Layered architecture view showing presentation, service, core, data, and utility layers.

### 4. Critical Path (`critical_path.mmd`)
Focus on the most critical modules and their dependencies. Useful for understanding high-impact components.

## 🔧 How to View Diagrams

1. **Online**: Copy the `.mmd` file content to [Mermaid Live Editor](https://mermaid.live/)
2. **VS Code**: Install the Mermaid Preview extension
3. **Command Line**: Use `mmdc` (mermaid-cli) to generate images
4. **Documentation**: Include in Markdown files that support Mermaid

