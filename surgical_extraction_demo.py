#!/usr/bin/env python

import os
import sys
from pprint import pprint

from aider_integration_service import AiderIntegrationService
from surgical_file_extractor import SurgicalFileExtractor


def format_extraction_output(symbol_name, file_path, content, extraction_range):
    """Format the extracted content for display."""
    header = f"# 🎯 OPTIMIZED: Send only what's needed\n\n"
    header += f"### REQUESTED FILE CONTEXT (Surgical Extraction)\n\n"
    header += f"--- File: {file_path} (Targeted extraction for {symbol_name}) ---\n\n"
    
    # Count lines
    lines = content.splitlines() if content else []
    line_count = len(lines)
    
    output = f"{header}"
    output += f"# THE COMPLETE TARGET FUNCTION ({line_count} lines)\n"
    output += f"```python\n{content}\n```\n\n"
    
    # Add extraction metadata
    if extraction_range:
        output += f"### EXTRACTION METADATA\n"
        output += f"- Symbol: {symbol_name}\n"
        output += f"- File: {file_path}\n"
        output += f"- Start Line: {extraction_range.start_line}\n"
        output += f"- End Line: {extraction_range.end_line}\n"
        output += f"- Total Lines: {extraction_range.total_lines}\n"
    
    return output


def main():
    """Main function to demonstrate the Surgical File Extractor."""
    if len(sys.argv) < 4:
        print("Usage: python surgical_extraction_demo.py <project_path> <file_path> <symbol_name>")
        sys.exit(1)
    
    project_path = sys.argv[1]
    file_path = sys.argv[2]
    symbol_name = sys.argv[3]
    
    # Initialize the AiderIntegrationService
    try:
        aider_service = AiderIntegrationService()
    except Exception as e:
        print(f"Error initializing AiderIntegrationService: {e}")
        print("Using mock service for demonstration purposes.")
        aider_service = type('MockAiderService', (), {'coder': type('MockCoder', (), {'repo_map': None})})()
    
    # Initialize the SurgicalFileExtractor
    extractor = SurgicalFileExtractor(aider_service)
    
    # Get all symbols in the file
    print(f"\nSymbols defined in {file_path}:")
    symbols = extractor.get_symbols_in_file(project_path, file_path)
    if symbols:
        for symbol in symbols:
            print(f"  - {symbol.name} ({symbol.symbol_type}) at line {symbol.start_line}")
    else:
        print("  No symbols found or repository map not available.")
    
    # Extract the symbol range
    print(f"\nExtracting symbol: {symbol_name}")
    extraction_range = extractor.extract_symbol_range(symbol_name, file_path, project_path)
    if extraction_range:
        print(f"  Found at lines {extraction_range.start_line}-{extraction_range.end_line} ({extraction_range.total_lines} lines)")
    else:
        print(f"  Symbol not found in {file_path}")
        return
    
    # Extract the symbol content
    content = extractor.extract_symbol_content(symbol_name, file_path, project_path)
    if content:
        # Format and display the output
        output = format_extraction_output(symbol_name, file_path, content, extraction_range)
        print("\n" + output)
    else:
        print(f"  Failed to extract content for {symbol_name}")


if __name__ == "__main__":
    main()
