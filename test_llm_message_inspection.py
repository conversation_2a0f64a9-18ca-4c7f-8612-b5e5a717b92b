#!/usr/bin/env python3
"""
Comprehensive test to verify that the LLM receives NO repository maps
outside of MAP_REQUEST in ANY way.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_all_llm_messages():
    """Test ALL messages that would be sent to the LLM to ensure no repository maps leak through."""
    print("🔍 Comprehensive LLM Message Inspection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,  # Large token limit
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test 1: Check format_chat_chunks (what actually gets sent to LLM)
        print("\n🧪 Test 1: format_chat_chunks (ACTUAL LLM MESSAGES)")
        print("-" * 60)
        
        try:
            chunks = coder.format_chat_chunks()
            all_messages = chunks.all_messages()
            
            print(f"📊 Total messages to LLM: {len(all_messages)}")
            
            repo_map_found = False
            large_content_found = False
            
            for i, msg in enumerate(all_messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                content_length = len(content)
                
                print(f"\nMessage {i+1} ({role}): {content_length} characters")
                
                # Check for repository map indicators
                repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ', 'from ']
                repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in content)
                
                # Check for large content that might be a repository map
                if content_length > 5000:
                    print(f"   ⚠️  LARGE CONTENT DETECTED: {content_length} characters")
                    print(f"   Content preview: {content[:200]}...")
                    large_content_found = True
                    
                    if repo_indicator_count > 10:  # Many code indicators
                        print(f"   ❌ REPOSITORY MAP DETECTED! ({repo_indicator_count} code indicators)")
                        repo_map_found = True
                    else:
                        print(f"   ✅ Not a repository map ({repo_indicator_count} code indicators)")
                
                # Check for specific repository map content
                if any(phrase in content.lower() for phrase in ['repository structure', 'repo map', 'file listing']):
                    if content_length > 1000:
                        print(f"   ❌ REPOSITORY CONTENT DETECTED!")
                        repo_map_found = True
                
                # Show first 100 chars of each message for inspection
                if content_length > 0:
                    preview = content[:100].replace('\n', '\\n')
                    print(f"   Preview: {preview}...")
            
            if not repo_map_found and not large_content_found:
                print("\n✅ NO repository maps found in LLM messages")
                return True
            else:
                print(f"\n❌ Repository content detected in LLM messages!")
                return False
                
        except Exception as e:
            print(f"❌ Error testing format_chat_chunks: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Error in comprehensive LLM message inspection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_message_methods():
    """Test individual message generation methods to find any repository map leaks."""
    print("\n🔍 Individual Message Methods Inspection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Test all message generation methods
        message_methods = [
            ("get_repo_messages", lambda: coder.get_repo_messages()),
            ("get_readonly_files_messages", lambda: coder.get_readonly_files_messages()),
            ("get_chat_files_messages", lambda: coder.get_chat_files_messages()),
        ]
        
        all_clean = True
        
        for method_name, method_call in message_methods:
            print(f"\n🧪 Testing {method_name}:")
            try:
                messages = method_call()
                
                if not messages:
                    print(f"   ✅ {method_name} returns empty/None")
                    continue
                
                for i, msg in enumerate(messages):
                    if isinstance(msg, dict) and 'content' in msg:
                        content = msg['content']
                        content_length = len(content)
                        role = msg.get('role', 'unknown')
                        
                        print(f"   Message {i+1} ({role}): {content_length} characters")
                        
                        if content_length > 5000:
                            print(f"     ⚠️  LARGE CONTENT: {content_length} characters")
                            print(f"     Preview: {content[:200]}...")
                            
                            # Check for repository map indicators
                            repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ']
                            repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in content)
                            
                            if repo_indicator_count > 10:
                                print(f"     ❌ REPOSITORY MAP DETECTED! ({repo_indicator_count} indicators)")
                                all_clean = False
                            else:
                                print(f"     ✅ Not a repository map ({repo_indicator_count} indicators)")
                        else:
                            preview = content[:100].replace('\n', '\\n')
                            print(f"     Preview: {preview}...")
                            
            except Exception as e:
                print(f"   ❌ {method_name} failed: {e}")
                all_clean = False
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error testing individual message methods: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_prompt_inspection():
    """Test the system prompt to ensure no repository maps are embedded."""
    print("\n🔍 System Prompt Inspection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Get the system prompt
        system_prompt = coder.gpt_prompts.main_system
        
        print(f"📊 System prompt length: {len(system_prompt)} characters")
        print(f"System prompt preview: {system_prompt[:200]}...")
        
        # Check for repository map content in system prompt
        repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ', 'from ']
        repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in system_prompt)
        
        if repo_indicator_count > 10:
            print(f"❌ REPOSITORY MAP DETECTED in system prompt! ({repo_indicator_count} indicators)")
            return False
        else:
            print(f"✅ System prompt clean ({repo_indicator_count} indicators)")
            return True
        
    except Exception as e:
        print(f"❌ Error testing system prompt: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_query_processing():
    """Test how user queries are processed to ensure no repository maps are added."""
    print("\n🔍 User Query Processing Inspection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Test user query processing
        test_query = "How does the coder system work?"
        
        print(f"🧪 Testing user query: '{test_query}'")
        
        # Check if preprocessing adds repository content
        processed_query = coder.preproc_user_input(test_query)
        
        if processed_query:
            processed_length = len(processed_query)
            original_length = len(test_query)
            
            print(f"   Original query length: {original_length} characters")
            print(f"   Processed query length: {processed_length} characters")
            
            if processed_length > original_length * 2:  # Significant increase
                print(f"   ⚠️  QUERY SIGNIFICANTLY EXPANDED!")
                print(f"   Processed query preview: {processed_query[:300]}...")
                
                # Check for repository map indicators
                repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ']
                repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in processed_query)
                
                if repo_indicator_count > 5:
                    print(f"   ❌ REPOSITORY CONTENT ADDED TO QUERY! ({repo_indicator_count} indicators)")
                    return False
                else:
                    print(f"   ✅ No repository content added ({repo_indicator_count} indicators)")
            else:
                print(f"   ✅ Query not significantly expanded")
        else:
            print(f"   ✅ Query preprocessing returned None/empty")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing user query processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive LLM message inspection tests."""
    print("🚀 Comprehensive LLM Message Inspection")
    print("=" * 100)
    
    tests = [
        ("All LLM Messages", test_all_llm_messages),
        ("Individual Message Methods", test_individual_message_methods),
        ("System Prompt", test_system_prompt_inspection),
        ("User Query Processing", test_user_query_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 COMPREHENSIVE LLM MESSAGE INSPECTION SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 PERFECT! LLM receives NO repository maps outside MAP_REQUEST!")
        print("\n📋 Confirmed:")
        print("  ✅ No repository maps in LLM messages")
        print("  ✅ No repository maps in individual message methods")
        print("  ✅ No repository maps in system prompt")
        print("  ✅ No repository maps added to user queries")
        print("\n🎯 ANSWER: Repository maps are NOT sent to LLM outside MAP_REQUEST")
    else:
        print("⚠️  REPOSITORY MAPS ARE BEING SENT TO LLM!")
        print("   This means the complete repository map is leaking to the LLM")
        print("   outside of the MAP_REQUEST system.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
