#!/usr/bin/env python3
"""
Check if .pyc files are included in the exclusion list
"""

# Define the exclusion list from optimize_repomap.py
exclude_extensions = {
    # Media files
    '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.bmp', '.tiff',
    '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.ogg',
    '.wav', '.flac', '.aac', '.m4a',
    
    # Font files
    '.ttf', '.woff', '.woff2', '.eot', '.otf',
    
    # Build artifacts
    '.pyc', '.pyo', '.class', '.o', '.so', '.dll', '.exe', '.obj',
    '.lib', '.a', '.dylib',
    
    # Database files
    '.db', '.db-shm', '.db-wal', '.sqlite', '.sqlite3',
    
    # Cache and temporary files
    '.cache', '.tmp', '.temp', '.log',
    
    # Documentation files (as requested)
    '.txt', '.pdf', '.doc', '.docx', '.rtf',
    
    # Archive files
    '.zip', '.tar', '.gz', '.rar', '.7z', '.bz2',
    
    # IDE/Editor files
    '.swp', '.swo', '.bak', '.orig', '.rej',
    
    # OS files
    '.DS_Store', 'Thumbs.db',
    
    # Binary data
    '.bin', '.dat', '.dump'
}

print('📋 Currently Excluded Extensions:')
for ext in sorted(exclude_extensions):
    print(f'   {ext}')

print(f'\n📊 Total excluded: {len(exclude_extensions)} file types')

if '.pyc' in exclude_extensions:
    print('✅ .pyc files are already excluded!')
else:
    print('❌ .pyc files are NOT excluded')

# Check what's in the build artifacts section specifically
build_artifacts = {'.pyc', '.pyo', '.class', '.o', '.so', '.dll', '.exe', '.obj', '.lib', '.a', '.dylib'}
print(f'\n🔧 Build artifacts excluded: {sorted(build_artifacts)}')
