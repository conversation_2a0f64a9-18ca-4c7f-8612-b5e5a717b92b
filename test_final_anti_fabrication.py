#!/usr/bin/env python3
"""
Final comprehensive test to verify anti-fabrication fixes work.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_anti_fabrication_enforcement():
    """Test that anti-fabrication rules are properly enforced."""
    print("🔍 Anti-Fabrication Enforcement Test")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test 1: Check that specific problematic examples are removed
        print("\n🧪 Test 1: Checking for problematic examples")
        print("-" * 60)
        
        problematic_phrases = [
            "close_position_based_on_conditions",
            "position_exit_manager",
            "trade_management",
        ]
        
        all_prompts = [
            ("repo_content_prefix", coder.gpt_prompts.repo_content_prefix),
            ("file_access_reminder", coder.gpt_prompts.file_access_reminder),
            ("main_system", coder.gpt_prompts.main_system),
            ("reality_check_prompt", coder.gpt_prompts.reality_check_prompt),
        ]
        
        problematic_found = False
        
        for prompt_name, prompt_content in all_prompts:
            print(f"   Checking {prompt_name}:")
            
            for phrase in problematic_phrases:
                if phrase in prompt_content:
                    print(f"     ❌ FOUND PROBLEMATIC PHRASE: '{phrase}'")
                    problematic_found = True
                else:
                    print(f"     ✅ No '{phrase}' found")
        
        if not problematic_found:
            print("   ✅ No problematic examples found in prompts")
        
        # Test 2: Check anti-fabrication rules are present
        print("\n🧪 Test 2: Checking anti-fabrication rules")
        print("-" * 60)
        
        anti_fabrication_keywords = [
            "NEVER fabricate",
            "NEVER guess",
            "NEVER hallucinate",
            "retrieve the actual implementation",
            "CRITICAL ANTI-FABRICATION",
            "VIOLATION WARNING",
        ]
        
        anti_fabrication_found = 0
        
        for prompt_name, prompt_content in all_prompts:
            for keyword in anti_fabrication_keywords:
                if keyword in prompt_content:
                    anti_fabrication_found += 1
                    print(f"   ✅ Found '{keyword}' in {prompt_name}")
        
        print(f"   Total anti-fabrication rules found: {anti_fabrication_found}")
        
        if anti_fabrication_found >= 5:
            print("   ✅ Sufficient anti-fabrication rules present")
            anti_fabrication_ok = True
        else:
            print("   ❌ Insufficient anti-fabrication rules")
            anti_fabrication_ok = False
        
        # Test 3: Verify repository map is not accessible
        print("\n🧪 Test 3: Verifying repository map isolation")
        print("-" * 60)
        
        repo_map_result = coder.get_repo_map()
        if repo_map_result is None:
            print("   ✅ get_repo_map() returns None - repository map isolated")
            repo_map_isolated = True
        else:
            print(f"   ❌ get_repo_map() returns content: {len(repo_map_result)} characters")
            repo_map_isolated = False
        
        # Test 4: Check message generation is clean
        print("\n🧪 Test 4: Checking message generation")
        print("-" * 60)
        
        repo_messages = coder.get_repo_messages()
        total_message_chars = sum(len(msg.get('content', '')) for msg in repo_messages)
        
        print(f"   Repository messages total: {total_message_chars} characters")
        
        if total_message_chars < 500:  # Should be small Smart Map Request instructions only
            print("   ✅ Repository messages are minimal")
            messages_clean = True
        else:
            print("   ❌ Repository messages are too large")
            messages_clean = False
        
        # Overall result
        all_tests_passed = (
            not problematic_found and
            anti_fabrication_ok and
            repo_map_isolated and
            messages_clean
        )
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Error in anti-fabrication enforcement test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_correctness():
    """Test that the correct workflow is enforced."""
    print("\n🔍 Workflow Correctness Test")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Test workflow instructions
        print("🧪 Checking workflow instructions:")
        
        workflow_requirements = [
            "MAP_REQUEST",
            "CONTEXT_REQUEST", 
            "REQUEST_FILE",
            "NEVER ask users to manually add files",
            "STEP 1",
            "STEP 2",
            "STEP 3",
            "STEP 4",
        ]
        
        workflow_found = 0
        
        prompt_content = coder.gpt_prompts.files_no_full_files_with_repo_map
        
        for requirement in workflow_requirements:
            if requirement in prompt_content:
                workflow_found += 1
                print(f"   ✅ Found '{requirement}'")
            else:
                print(f"   ❌ Missing '{requirement}'")
        
        print(f"   Workflow requirements found: {workflow_found}/{len(workflow_requirements)}")
        
        return workflow_found >= len(workflow_requirements) - 1  # Allow 1 missing
        
    except Exception as e:
        print(f"❌ Error in workflow correctness test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run final comprehensive anti-fabrication tests."""
    print("🚀 Final Anti-Fabrication Verification")
    print("=" * 100)
    
    tests = [
        ("Anti-Fabrication Enforcement", test_anti_fabrication_enforcement),
        ("Workflow Correctness", test_workflow_correctness),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 FINAL ANTI-FABRICATION VERIFICATION SUMMARY")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL ANTI-FABRICATION FIXES SUCCESSFUL!")
        print("\n📋 Confirmed:")
        print("  ✅ Problematic examples removed from prompts")
        print("  ✅ Strong anti-fabrication rules added")
        print("  ✅ Repository map properly isolated")
        print("  ✅ Message generation is clean")
        print("  ✅ Correct workflow enforced")
        print("\n🎯 LLM can no longer fabricate code without context!")
        print("🎯 LLM must use MAP_REQUEST → CONTEXT_REQUEST/REQUEST_FILE workflow!")
        print("🎯 LLM will be forced to retrieve actual code before analysis!")
    else:
        print("⚠️  SOME ANTI-FABRICATION FIXES FAILED!")
        print("   Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
