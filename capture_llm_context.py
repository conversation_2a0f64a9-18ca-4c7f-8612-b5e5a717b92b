#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to capture and display the actual context sent to the LLM.
This will help debug the new direct IR context flow.
"""

import sys
import os

# Add aider to path
sys.path.insert(0, "aider-main")

def capture_context_injection():
    """Capture the context that gets injected into the LLM conversation."""
    
    print("🔍 CAPTURING LLM CONTEXT INJECTION")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        print("✅ Imported required modules")
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Use external project path
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
            print(f"⚠️  Using current directory instead: {external_project}")
        
        repo = GitRepo(io, [], external_project)
        
        print(f"📁 Using project path: {external_project}")
        
        # Create coder
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192,
            verbose=True,
            dry_run=True
        )
        
        print("✅ Created coder instance")
        
        # Test query
        test_query = "How does position management work in the trading system?"
        print(f"\n📝 Testing query: '{test_query}'")
        
        # Capture the context injection
        class ContextCapturingCoder(coder.__class__):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.captured_context = None
                self.captured_messages = []
            
            def process_direct_ir_context(self, user_message):
                print(f"🎯 process_direct_ir_context called with: '{user_message}'")
                
                # Call the original method
                result = super().process_direct_ir_context(user_message)
                
                if result:
                    print("✅ IR context generation successful")
                    
                    # Capture the injected context
                    if hasattr(self, 'cur_messages') and self.cur_messages:
                        for i, msg in enumerate(self.cur_messages):
                            if msg.get('role') == 'system' and 'Intelligent Context for Your Query' in msg.get('content', ''):
                                self.captured_context = msg['content']
                                print(f"📦 Captured context message at index {i}")
                                break
                    
                return result
            
            def send_message(self, inp):
                # Capture all messages before sending
                if hasattr(self, 'cur_messages'):
                    self.captured_messages = self.cur_messages.copy()
                
                # Don't actually send to avoid LLM calls
                print("🛑 Intercepted send_message to avoid LLM calls")
                return iter([])  # Return empty generator
        
        # Replace the coder's class
        original_class = coder.__class__
        coder.__class__ = ContextCapturingCoder
        
        # Initialize the new attributes
        coder.captured_context = None
        coder.captured_messages = []
        
        # Test the context injection
        result_generator = coder.run_stream(test_query)
        list(result_generator)  # Consume the generator
        
        # Display the captured context
        if coder.captured_context:
            print("\n🎯 CAPTURED CONTEXT SENT TO LLM:")
            print("=" * 60)
            
            # Save to file for detailed inspection
            with open("captured_llm_context.txt", "w", encoding="utf-8") as f:
                f.write(coder.captured_context)
            
            print(f"💾 Full context saved to: captured_llm_context.txt")
            print(f"📏 Context size: {len(coder.captured_context)} characters")
            
            # Show preview
            print("\n📄 CONTEXT PREVIEW (first 1000 chars):")
            print("-" * 50)
            print(coder.captured_context[:1000])
            if len(coder.captured_context) > 1000:
                print("...")
            print("-" * 50)
            
            # Analyze the context
            print("\n📊 CONTEXT ANALYSIS:")
            content = coder.captured_context
            print(f"   • Contains 'trading': {'trading' in content.lower()}")
            print(f"   • Contains 'position': {'position' in content.lower()}")
            print(f"   • Contains 'BacktestTradeLogger': {'BacktestTradeLogger' in content}")
            print(f"   • Contains 'Position' class: {'class Position' in content}")
            print(f"   • Contains 'aider': {'aider' in content.lower()}")
            print(f"   • Contains file paths: {content.count('file:')}")
            
        else:
            print("❌ No context was captured - context injection may have failed")
        
        # Display all messages
        if coder.captured_messages:
            print(f"\n📨 ALL MESSAGES CAPTURED ({len(coder.captured_messages)} total):")
            for i, msg in enumerate(coder.captured_messages):
                role = msg.get('role', 'unknown')
                content_preview = msg.get('content', '')[:100] + "..." if len(msg.get('content', '')) > 100 else msg.get('content', '')
                print(f"   {i+1}. {role}: {content_preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during context capture: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_path_detection():
    """Test the path detection logic specifically."""
    
    print("\n🔍 TESTING PATH DETECTION LOGIC")
    print("=" * 60)
    
    try:
        from aider.coders import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        # Test with external project
        external_project = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        if not os.path.exists(external_project):
            external_project = "."
        
        repo = GitRepo(io, [], external_project)
        
        coder = Coder.create(
            main_model=model,
            edit_format="informative",
            io=io,
            repo=repo,
            fnames=[],
            read_only_fnames=[],
            map_tokens=8192
        )
        
        print(f"📁 Current working directory: {os.getcwd()}")
        print(f"📁 Repo root: {repo.root if repo else 'None'}")
        print(f"📁 Coder abs_fnames: {list(coder.abs_fnames) if coder.abs_fnames else 'Empty'}")
        print(f"📁 Coder abs_read_only_fnames: {list(coder.abs_read_only_fnames) if coder.abs_read_only_fnames else 'Empty'}")
        
        # Test the path detection method
        detected_path = coder._get_project_path_for_context()
        print(f"📁 Detected project path: {detected_path}")
        
        # Check if it's correct
        expected_path = external_project
        if detected_path == expected_path:
            print("✅ Path detection is correct!")
        else:
            print(f"❌ Path detection is wrong!")
            print(f"   Expected: {expected_path}")
            print(f"   Got: {detected_path}")
        
        return detected_path == expected_path
        
    except Exception as e:
        print(f"❌ Error during path detection test: {e}")
        return False

if __name__ == "__main__":
    print("🔍 DEBUGGING NEW DIRECT IR CONTEXT FLOW")
    print("Investigating path detection and context injection\n")
    
    # Test path detection first
    path_correct = analyze_path_detection()
    
    # Test context capture
    context_captured = capture_context_injection()
    
    print(f"\n📋 SUMMARY:")
    print(f"   Path Detection: {'✅ CORRECT' if path_correct else '❌ WRONG'}")
    print(f"   Context Capture: {'✅ SUCCESS' if context_captured else '❌ FAILED'}")
    
    if path_correct and context_captured:
        print("\n🎉 Both tests passed! Check 'captured_llm_context.txt' for the full context.")
        print("This context should contain trading system information, not aider information.")
    else:
        print("\n❌ Issues found. The path detection or context injection needs fixing.")
    
    print(f"\n📁 Files created:")
    print(f"   • captured_llm_context.txt - Full context sent to LLM")
    print(f"   • Use this to verify the context contains trading system info")
