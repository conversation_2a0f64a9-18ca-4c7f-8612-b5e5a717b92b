#!/usr/bin/env python3
"""
Debug script to investigate the MAP_REQUEST issue.
This script will test the Smart Map Request system to see why it's generating
complete repository maps instead of focused maps.
"""

import os
import sys
import json

# Add the aider-main directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_map_request_system():
    """Test the MAP_REQUEST system to identify the issue."""

    print("🔍 Debugging MAP_REQUEST System Issue")
    print("=" * 60)

    try:
        # Import required modules
        from aider.smart_map_request_handler import SmartMapRequestHandler
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput

        print("✅ Successfully imported required modules")

        # Create components
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Get the correct root directory (current working directory)
        root_dir = os.getcwd()
        print(f"   Using root directory: {root_dir}")

        # Create RepoMap instance
        repo_map = RepoMap(
            map_tokens=8192,
            root=root_dir,
            main_model=model,
            io=io,
            verbose=True
        )

        print("✅ Created RepoMap instance")

        # Create SmartMapRequestHandler
        handler = SmartMapRequestHandler(
            repo_map=repo_map,
            root_dir=root_dir,
            io=io
        )

        print("✅ Created SmartMapRequestHandler")

        # Test with a simple MAP_REQUEST
        test_request = {
            "keywords": ["backtest", "trading", "strategy"],
            "type": "implementation",
            "scope": "all",
            "max_results": 5
        }

        print(f"\n🧪 Testing MAP_REQUEST:")
        print(f"   Request: {json.dumps(test_request, indent=2)}")

        # Process the request
        print("\n📋 Processing MAP_REQUEST...")
        focused_map = handler.handle_map_request(test_request)

        print(f"\n📊 Results:")
        print(f"   Map length: {len(focused_map)} characters")
        print(f"   Map words: {len(focused_map.split())} words")

        # Check if it's a focused map or complete map
        if "backtest" in focused_map.lower():
            print("✅ Map contains backtest-related content")
        else:
            print("❌ Map does not contain backtest-related content")

        # Check for signs of complete repository map
        if len(focused_map) > 50000:  # Very large maps are likely complete
            print("⚠️  Map is very large - might be complete repository map")
        else:
            print("✅ Map size suggests it's focused")

        # Count number of files mentioned
        file_count = focused_map.count('.py:')
        print(f"   Files mentioned: {file_count}")

        if file_count > 50:
            print("⚠️  Too many files - likely complete repository map")
        else:
            print("✅ Reasonable number of files for focused map")

        # Save the result for inspection
        with open("debug_map_output.txt", "w", encoding="utf-8") as f:
            f.write(focused_map)
        print(f"\n💾 Map saved to debug_map_output.txt for inspection")

        # Test the search functionality specifically
        print(f"\n🔍 Testing search functionality...")

        # Test file search
        all_files = handler._get_repository_files()
        print(f"   Total repository files: {len(all_files)}")

        # Test keyword expansion
        expanded_keywords = handler._expand_keywords(test_request["keywords"])
        print(f"   Original keywords: {test_request['keywords']}")
        print(f"   Expanded keywords: {expanded_keywords}")

        # Test hierarchical search
        relevant_files = handler._smart_hierarchical_search(
            test_request["keywords"],
            test_request["type"],
            test_request["scope"],
            test_request["max_results"]
        )

        print(f"   Relevant files found: {len(relevant_files)}")

        if relevant_files:
            print("   Top relevant files:")
            for i, file_info in enumerate(relevant_files[:3], 1):
                print(f"      {i}. {file_info['file']} (score: {file_info['relevance_score']:.1f})")
        else:
            print("   ❌ No relevant files found - this is the problem!")

        return True

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Debug MAP_REQUEST Issue")
    print("This script tests the Smart Map Request system to identify why")
    print("it's generating complete repository maps instead of focused maps.")
    print()

    success = test_map_request_system()

    if success:
        print("\n🎉 Testing completed successfully!")
        print("Check the output above and debug_map_output.txt for details.")
    else:
        print("\n💥 Testing failed!")
        print("Check the error messages above for details.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
