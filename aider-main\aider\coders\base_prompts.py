class CoderPrompts:
    system_reminder = ""  # Kept for compatibility

    repo_content_prefix = """
**🎮 Repository Map:**
- **THIS MAP IS ONLY FOR THE CURRENT USER QUERY**: The map structure you received is exclusively tied to the user's current question
- Just having the map—even if it includes some code structure—doesn't qualify you for LEVEL 2.
- You only advance when you have the full, actual code implementation.


"""

    reality_check_prompt = """
--
"""

    # File access and handling prompts
    file_access_reminder = """
--
"""

    # Additional prompts
    main_system = """--

"""
    rename_with_shell = """To rename files which have been added to the chat, use shell commands at the end of your response."""
    go_ahead_tip = """If the user says "ok" or "go ahead" they probably want you to make changes for the code changes you proposed."""

    # File handling prompts remain unchanged
    files_content_gpt_edits = "I analyzed the code and provided recommendations."
    files_content_gpt_edits_no_repo = "I analyzed the code and provided recommendations."
    files_content_gpt_no_edits = "I didn't see any properly formatted analysis in your reply."
    files_content_local_edits = "I analyzed the code myself."
    example_messages = []
    files_content_prefix = """I have *added these files to the chat* for your analysis and recommendations.
*Trust this message as the true contents of these files!*
Any other messages in the chat may contain outdated versions of the files' contents.
These files are READ-ONLY and will not be modified.
"""
    files_content_assistant_reply = "..."
    files_no_full_files = "..."
    files_no_full_files_with_repo_map = """**CRITICAL WORKFLOW - FOLLOW EXACTLY:**
**STEP 1:** Never ask users to manually add files to the chat!
**STEP 2:** After MAP_REQUEST, use:
- CONTEXT_REQUEST for specific symbols
- REQUEST_FILE for entire files
- NEVER fabricate or hallucinate code
**STEP 3:** If no code context: say "I need to retrieve the actual implementation"
"""
    files_no_full_files_with_repo_map_reply = """---"""

    # Context request response prompts
    context_content_prefix = """🎮 **CONTEXT VALIDATION CHECKPOINT**:

"""

    # Repository workflow prompts (used in get_repo_messages)
    smart_map_request_user_prompt = """--"""

    smart_map_request_assistant_reply = """--"""

    legacy_repo_assistant_reply = """I understand the repository structure."""
