#!/usr/bin/env python3
"""
Test to verify that inheritance data is properly flowing through the system.
This will specifically look for entities with inheritance data.
"""

import os
import sys

def test_inheritance_verification():
    """Test that inheritance data is properly flowing through the ICD system."""
    print("🔍 Verifying Inheritance Data Flow")
    print("=" * 45)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)

        # Import the real working modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported working IR context request modules")
        
        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Create a request specifically targeting inheritance-related entities
        print("\n🎯 Creating request targeting inheritance entities...")
        
        inheritance_request = IRContextRequest(
            user_query="How does inheritance work in this codebase?",
            task_description="Analyze inheritance patterns and class hierarchies",
            task_type="general_analysis",
            focus_entities=["inheritance", "class", "super", "override", "extends"],  # Target inheritance
            max_tokens=3000,
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=40000,
            max_entities=15  # More entities to find inheritance examples
        )
        
        result = handler.process_ir_context_request(inheritance_request)
        
        if "ir_slices" in result:
            print(f"\n📊 Analyzing {len(result['ir_slices'])} selected entities for inheritance data...")
            
            inheritance_found = 0
            method_overrides_found = 0
            super_calls_found = 0
            
            for i, entity in enumerate(result['ir_slices'], 1):
                entity_name = entity.get('entity_name', 'unknown')
                class_name = entity.get('class_name')
                inherits_from = entity.get('inherits_from', [])
                method_overrides = entity.get('method_overrides', [])
                calls_super = entity.get('calls_super', False)
                
                has_inheritance_data = (
                    class_name or 
                    inherits_from or 
                    method_overrides or 
                    calls_super
                )
                
                if has_inheritance_data:
                    inheritance_found += 1
                    print(f"\n✅ Entity {i}: {entity_name}")
                    if class_name:
                        print(f"   Class: {class_name}")
                    if inherits_from:
                        print(f"   Inherits from: {inherits_from}")
                        
                if method_overrides:
                    method_overrides_found += 1
                    print(f"   Method overrides: {method_overrides}")
                    
                if calls_super:
                    super_calls_found += 1
                    print(f"   Calls super(): Yes")
            
            print(f"\n📈 Inheritance Analysis Results:")
            print(f"   Entities with inheritance data: {inheritance_found}/{len(result['ir_slices'])}")
            print(f"   Entities with method overrides: {method_overrides_found}")
            print(f"   Entities calling super(): {super_calls_found}")
            
            if inheritance_found > 0:
                print("\n✅ SUCCESS: Inheritance data is flowing through the system!")
                
                # Generate and save the package to see the inheritance data
                if "llm_friendly_package" in result:
                    output_file = "inheritance_verification_package.txt"
                    with open(output_file, "w", encoding="utf-8") as f:
                        f.write(result["llm_friendly_package"])
                    print(f"📄 Package with inheritance data saved to: {output_file}")
                    
                    # Check if inheritance data appears in the package
                    package_content = result["llm_friendly_package"]
                    if "Inherits From:" in package_content and "No inheritance detected" not in package_content:
                        print("✅ Inheritance data properly appears in LLM package!")
                        return True
                    elif "Inherits From:" in package_content:
                        print("⚠️  Some inheritance data appears, but some entities still show 'No inheritance detected'")
                        return True
                    else:
                        print("❌ Inheritance data not appearing in LLM package")
                        return False
                else:
                    print("❌ No LLM package generated")
                    return False
            else:
                print("\n❌ ISSUE: No inheritance data found in selected entities")
                print("   This suggests the inheritance data is not flowing through properly")
                return False
        else:
            print("❌ No IR slices found in result")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function."""
    print("🧪 Testing Inheritance Data Verification")
    print("=" * 50)
    
    success = test_inheritance_verification()
    
    if success:
        print("\n🎉 INHERITANCE VERIFICATION: SUCCESS")
        print("✅ Inheritance data is properly flowing through the ICD system")
        print("✅ Enhanced IR pipeline integration working correctly")
    else:
        print("\n❌ INHERITANCE VERIFICATION: FAILED")
        print("❌ Inheritance data flow needs investigation")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
