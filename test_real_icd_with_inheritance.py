#!/usr/bin/env python3
"""
Generate a complete LLM-friendly package using the REAL ICD system with inheritance data.

This uses the actual Intelligent Code Discovery system to extract real source code
for the KEY IMPLEMENTATIONS section, combined with enhanced inheritance analysis.
"""

import json
import sys
from pathlib import Path

# Import the enhanced IR generation
from mid_level_ir_with_inheritance import run_enhanced_ir_pipeline

# Import the real ICD components
try:
    from aider_integration_service import AiderIntegrationService
    from surgical_file_extractor import SurgicalFileExtractor
    ICD_AVAILABLE = True
except ImportError:
    ICD_AVAILABLE = False
    print("⚠️ Real ICD system not available")


def generate_real_icd_inheritance_package():
    """Generate LLM package using real ICD system with inheritance data."""
    print("🎯 Generating LLM Package with REAL ICD + Inheritance Data")
    print("=" * 65)
    
    if not ICD_AVAILABLE:
        print("❌ Real ICD system not available - cannot extract real source code")
        return False
    
    # Step 1: Generate IR with inheritance data
    print("📊 Step 1: Generating enhanced IR...")
    ir_data = run_enhanced_ir_pipeline(".")
    
    # Step 2: Initialize real ICD system
    print("🔧 Step 2: Initializing real ICD system...")
    aider_service = AiderIntegrationService(".")
    file_extractor = SurgicalFileExtractor(aider_service)
    
    # Step 3: Find context selection entities with inheritance
    print("🔍 Step 3: Finding context selection entities...")
    
    context_entities = []
    implementation_entities = []
    
    for module in ir_data['modules']:
        for entity in module['entities']:
            entity_name = entity['name'].lower()
            
            # Look for context selection related entities
            if any(keyword in entity_name for keyword in [
                'context', 'selection', 'process_context', 'parse_context', 
                'request', 'handler', 'coder'
            ]):
                # Prioritize entities with inheritance data
                has_inheritance = (
                    entity.get('class_name') or 
                    entity.get('inherits_from') or 
                    entity.get('method_overrides') or 
                    entity.get('calls_super') or
                    entity.get('overridden_by')
                )
                
                entity_info = {
                    'entity': entity,
                    'module': module,
                    'has_inheritance': has_inheritance
                }
                
                if has_inheritance:
                    context_entities.insert(0, entity_info)
                else:
                    context_entities.append(entity_info)
                
                # Collect for implementations
                if entity['type'] in ['function', 'async_function']:
                    implementation_entities.append(entity_info)
    
    # Limit to top entities
    context_entities = context_entities[:8]
    implementation_entities = implementation_entities[:8]
    
    print(f"   Found {len(context_entities)} context entities")
    print(f"   Found {len(implementation_entities)} implementation entities")
    
    # Step 4: Extract real source code using ICD
    print("📦 Step 4: Extracting real source code...")
    
    real_implementations = []
    for entity_info in implementation_entities:
        entity = entity_info['entity']
        module = entity_info['module']
        
        try:
            # Extract real source code using ICD
            source_code = file_extractor.extract_symbol_content(
                entity['name'], 
                module['file'], 
                "."
            )
            
            if source_code:
                real_implementations.append({
                    'entity': entity,
                    'module': module,
                    'source_code': source_code,
                    'has_inheritance': entity_info['has_inheritance']
                })
                print(f"   ✅ Extracted: {entity['name']}")
            else:
                print(f"   ⚠️ Could not extract: {entity['name']}")
                
        except Exception as e:
            print(f"   ❌ Error extracting {entity['name']}: {e}")
    
    # Step 5: Generate complete package with real ICD data
    print("🏗️ Step 5: Generating complete package...")
    
    package_content = generate_real_icd_package_content(context_entities, real_implementations)
    
    # Step 6: Save the package
    output_file = "real_icd_inheritance_package_comparison.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(package_content)
    
    print(f"✅ Real ICD package saved to: {output_file}")
    print(f"📁 Package size: {len(package_content):,} characters")
    print(f"🔧 Real implementations: {len(real_implementations)}")
    
    return True


def generate_real_icd_package_content(context_entities, real_implementations):
    """Generate package content with real ICD source code and inheritance data."""
    
    content = """# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

"""
    
    # Generate critical entities section with inheritance data
    for i, entity_info in enumerate(context_entities, 1):
        entity = entity_info['entity']
        module = entity_info['module']
        
        entity_name = entity['name']
        entity_type = entity['type']
        
        # Determine if this is likely a method
        is_method = entity.get('class_name') is not None
        
        if is_method:
            class_name = entity['class_name']
            content += f"### {i}. {entity_name} (method)\n"
            content += f"- File: {module['file']}\n"
            content += f"- Belongs to Class: `{class_name}`\n"
            
            # Add inheritance information from IR
            # Find the class entity to get inheritance info
            class_entity = None
            for class_ent in module['entities']:
                if class_ent['type'] == 'class' and class_ent['name'] == class_name:
                    class_entity = class_ent
                    break
            
            if class_entity and class_entity.get('inherits_from'):
                inherits_from = class_entity['inherits_from']
                content += f"- Inherits From: {inherits_from}\n"
            else:
                content += f"- Inherits From: No inheritance detected\n"
            
            content += f"- Criticality: {entity['criticality']} | Risk: {entity['change_risk']}\n"
            
            # Add class context section
            content += f"\n#### 🔁 Class Context\n"
            content += f"- Part of `{class_name}` class\n"
            
            if class_entity and class_entity.get('inherits_from'):
                inherits_from = class_entity['inherits_from']
                content += f"- Inheritance chain: {' → '.join(inherits_from)}\n"
            
            # Add override information
            method_overrides = entity.get('method_overrides', [])
            if method_overrides:
                content += f"- Overrides: {', '.join(method_overrides)}\n"
            
            overridden_by = entity.get('overridden_by', [])
            if overridden_by:
                content += f"- Overridden by: {', '.join(overridden_by)}\n"
            
            # Add method details section
            content += f"\n#### 🧩 Method Details\n"
            
            # Add super() call information
            calls_super = entity.get('calls_super', False)
            content += f"- Calls super(): {'Yes' if calls_super else 'No'}\n"
            
        else:
            # For functions or other entities
            content += f"### {i}. {entity_name} ({entity_type})\n"
            content += f"- File: {module['file']}\n"
            content += f"- Criticality: {entity['criticality']} | Risk: {entity['change_risk']}\n"
        
        # Add calls and usage information
        calls = entity.get('calls', [])
        if calls:
            calls_display = calls[:3] + ["..."] if len(calls) > 3 else calls
            content += f"- **Calls**: {calls_display} (total: {len(calls)})\n"
        
        used_by = entity.get('used_by', [])
        if used_by:
            used_by_display = used_by[:3] + ["..."] if len(used_by) > 3 else used_by
            content += f"- **Used by**: {used_by_display} (total: {len(used_by)})\n"
        
        # Add side effects
        side_effects = entity.get('side_effects', [])
        if side_effects and side_effects != ['none']:
            content += f"- **Side Effects**: {', '.join(side_effects[:3])}\n"
        
        content += "\n"
    
    # Add KEY IMPLEMENTATIONS section with REAL source code
    content += f"""## KEY IMPLEMENTATIONS ({len(real_implementations)} functions)

"""
    
    for i, impl in enumerate(real_implementations, 1):
        entity = impl['entity']
        source_code = impl['source_code']
        
        entity_name = entity['name']
        
        content += f"### {i}. {entity_name}\n"
        content += f"```python\n"
        
        # Use REAL source code from ICD system
        if len(source_code) > 800:  # Limit each function to ~800 chars
            lines = source_code.split('\n')
            truncated_lines = []
            char_count = 0
            for line in lines:
                if char_count + len(line) > 800:
                    truncated_lines.append("    # ... (implementation continues)")
                    break
                truncated_lines.append(line)
                char_count += len(line)
            source_code = '\n'.join(truncated_lines)
        
        content += f"{source_code}\n"
        content += f"```\n\n"
    
    # Add analysis instructions
    content += """## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes
5. **Analyze inheritance patterns** - understand OOP relationships and method overrides

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context with enhanced inheritance data and real source code implementations.
"""
    
    return content


def main():
    """Main function."""
    try:
        success = generate_real_icd_inheritance_package()
        
        if success:
            print("\n🎉 REAL ICD + INHERITANCE PACKAGE: SUCCESS")
            print("✅ Used actual ICD system for source code extraction")
            print("✅ Combined with enhanced inheritance analysis")
            print("✅ Generated complete package with real implementations")
        else:
            print("\n❌ REAL ICD + INHERITANCE PACKAGE: FAILED")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
