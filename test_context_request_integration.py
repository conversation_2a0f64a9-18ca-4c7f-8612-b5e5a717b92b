#!/usr/bin/env python

import os
import sys
import time
import re
from pathlib import Path

# Add the aider-main directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "aider-main"))

try:
    from aider.context_request import AiderContextRequestIntegration, ContextRequestHandler, ContextRequest, SymbolRequest
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    sys.exit(1)


class MockIO:
    """Mock IO class for testing."""

    def __init__(self):
        self.outputs = []
        self.warnings = []
        self.errors = []

    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

    def tool_warning(self, message, **kwargs):
        self.warnings.append(message)
        print(f"[WARNING] {message}")

    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


class MockCoder:
    """Mock Coder class for testing."""

    def __init__(self):
        self.io = MockIO()
        self.root = os.getcwd()
        self.current_query_context_requests = 0
        self.repo_map = None

        # Initialize context request integration
        try:
            self.context_request_integration = AiderContextRequestIntegration(self.root)
        except Exception as e:
            self.io.tool_error(f"Failed to initialize context request integration: {e}")

    def send(self, messages, functions=None):
        """Mock send method."""
        # Simulate a delay
        time.sleep(0.5)

        # Return a mock response
        yield "This is a mock response from the LLM."

    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")

        # Get the repository overview
        repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
"""

        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )

        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)

        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt


def main():
    print("\n=== Testing CONTEXT_REQUEST Integration ===")

    # Create a mock coder
    coder = MockCoder()

    # Simulate a user message
    user_message = "How does the surgical file extractor work?"
    print(f"\nUser: {user_message}")

    # Simulate an LLM response with a context request
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: {
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    print(f"\nLLM: {llm_response}")

    # Process the context request
    print("\nProcessing context request...")
    start_time = time.time()
    cleaned_content, context_prompt = coder.process_context_requests(llm_response, user_message)

    # Check if a context request was detected
    if context_prompt:
        print("✅ Context request detected and processed!")
        print("\nAugmented prompt:")
        print(context_prompt[:200] + "..." if len(context_prompt) > 200 else context_prompt)

        # Create a messages list for testing
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": user_message}
        ]

        # Simulate sending the augmented prompt to the LLM
        print("\nSending augmented prompt to LLM...")

        # Use the send method to test if it hangs
        response_chunks = []
        for chunk in coder.send(messages):
            response_chunks.append(chunk)
            print(f"Received chunk: {chunk}")

        end_time = time.time()

        # Check if the process completed in a reasonable time
        if end_time - start_time < 5:  # Less than 5 seconds
            print("✅ Process completed successfully without hanging!")
        else:
            print("❌ Process took too long, might be hanging.")
    else:
        print("❌ Context request was not detected or processed")

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
