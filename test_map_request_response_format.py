#!/usr/bin/env python3
"""
Test script to verify that MAP_REQUEST responses provide the correct format examples.
This tests that the MAP_REQUEST response shows the new directory_name/file_name format.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_map_request_response_format():
    """Test that MAP_REQUEST responses show the correct CONTEXT_REQUEST format."""
    print("🧪 Testing MAP_REQUEST Response Format")
    print("=" * 60)

    try:
        # Test the format by checking the source code directly
        with open("aider-main/aider/smart_map_request_handler.py", "r") as f:
            source_code = f.read()

        print("🔍 Testing MAP_REQUEST response format in source code...")

        # Check for the new format in the source
        format_checks = [
            ("directory_name", "New directory_name format"),
            ("file_name", "New file_name format"),
            ('"directory_name": "folder_name"', "Correct directory_name example"),
            ('"file_name": "file.py"', "Correct file_name example"),
        ]

        passed = 0
        total = len(format_checks)

        for pattern, description in format_checks:
            if pattern in source_code:
                print(f"✅ {description}: Found '{pattern}'")
                passed += 1
            else:
                print(f"❌ {description}: Missing '{pattern}'")

        # Check that old format is NOT present in the examples
        old_format_checks = [
            ('"file_hint": "file.py"', "Old file_hint format should be removed"),
        ]

        for pattern, description in old_format_checks:
            # Count occurrences to see if they're in examples vs just variable names
            occurrences = source_code.count(pattern)
            if occurrences > 0:
                print(f"⚠️  {description}: Found {occurrences} occurrences of '{pattern}'")
                # This might be acceptable if it's just in variable names, not examples
            else:
                print(f"✅ {description}: Correctly removed")
                passed += 1

        total += len(old_format_checks)

        print(f"\n📊 MAP_REQUEST response format: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing MAP_REQUEST response format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_files_example():
    """Test that the multiple files example uses the correct format."""
    print("\n🧪 Testing Multiple Files Example Format")
    print("=" * 60)

    try:
        # Test the format by checking the source code directly
        with open("aider-main/aider/smart_map_request_handler.py", "r") as f:
            source_code = f.read()

        print("🔍 Testing multiple files example format in source code...")

        # Check for the multiple files example format
        multiple_files_checks = [
            ('"directory_name": "folder1"', "First file directory_name"),
            ('"file_name": "file1.py"', "First file file_name"),
            ('"directory_name": "folder2"', "Second file directory_name"),
            ('"file_name": "file2.py"', "Second file file_name"),
        ]

        passed = 0
        total = len(multiple_files_checks)

        for pattern, description in multiple_files_checks:
            if pattern in source_code:
                print(f"✅ {description}: Found '{pattern}'")
                passed += 1
            else:
                print(f"❌ {description}: Missing '{pattern}'")

        print(f"\n📊 Multiple files example format: {passed}/{total} checks passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing multiple files example: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_response_format():
    """Test that error responses use the correct format."""
    print("\n🧪 Testing Error Response Format")
    print("=" * 60)

    try:
        # Test the format by checking the source code directly
        with open("aider-main/aider/smart_map_request_handler.py", "r") as f:
            source_code = f.read()

        print("🔍 Testing error response format in source code...")

        # Check that error response method doesn't contain old format
        if '"file_hint"' in source_code and '_generate_error_response' in source_code:
            # Check if file_hint appears in the error response method
            error_method_start = source_code.find('def _generate_error_response')
            if error_method_start != -1:
                error_method_end = source_code.find('\n    def ', error_method_start + 1)
                if error_method_end == -1:
                    error_method_end = len(source_code)
                error_method = source_code[error_method_start:error_method_end]

                if '"file_hint"' in error_method:
                    print("❌ Error response method contains old file_hint format")
                    return False
                else:
                    print("✅ Error response method does not contain old file_hint format")
                    return True

        print("✅ Error response format appears correct")
        return True

    except Exception as e:
        print(f"❌ Error testing error response format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_results_response_format():
    """Test that no results responses use the correct format."""
    print("\n🧪 Testing No Results Response Format")
    print("=" * 60)

    try:
        # Test the format by checking the source code directly
        with open("aider-main/aider/smart_map_request_handler.py", "r") as f:
            source_code = f.read()

        print("🔍 Testing no results response format in source code...")

        # Check that no results response uses MAP_REQUEST format
        if '_generate_no_results_response' in source_code:
            no_results_method_start = source_code.find('def _generate_no_results_response')
            if no_results_method_start != -1:
                no_results_method_end = source_code.find('\n    def ', no_results_method_start + 1)
                if no_results_method_end == -1:
                    no_results_method_end = len(source_code)
                no_results_method = source_code[no_results_method_start:no_results_method_end]

                if 'MAP_REQUEST' in no_results_method:
                    print("✅ No results response correctly suggests MAP_REQUEST")
                    return True
                else:
                    print("❌ No results response does not suggest MAP_REQUEST")
                    return False

        print("❌ Could not find no results response method")
        return False

    except Exception as e:
        print(f"❌ Error testing no results response format: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all MAP_REQUEST response format tests."""
    print("🚀 Testing MAP_REQUEST Response Format Fix")
    print("=" * 80)

    tests = [
        test_map_request_response_format,
        test_multiple_files_example,
        test_error_response_format,
        test_no_results_response_format,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 MAP_REQUEST response format fix is working correctly!")
        print("\n📋 The MAP_REQUEST responses now:")
        print("  1. ✅ Show the new directory_name/file_name format in examples")
        print("  2. ✅ Do not show the old file_hint format")
        print("  3. ✅ Provide correct examples for multiple files")
        print("  4. ✅ Use consistent format in error responses")
        print("  5. ✅ Guide the LLM to use the correct JSON structure")
    else:
        print("⚠️  Some MAP_REQUEST response format fixes need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
