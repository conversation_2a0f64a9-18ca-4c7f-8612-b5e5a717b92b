#!/usr/bin/env python

import os
import re
from typing import Dict, List, Optional, Any


class AiderTemplateRenderer:
    """
    Formats extracted context into a well-structured prompt for the LLM.
    """

    def __init__(self):
        """Initialize the template renderer."""
        pass

    def format_repository_overview(self, repo_overview: str) -> str:
        """
        Format the repository overview section.

        Args:
            repo_overview: The repository overview text

        Returns:
            Formatted repository overview section
        """
        # Check if the repo_overview is just a list of files (one per line)
        if repo_overview and not "│" in repo_overview:
            # Format it as a simple file list
            formatted_overview = "### REPOSITORY FILES\n"
            for line in repo_overview.strip().split("\n"):
                if line.strip():
                    formatted_overview += f"- {line.strip()}\n"

            # Add focused map guidance
            formatted_overview += "\n**📍 IMPORTANT**: This map contains only files relevant to the current query. To explore different parts of the repository or when users ask about other functionality, first execute MAP_REQUEST to discover relevant files, then CONTEXT_REQUEST to get the code details.\n"
            return formatted_overview + "\n---"
        else:
            # Use the standard format with focused map guidance
            return f"""### REPOSITORY OVERVIEW (Aider-Generated Slice)
{repo_overview}

**📍 IMPORTANT**: This map contains only files relevant to the current query. To explore different parts of the repository or when users ask about other functionality, first execute MAP_REQUEST to discover relevant files, then CONTEXT_REQUEST to get the code details.
---"""

    def format_conversation_history(self, conversation_history: List[Dict[str, str]]) -> str:
        """
        Format the conversation history section.

        Args:
            conversation_history: List of conversation messages

        Returns:
            Formatted conversation history section
        """
        if not conversation_history:
            return ""

        result = "PREVIOUS CONVERSATION HISTORY (FOR REFERENCE ONLY - DO NOT ANSWER THESE QUERIES):\n"
        result += "The queries below are from PREVIOUS turns and should be COMPLETELY IGNORED when formulating your response.\n"
        result += "They are provided ONLY for context and to help you understand the conversation flow.\n\n"

        for message in conversation_history:
            role = message.get("role", "")
            content = message.get("content", "")
            if role == "user":
                result += f"PREVIOUS User Query (IGNORE THIS): {content}\n\n"
            elif role == "assistant":
                result += f"PREVIOUS Assistant Response: {content}\n\n"

        result += "⚠️ END OF PREVIOUS CONVERSATION ⚠️\n"
        result += "IMPORTANT REMINDER: ONLY ANSWER THE CURRENT QUERY AT THE TOP OF THIS MESSAGE.\n"
        result += "DO NOT answer any questions from the previous conversation history above.\n"
        result += "---\n"
        return result

    def format_extracted_symbols(self, extracted_symbols: List[Dict[str, Any]]) -> str:
        """
        Format the extracted symbols section.

        Args:
            extracted_symbols: List of extracted symbols

        Returns:
            Formatted extracted symbols section
        """
        if not extracted_symbols:
            return ""

        result = "### REQUESTED SYMBOL DEFINITIONS (Surgically Extracted)\n\n"

        for symbol in extracted_symbols:
            file_path = symbol.get("file_path", "")
            symbol_name = symbol.get("symbol_name", "")
            content = symbol.get("content", "")
            essential_imports = symbol.get("essential_imports", "")
            containing_class = symbol.get("containing_class", "")

            result += f"--- Definition from: {file_path} ---\n"

            # Add imports section if available
            if essential_imports:
                result += "# File header & imports\n"
                result += f"{essential_imports}\n"

            # Add containing class if available
            if containing_class:
                result += f"class {containing_class}\n"

            # Add the symbol content
            result += f"{content}\n"
            result += "---\n\n"

        return result

    def format_dependency_snippets(self, dependency_snippets: List[Dict[str, Any]]) -> str:
        """
        Format the dependency snippets section.

        Args:
            dependency_snippets: List of dependency snippets

        Returns:
            Formatted dependency snippets section
        """
        result = "### KEY DEPENDENCY SNIPPETS (Surgically Extracted for Clarity)\n\n"

        if not dependency_snippets:
            result += "No direct dependencies were found for the requested symbols. The implementation appears to be self-contained.\n\n"
            result += "---\n\n"
            return result

        # Filter out test files and self-references
        filtered_snippets = []
        for snippet in dependency_snippets:
            file_path = snippet.get("file_path", "")
            # Skip test files and self-references
            if "test_" in file_path.lower() or file_path.endswith("_test.py"):
                continue
            filtered_snippets.append(snippet)

        if not filtered_snippets:
            result += "No functional dependencies were found for the requested symbols (only test references). The implementation appears to be self-contained.\n\n"
            result += "---\n\n"
            return result

        for i, snippet in enumerate(filtered_snippets):
            file_path = snippet.get("file_path", "")
            symbol_name = snippet.get("symbol_name", "")
            content = snippet.get("content", "")
            usage_type = snippet.get("usage_type", "unknown")

            result += f"--- Dependency {i+1}: `{symbol_name}` ({usage_type}) from `{file_path}` ---\n"
            result += f"{content}\n"
            result += "---\n\n"

        return result

    def format_not_found_symbols(self, not_found_symbols: List[Dict[str, Any]]) -> str:
        """
        Format the not found symbols section.

        Args:
            not_found_symbols: List of symbols that could not be found

        Returns:
            Formatted not found symbols section
        """
        if not not_found_symbols:
            return ""

        result = "### ⚠️ SYMBOLS NOT FOUND ⚠️\n\n"
        result += "The following symbols could not be found or extracted:\n\n"

        for symbol in not_found_symbols:
            symbol_name = symbol.get("symbol_name", "")
            reason = symbol.get("reason", "Unknown reason")
            result += f"- **{symbol_name}**: {reason}\n"

        result += "\n**SUGGESTIONS:**\n"
        result += "1. Check if the symbol name is spelled correctly\n"
        result += "2. Try using REQUEST_FILE to get the entire file and search manually\n"
        result += "3. {MAP_REQUEST: {\"keywords\": [\"alternative\", \"terms\"], \"type\": \"implementation\", \"scope\": \"all\", \"max_results\": 8}}\n"
        result += "4. The symbol might be in a different file than expected\n\n"
        result += "---\n\n"

        return result

    def format_instructions(self, original_query: str, reason_for_request: str) -> str:
        """
        Format the instructions section.

        Args:
            original_query: The original user query
            reason_for_request: The reason for the context request

        Returns:
            Formatted instructions section
        """
        return f"""⚠️ INSTRUCTIONS FOR THIS TURN ⚠️
You requested additional context for: "{reason_for_request}"
The surgically extracted implementations are provided above, along with surgical snippets of their key direct dependencies.

⚠️ FOCUS ON CURRENT QUERY ONLY ⚠️
1. ONLY answer the CURRENT USER QUERY highlighted at the top of this message: "{original_query}"
2. IGNORE any previous queries from conversation history - they are provided only for context
3. DO NOT answer questions from previous turns in the conversation
4. If you're unsure which query to answer, ALWAYS prioritize the one marked as "CURRENT USER QUERY"
5. ONLY address the specific function/symbol mentioned in the current query
6. DO NOT assume the user is asking about multiple functions unless explicitly stated
7. If the user previously asked about function X and now asks about function Y, ONLY answer about function Y
8. If the current query is a simple question like "what was the first msg i sent you?", ONLY answer that specific question

CRITICAL INSTRUCTIONS:
1. The code context above ALREADY CONTAINS the complete implementation you need to answer the previous query.
2. DO NOT overthink this - the surgical extraction system has ALREADY found the right file and extracted the code.
3. This approach works for ~90% of code-related queries, including questions about:
   - How code works
   - Implementation details
   - Function parameters
   - Return values
   - Error handling
   - Dependencies
   - Usage patterns
   - Code structure

ONLY IF ABSOLUTELY NECESSARY:
If critical information is clearly missing (undefined variables or methods), you may request additional context:
{{CONTEXT_REQUEST: {{"symbols_of_interest": [{{"type": "method_definition", "name": "EXACT_SYMBOL_NAME_FROM_MAP"}}], "reason_for_request": "Brief reason"}}}}
🚨 **CRITICAL**: Use ONLY the EXACT symbol names shown in your repository map - DO NOT make assumptions about class/function names.

REMEMBER:
1. The surgical extraction system has already done the work of finding and extracting the relevant code for you.
2. You do NOT need to figure out which file contains the code or request additional files.
3. ONLY answer the CURRENT QUERY at the top of this message.
4. DO NOT answer questions from previous conversation history.
5. If the current query is a simple question like "what was the first msg i sent you?", just answer that specific question directly."""

    def render_augmented_prompt(self,
                               original_query: str,
                               repo_overview: str,
                               extracted_context: Dict[str, Any],
                               conversation_history: Optional[List[Dict[str, str]]] = None) -> str:
        """
        Render the augmented prompt with the extracted context using clean, organized formatting.

        Args:
            original_query: The original user query
            repo_overview: The repository overview
            extracted_context: The extracted context
            conversation_history: Optional conversation history

        Returns:
            The augmented prompt
        """
        # Extract data from the context
        original_user_query_context = extracted_context.get("original_user_query_context", "")
        reason_for_request = extracted_context.get("reason_for_request", "")
        extracted_symbols = extracted_context.get("extracted_symbols", [])
        dependency_snippets = extracted_context.get("dependency_snippets", [])
        not_found_symbols = extracted_context.get("not_found_symbols", [])

        # Extract the function name from the query if possible
        function_name = ""

        # Pattern 1: "How does X work?"
        if "how does" in original_query.lower() and "work" in original_query.lower():
            match = re.search(r"how does\s+(?:the\s+)?([a-zA-Z0-9_]+(?:\.[a-zA-Z0-9_]+)?)\s+work", original_query.lower())
            if match:
                function_name = match.group(1)

        # Pattern 2: "What does X do?"
        elif "what does" in original_query.lower() and "do" in original_query.lower():
            match = re.search(r"what does\s+(?:the\s+)?([a-zA-Z0-9_]+(?:\.[a-zA-Z0-9_]+)?)\s+do", original_query.lower())
            if match:
                function_name = match.group(1)

        # Pattern 3: "Explain X function/method"
        elif "explain" in original_query.lower() and ("function" in original_query.lower() or "method" in original_query.lower()):
            match = re.search(r"explain\s+(?:the\s+)?([a-zA-Z0-9_]+(?:\.[a-zA-Z0-9_]+)?)\s+(?:function|method)", original_query.lower())
            if match:
                function_name = match.group(1)

        # Pattern 4: "Tell me about X"
        elif "tell me about" in original_query.lower():
            match = re.search(r"tell me about\s+(?:the\s+)?([a-zA-Z0-9_]+(?:\.[a-zA-Z0-9_]+)?)", original_query.lower())
            if match:
                function_name = match.group(1)

        # Extract from extracted symbols if we couldn't find it in the query
        if not function_name and extracted_symbols:
            # Use the first symbol name as a fallback
            function_name = extracted_symbols[0].get("symbol_name", "")

        # Use the new clean template format
        return self._render_clean_template(
            original_query=original_query,
            function_name=function_name,
            extracted_symbols=extracted_symbols,
            not_found_symbols=not_found_symbols,
            dependency_snippets=dependency_snippets,
            repo_overview=repo_overview,
            conversation_history=conversation_history
        )

    def _render_clean_template(self,
                              original_query: str,
                              function_name: str,
                              extracted_symbols: List[Dict[str, Any]],
                              not_found_symbols: List[Dict[str, Any]],
                              dependency_snippets: List[Dict[str, Any]],
                              repo_overview: str,
                              conversation_history: Optional[List[Dict[str, str]]] = None) -> str:
        """
        Render a clean, organized template based on the improved format.

        Args:
            original_query: The original user query
            function_name: The extracted function name
            extracted_symbols: Successfully extracted symbols
            not_found_symbols: Symbols that could not be found
            dependency_snippets: Dependency code snippets
            repo_overview: Repository overview
            conversation_history: Optional conversation history

        Returns:
            Clean, organized prompt
        """
        prompt = "# Code Context Response\n\n"

        # Add context validation checkpoint from base_prompts
        try:
            from aider.coders.base_prompts import CoderPrompts
            prompts = CoderPrompts()
            prompt += prompts.context_content_prefix
        except ImportError:
            # Fallback if import fails
            pass

        prompt += "---\n\n"

        # Handle different scenarios
        if extracted_symbols:
            # SUCCESS CASE: Show function implementation
            prompt += self._render_function_implementation(extracted_symbols)

            # Add dependencies if available
            if dependency_snippets:
                prompt += self._render_dependencies_section(dependency_snippets)

            # CRITICAL FIX: Do NOT show missing symbols note when we have successful extractions
            # If we found the main symbol, that's success - don't confuse the LLM with "not found" messages

        elif not_found_symbols:
            # Partial or complete failure case
            prompt += "## Context Request Results\n\n"
            prompt += "⚠️ **Some symbols could not be found:**\n\n"

            for symbol in not_found_symbols:
                symbol_name = symbol.get("symbol_name", "")
                reason = symbol.get("reason", "Unknown reason")
                prompt += f"- `{symbol_name}`: {reason}\n"

            prompt += "\n**Suggestions:**\n"
            prompt += "1. Check if the symbol name is spelled correctly\n"
            prompt += "2. The symbol might be in a different file than expected\n"
            prompt += "3. Try using a broader search or different keywords\n\n"

            # Include repository overview for broader context when symbols not found
            if repo_overview:
                prompt += "## Repository Context\n\n"
                prompt += "Since the specific symbols weren't found, here's the broader repository context:\n\n"
                prompt += self.format_repository_overview(repo_overview)
                prompt += "\n"

        return prompt

    def _render_function_implementation(self, extracted_symbols: List[Dict[str, Any]]) -> str:
        """Render the function implementation section in clean format with NO HARDCODED content."""
        if not extracted_symbols:
            return ""

        # Get the first symbol (main function) - DYNAMIC based on actual extraction
        symbol = extracted_symbols[0]
        file_path = symbol.get("file_path", "")
        symbol_name = symbol.get("symbol_name", "")
        content = symbol.get("content", "")
        essential_imports = symbol.get("essential_imports", "")
        containing_class = symbol.get("containing_class", "")

        prompt = "## Function Implementation\n\n"
        prompt += f"**File:** `{file_path}`\n"

        if containing_class:
            prompt += f"**Class:** `{containing_class}`\n"

        prompt += "\n"

        # Add imports section if available - DYNAMIC based on actual imports
        if essential_imports:
            prompt += "### File Structure and Imports\n"
            prompt += "```python\n"
            prompt += essential_imports + "\n"
            prompt += "```\n\n"

        # Add method implementation - DYNAMIC content
        prompt += "### Method Implementation\n"
        prompt += "```python\n"
        prompt += content + "\n"
        prompt += "```\n\n"

        # Add class context if available - DYNAMIC based on actual class
        if containing_class:
            prompt += "### Class Context\n"
            prompt += f"This method belongs to the `{containing_class}` class and uses the following instance variables:\n"

            # Extract instance variables from the content - DYNAMIC extraction
            instance_vars = self._extract_instance_variables(content)
            if instance_vars:
                for var in instance_vars:
                    prompt += f"- `{var}`: {self._get_variable_description(var)}\n"
            else:
                prompt += "- Instance variables are used but not explicitly documented in this method\n"
            prompt += "\n"

        # Add dependencies note - DYNAMIC based on actual implementation
        prompt += "### Dependencies Note\n"
        prompt += "The implementation is self-contained within the provided code. No additional method definitions are needed to understand how this function works.\n\n"

        return prompt

    def _extract_instance_variables(self, content: str) -> List[str]:
        """Extract instance variables (self.xxx) from method content - DYNAMIC extraction."""
        import re

        # Find all self.variable_name patterns
        pattern = r'self\.([a-zA-Z_][a-zA-Z0-9_]*)'
        matches = re.findall(pattern, content)

        # Remove duplicates and sort
        unique_vars = sorted(list(set(matches)))

        return unique_vars

    def _get_variable_description(self, var_name: str) -> str:
        """Get a description for common variable names - DYNAMIC based on variable name."""
        descriptions = {
            'session': 'Database session for data operations',
            'order_executor': 'Service to execute trading orders',
            'telegram_manager': 'Service for sending notifications',
            'candle_service': 'Service to fetch price/candle data',
            'technical_analysis_service': 'Service for technical indicators',
            'target_service': 'Service for price targets',
            'manual_position_handler': 'Handler for manual position changes',
            'close_strategies': 'Dictionary mapping symbols to their strategies',
            'error_handler': 'Service for error handling and notifications',
            'cash_manager': 'Service for account management',
            'io': 'Input/output service',
            'repo': 'Repository/git service',
            'model': 'AI model service',
            'root': 'Root directory path'
        }

        return descriptions.get(var_name, f"Instance variable used by the class")

    def _render_dependencies_section(self, dependency_snippets: List[Dict[str, Any]]) -> str:
        """Render the dependencies section."""
        if not dependency_snippets:
            return ""

        prompt = "### Dependencies\n\n"
        prompt += "This function uses the following dependencies:\n\n"

        for i, snippet in enumerate(dependency_snippets):
            file_path = snippet.get("file_path", "")
            symbol_name = snippet.get("symbol_name", "")
            usage_type = snippet.get("usage_type", "unknown")

            prompt += f"**{symbol_name}** ({usage_type}) from `{file_path}`\n\n"

        return prompt

    def _render_missing_symbols_note(self, not_found_symbols: List[Dict[str, Any]]) -> str:
        """Render a note about missing symbols."""
        if not not_found_symbols:
            return ""

        prompt = "### Note on Missing Symbols\n\n"
        prompt += "⚠️ Some requested symbols were not found:\n\n"

        for symbol in not_found_symbols:
            symbol_name = symbol.get("symbol_name", "")
            reason = symbol.get("reason", "Unknown reason")
            prompt += f"- `{symbol_name}`: {reason}\n"

        prompt += "\nThe implementation above should still provide sufficient context to answer your question.\n\n"

        return prompt

    def _extract_instance_variables(self, content: str) -> List[str]:
        """Extract instance variables (self.xxx) from method content."""
        import re

        # Find all self.variable_name patterns
        pattern = r'self\.([a-zA-Z_][a-zA-Z0-9_]*)'
        matches = re.findall(pattern, content)

        # Remove duplicates and sort
        unique_vars = sorted(list(set(matches)))

        return unique_vars

    def _get_variable_description(self, var_name: str) -> str:
        """Get a description for common variable names."""
        descriptions = {
            'session': 'Database session for position data',
            'order_executor': 'Service to execute position closures',
            'telegram_manager': 'Service for sending notifications',
            'candle_service': 'Service to fetch price/candle data',
            'technical_analysis_service': 'Service for technical indicators',
            'target_service': 'Service for price targets',
            'manual_position_handler': 'Handler for manual position changes',
            'close_strategies': 'Dictionary mapping symbols to their close strategies',
            'error_handler': 'Service for error handling and notifications',
            'cash_manager': 'Service for cash/account management',
            'io': 'Input/output service',
            'repo': 'Repository/git service',
            'model': 'AI model service',
            'root': 'Root directory path'
        }

        return descriptions.get(var_name, f"Instance variable used by the class")
