#!/usr/bin/env python3
"""
Test script to verify the game-based codebase exploration system
"""

import sys
import os

def test_game_system_prompts():
    """Test that the game system prompts are correctly set up"""
    print("🎮 Testing Game-Based Codebase Exploration System")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
        
        from aider.coders.base_prompts import CoderPrompts
        
        prompts = CoderPrompts()
        
        # Test the main system prompt (game rules)
        main_system = prompts.main_system
        print("🎯 Main System Prompt (Game Rules):")
        print(f"   Length: {len(main_system)} characters")
        
        # Check for game elements
        game_elements = [
            ("CODEBASE EXPLORATION GAME", "✅" if "CODEBASE EXPLORATION GAME" in main_system else "❌"),
            ("LEVEL SYSTEM", "✅" if "LEVEL SYSTEM" in main_system else "❌"),
            ("LEVEL 0 - <PERSON><PERSON><PERSON> KNOWLEDGE", "✅" if "LEVEL 0 - ZER<PERSON> KNOWLEDGE" in main_system else "❌"),
            ("LEVEL 1 - REPOSITORY EXPLORER", "✅" if "LEVEL 1 - REPOSITORY EXPLORER" in main_system else "❌"),
            ("LEVEL 2 - CODE ANALYST", "✅" if "LEVEL 2 - CODE ANALYST" in main_system else "❌"),
            ("NO CHEATING", "✅" if "NO CHEATING" in main_system else "❌"),
            ("GAME OVER", "✅" if "GAME OVER" in main_system else "❌")
        ]
        
        print("\n🎮 Game Elements Check:")
        for element, status in game_elements:
            print(f"   {status} {element}")
        
        # Test user prompt (level 0 status)
        user_prompt = prompts.smart_map_request_user_prompt
        print(f"\n📋 User Prompt (Level 0 Status):")
        print(f"   Length: {len(user_prompt)} characters")
        
        level0_elements = [
            ("GAME STATUS", "✅" if "GAME STATUS" in user_prompt else "❌"),
            ("LEVEL 0 - ZERO KNOWLEDGE", "✅" if "LEVEL 0 - ZERO KNOWLEDGE" in user_prompt else "❌"),
            ("LEVEL 0 RULES", "✅" if "LEVEL 0 RULES" in user_prompt else "❌"),
            ("LEVEL 0 CAPABILITIES", "✅" if "LEVEL 0 CAPABILITIES" in user_prompt else "❌"),
            ("TO ADVANCE TO LEVEL 1", "✅" if "TO ADVANCE TO LEVEL 1" in user_prompt else "❌")
        ]
        
        print("\n📍 Level 0 Elements Check:")
        for element, status in level0_elements:
            print(f"   {status} {element}")
        
        # Test assistant reply (game acknowledgment)
        assistant_reply = prompts.smart_map_request_assistant_reply
        print(f"\n🤖 Assistant Reply (Game Acknowledgment):")
        print(f"   Length: {len(assistant_reply)} characters")
        
        acknowledgment_elements = [
            ("GAME ACKNOWLEDGED", "✅" if "GAME ACKNOWLEDGED" in assistant_reply else "❌"),
            ("LEVEL 0 - ZERO KNOWLEDGE", "✅" if "LEVEL 0 - ZERO KNOWLEDGE" in assistant_reply else "❌"),
            ("game rules", "✅" if "game rules" in assistant_reply else "❌"),
            ("cannot skip levels", "✅" if "cannot skip levels" in assistant_reply else "❌"),
            ("MAP_REQUEST to advance", "✅" if "MAP_REQUEST to advance" in assistant_reply else "❌")
        ]
        
        print("\n🎯 Acknowledgment Elements Check:")
        for element, status in acknowledgment_elements:
            print(f"   {status} {element}")
        
        # Overall assessment
        all_game_elements = all(status == "✅" for _, status in game_elements)
        all_level0_elements = all(status == "✅" for _, status in level0_elements)
        all_acknowledgment_elements = all(status == "✅" for _, status in acknowledgment_elements)
        
        if all_game_elements and all_level0_elements and all_acknowledgment_elements:
            print("\n🎉 SUCCESS: Game-based system is properly implemented!")
            return True
        else:
            print("\n❌ ISSUES: Some game elements are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_expected_game_behavior():
    """Test what the expected game behavior should be"""
    print("\n🎮 Expected Game Behavior")
    print("=" * 70)
    
    print("📝 When user asks: 'How does the close_position_based_on_conditions function work?'")
    print("\n🎯 Expected LLM Response (Level 0):")
    
    expected_response = """🎮 **CURRENT LEVEL**: LEVEL 0 - ZERO KNOWLEDGE

I have ZERO knowledge about this codebase and cannot provide any code explanations at this level.

**My roadmap to help you:**
1. **LEVEL 0 → LEVEL 1**: Use MAP_REQUEST to explore repository structure
2. **LEVEL 1 → LEVEL 2**: Use CONTEXT_REQUEST to get specific function implementation  
3. **LEVEL 2**: Analyze actual code and answer your question

**Starting Level 0 → Level 1 advancement:**

{MAP_REQUEST: {"keywords": ["close_position_based_on_conditions", "position", "close", "conditions"], "type": "implementation", "scope": "all", "max_results": 8}}"""
    
    print(expected_response)
    
    print(f"\n🔍 Key Game Behaviors:")
    print(f"   ✅ Acknowledges current level (LEVEL 0)")
    print(f"   ✅ Explains what it cannot do at this level")
    print(f"   ✅ Provides clear roadmap with level progression")
    print(f"   ✅ Uses MAP_REQUEST to advance to next level")
    print(f"   ✅ No assumptions or fabricated explanations")
    
    print(f"\n🎮 Level Progression:")
    print(f"   📍 LEVEL 0: Acknowledge ignorance → MAP_REQUEST")
    print(f"   📍 LEVEL 1: Review repository map → CONTEXT_REQUEST")
    print(f"   📍 LEVEL 2: Analyze actual code → Answer question")
    
    return True

def test_game_advantages():
    """Test the advantages of the game system"""
    print("\n🏆 Game System Advantages")
    print("=" * 70)
    
    advantages = [
        "🎯 **Clear Boundaries**: LLM knows exactly what it can/cannot do",
        "🔒 **Prevents Cheating**: Cannot skip levels or use higher capabilities",
        "🎮 **Gamification**: Makes following rules feel like playing a game",
        "📊 **Transparency**: User can see what level LLM is at",
        "⬆️ **Progression**: Clear path from ignorance to knowledge",
        "🛡️ **Anti-Fabrication**: Cannot make up code explanations",
        "🎯 **Goal-Oriented**: Each level has specific objectives",
        "🔄 **Systematic**: Forces proper workflow progression"
    ]
    
    print("🎮 Why the Game System Works:")
    for advantage in advantages:
        print(f"   {advantage}")
    
    print(f"\n🆚 Comparison with Previous Approaches:")
    print(f"   ❌ **Old**: 'Act as expert' → LLM acts like expert")
    print(f"   ❌ **Old**: 'Play dumb' → Conflicted with expert instructions")
    print(f"   ✅ **New**: 'You are at Level 0' → Clear game state")
    print(f"   ✅ **New**: 'Cannot use higher level capabilities' → Clear restrictions")
    print(f"   ✅ **New**: 'Advance by completing goals' → Clear progression")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Game-Based Codebase Exploration System")
    print("=" * 80)
    
    success1 = test_game_system_prompts()
    success2 = test_expected_game_behavior()
    success3 = test_game_advantages()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("🎉 ALL TESTS PASSED: Game-based system is ready!")
        print("\n🎮 Game System Summary:")
        print("   📍 LEVEL 0: Zero Knowledge → Roadmap + MAP_REQUEST")
        print("   📍 LEVEL 1: Repository Explorer → Review map + CONTEXT_REQUEST")
        print("   📍 LEVEL 2: Code Analyst → Analyze code + Answer questions")
        print("\n🔒 Key Features:")
        print("   ✅ Clear level boundaries and capabilities")
        print("   ✅ Cannot skip levels or cheat")
        print("   ✅ Gamified approach makes rules engaging")
        print("   ✅ Transparent progression system")
        print("   ✅ Anti-fabrication built into game mechanics")
    else:
        print("❌ SOME TESTS FAILED: Check output above for details")
        
    print("=" * 80)
