# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-05-30 21:32:23
# Project: C:\Users\<USER>\Documents\aider_project\aider__500
# User Query: Find performance bottlenecks
# Task Description: Identify slow code paths
# Task Type: debugging
# Max Tokens: 3000
# Focus Entities: performance, bottleneck
# Package Size: 8,040 characters

================================================================================

# USER QUERY
Find performance bottlenecks

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Identify slow code paths

## CRITICAL ENTITIES (6 most important)

### 1. event (function)
- File: aider-main\aider\analytics.py


- Criticality: high | Risk: high
- **Calls**: ["_redact_model_name", "update", "items", "track", "capture", "..."] (total: 9)
- **Used by**: ["test_analytics", "commands", "base_coder", "base_coder_old", "onboarding"] (total: 5)
- **Side Effects**: modifies_container, database_io, modifies_state

### 2. cmd_add (function)
- File: aider-main\aider\commands.py


- Criticality: high | Risk: high
- **Calls**: ["parse_quoted_filenames", "is_absolute", "Path", "ignored_file", "tool_warning", "..."] (total: 20)
- **Used by**: ["test_commands"] (total: 1)
- **Side Effects**: writes_log, network_io, database_io

### 3. read_text (function)
- File: aider-main\aider\io.py


- Criticality: high | Risk: high
- **Calls**: ["is_image_file", "read_image", "open", "read", "tool_error"] (total: 5)
- **Used by**: ["repo", "surgical_context_extractor", "problem_stats", "help", "test_main", "..."] (total: 10)
- **Side Effects**: modifies_state, writes_log, modifies_file

### 4. write_text (function)
- File: aider-main\aider\io.py


- Criticality: high | Risk: high
- **Calls**: ["open", "write", "sleep", "tool_error"] (total: 4)
- **Used by**: ["test_repo", "test_coder", "test_sanity_check_repo", "test_io", "test_file_not_found", "..."] (total: 10)
- **Side Effects**: modifies_state, writes_log, modifies_file

### 5. confirm_ask (function)
- File: aider-main\aider\io.py


- Criticality: high | Risk: high
- **Calls**: ["ring_bell", "append", "startswith", "lower", "tool_output", "..."] (total: 16)
- **Used by**: ["commands", "io", "test_io", "utils", "base_coder", "..."] (total: 8)
- **Side Effects**: network_io, modifies_state, writes_log

### 6. send_completion (function)
- File: aider-main\aider\models.py


- Criticality: high | Risk: high
- **Calls**: ["get", "sanity_check_messages", "is_deepseek_r1", "ensure_alternating_roles", "update", "..."] (total: 12)
- **Used by**: ["base_coder", "test_sendchat", "base_coder_old", "models", "test_models"] (total: 5)
- **Side Effects**: modifies_container, network_io, database_io

## KEY IMPLEMENTATIONS (6 functions)
Complete code available on request for any function.

### 1. event
```python
    def event(self, event_name, main_model=None, **kwargs):
        if not self.mp and not self.ph and not self.logfile:
            return

        properties = {}

        if main_model:
            properties["main_model"] = self._redact_model_name(main_model)
            properties["weak_model"] = self._redact_model_name(main_model.weak_model)
            properties["editor_model"] = self._redact_model_name(main_model.editor_model)

        properties.update(kwargs)

        # Handle numeric values
        for key, value in properties.items():
            if isinstance(value, (int, float)):
                properties[key] = value
            else:
                properties[key] = str(value)

        if self.mp:
            try:
                self.mp.track(self.user_id, event_name, dict(properties))
    # ... (implementation continues)
```

### 2. cmd_add
```python
    def cmd_add(self, args):
        "Add files to the chat so aider can edit them or review them in detail"

        all_matched_files = set()

        filenames = parse_quoted_filenames(args)
        for word in filenames:
            if Path(word).is_absolute():
                fname = Path(word)
            else:
                fname = Path(self.coder.root) / word

            if self.coder.repo and self.coder.repo.ignored_file(fname):
                self.io.tool_warning(f"Skipping {fname} due to aiderignore or --subtree-only.")
                continue

            if fname.exists():
                if fname.is_file():
                    all_matched_files.add(str(fname))
                    continue
                # an existing dir, escape any special chars so they won't be globs
    # ... (implementation continues)
```

### 3. read_text
```python
    def read_text(self, filename, silent=False):
        if is_image_file(filename):
            return self.read_image(filename)

        try:
            with open(str(filename), "r", encoding=self.encoding) as f:
                return f.read()
        except FileNotFoundError:
            if not silent:
                self.tool_error(f"{filename}: file not found error")
            return
        except IsADirectoryError:
            if not silent:
                self.tool_error(f"{filename}: is a directory")
            return
        except OSError as err:
            if not silent:
                self.tool_error(f"{filename}: unable to read: {err}")
            return
        except UnicodeError as e:
            if not silent:
                self.tool_error(f"{filename}: {e}")
    # ... (implementation continues)
```

### 4. write_text
```python
    def write_text(self, filename, content, max_retries=5, initial_delay=0.1):
        """
        Writes content to a file, retrying with progressive backoff if the file is locked.

        :param filename: Path to the file to write.
        :param content: Content to write to the file.
        :param max_retries: Maximum number of retries if a file lock is encountered.
        :param initial_delay: Initial delay (in seconds) before the first retry.
        """
        if self.dry_run:
            return

        delay = initial_delay
        for attempt in range(max_retries):
            try:
                with open(str(filename), "w", encoding=self.encoding, newline=self.newline) as f:
                    f.write(content)
                return  # Successfully wrote the file
    # ... (implementation continues)
```

### 5. confirm_ask
```python
    def confirm_ask(
        self,
        question,
        default="y",
        subject=None,
        explicit_yes_required=False,
        group=None,
        allow_never=False,
    ):
        self.num_user_asks += 1

        # Ring the bell if needed
        self.ring_bell()

        question_id = (question, subject)

        if question_id in self.never_prompts:
            return False

        if group and not group.show_group:
            group = None
        if group:
            allow_never = True

        valid_responses = ["yes", "no", "skip", "all"]
        options = " (Y)es/(N)o"
        if group:
            if not explicit_yes_required:
                options += "/(A)ll"
            options += "/(S)kip all"
        if allow_never:
            options += "/(D)on't ask again"
    # ... (implementation continues)
```

### 6. send_completion
```python
    def send_completion(self, messages, functions, stream, temperature=None):
        if os.environ.get("AIDER_SANITY_CHECK_TURNS"):
            sanity_check_messages(messages)

        if self.is_deepseek_r1():
            messages = ensure_alternating_roles(messages)

        kwargs = dict(
            model=self.name,
            stream=stream,
        )

        if self.use_temperature is not False:
            if temperature is None:
                if isinstance(self.use_temperature, bool):
                    temperature = 0
                else:
                    temperature = float(self.use_temperature)

            kwargs["temperature"] = temperature

        if functions is not None:
            function = functions[0]
            kwargs["tools"] = [dict(type="function", function=function)]
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 6 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Find performance bottlenecks

Provide specific, actionable insights based on this focused context.

