#!/usr/bin/env python3
"""
Context Bundle Builder for IAA Protocol

This module implements the ContextBundleBuilder that was specified in the original
PHASE_2_PLAN.txt design. It provides enhanced context selection with detailed
score breakdowns and memory integration.

Key Features:
- Detailed score breakdown for each selected entity
- Memory-aware scoring using AnalysisMemory
- Enhanced context bundles with rich metadata
- Token budget optimization
- Integration with existing IntelligentContextSelector
"""

import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone


@dataclass
class EntityScoreBreakdown:
    """Detailed score breakdown for an entity."""
    criticality: float = 0.0
    change_risk: float = 0.0
    task_relevance: float = 0.0
    confidence_gap: float = 0.0
    dependency_proximity: float = 0.0
    complexity: float = 0.0
    doc_gap: float = 0.0
    historical_relevance: float = 0.0
    total_score: float = 0.0


@dataclass
class EnhancedContextEntity:
    """Enhanced context entity with detailed scoring information."""
    id: str
    type: str
    file: str
    module_name: str
    entity_name: str
    
    # Scoring information
    score_breakdown: EntityScoreBreakdown
    total_score: float
    
    # Source code content
    code: Optional[str] = None
    line_range: Optional[Tuple[int, int]] = None
    
    # Metadata
    criticality: str = "low"
    change_risk: str = "low"
    used_by: List[str] = field(default_factory=list)
    calls: List[str] = field(default_factory=list)
    side_effects: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)


@dataclass
class EnhancedContextBundle:
    """Enhanced context bundle with detailed metadata and scoring."""
    task: str
    task_type: str
    context_bundle: List[EnhancedContextEntity]
    selected_by: str
    token_estimate: int
    
    # Enhanced metadata
    selection_timestamp: str
    selection_rationale: str
    score_distribution: Dict[str, int]
    confidence_analysis: Dict[str, Any]
    memory_insights: List[str]


class ContextBundleBuilder:
    """
    Enhanced context bundle builder that provides detailed scoring and memory integration.
    
    This class implements the ContextBundleBuilder specified in the original IAA Protocol
    design, providing rich context bundles with detailed score breakdowns.
    """
    
    def __init__(self, ir_data: Dict[str, Any], analysis_memory, 
                 confidence_tracker, token_budget: int = 8000):
        """
        Initialize the context bundle builder.
        
        Args:
            ir_data: Mid-Level IR data
            analysis_memory: AnalysisMemory instance for historical data
            confidence_tracker: ConfidenceTracker for confidence scoring
            token_budget: Maximum token budget for context selection
        """
        self.ir_data = ir_data
        self.analysis_memory = analysis_memory
        self.confidence_tracker = confidence_tracker
        self.token_budget = token_budget
        self.modules = ir_data.get('modules', [])
        
        # Build entity lookup maps
        self.entity_map = self._build_entity_map()
        self.dependency_graph = self._build_dependency_graph()
        
    def _build_entity_map(self) -> Dict[str, Dict[str, Any]]:
        """Build a map of entity IDs to entity data."""
        entity_map = {}
        
        for module in self.modules:
            module_name = module.get('name', '')
            file_path = module.get('file', '')
            
            for entity in module.get('entities', []):
                entity_name = entity.get('name', '')
                entity_id = f"{module_name}.{entity_name}"
                
                entity_data = {
                    **entity,
                    'module_name': module_name,
                    'file_path': file_path,
                    'entity_id': entity_id
                }
                
                entity_map[entity_id] = entity_data
                
        return entity_map
    
    def _build_dependency_graph(self) -> Dict[str, List[str]]:
        """Build a dependency graph for proximity scoring."""
        graph = {}
        
        for entity_id, entity_data in self.entity_map.items():
            calls = entity_data.get('calls', [])
            graph[entity_id] = calls
            
        return graph
    
    def build(self, task: str, task_type: str = "general_analysis", 
              focus_entities: Optional[List[str]] = None) -> EnhancedContextBundle:
        """
        Build an enhanced context bundle with detailed scoring.
        
        Args:
            task: Task description
            task_type: Type of analysis task
            focus_entities: Optional focus entities
            
        Returns:
            EnhancedContextBundle with detailed metadata
        """
        print(f"🏗️ Building enhanced context bundle for: {task}")
        
        # Step 1: Score all entities
        scored_entities = self._score_all_entities(task, task_type, focus_entities)
        
        # Step 2: Select entities within budget
        selected_entities = self._select_entities_within_budget(scored_entities)
        
        # Step 3: Build enhanced context bundle
        context_bundle = self._build_enhanced_bundle(
            task, task_type, selected_entities, scored_entities
        )
        
        print(f"✅ Enhanced context bundle built:")
        print(f"   Selected entities: {len(context_bundle.context_bundle)}")
        print(f"   Token estimate: {context_bundle.token_estimate}")
        print(f"   Score distribution: {context_bundle.score_distribution}")
        
        return context_bundle
    
    def _score_all_entities(self, task: str, task_type: str, 
                           focus_entities: Optional[List[str]] = None) -> List[EnhancedContextEntity]:
        """Score all entities with detailed breakdown."""
        scored_entities = []
        
        for entity_id, entity_data in self.entity_map.items():
            # Calculate detailed score breakdown
            score_breakdown = self._calculate_detailed_score(
                entity_id, entity_data, task, task_type, focus_entities
            )
            
            # Create enhanced context entity
            enhanced_entity = EnhancedContextEntity(
                id=entity_id,
                type=entity_data.get('type', 'unknown'),
                file=entity_data.get('file_path', ''),
                module_name=entity_data.get('module_name', ''),
                entity_name=entity_data.get('name', ''),
                score_breakdown=score_breakdown,
                total_score=score_breakdown.total_score,
                criticality=entity_data.get('criticality', 'low'),
                change_risk=entity_data.get('change_risk', 'low'),
                used_by=entity_data.get('used_by', []),
                calls=entity_data.get('calls', []),
                side_effects=entity_data.get('side_effects', []),
                errors=entity_data.get('errors', [])
            )
            
            scored_entities.append(enhanced_entity)
        
        # Sort by total score
        scored_entities.sort(key=lambda e: e.total_score, reverse=True)
        
        return scored_entities
    
    def _calculate_detailed_score(self, entity_id: str, entity_data: Dict[str, Any],
                                 task: str, task_type: str, 
                                 focus_entities: Optional[List[str]] = None) -> EntityScoreBreakdown:
        """Calculate detailed score breakdown for an entity."""
        breakdown = EntityScoreBreakdown()
        
        # 1. Criticality score
        criticality_map = {'low': 0.2, 'medium': 0.6, 'high': 1.0}
        breakdown.criticality = criticality_map.get(entity_data.get('criticality', 'low'), 0.2)
        
        # 2. Change risk score
        risk_map = {'low': 0.2, 'medium': 0.6, 'high': 1.0}
        breakdown.change_risk = risk_map.get(entity_data.get('change_risk', 'low'), 0.2)
        
        # 3. Task relevance score
        breakdown.task_relevance = self._calculate_task_relevance(entity_data, task)
        
        # 4. Confidence gap score (from memory)
        breakdown.confidence_gap = self._calculate_confidence_gap_score(entity_id)
        
        # 5. Dependency proximity score
        breakdown.dependency_proximity = self._calculate_dependency_proximity(entity_id, focus_entities)
        
        # 6. Complexity score
        breakdown.complexity = self._calculate_complexity_score(entity_data)
        
        # 7. Documentation gap score
        breakdown.doc_gap = self._calculate_doc_gap_score(entity_data)
        
        # 8. Historical relevance score (from memory)
        breakdown.historical_relevance = self._calculate_historical_relevance(entity_id)
        
        # Calculate total score with task-specific weights
        weights = self._get_task_weights(task_type)
        breakdown.total_score = (
            breakdown.criticality * weights.get('criticality', 0.2) +
            breakdown.change_risk * weights.get('change_risk', 0.15) +
            breakdown.task_relevance * weights.get('task_relevance', 0.25) +
            breakdown.confidence_gap * weights.get('confidence_gap', 0.1) +
            breakdown.dependency_proximity * weights.get('dependency_proximity', 0.15) +
            breakdown.complexity * weights.get('complexity', 0.05) +
            breakdown.doc_gap * weights.get('doc_gap', 0.05) +
            breakdown.historical_relevance * weights.get('historical_relevance', 0.05)
        )
        
        return breakdown

    def _calculate_task_relevance(self, entity_data: Dict[str, Any], task: str) -> float:
        """Calculate task relevance score based on text matching."""
        task_words = set(task.lower().split())

        # Check entity name
        entity_name = entity_data.get('name', '').lower()
        name_words = set(entity_name.replace('_', ' ').split())
        name_overlap = len(task_words.intersection(name_words)) / max(len(task_words), 1)

        # Check module name
        module_name = entity_data.get('module_name', '').lower()
        module_words = set(module_name.replace('_', ' ').split())
        module_overlap = len(task_words.intersection(module_words)) / max(len(task_words), 1)

        # Check docstring if available
        doc = entity_data.get('doc', '').lower()
        doc_words = set(doc.split()) if doc else set()
        doc_overlap = len(task_words.intersection(doc_words)) / max(len(task_words), 1)

        return (name_overlap * 0.5 + module_overlap * 0.3 + doc_overlap * 0.2)

    def _calculate_confidence_gap_score(self, entity_id: str) -> float:
        """Calculate confidence gap score from analysis memory."""
        if entity_id in self.analysis_memory.low_confidence_entities:
            return 1.0  # High score for entities needing more analysis
        elif entity_id in self.analysis_memory.high_confidence_entities:
            return 0.0  # Low score for well-understood entities
        else:
            return 0.5  # Medium score for unknown entities

    def _calculate_dependency_proximity(self, entity_id: str, focus_entities: Optional[List[str]]) -> float:
        """Calculate dependency proximity score."""
        if not focus_entities:
            return 0.0

        score = 0.0

        # Check if this entity is directly in focus
        for focus in focus_entities:
            if focus in entity_id:
                score += 1.0

        # Check dependency relationships
        calls = self.dependency_graph.get(entity_id, [])
        for call in calls:
            for focus in focus_entities:
                if focus in call:
                    score += 0.5

        return min(score, 1.0)

    def _calculate_complexity_score(self, entity_data: Dict[str, Any]) -> float:
        """Calculate complexity score."""
        # Use number of calls and dependencies as complexity indicator
        calls_count = len(entity_data.get('calls', []))
        used_by_count = len(entity_data.get('used_by', []))

        complexity = calls_count + used_by_count
        return min(complexity / 10.0, 1.0)  # Normalize to 0-1

    def _calculate_doc_gap_score(self, entity_data: Dict[str, Any]) -> float:
        """Calculate documentation gap score."""
        doc = entity_data.get('doc', '')
        if not doc or len(doc.strip()) < 10:
            return 1.0  # High score for poor documentation
        elif len(doc.strip()) < 50:
            return 0.5  # Medium score for minimal documentation
        else:
            return 0.0  # Low score for good documentation

    def _calculate_historical_relevance(self, entity_id: str) -> float:
        """Calculate historical relevance from analysis memory."""
        return self.analysis_memory.get_historical_relevance_score(entity_id)

    def _get_task_weights(self, task_type: str) -> Dict[str, float]:
        """Get task-specific weights for scoring factors."""
        weights = {
            'debugging': {
                'criticality': 0.25, 'change_risk': 0.15, 'task_relevance': 0.2,
                'confidence_gap': 0.15, 'dependency_proximity': 0.15,
                'complexity': 0.05, 'doc_gap': 0.03, 'historical_relevance': 0.02
            },
            'feature_development': {
                'criticality': 0.2, 'change_risk': 0.2, 'task_relevance': 0.25,
                'confidence_gap': 0.1, 'dependency_proximity': 0.15,
                'complexity': 0.05, 'doc_gap': 0.03, 'historical_relevance': 0.02
            },
            'refactoring': {
                'criticality': 0.2, 'change_risk': 0.25, 'task_relevance': 0.2,
                'confidence_gap': 0.1, 'dependency_proximity': 0.15,
                'complexity': 0.05, 'doc_gap': 0.03, 'historical_relevance': 0.02
            },
            'documentation': {
                'criticality': 0.15, 'change_risk': 0.1, 'task_relevance': 0.25,
                'confidence_gap': 0.1, 'dependency_proximity': 0.1,
                'complexity': 0.05, 'doc_gap': 0.2, 'historical_relevance': 0.05
            }
        }

        return weights.get(task_type, weights['debugging'])

    def _select_entities_within_budget(self, scored_entities: List[EnhancedContextEntity]) -> List[EnhancedContextEntity]:
        """Select entities within token budget."""
        selected = []
        total_tokens = 0

        for entity in scored_entities:
            estimated_tokens = self._estimate_tokens(entity)
            if total_tokens + estimated_tokens <= self.token_budget * 0.9:
                selected.append(entity)
                total_tokens += estimated_tokens
            else:
                break

        return selected

    def _estimate_tokens(self, entity: EnhancedContextEntity) -> int:
        """Estimate tokens for an entity."""
        base_tokens = {
            'function': 60,
            'class': 120,
            'variable': 15,
            'constant': 15
        }

        tokens = base_tokens.get(entity.type, 60)

        # Add tokens for complexity
        tokens += len(entity.calls) * 5
        tokens += len(entity.used_by) * 3

        return tokens

    def _build_enhanced_bundle(self, task: str, task_type: str,
                              selected_entities: List[EnhancedContextEntity],
                              all_scored_entities: List[EnhancedContextEntity]) -> EnhancedContextBundle:
        """Build the final enhanced context bundle."""
        total_tokens = sum(self._estimate_tokens(e) for e in selected_entities)

        # Calculate score distribution
        score_ranges = {'0-2': 0, '2-4': 0, '4-6': 0, '6-8': 0, '8-10': 0}
        for entity in selected_entities:
            score = entity.total_score
            if score < 2:
                score_ranges['0-2'] += 1
            elif score < 4:
                score_ranges['2-4'] += 1
            elif score < 6:
                score_ranges['4-6'] += 1
            elif score < 8:
                score_ranges['6-8'] += 1
            else:
                score_ranges['8-10'] += 1

        # Generate selection rationale
        rationale = self._generate_selection_rationale(selected_entities, task_type)

        # Get memory insights
        memory_insights = self._get_memory_insights(selected_entities)

        # Build confidence analysis
        confidence_analysis = self._build_confidence_analysis(selected_entities)

        return EnhancedContextBundle(
            task=task,
            task_type=task_type,
            context_bundle=selected_entities,
            selected_by="ContextBundleBuilder(v2.0.1)",
            token_estimate=total_tokens,
            selection_timestamp=datetime.now(timezone.utc).isoformat(),
            selection_rationale=rationale,
            score_distribution=score_ranges,
            confidence_analysis=confidence_analysis,
            memory_insights=memory_insights
        )

    def _generate_selection_rationale(self, entities: List[EnhancedContextEntity], task_type: str) -> str:
        """Generate human-readable selection rationale."""
        high_score_count = len([e for e in entities if e.total_score >= 6])
        critical_count = len([e for e in entities if e.criticality == 'high'])

        rationale = f"""Enhanced Context Selection for {task_type}:
- Selected {len(entities)} entities based on multi-factor scoring
- {high_score_count} high-relevance entities (score >= 6.0)
- {critical_count} critical entities included
- Memory-aware selection with confidence gap analysis
- Task-specific weighting optimized for {task_type}"""

        return rationale

    def _get_memory_insights(self, entities: List[EnhancedContextEntity]) -> List[str]:
        """Get insights from analysis memory."""
        insights = []

        low_confidence_count = len([e for e in entities if e.id in self.analysis_memory.low_confidence_entities])
        if low_confidence_count > 0:
            insights.append(f"{low_confidence_count} entities selected for confidence improvement")

        high_confidence_count = len([e for e in entities if e.id in self.analysis_memory.high_confidence_entities])
        if high_confidence_count > 0:
            insights.append(f"{high_confidence_count} well-understood entities included for context")

        return insights

    def _build_confidence_analysis(self, entities: List[EnhancedContextEntity]) -> Dict[str, Any]:
        """Build confidence analysis for selected entities."""
        confidence_scores = []

        for entity in entities:
            if hasattr(self.confidence_tracker, 'entity_confidences'):
                confidence = self.confidence_tracker.entity_confidences.get(entity.id, 0.5)
                confidence_scores.append(confidence)

        if confidence_scores:
            return {
                'average_confidence': sum(confidence_scores) / len(confidence_scores),
                'min_confidence': min(confidence_scores),
                'max_confidence': max(confidence_scores),
                'entities_analyzed': len(confidence_scores)
            }
        else:
            return {'message': 'No confidence data available'}

    def to_dict(self, bundle: EnhancedContextBundle) -> Dict[str, Any]:
        """Convert enhanced context bundle to dictionary format."""
        return {
            'task': bundle.task,
            'task_type': bundle.task_type,
            'context_bundle': [
                {
                    'id': entity.id,
                    'type': entity.type,
                    'file': entity.file,
                    'code': entity.code or f"# Code for {entity.entity_name}",
                    'score_breakdown': {
                        'criticality': entity.score_breakdown.criticality,
                        'change_risk': entity.score_breakdown.change_risk,
                        'task_relevance': entity.score_breakdown.task_relevance,
                        'confidence_gap': entity.score_breakdown.confidence_gap,
                        'dependency_proximity': entity.score_breakdown.dependency_proximity,
                        'complexity': entity.score_breakdown.complexity,
                        'doc_gap': entity.score_breakdown.doc_gap,
                        'historical_relevance': entity.score_breakdown.historical_relevance
                    },
                    'total_score': entity.total_score
                }
                for entity in bundle.context_bundle
            ],
            'selected_by': bundle.selected_by,
            'token_estimate': bundle.token_estimate,
            'selection_timestamp': bundle.selection_timestamp,
            'selection_rationale': bundle.selection_rationale,
            'score_distribution': bundle.score_distribution,
            'confidence_analysis': bundle.confidence_analysis,
            'memory_insights': bundle.memory_insights
        }
