#!/usr/bin/env python3
"""
Test script to verify that the RepoMap-style file discovery works correctly
for CONTEXT_REQUEST file resolution.
"""

import os
import sys

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_repomap_style_discovery():
    """Test the RepoMap-style file discovery mechanism."""
    print("🧪 Testing RepoMap-Style File Discovery")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a context request handler
        project_path = "aider-main"  # Use aider-main as test project
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(project_path, aider_service)
        
        print(f"📁 Project path: {project_path}")
        print(f"✅ Handler created successfully")
        
        # Test cases for file discovery
        test_cases = [
            # (file_hint, expected_to_find, description)
            ("aider/coders/base_coder.py", True, "Exact path match"),
            ("coders/base_coder.py", True, "Partial path match"),
            ("base_coder.py", True, "Filename only match"),
            ("aider\\coders\\base_coder.py", True, "Windows-style path"),
            ("nonexistent/file.py", False, "Non-existent file"),
            ("aider/repomap.py", True, "Another exact path"),
            ("repomap.py", True, "Another filename only"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for file_hint, expected_to_find, description in test_cases:
            print(f"\n🔍 Testing: {description}")
            print(f"   File hint: {file_hint}")
            
            try:
                discovered_file = handler._discover_file_like_repomap(file_hint)
                
                if expected_to_find:
                    if discovered_file:
                        rel_path = os.path.relpath(discovered_file, project_path)
                        print(f"   ✅ Found: {rel_path}")
                        print(f"   📂 Full path: {discovered_file}")
                        
                        # Verify the file actually exists
                        if os.path.exists(discovered_file):
                            print(f"   ✅ File exists on disk")
                            passed += 1
                        else:
                            print(f"   ❌ File doesn't exist on disk: {discovered_file}")
                    else:
                        print(f"   ❌ Expected to find file but didn't")
                else:
                    if discovered_file:
                        print(f"   ❌ Expected not to find file but found: {discovered_file}")
                    else:
                        print(f"   ✅ Correctly didn't find non-existent file")
                        passed += 1
                        
            except Exception as e:
                print(f"   ❌ Error during discovery: {e}")
        
        print(f"\n📊 File discovery test: {passed}/{total} cases passed")
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing file discovery: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_matching():
    """Test the directory parts matching logic."""
    print("\n🧪 Testing Directory Parts Matching")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a handler to test the method
        handler = ContextRequestHandler(".", AiderIntegrationService())
        
        test_cases = [
            # (target_parts, candidate_parts, expected_match, description)
            (["aider", "coders"], ["aider", "coders"], True, "Exact match"),
            (["coders"], ["aider", "coders"], True, "Partial match (end)"),
            (["aider"], ["aider", "coders"], True, "Partial match (start)"),
            (["aider", "coders"], ["coders"], False, "Target longer than candidate"),
            (["wrong"], ["aider", "coders"], False, "No match"),
            ([], ["aider", "coders"], True, "Empty target (always matches)"),
            (["coders"], ["aider", "coders", "extra"], True, "Partial match with extra"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for target_parts, candidate_parts, expected_match, description in test_cases:
            print(f"\n🔍 Testing: {description}")
            print(f"   Target: {target_parts}")
            print(f"   Candidate: {candidate_parts}")
            
            try:
                result = handler._directory_parts_match(target_parts, candidate_parts)
                
                if result == expected_match:
                    print(f"   ✅ Correct result: {result}")
                    passed += 1
                else:
                    print(f"   ❌ Expected {expected_match}, got {result}")
                    
            except Exception as e:
                print(f"   ❌ Error during matching: {e}")
        
        print(f"\n📊 Directory matching test: {passed}/{total} cases passed")
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing directory matching: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_context_request():
    """Test the integration with actual CONTEXT_REQUEST processing."""
    print("\n🧪 Testing Integration with CONTEXT_REQUEST")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a context request handler
        project_path = "aider-main"
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(project_path, aider_service)
        
        # Test symbol request with file hint
        symbol = SymbolRequest(
            type="method_definition",
            name="get_repo_map",
            file_hint="aider/coders/base_coder.py"
        )
        
        print(f"🎯 Testing symbol: {symbol.name}")
        print(f"   File hint: {symbol.file_hint}")
        
        # Test the _find_file_for_symbol method
        found_file = handler._find_file_for_symbol(symbol)
        
        if found_file:
            print(f"   ✅ Found file: {found_file}")
            
            # Verify the file exists
            full_path = os.path.join(project_path, found_file) if not os.path.isabs(found_file) else found_file
            if os.path.exists(full_path):
                print(f"   ✅ File exists on disk")
                
                # Try to extract symbol content
                file_path, symbol_name, content = handler._extract_symbol_content(symbol)
                if content:
                    print(f"   ✅ Successfully extracted symbol content")
                    print(f"   📝 Content length: {len(content)} characters")
                    print(f"   📝 Content preview: {content[:100]}...")
                    return True
                else:
                    print(f"   ❌ Failed to extract symbol content")
                    return False
            else:
                print(f"   ❌ File doesn't exist: {full_path}")
                return False
        else:
            print(f"   ❌ Failed to find file for symbol")
            return False
            
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all RepoMap-style file discovery tests."""
    print("🚀 Testing RepoMap-Style File Discovery for CONTEXT_REQUEST")
    print("=" * 80)

    tests = [
        test_repomap_style_discovery,
        test_directory_matching,
        test_integration_with_context_request,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 80)
    print(f"🎯 FINAL RESULTS: {passed}/{total} test categories passed")

    if passed == total:
        print("🎉 RepoMap-style file discovery is working correctly!")
        print("\n📋 The fix ensures:")
        print("  1. ✅ Uses same file discovery logic as RepoMap")
        print("  2. ✅ Handles both forward and backward slashes")
        print("  3. ✅ Supports partial directory matching")
        print("  4. ✅ Skips hidden and build directories")
        print("  5. ✅ Integrates with CONTEXT_REQUEST processing")
        print("\n🔧 Technical details:")
        print("  - Replicates os.walk() logic from RepoMap")
        print("  - Normalizes paths for cross-platform compatibility")
        print("  - Supports exact, partial, and filename-only matching")
        print("  - Consistent with repository map file discovery")
    else:
        print("⚠️  Some file discovery features need attention. Please review the failed tests.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
