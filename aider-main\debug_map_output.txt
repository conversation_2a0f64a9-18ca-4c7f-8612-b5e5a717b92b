# Focused Repository Map

**Search Keywords**: backtest, trading, strategy
**Files Found**: 5

⚠️  **CRITICAL INSTRUCTION**: This map shows file structure and symbols only. DO NOT analyze or explain functionality based on this map alone. Your NEXT response must be EXACTLY this format:

```json
{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME_FROM_MAP", "directory_name": "folder_name", "file_name": "file.py"}]}}
```

🚨 **CRITICAL**: Use ONLY the EXACT symbol names shown in the repository map below. DO NOT make assumptions about class/function names (like assuming 'Backtest' class exists). If you don't see the exact symbol name in the map, you CANNOT request it, and always follow the protocol if you faild to find the exact name(s). PROTOCOL: MAP_REQUEST → Get the exact name(s) → Wait → CONTEXT_REQUEST

**IMPORTANT**: If you see the same method/function name in multiple files, request context from ALL of them:

```json
{CONTEXT_REQUEST: {"original_user_query_context": "brief summary", "symbols_of_interest": [{"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME", "directory_name": "folder1", "file_name": "file1.py"}, {"type": "method_definition", "name": "ACTUAL_SYMBOL_NAME", "directory_name": "folder2", "file_name": "file2.py"}]}}
```

**WARNING: If you don't follow this protocol, you will NOT be able to assist the user properly.**

## Search Results

1. **C:\Users\<USER>\Documents\aider__v2\aider\aider-main\aider\website\docs\troubleshooting\imports.md** (score: 30.0)
   - Match types: filename

2. **C:\Users\<USER>\Documents\aider__v2\aider\aider-main\tests\fixtures\languages\arduino\test.ino** (score: 30.0)
   - Match types: filename

3. **C:\Users\<USER>\Documents\aider__v2\aider\aider-main\tests\fixtures\languages\c\test.c** (score: 30.0)
   - Match types: filename

4. **C:\Users\<USER>\Documents\aider__v2\aider\aider-main\tests\fixtures\languages\chatito\test.chatito** (score: 30.0)
   - Match types: filename

5. **C:\Users\<USER>\Documents\aider__v2\aider\aider-main\tests\fixtures\languages\commonlisp\test.lisp** (score: 30.0)
   - Match types: filename

## Repository Structure


aider\website\docs\troubleshooting\imports.md

tests\fixtures\languages\arduino\test.ino:
⋮
│void setup() {
│  // Initialize serial communication
│  Serial.begin(9600);
│  pinMode(LED_BUILTIN, OUTPUT);
⋮
│void loop() {
│  // Main code that runs repeatedly
│  digitalWrite(LED_BUILTIN, HIGH);
│  delay(1000);
│  digitalWrite(LED_BUILTIN, LOW);
│  delay(1000);
│  Serial.println("Blinking LED");
⋮
│int calculateDelay(int baseDelay, int multiplier) {
│  return baseDelay * multiplier;
⋮

tests\fixtures\languages\c\test.c:
⋮
│int main() {
│    printf("Hello, World!\n");
│    return 0;
⋮
│int main(int argc, char **argv) {
│    printf("Hello, World!\n");
│    return 0;
⋮
│void print_message(const char *message) {
│    printf("%s\n", message);
⋮

tests\fixtures\languages\chatito\test.chatito:
│%[intent]('training': '60', 'testing': '40')
⋮
│%[name]('training': '50', 'testing': '50')
⋮
│~[endPolite]
⋮

tests\fixtures\languages\commonlisp\test.lisp:
⋮
│(defun greet (name)
│  "Return a greeting string for NAME."
⋮
│(defclass person ()
⋮
│(defmethod print-object ((obj person) stream)
│  (print-unreadable-object (obj stream :type t)
⋮
